(()=>{var e={};e.id=3147,e.ids=[3147],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},24353:(e,s,t)=>{Promise.resolve().then(t.bind(t,36227))},28253:(e,s,t)=>{"use strict";t.d(s,{CartProvider:()=>l,_:()=>n});var r=t(60687),i=t(43210);let a=(0,i.createContext)(void 0),n=()=>{let e=(0,i.useContext)(a);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},l=({children:e})=>{let[s,t]=(0,i.useState)([]);(0,i.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,i.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(s))},[s]);let n=e=>{t(s=>s.filter(s=>s.id!==e))},l=s.reduce((e,s)=>e+s.quantity,0),d=s.reduce((e,s)=>e+s.price*s.quantity,0),c=s.reduce((e,s)=>e+s.pv*s.quantity,0);return(0,r.jsx)(a.Provider,{value:{items:s,addItem:e=>{t(s=>{let t=s.findIndex(s=>s.id===e.id);if(!(t>=0))return[...s,e];{let r=[...s];return r[t]={...r[t],quantity:r[t].quantity+e.quantity},r}})},removeItem:n,updateQuantity:(e,s)=>{if(s<=0)return void n(e);t(t=>t.map(t=>t.id===e?{...t,quantity:s}:t))},clearCart:()=>{t([])},itemCount:l,subtotal:d,totalPV:c},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36227:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\mobile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\mobile\\page.tsx","default")},37043:(e,s,t)=>{"use strict";t.d(s,{CartProvider:()=>i});var r=t(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},37505:(e,s,t)=>{Promise.resolve().then(t.bind(t,71816))},41750:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},45851:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var r=t(60687),i=t(25217),a=t(8693),n=t(43210);function l({children:e}){let[s]=(0,n.useState)(()=>new i.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(a.Ht,{client:s,children:e})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var r=t(60687),i=t(43210);let a=()=>"serviceWorker"in navigator,n=async()=>{if(!a())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),l(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},l=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let s=e.installing;s&&s.addEventListener("statechange",()=>{"installed"===s.state&&navigator.serviceWorker.controller&&d()})})},d=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},c=({children:e})=>{let[s,t]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{n();let e=()=>{t(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,s&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,s,t)=>{Promise.resolve().then(t.bind(t,37043)),Promise.resolve().then(t.bind(t,23229)),Promise.resolve().then(t.bind(t,82113)),Promise.resolve().then(t.bind(t,41750))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71816:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(60687),i=t(43210),a=t(82136),n=t(59391),l=t(23877),d=t(85814),c=t.n(d);function o({userId:e,maxLevel:s=3,initialPageSize:t=10}){let[a,n]=(0,i.useState)(!0),[d,c]=(0,i.useState)(null),[o,m]=(0,i.useState)(null),[x,h]=(0,i.useState)(null),[u,p]=(0,i.useState)([]),[v,b]=(0,i.useState)(new Set),[f,g]=(0,i.useState)(""),[j,y]=(0,i.useState)([]),[N,w]=(0,i.useState)(!1),[C,P]=(0,i.useState)(!1),[S,k]=(0,i.useState)(null),M=(0,i.useCallback)(async e=>{if(e.children)return e.children;try{let s=new URLSearchParams({userId:e.id.toString(),maxLevel:"1",pageSize:t.toString(),includePerformanceMetrics:"true"}),r=await fetch(`/api/genealogy?${s.toString()}`);if(!r.ok)throw Error("Failed to fetch user children");let i=await r.json();return i.children?.map(s=>({id:s.id,name:s.name,email:s.email,rankName:s.rank.name,level:e.level+1,downlineCount:s._count.downline,createdAt:s.createdAt,walletBalance:s.walletBalance,performanceMetrics:s.performanceMetrics}))||[]}catch(e){return console.error("Error fetching user children:",e),[]}},[t]),L=(0,i.useCallback)(async e=>{if(v.has(e.id))b(s=>{let t=new Set(s);return t.delete(e.id),t});else if(b(s=>{let t=new Set(s);return t.add(e.id),t}),!e.children){n(!0);try{let s=await M(e);x&&x.id===e.id&&h({...x,children:s}),o&&o.id===e.id&&m({...o,children:s}),p(t=>t.map(t=>t.id===e.id?{...t,children:s}:t))}catch(e){console.error("Error expanding node:",e)}finally{n(!1)}}},[v,x,o,M]),A=(0,i.useCallback)(async e=>{n(!0);try{let s={...e};if(!e.children){let t=await M(e);s={...e,children:t}}x&&p(e=>[...e,x]),h(s)}catch(e){console.error("Error navigating to child:",e)}finally{n(!1)}},[x,M]),E=(0,i.useCallback)(()=>{if(0===u.length)return;let e=u[u.length-1];p(e=>e.slice(0,-1)),h(e)},[u]),I=(0,i.useCallback)(async()=>{if(f.trim()){w(!0);try{let e=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:f,page:1,pageSize:10})});if(!e.ok)throw Error("Failed to search genealogy");let s=await e.json();y(s.users)}catch(e){console.error("Error searching genealogy:",e)}finally{w(!1)}}},[f]),q=(0,i.useCallback)(async e=>{n(!0);try{let s=new URLSearchParams({userId:e.id.toString(),maxLevel:"1",pageSize:t.toString(),includePerformanceMetrics:"true"}),r=await fetch(`/api/genealogy?${s.toString()}`);if(!r.ok)throw Error("Failed to fetch user data");let i=await r.json(),a={id:i.id,name:i.name,email:i.email,rankName:i.rank.name,level:0,downlineCount:i._count.downline,createdAt:i.createdAt,walletBalance:i.walletBalance,performanceMetrics:i.performanceMetrics,children:i.children?.map(e=>({id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:1,downlineCount:e._count.downline,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics}))};p([]),h(a),P(!1)}catch(e){console.error("Error selecting user from search:",e)}finally{n(!1)}},[t]),D=e=>void 0===e?"":new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);if(a&&!x)return(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading genealogy data..."})]});if(d)return(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,r.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,r.jsx)(l.BS8,{className:"mr-2"}),"Error loading genealogy data"]}),(0,r.jsx)("p",{className:"text-red-600",children:d})]});if(S){var _;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center bg-gray-50",children:[(0,r.jsxs)("button",{onClick:()=>k(null),className:"flex items-center text-blue-600",children:[(0,r.jsx)(l.QVr,{className:"mr-2"}),"Back"]}),(0,r.jsx)("h3",{className:"font-semibold",children:"User Details"}),(0,r.jsx)("div",{className:"w-6"})," "]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:S.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:S.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:S.email}),(0,r.jsx)("div",{className:"mt-1 text-xs px-2 py-0.5 inline-block rounded-full bg-blue-100 text-blue-800",children:S.rankName})]})]}),(0,r.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"User ID"}),(0,r.jsx)("div",{children:S.id})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Downline Members"}),(0,r.jsx)("div",{children:S.downlineCount})]}),void 0!==S.walletBalance&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Wallet Balance"}),(0,r.jsx)("div",{children:D(S.walletBalance)})]}),S.createdAt&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Member Since"}),(0,r.jsx)("div",{children:(_=S.createdAt)?new Date(_).toLocaleDateString():"N/A"})]}),S.level>0&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Network Level"}),(0,r.jsxs)("div",{children:["Level ",S.level]})]})]}),S.performanceMetrics&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,r.jsx)("h3",{className:"font-medium mb-3",children:"Performance Metrics"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(l.AsH,{className:"text-blue-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Personal Sales"})]}),(0,r.jsx)("div",{className:"text-lg font-semibold",children:D(S.performanceMetrics.personalSales)})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(l.YXz,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Team Sales"})]}),(0,r.jsx)("div",{className:"text-lg font-semibold",children:D(S.performanceMetrics.teamSales)})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(l.lcY,{className:"text-yellow-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Rebates Earned"})]}),(0,r.jsx)("div",{className:"text-lg font-semibold",children:D(S.performanceMetrics.rebatesEarned)})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-3 rounded-md",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(l.YXz,{className:"text-purple-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"New Members (30d)"})]}),(0,r.jsx)("div",{className:"text-lg font-semibold",children:S.performanceMetrics.newTeamMembers})]})]})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:(0,r.jsx)("button",{onClick:()=>{k(null),A(S)},className:"px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm",children:"View Downline"})})})]})]})}return C?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center bg-gray-50",children:[(0,r.jsxs)("button",{onClick:()=>P(!1),className:"flex items-center text-blue-600",children:[(0,r.jsx)(l.QVr,{className:"mr-2"}),"Back"]}),(0,r.jsx)("h3",{className:"font-semibold",children:"Search Genealogy"}),(0,r.jsx)("div",{className:"w-6"})," "]}),(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID...",value:f,onChange:e=>g(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("button",{onClick:I,disabled:N,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:N?(0,r.jsx)(l.hW,{className:"animate-spin"}):(0,r.jsx)(l.KSO,{})})]})}),(0,r.jsx)("div",{className:"divide-y",children:0===j.length?(0,r.jsx)("div",{className:"p-4 text-center text-gray-500",children:N?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Searching..."})]}):f?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:"No results found."}),(0,r.jsx)("p",{className:"text-sm mt-1",children:"Try a different search term."})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)(l.KSO,{className:"text-4xl text-gray-300 mx-auto mb-2"}),(0,r.jsx)("p",{children:"Enter a search term to find users."})]})}):j.map(e=>(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:e.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]}),(0,r.jsx)("button",{onClick:()=>q(e),className:"px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 text-sm",children:"View"})]})},e.id))})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center bg-gray-50",children:[u.length>0?(0,r.jsxs)("button",{onClick:E,className:"flex items-center text-blue-600",children:[(0,r.jsx)(l.QVr,{className:"mr-2"}),"Back"]}):(0,r.jsx)("div",{}),(0,r.jsx)("h3",{className:"font-semibold",children:"Mobile Genealogy"}),(0,r.jsx)("button",{onClick:()=>P(!0),className:"text-blue-600",children:(0,r.jsx)(l.KSO,{})})]}),x&&(0,r.jsxs)("div",{className:"p-4 border-b bg-blue-50",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-3",children:(0,r.jsx)(l.x$1,{className:"text-blue-500"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:x.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:x.email}),(0,r.jsx)("div",{className:"mt-1 text-xs px-2 py-0.5 inline-block rounded-full bg-blue-100 text-blue-800",children:x.rankName})]}),(0,r.jsx)("button",{onClick:()=>k(x),className:"px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm",children:"Details"})]}),(0,r.jsxs)("div",{className:"mt-3 grid grid-cols-3 gap-2 text-center text-sm",children:[(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"font-medium",children:x.downlineCount}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Downline"})]}),x.performanceMetrics&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"font-medium",children:D(x.performanceMetrics.personalSales)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Personal"})]}),(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"font-medium",children:D(x.performanceMetrics.teamSales)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Team"})]})]})]})]}),(0,r.jsxs)("div",{className:"divide-y",children:[a&&(0,r.jsxs)("div",{className:"p-4 flex items-center justify-center",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]}),!a&&x?.children?.length===0&&(0,r.jsx)("div",{className:"p-4 text-center text-gray-500",children:(0,r.jsx)("p",{children:"No downline members found."})}),!a&&x?.children?.map(e=>(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:e.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-400 mt-1",children:[(0,r.jsxs)("span",{className:"mr-2",children:["ID: ",e.id]}),(0,r.jsxs)("span",{children:["Downline: ",e.downlineCount]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("button",{onClick:()=>k(e),className:"p-1 text-blue-600",children:(0,r.jsx)(l.__w,{})}),e.downlineCount>0&&(0,r.jsx)("button",{onClick:()=>A(e),className:"p-1 text-green-600",children:(0,r.jsx)(l.YXz,{})})]})]}),e.downlineCount>0&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("button",{onClick:()=>L(e),className:"flex items-center text-sm text-blue-600",children:v.has(e.id)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.Vr3,{className:"mr-1"}),"Hide Details"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.X6T,{className:"mr-1"}),"Show Details"]})}),v.has(e.id)&&(0,r.jsxs)("div",{className:"mt-2 pl-4 border-l-2 border-gray-200",children:[e.performanceMetrics&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,r.jsx)("div",{className:"font-medium",children:D(e.performanceMetrics.personalSales)})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,r.jsx)("div",{className:"font-medium",children:D(e.performanceMetrics.teamSales)})]})]}),(0,r.jsxs)("button",{onClick:()=>A(e),className:"w-full mt-2 px-3 py-1.5 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 text-sm flex items-center justify-center",children:[(0,r.jsx)(l.YXz,{className:"mr-2"}),"View Downline (",e.downlineCount,")"]})]})]})]},e.id))]})]})}function m(){let{data:e,status:s}=(0,a.useSession)(),[t,d]=(0,i.useState)(void 0),{data:m,isLoading:x}=(0,n.I)({queryKey:["user"],queryFn:async()=>{if(!e?.user?.email)return null;let s=await fetch("/api/users/me");if(!s.ok)throw Error("Failed to fetch user data");return await s.json()},enabled:"authenticated"===s});return(m&&!t&&d(m.id),"loading"===s||x)?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(l.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,r.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Mobile Genealogy View"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Optimized for mobile devices and touch screens"})]}),(0,r.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(l.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"md:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 bg-gray-50 border-b border-gray-200 flex items-center",children:[(0,r.jsx)(l.q5F,{className:"text-blue-500 mr-2"}),(0,r.jsx)("h2",{className:"font-medium",children:"Mobile Genealogy View"})]}),(0,r.jsx)("div",{className:"max-w-sm mx-auto my-4",children:t?(0,r.jsx)(o,{userId:t,maxLevel:3,initialPageSize:10}):(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading genealogy data..."})]})})]})}),(0,r.jsx)("div",{className:"md:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 bg-gray-50 border-b border-gray-200 flex items-center",children:[(0,r.jsx)(l.z4D,{className:"text-blue-500 mr-2"}),(0,r.jsx)("h2",{className:"font-medium",children:"About Mobile View"})]}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{children:"The mobile genealogy view is designed specifically for mobile devices and touch screens. It provides a simplified, list-based view of your genealogy that's easy to navigate on smaller screens."}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Key Features"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"Touch-friendly navigation"}),(0,r.jsx)("li",{children:"Simplified list view of your downline"}),(0,r.jsx)("li",{children:"Expandable details for each member"}),(0,r.jsx)("li",{children:"Quick access to performance metrics"}),(0,r.jsx)("li",{children:"Search functionality to find specific members"}),(0,r.jsx)("li",{children:"Optimized for small screens and mobile data connections"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"How to Use"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Tap on a member's name to see their details"}),(0,r.jsx)("li",{children:'Use the "Show Details" button to expand information'}),(0,r.jsx)("li",{children:'Tap "View Downline" to navigate to a member\'s downline'}),(0,r.jsx)("li",{children:"Use the back button to return to previous views"}),(0,r.jsx)("li",{children:"Use the search icon to find specific members"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"When to Use Mobile View"}),(0,r.jsx)("p",{className:"text-yellow-700",children:"The mobile view is ideal for:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700 mt-2",children:[(0,r.jsx)("li",{children:"Checking your genealogy on the go"}),(0,r.jsx)("li",{children:"Devices with smaller screens"}),(0,r.jsx)("li",{children:"Touch-based navigation"}),(0,r.jsx)("li",{children:"Limited bandwidth connections"}),(0,r.jsx)("li",{children:"Quick lookups of specific members"})]})]}),(0,r.jsx)("p",{children:"For a more comprehensive visualization with advanced features, switch to the desktop view when you're on a larger screen."})]})})]})})]})]})}},74104:(e,s,t)=>{Promise.resolve().then(t.bind(t,28253)),Promise.resolve().then(t.bind(t,97695)),Promise.resolve().then(t.bind(t,45851)),Promise.resolve().then(t.bind(t,63345))},79551:e=>{"use strict";e.exports=require("url")},80138:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["genealogy",{children:["mobile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36227)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\mobile\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\mobile\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/genealogy/mobile/page",pathname:"/genealogy/mobile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},82113:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h,metadata:()=>x});var r=t(37413),i=t(22376),a=t.n(i),n=t(68726),l=t.n(n);t(61135);var d=t(23229),c=t(37043),o=t(82113),m=t(41750);let x={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function h({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${l().variable} antialiased`,children:(0,r.jsx)(d.AuthProvider,{children:(0,r.jsx)(o.default,{children:(0,r.jsx)(c.CartProvider,{children:(0,r.jsx)(m.default,{children:e})})})})})})}},96111:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},97695:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>a});var r=t(60687),i=t(82136);function a({children:e}){return(0,r.jsx)(i.SessionProvider,{children:e})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,8414,9567,3877,9391],()=>t(80138));module.exports=r})();
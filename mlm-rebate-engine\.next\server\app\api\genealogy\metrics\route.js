(()=>{var e={};e.id=2111,e.ids=[2111],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>l,Nh:()=>c,aP:()=>u});var s=r(96330),a=r(13581),n=r(85663),o=r(55511),i=r.n(o);async function l(e){return await n.Ay.hash(e,10)}function u(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,a.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new s.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let a=await n.Ay.compare(e.password,r.password);if(console.log("Password valid:",a),!a)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:o,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var a=r(12269);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=a?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(s,n,i):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(35426));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=global.prisma||new s.PrismaClient({log:["query"]})},33121:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>I,routeModule:()=>A,serverHooks:()=>S,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{GET:()=>m});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),l=r(19854),u=r(12909),c=r(31183),d=r(70762);let p=d.z.object({userId:d.z.string().transform(e=>parseInt(e)),timeRange:d.z.enum(["last30days","last90days","last6months","last12months"]).default("last30days")});async function m(e){try{let t,r=await (0,l.getServerSession)(u.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to access genealogy metrics"},{status:401});let s=new URL(e.url),a=s.searchParams.get("userId"),n=s.searchParams.get("timeRange")||"last30days",o=p.safeParse({userId:a,timeRange:n});if(!o.success)return i.NextResponse.json({error:"Invalid parameters",details:o.error.format()},{status:400});let{userId:d,timeRange:m}=o.data,b=new Date;switch(m){case"last30days":default:t=new Date(b.getTime()-2592e6);break;case"last90days":t=new Date(b.getTime()-7776e6);break;case"last6months":t=new Date(b.getFullYear(),b.getMonth()-6,b.getDate());break;case"last12months":t=new Date(b.getFullYear()-1,b.getMonth(),b.getDate())}let A=await g(d),x=A.map(e=>e.id),v=A.length,S=await c.z.user.count({where:{id:{in:x},purchases:{some:{createdAt:{gte:t},status:"completed"}}}}),I=await c.z.user.count({where:{id:{in:x},createdAt:{gte:new Date(b.getTime()-2592e6)}}}),M=(await c.z.purchase.aggregate({where:{userId:{in:x},createdAt:{gte:t},status:"completed"},_sum:{totalAmount:!0}}))._sum.totalAmount||0,k=(await c.z.rebate.aggregate({where:{receiverId:{in:x},createdAt:{gte:t},status:"completed"},_sum:{amount:!0}}))._sum.amount||0,O=(await c.z.rank.findMany({select:{id:!0,name:!0,_count:{select:{users:{where:{id:{in:x}}}}}},orderBy:{level:"asc"}})).map(e=>({rankName:e.name,count:e._count.users})),P=await f(x,t),j=await w(x,t),_=await h(x,t),E=await y(d);return i.NextResponse.json({totalMembers:v,activeMembers:S,newMembersLast30Days:I,totalSales:M,averageSalesPerMember:v>0?M/v:0,totalRebates:k,rankDistribution:O,salesByMonth:P,newMembersByMonth:j,topPerformers:_,networkDepth:E})}catch(e){return console.error("Error fetching genealogy metrics:",e),i.NextResponse.json({error:"Failed to fetch genealogy metrics"},{status:500})}}async function g(e){let t=await c.z.user.findMany({select:{id:!0,uplineId:!0}}),r=new Map;t.forEach(e=>{e.uplineId&&(r.has(e.uplineId)||r.set(e.uplineId,[]),r.get(e.uplineId)?.push(e.id))});let s=[];return!function e(t){(r.get(t)||[]).forEach(t=>{s.push({id:t}),e(t)})}(e),s}async function f(e,t){let r=await c.z.purchase.findMany({where:{userId:{in:e},createdAt:{gte:t},status:"completed"},select:{createdAt:!0,totalAmount:!0,userId:!0}}),s=new Map;r.forEach(t=>{let r=t.createdAt.toISOString().substring(0,7);s.has(r)||s.set(r,{personalSales:0,teamSales:0});let a=s.get(r);a.teamSales+=t.totalAmount,t.userId===e[0]&&(a.personalSales+=t.totalAmount)});let a=Array.from(s.entries()).map(([e,t])=>({month:b(e),personalSales:t.personalSales,teamSales:t.teamSales}));return a.sort((e,t)=>{let r=e.month.split(" "),s=t.month.split(" "),a=parseInt(r[1]),n=parseInt(s[1]);if(a!==n)return a-n;let o=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];return o.indexOf(r[0])-o.indexOf(s[0])}),a}async function w(e,t){let r=await c.z.user.findMany({where:{id:{in:e},createdAt:{gte:t}},select:{createdAt:!0}}),s=new Map;r.forEach(e=>{let t=e.createdAt.toISOString().substring(0,7);s.has(t)||s.set(t,0),s.set(t,s.get(t)+1)});let a=Array.from(s.entries()).map(([e,t])=>({month:b(e),count:t}));return a.sort((e,t)=>{let r=e.month.split(" "),s=t.month.split(" "),a=parseInt(r[1]),n=parseInt(s[1]);if(a!==n)return a-n;let o=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];return o.indexOf(r[0])-o.indexOf(s[0])}),a}async function h(e,t){let r=await c.z.purchase.findMany({where:{userId:{in:e},createdAt:{gte:t},status:"completed"},select:{userId:!0,totalAmount:!0}}),s=new Map;r.forEach(e=>{s.has(e.userId)||s.set(e.userId,{personalSales:0,teamSales:0});let t=s.get(e.userId);t.personalSales+=e.totalAmount,t.teamSales+=e.totalAmount});let a=(await c.z.user.findMany({where:{id:{in:Array.from(s.keys())}},select:{id:!0,name:!0,_count:{select:{downline:!0}}}})).map(e=>{let t=s.get(e.id)||{personalSales:0,teamSales:0};return{id:e.id,name:e.name,personalSales:t.personalSales,teamSales:t.teamSales,downlineCount:e._count.downline}});return a.sort((e,t)=>t.teamSales-e.teamSales),a.slice(0,5)}async function y(e){let t=await c.z.user.findMany({select:{id:!0,uplineId:!0}}),r=new Map;t.forEach(e=>{e.uplineId&&(r.has(e.uplineId)||r.set(e.uplineId,[]),r.get(e.uplineId)?.push(e.id))});let s=new Map;!function e(t,a){let n=r.get(t)||[];s.has(a)||s.set(a,0),s.set(a,s.get(a)+n.length),n.forEach(t=>{e(t,a+1)})}(e,1);let a=Array.from(s.entries()).map(([e,t])=>({level:e,count:t}));return a.sort((e,t)=>e.level-t.level),a}function b(e){let[t,r]=e.split("-");return`${["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][parseInt(r)-1]} ${t}`}let A=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/genealogy/metrics/route",pathname:"/api/genealogy/metrics",filename:"route",bundlePath:"app/api/genealogy/metrics/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\metrics\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:S}=A;function I(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,8044,3112,8381],()=>r(33121));module.exports=s})();
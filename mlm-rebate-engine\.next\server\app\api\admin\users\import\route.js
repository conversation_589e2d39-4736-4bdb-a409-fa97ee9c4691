"use strict";(()=>{var e={};e.id=8682,e.ids=[8682],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6500:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>g,serverHooks:()=>v,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>x,POST:()=>f});var a=t(96559),o=t(48088),i=t(37719),n=t(31183),u=t(32190),p=t(19854),d=t(12909),l=t(91818),c=t(70762);let m=c.z.object({defaultPassword:c.z.string().min(8).default("Password123!"),skipDuplicates:c.z.boolean().default(!0)});async function f(e){try{let r=await (0,p.getServerSession)(d.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=parseInt(r.user.id),s=await n.z.user.findUnique({where:{id:t},select:{id:!0,name:!0,rankId:!0}});if(s?.rankId!==6)return u.NextResponse.json({error:"You must be an admin to access this endpoint"},{status:403});if((e.headers.get("content-type")||"").includes("multipart/form-data")){let r=await e.formData(),t=r.get("file"),s=r.get("options");if(!t)return u.NextResponse.json({error:"No file provided"},{status:400});if(s)try{let e=JSON.parse(s),r=m.safeParse(e);r.success&&r.data}catch(e){console.error("Error parsing import options:",e)}let a=Buffer.from(await t.arrayBuffer()),o=await (0,l.U4)(a);return u.NextResponse.json({validationResults:o,totalRows:o.length,validRows:o.filter(e=>e.isValid).length,invalidRows:o.filter(e=>!e.isValid).length})}{let{validatedData:r,options:t={defaultPassword:"Password123!"}}=await e.json();if(!r||!Array.isArray(r))return u.NextResponse.json({error:"Invalid validated data"},{status:400});let a=await (0,l.y6)(r,t.defaultPassword,s.id,s.name);return await n.z.userAudit.create({data:{userId:s.id,action:"bulk_import",details:JSON.stringify({totalProcessed:a.totalProcessed,successful:a.successful,failed:a.failed,duplicates:a.duplicates,timestamp:new Date().toISOString()})}}),u.NextResponse.json(a)}}catch(e){return console.error("Error importing users:",e),u.NextResponse.json({error:"Failed to import users"},{status:500})}}async function x(e){try{let e=await (0,p.getServerSession)(d.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let r=parseInt(e.user.id),t=await n.z.user.findUnique({where:{id:r},select:{rankId:!0}});if(t?.rankId!==6)return u.NextResponse.json({error:"You must be an admin to access this endpoint"},{status:403});let s=(0,l.Ol)();return new u.NextResponse(s,{headers:{"Content-Type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","Content-Disposition":"attachment; filename=user_import_template.xlsx"}})}catch(e){return console.error("Error generating template:",e),u.NextResponse.json({error:"Failed to generate template"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/users/import/route",pathname:"/api/admin/users/import",filename:"route",bundlePath:"app/api/admin/users/import/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\import\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:w,serverHooks:v}=g;function q(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:w})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,8381,632,1426],()=>t(6500));module.exports=s})();
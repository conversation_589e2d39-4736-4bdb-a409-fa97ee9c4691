"use strict";exports.id=9753,exports.ids=[9753],exports.modules={9753:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var r=s(60687),n=s(43210),a=s(30474),i=s(85814),o=s.n(i),l=s(23877);let c=()=>{let[e,t]=(0,n.useState)(!0),[s,i]=(0,n.useState)([]),[c,d]=(0,n.useState)(null),[m,u]=(0,n.useState)(null),[x,h]=(0,n.useState)(!1),[f,p]=(0,n.useState)(null),[g,b]=(0,n.useState)(!1);(0,n.useEffect)(()=>{j()},[]);let j=async()=>{try{t(!0);let e=await fetch("/api/products?limit=5&sort=popular");if(!e.ok)throw Error("Failed to fetch products");let s=(await e.json()).products||[];i(s),s.length>0&&d(s[0])}catch(e){console.error("Error fetching products:",e),p("Failed to load products. Please try again.")}finally{t(!1)}},w=async()=>{if(c)try{b(!0),p(null);let e=await fetch("/api/shareable-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:c.id,title:c.name,description:c.description,customImage:c.image})});if(!e.ok)throw Error("Failed to generate link");let t=await e.json();u(t.link)}catch(e){console.error("Error generating link:",e),p("Failed to generate link. Please try again.")}finally{b(!1)}},y=async()=>{if(m)try{let e=window.location.origin,t=`${e}/s/${m.code}`;await navigator.clipboard.writeText(t),h(!0),setTimeout(()=>{h(!1)},2e3)}catch(e){console.error("Error copying to clipboard:",e),p("Failed to copy link to clipboard")}},N=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return e?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 flex items-center justify-center h-64",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Loading products..."})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 bg-green-50 border-b border-green-100",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,r.jsx)(l.AnD,{className:"mr-2 text-green-600"}),"Quick Share"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Generate shareable links for products and earn commissions"})]}),(0,r.jsxs)("div",{className:"p-4",children:[f&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm",children:f}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select a product to share"}),(0,r.jsx)("select",{className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500",value:c?.id||"",onChange:e=>{let t=parseInt(e.target.value);d(s.find(e=>e.id===t)||null),u(null)},children:s.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.name," - ",N(e.price)," (",e.pv," PV)"]},e.id))})]}),c&&(0,r.jsxs)("div",{className:"mb-4 flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-16 w-16 bg-gray-100 rounded-md overflow-hidden mr-3",children:c.image?(0,r.jsx)(a.default,{src:c.image,alt:c.name,width:64,height:64,className:"object-cover w-full h-full"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(l.AsH,{className:"text-gray-400"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:c.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500 line-clamp-2",children:c.description}),(0,r.jsxs)("div",{className:"mt-1 flex items-center",children:[(0,r.jsx)("span",{className:"text-green-600 font-medium mr-3",children:N(c.price)}),(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:[c.pv," PV"]})]})]})]}),m?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-700 truncate mr-2",children:[window.location.origin,"/s/",m.code]}),(0,r.jsx)("button",{type:"button",className:"flex-shrink-0 text-blue-600 hover:text-blue-800",onClick:y,title:"Copy link",children:x?(0,r.jsx)(l.CMH,{className:"text-green-600"}):(0,r.jsx)(l.paH,{})})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:()=>{if(!m)return;let e=window.location.origin,t=`${e}/s/${m.code}`,s=`Check out ${c?.name}! ${t}`;window.open(`https://wa.me/?text=${encodeURIComponent(s)}`,"_blank")},children:[(0,r.jsx)(l.EcP,{className:"mr-1"}),"WhatsApp"]}),(0,r.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>{if(!m)return;let e=window.location.origin,t=`${e}/s/${m.code}`;window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(t)}`,"_blank")},children:[(0,r.jsx)(l.iYk,{className:"mr-1"}),"Facebook"]}),(0,r.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",onClick:()=>{if(!m)return;let e=window.location.origin,t=`${e}/s/${m.code}`,s=`Check out ${c?.name}!`,r=`I thought you might be interested in this product:

${c?.name}

${t}`;window.open(`mailto:?subject=${encodeURIComponent(s)}&body=${encodeURIComponent(r)}`)},children:[(0,r.jsx)(l.maD,{className:"mr-1"}),"Email"]})]})]}):(0,r.jsx)("button",{type:"button",className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:w,disabled:!c||g,children:g?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.hW,{className:"animate-spin mr-2"}),"Generating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.AnD,{className:"mr-2"}),"Generate Shareable Link"]})}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)(o(),{href:"/referrals",className:"text-sm text-blue-600 hover:underline",children:"View all my shareable links"})})]})]})}}};
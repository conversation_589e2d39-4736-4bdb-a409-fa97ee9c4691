(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8733],{66837:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),l=t(12115),r=t(12108),i=t(35695),n=t(70357),d=t(99526),c=t(29911),o=t(46710),x=t(13568);function m(e){let{isOpen:s,onClose:t,onImportComplete:r}=e,[i,n]=(0,l.useState)(null),[d,m]=(0,l.useState)(!1),[u,h]=(0,l.useState)(!1),[p,g]=(0,l.useState)(!1),[j,b]=(0,l.useState)([]),[f,y]=(0,l.useState)(null),[N,v]=(0,l.useState)("upload"),[w,k]=(0,l.useState)({defaultPassword:"Password123!",skipDuplicates:!0}),C=(0,l.useRef)(null),S=(0,l.useCallback)(e=>{e.length>0&&n(e[0])},[]),{getRootProps:E,getInputProps:I,isDragActive:F}=(0,o.VB)({onDrop:S,accept:{"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":[".xlsx"],"application/vnd.ms-excel":[".xls"]},maxFiles:1}),R=async()=>{if(i){h(!0);try{let e=new FormData;e.append("file",i),e.append("options",JSON.stringify(w));let s=await fetch("/api/admin/users/import",{method:"POST",body:e});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to validate file")}let t=await s.json();b(t.validationResults),v("validate")}catch(e){console.error("Error validating file:",e),x.oR.error("Failed to validate file: "+(e instanceof Error?e.message:"Unknown error"))}finally{h(!1)}}},D=async()=>{g(!0);try{let e=j.filter(e=>e.isValid),s=await fetch("/api/admin/users/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({validatedData:e,options:w})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to import users")}let t=await s.json();y(t),v("summary"),r(),x.oR.success("Successfully imported ".concat(t.successful," users"))}catch(e){console.error("Error importing users:",e),x.oR.error("Failed to import users: "+(e instanceof Error?e.message:"Unknown error"))}finally{g(!1)}},U=async()=>{try{let e=await fetch("/api/admin/users/import",{method:"GET"});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to download template")}let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.style.display="none",a.href=t,a.download="user_import_template.xlsx",document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}catch(e){console.error("Error downloading template:",e),x.oR.error("Failed to download template: "+(e instanceof Error?e.message:"Unknown error"))}},P=()=>{n(null),b([]),y(null),v("upload")},O=()=>{P(),t()};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Import Users"}),(0,a.jsx)("button",{onClick:O,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center ".concat("upload"===N?"text-blue-600":"text-gray-500"),children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("upload"===N?"bg-blue-100":"bg-gray-100"),children:"1"}),(0,a.jsx)("span",{className:"ml-2",children:"Upload"})]}),(0,a.jsx)("div",{className:"w-12 h-1 mx-2 bg-gray-200"}),(0,a.jsxs)("div",{className:"flex items-center ".concat("validate"===N?"text-blue-600":"text-gray-500"),children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("validate"===N?"bg-blue-100":"bg-gray-100"),children:"2"}),(0,a.jsx)("span",{className:"ml-2",children:"Validate"})]}),(0,a.jsx)("div",{className:"w-12 h-1 mx-2 bg-gray-200"}),(0,a.jsxs)("div",{className:"flex items-center ".concat("summary"===N?"text-blue-600":"text-gray-500"),children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("summary"===N?"bg-blue-100":"bg-gray-100"),children:"3"}),(0,a.jsx)("span",{className:"ml-2",children:"Complete"})]})]}),"upload"===N&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(c.__w,{className:"text-blue-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Upload an Excel file (.xlsx) containing user data. You can download a template to get started."})]}),(0,a.jsx)("div",{className:"flex justify-end mb-4",children:(0,a.jsxs)("button",{onClick:U,className:"flex items-center text-sm text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(c.WCW,{className:"mr-1"}),"Download Template"]})}),(0,a.jsxs)("div",{...E(),className:"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ".concat(F?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400"),children:[(0,a.jsx)("input",{...I(),ref:C}),(0,a.jsx)(c.Ru,{className:"mx-auto text-4xl text-green-500 mb-3"}),i?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-800",children:i.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[(i.size/1024).toFixed(2)," KB"]}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),n(null)},className:"mt-2 text-sm text-red-600 hover:text-red-800",children:"Remove"})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-800",children:"Drag & drop an Excel file here"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"or click to select a file"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Supported formats: .xlsx, .xls"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Import Options"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Default Password"}),(0,a.jsx)("input",{type:"text",value:w.defaultPassword,onChange:e=>k({...w,defaultPassword:e.target.value}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter default password for new users"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"This password will be assigned to all imported users."})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"skipDuplicates",checked:w.skipDuplicates,onChange:e=>k({...w,skipDuplicates:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"skipDuplicates",className:"ml-2 block text-sm text-gray-700",children:"Skip duplicate users (based on email)"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:O,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{onClick:R,disabled:!i||u,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin mr-2"}),"Validating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.HVe,{className:"mr-2"}),"Validate File"]})})]})]}),"validate"===N&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-800",children:"Validation Results"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500 mr-1"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Valid: ",j.filter(e=>e.isValid).length]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500 mr-1"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Invalid: ",j.filter(e=>!e.isValid).length]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-500 mr-1"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Total: ",j.length]})]})]})]}),0===j.length?(0,a.jsx)("div",{className:"bg-yellow-50 p-4 rounded-md",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(c.BS8,{className:"text-yellow-400 mt-0.5 mr-2"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700",children:"No data found in the uploaded file. Please check the file format and try again."})]})}):(0,a.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Row"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Member ID"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Upline"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errors"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map((e,s)=>{var t,l,r,i;return(0,a.jsxs)("tr",{className:e.isValid?"":"bg-red-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.row}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.isValid?(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:[(0,a.jsx)(c.CMH,{className:"mr-1"})," Valid"]}):(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:[(0,a.jsx)(c.QCr,{className:"mr-1"})," Invalid"]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(t=e.data)?void 0:t.memberId)||"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(null==(l=e.data)?void 0:l.name)||"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(r=e.data)?void 0:r.email)||"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(i=e.data)?void 0:i.uplineId)||"-"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-red-500",children:e.errors.join(", ")})]},s)})})]})})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:P,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Back"}),(0,a.jsx)("button",{onClick:D,disabled:0===j.filter(e=>e.isValid).length||p,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin mr-2"}),"Importing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.CMH,{className:"mr-2"}),"Confirm Import"]})})]})]}),"summary"===N&&f&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(c.CMH,{className:"h-5 w-5 text-green-400"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Import Completed Successfully"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,a.jsxs)("p",{children:[f.successful," users were successfully imported.",f.failed>0&&" ".concat(f.failed," users failed to import."),f.duplicates>0&&" ".concat(f.duplicates," duplicates were skipped.")]})})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-white border rounded-md p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:f.successful}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Successfully Imported"})]}),(0,a.jsxs)("div",{className:"bg-white border rounded-md p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:f.failed}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Failed to Import"})]}),(0,a.jsxs)("div",{className:"bg-white border rounded-md p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:f.duplicates}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Duplicates Skipped"})]})]}),f.importedUsers.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Imported Users"}),(0,a.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Member ID"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Upline ID"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.importedUsers.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.memberId}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.uplineId||"-"})]},e.id))})]})})})]}),f.errors.length>0&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Errors"}),(0,a.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Row"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errors"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.errors.map((e,s)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.row}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-red-500",children:e.errors.join(", ")})]},s))})]})})})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:O,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Close"}),(0,a.jsx)("button",{onClick:P,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Import More Users"})]})]})]})]})}):null}function u(e){let{isOpen:s,onClose:t}=e,[r,i]=(0,l.useState)(!1),[n,d]=(0,l.useState)([]),[o,m]=(0,l.useState)(!1),[u,h]=(0,l.useState)({includeRank:!0,includeDownlineCount:!0,includeJoinDate:!0,includeEarnings:!1,activeOnly:!1});(0,l.useEffect)(()=>{s&&p()},[s]);let p=async()=>{m(!0);try{let e=await fetch("/api/ranks");if(!e.ok)throw Error("Failed to fetch ranks");let s=await e.json();d(s)}catch(e){console.error("Error fetching ranks:",e),x.oR.error("Failed to fetch ranks")}finally{m(!1)}},g=async()=>{i(!0);try{let e=document.createElement("form");e.method="POST",e.action="/api/admin/users/export",e.target="_blank";let s=document.createElement("input");s.type="hidden",s.name="options",s.value=JSON.stringify(u),e.appendChild(s),document.body.appendChild(e),e.submit(),document.body.removeChild(e),x.oR.success("Export started. Your download will begin shortly."),t()}catch(e){console.error("Error exporting users:",e),x.oR.error("Failed to export users: "+(e instanceof Error?e.message:"Unknown error"))}finally{i(!1)}};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Export Users"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Export Options"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"includeRank",checked:u.includeRank,onChange:e=>h({...u,includeRank:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"includeRank",className:"ml-2 block text-sm text-gray-700",children:"Include Rank"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"includeDownlineCount",checked:u.includeDownlineCount,onChange:e=>h({...u,includeDownlineCount:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"includeDownlineCount",className:"ml-2 block text-sm text-gray-700",children:"Include Downline Count"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"includeJoinDate",checked:u.includeJoinDate,onChange:e=>h({...u,includeJoinDate:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"includeJoinDate",className:"ml-2 block text-sm text-gray-700",children:"Include Join Date"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"includeEarnings",checked:u.includeEarnings,onChange:e=>h({...u,includeEarnings:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"includeEarnings",className:"ml-2 block text-sm text-gray-700",children:"Include Earnings"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"activeOnly",checked:u.activeOnly,onChange:e=>h({...u,activeOnly:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"activeOnly",className:"ml-2 block text-sm text-gray-700",children:"Active Users Only"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Rank"}),(0,a.jsxs)("select",{value:u.rankFilter||"",onChange:e=>h({...u,rankFilter:e.target.value?parseInt(e.target.value):void 0}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Ranks"}),o?(0,a.jsx)("option",{disabled:!0,children:"Loading ranks..."}):n.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Join Date From"}),(0,a.jsx)("input",{type:"date",value:u.dateRangeStart||"",onChange:e=>h({...u,dateRangeStart:e.target.value||void 0}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Join Date To"}),(0,a.jsx)("input",{type:"date",value:u.dateRangeEnd||"",onChange:e=>h({...u,dateRangeEnd:e.target.value||void 0}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:t,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{onClick:g,disabled:r,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin mr-2"}),"Exporting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.Ru,{className:"mr-2"}),"Export to Excel"]})})]})]})]})}):null}function h(e){let{limit:s=10}=e,[t,r]=(0,l.useState)([]),[i,n]=(0,l.useState)(!0),[d,o]=(0,l.useState)(null);(0,l.useEffect)(()=>{m()},[]);let m=async()=>{n(!0),o(null);try{let e=await fetch("/api/admin/users/audit?limit=".concat(s));if(!e.ok)throw Error("Failed to fetch audit logs");let t=await e.json();r(t)}catch(e){console.error("Error fetching audit logs:",e),o("Failed to fetch audit logs"),x.oR.error("Failed to fetch audit logs")}finally{n(!1)}},u=e=>new Date(e).toLocaleString(),h=e=>{try{return JSON.parse(e)}catch(e){return null}},p=e=>{switch(e){case"import":return(0,a.jsx)(c.PiR,{className:"text-blue-500"});case"bulk_import":return(0,a.jsx)(c.PiR,{className:"text-green-500"});case"export":return(0,a.jsx)(c.Mbn,{className:"text-purple-500"});default:return(0,a.jsx)(c.OKX,{className:"text-gray-500"})}},g=e=>{let s=h(e.details);switch(e.action){case"import":return"Imported a single user";case"bulk_import":if(s)return"Imported ".concat(s.successful," users (").concat(s.totalProcessed," processed, ").concat(s.failed," failed, ").concat(s.duplicates," duplicates)");return"Bulk imported users";case"export":return"Exported user data";default:return e.action}};return i?(0,a.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading audit logs..."})]}):d?(0,a.jsx)("div",{className:"bg-red-50 p-4 rounded-md",children:(0,a.jsx)("p",{className:"text-red-700",children:d})}):0===t.length?(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(0,a.jsx)("p",{className:"text-gray-700",children:"No import/export history found."})}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsxs)("div",{className:"px-4 py-5 sm:px-6 border-b",children:[(0,a.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Import/Export History"}),(0,a.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Recent user import and export activities."})]}),(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:t.map(e=>(0,a.jsx)("li",{className:"px-4 py-4 sm:px-6 hover:bg-gray-50",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"mr-4",children:p(e.action)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:g(e)}),(0,a.jsxs)("div",{className:"flex mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500 mr-4",children:[(0,a.jsx)(c.x$1,{className:"mr-1"}),e.user.name]}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,a.jsx)(c.bfZ,{className:"mr-1"}),u(e.createdAt)]})]})]})]})})},e.id))})]})}let p=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)([]),[d,o]=(0,l.useState)({page:1,pageSize:10,totalItems:0,totalPages:0}),[x,p]=(0,l.useState)(""),[g,j]=(0,l.useState)(""),[b,f]=(0,l.useState)(!1),[y,N]=(0,l.useState)(null),[v,w]=(0,l.useState)(!1),[k,C]=(0,l.useState)(!1),[S,E]=(0,l.useState)({name:"",email:"",phone:"",rankId:"",uplineId:""}),[I,F]=(0,l.useState)(!1),[R,D]=(0,l.useState)(!1),[U,P]=(0,l.useState)(!1),[O,A]=(0,l.useState)({type:"",text:""});(0,l.useEffect)(()=>{V(),_()},[d.page,d.pageSize,x,g]);let V=async()=>{r(!0);try{let e=new URLSearchParams;e.append("page",d.page.toString()),e.append("pageSize",d.pageSize.toString()),x&&e.append("search",x),g&&e.append("rankId",g);let t=await fetch("/api/users?".concat(e.toString()));if(!t.ok)throw Error("Failed to fetch users: ".concat(t.statusText));let a=await t.json();s(a.users),o(a.pagination)}catch(e){console.error("Error fetching users:",e),A({type:"error",text:"Failed to fetch users. Please try again."})}finally{r(!1)}},_=async()=>{try{let e=await fetch("/api/ranks");if(!e.ok)throw Error("Failed to fetch ranks: ".concat(e.statusText));let s=await e.json();n(s)}catch(e){console.error("Error fetching ranks:",e)}},J=e=>{e>0&&e<=d.totalPages&&o(s=>({...s,page:e}))},T=e=>{N(e),w(!0),C(!1)},L=e=>{N(e),E({name:e.name,email:e.email,phone:e.phone||"",rankId:e.rankId.toString(),uplineId:e.uplineId?e.uplineId.toString():""}),C(!0),w(!1)},z=e=>{let{name:s,value:t}=e.target;E(e=>({...e,[s]:t}))},W=async e=>{if(e.preventDefault(),y){r(!0),A({type:"",text:""});try{let e=await fetch("/api/users/".concat(y.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:S.name,phone:S.phone||null,rankId:parseInt(S.rankId),uplineId:S.uplineId?parseInt(S.uplineId):null})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to update user")}A({type:"success",text:"User updated successfully"}),V(),C(!1)}catch(e){A({type:"error",text:e.message||"An error occurred while updating the user"})}finally{r(!1)}}},B=async e=>{if(confirm("Are you sure you want to reset this user's wallet balance to 0?")){r(!0),A({type:"",text:""});try{let s=await fetch("/api/users/".concat(e,"/wallet/reset"),{method:"POST"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to reset wallet balance")}A({type:"success",text:"Wallet balance reset successfully"}),V()}catch(e){A({type:"error",text:e.message||"An error occurred while resetting wallet balance"})}finally{r(!1)}}},M=()=>{P(!U)};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"p-6 border-b",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),o(e=>({...e,page:1}))},className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search by name or email",value:x,onChange:e=>{p(e.target.value)},className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(c.KSO,{className:"text-gray-400"})}),(0,a.jsx)("button",{type:"submit",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-blue-500 hover:text-blue-700",children:"Search"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>{F(!0)},className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[(0,a.jsx)(c.PiR,{className:"mr-2"}),"Import"]}),(0,a.jsxs)("button",{onClick:()=>{D(!0)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[(0,a.jsx)(c.Mbn,{className:"mr-2"}),"Export"]}),(0,a.jsxs)("button",{onClick:()=>f(!b),className:"flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:[(0,a.jsx)(c.YsJ,{className:"mr-2"}),"Filters",b?(0,a.jsx)(c.Ucs,{className:"ml-2"}):(0,a.jsx)(c.Vr3,{className:"ml-2"})]}),(0,a.jsxs)("button",{onClick:M,className:"flex items-center px-4 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200",children:[(0,a.jsx)(c.OKX,{className:"mr-2"}),"History"]})]})]}),b&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,a.jsxs)("select",{value:g,onChange:e=>{j(e.target.value),o(e=>({...e,page:1}))},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Ranks"}),i.map(e=>(0,a.jsx)("option",{value:e.id.toString(),children:e.name},e.id))]})]})})})]}),O.text&&(0,a.jsx)("div",{className:"mx-6 mt-4 p-4 rounded-md ".concat("success"===O.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:O.text}),(0,a.jsx)("div",{className:"overflow-x-auto",children:t&&0===e.length?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading users..."})]}):e.length>0?(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Downline"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:e.profileImage?(0,a.jsx)("img",{className:"h-10 w-10 rounded-full",src:e.profileImage,alt:e.name}):(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 font-medium",children:e.name.charAt(0).toUpperCase()})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.phone||"No phone"})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:e.rank.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e._count.downline}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.walletBalance.toFixed(2)]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>T(e),className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,a.jsx)(c.Ny1,{})}),(0,a.jsx)("button",{onClick:()=>L(e),className:"text-green-600 hover:text-green-900",title:"Edit User",children:(0,a.jsx)(c.uO9,{})}),(0,a.jsx)("button",{onClick:()=>B(e.id),className:"text-red-600 hover:text-red-900",title:"Reset Wallet Balance",children:(0,a.jsx)(c.aBc,{})})]})})]},e.id))})]}):(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No users found."})})}),e.length>0&&(0,a.jsxs)("div",{className:"px-6 py-4 flex items-center justify-between border-t",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-700 mr-2",children:"Rows per page:"}),(0,a.jsxs)("select",{value:d.pageSize,onChange:e=>{let s=parseInt(e.target.value);o(e=>({...e,pageSize:s,page:1}))},className:"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"10",children:"10"}),(0,a.jsx)("option",{value:"25",children:"25"}),(0,a.jsx)("option",{value:"50",children:"50"}),(0,a.jsx)("option",{value:"100",children:"100"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700 mr-4",children:[d.page," of ",d.totalPages," pages (",d.totalItems," total users)"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>J(1),disabled:1===d.page,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"First"}),(0,a.jsx)("button",{onClick:()=>J(d.page-1),disabled:1===d.page,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>J(d.page+1),disabled:d.page===d.totalPages,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"}),(0,a.jsx)("button",{onClick:()=>J(d.totalPages),disabled:d.page===d.totalPages,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Last"})]})]})]}),v&&y&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"User Details"}),(0,a.jsx)("button",{onClick:()=>w(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)("div",{className:"h-20 w-20 flex-shrink-0",children:y.profileImage?(0,a.jsx)("img",{className:"h-20 w-20 rounded-full",src:y.profileImage,alt:y.name}):(0,a.jsx)("div",{className:"h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-2xl font-medium",children:y.name.charAt(0).toUpperCase()})})}),(0,a.jsxs)("div",{className:"ml-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:y.name}),(0,a.jsx)("p",{className:"text-gray-500",children:y.email}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:y.rank.name})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Phone"}),(0,a.jsx)("p",{className:"text-gray-900",children:y.phone||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Wallet Balance"}),(0,a.jsxs)("p",{className:"text-gray-900",children:["₱",y.walletBalance.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Upline"}),(0,a.jsx)("p",{className:"text-gray-900",children:y.upline?"".concat(y.upline.name," (").concat(y.upline.email,")"):"None"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Downline Count"}),(0,a.jsx)("p",{className:"text-gray-900",children:y._count.downline})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Member Since"}),(0,a.jsx)("p",{className:"text-gray-900",children:new Date(y.createdAt).toLocaleDateString()})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{w(!1),L(y)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Edit User"}),(0,a.jsx)("button",{onClick:()=>w(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Close"})]})]})]})}),k&&y&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Edit User"}),(0,a.jsx)("button",{onClick:()=>C(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("form",{onSubmit:W,className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,a.jsx)("input",{type:"text",name:"name",value:S.name,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,a.jsx)("input",{type:"email",name:"email",value:S.email,disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Email cannot be changed"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),(0,a.jsx)("input",{type:"tel",name:"phone",value:S.phone,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,a.jsx)("select",{name:"rankId",value:S.rankId,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0,children:i.map(e=>(0,a.jsx)("option",{value:e.id.toString(),children:e.name},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Upline ID"}),(0,a.jsx)("input",{type:"text",name:"uplineId",value:S.uplineId,onChange:z,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Leave empty for no upline"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"submit",disabled:t,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):"Save Changes"}),(0,a.jsx)("button",{type:"button",onClick:()=>C(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"})]})]})]})}),(0,a.jsx)(m,{isOpen:I,onClose:()=>{F(!1)},onImportComplete:()=>{V()}}),(0,a.jsx)(u,{isOpen:R,onClose:()=>{D(!1)}}),U&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Import/Export History"}),(0,a.jsx)("button",{onClick:M,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsx)(h,{limit:10})]})]})};function g(){let{data:e,status:s}=(0,r.useSession)(),t=(0,i.useRouter)(),[o,x]=(0,l.useState)(!0),[m,u]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{"unauthenticated"===s&&t.push("/login")},[s,t]),(0,l.useEffect)(()=>{"authenticated"===s&&(async()=>{try{let e=await fetch("/api/users/me"),s=await e.json();u(6===s.rankId),x(!1)}catch(e){console.error("Error checking admin status:",e),x(!1)}})()},[s]),"loading"===s||o)?(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}):m?(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,a.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,a.jsx)(c.YXz,{className:"mr-2 text-blue-500"})," User Management"]})}),(0,a.jsx)(p,{})]})}):(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}},67395:(e,s,t)=>{Promise.resolve().then(t.bind(t,66837))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,3568,6710,357,9526,8441,1684,7358],()=>s(67395)),_N_E=e.O()}]);
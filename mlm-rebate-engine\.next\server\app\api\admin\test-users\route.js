(()=>{var e={};e.id=237,e.ids=[237],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>c,aP:()=>l});var s=t(96330),n=t(13581),o=t(85663),a=t(55511),i=t.n(a);async function u(e){return await o.Ay.hash(e,10)}function l(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await o.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:a,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(s,o,i):s[o]=e[o]}return s.default=e,t&&t.set(e,s),s}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=global.prisma||new s.PrismaClient({log:["query"]})},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53918:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>h,serverHooks:()=>x,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{DELETE:()=>w,GET:()=>g,POST:()=>m});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(19854),l=t(12909),c=t(31183),d=t(33873),p=t.n(d);async function f(){return{generateTestUsers:(await t(65523)(p().resolve(process.cwd(),"scripts/generate-test-users.js"))).generateTestUsers,cleanupTestUsers:(await t(65523)(p().resolve(process.cwd(),"scripts/cleanup-test-users.js"))).cleanupTestUsers}}async function g(e){try{let r=await (0,u.getServerSession)(l.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let s=parseInt(r.user.id),n=await c.z.user.findUnique({where:{id:s}});if(n?.rankId!==6)return i.NextResponse.json({error:"You must be an admin to access this endpoint"},{status:403});let o=new URL(e.url).searchParams.get("environment")||"development",a=[],d=t(29021);try{let e=p().resolve(process.cwd(),"test-users.json");if(d.existsSync(e)){let r=JSON.parse(d.readFileSync(e,"utf8"));r.users&&Array.isArray(r.users)&&a.push(...r.users.filter(e=>e.isTest&&e.environment===o))}}catch(e){console.warn("Could not read test users data file:",e)}let f=a.map(e=>e.id),g=await c.z.user.findMany({where:{id:{in:f.length>0?f:[-1]}},select:{id:!0,name:!0,email:!0,rankId:!0,uplineId:!0,walletBalance:!0,createdAt:!0},orderBy:{id:"asc"}});g.forEach(e=>{let r=a.find(r=>r.id===e.id);r&&(e.metadata={role:r.role,isTest:r.isTest,environment:r.environment,keepForDev:r.keepForDev})});let m={admin:g.filter(e=>e.metadata?.role==="admin"),ranked_distributor:g.filter(e=>e.metadata?.role==="ranked_distributor"),distributor:g.filter(e=>e.metadata?.role==="distributor"),viewer:g.filter(e=>e.metadata?.role==="viewer")};return i.NextResponse.json({environment:o,totalCount:g.length,usersByRole:m,users:g})}catch(e){return console.error("Error retrieving test users:",e),i.NextResponse.json({error:"Failed to retrieve test users"},{status:500})}}async function m(e){try{let r=await (0,u.getServerSession)(l.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=parseInt(r.user.id),s=await c.z.user.findUnique({where:{id:t}});if(s?.rankId!==6)return i.NextResponse.json({error:"You must be an admin to access this endpoint"},{status:403});let{environment:n="development",userCount:o=30,adminCount:a=1,distributorCount:d=20,rankedDistributorCount:p=5,viewerCount:g=4,maxLevels:m=10,generatePurchases:w=!0,generateRebates:h=!0}=await e.json(),{generateTestUsers:y}=await f(),v=await y({environment:n,userCount:o,adminCount:a,distributorCount:d,rankedDistributorCount:p,viewerCount:g,maxLevels:m,generatePurchases:w,generateRebates:h});return i.NextResponse.json({message:`Successfully generated ${v.length} test users`,environment:n,userCount:v.length})}catch(e){return console.error("Error generating test users:",e),i.NextResponse.json({error:"Failed to generate test users"},{status:500})}}async function w(e){try{let r=await (0,u.getServerSession)(l.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=parseInt(r.user.id),s=await c.z.user.findUnique({where:{id:t}});if(s?.rankId!==6)return i.NextResponse.json({error:"You must be an admin to access this endpoint"},{status:403});let{environment:n="development",retainKeyTesters:o=!0,dryRun:a=!1}=await e.json(),{cleanupTestUsers:d}=await f(),p=await d({environment:n,retainKeyTesters:o,dryRun:a});return i.NextResponse.json({message:"Successfully cleaned up test users",environment:n,deleted:p.deleted,retained:p.retained,dryRun:a})}catch(e){return console.error("Error cleaning up test users:",e),i.NextResponse.json({error:"Failed to clean up test users"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/test-users/route",pathname:"/api/admin/test-users",filename:"route",bundlePath:"app/api/admin/test-users/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:v,serverHooks:x}=h;function b(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:v})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65523:e=>{function r(e){return Promise.resolve().then(()=>{var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r})}r.keys=()=>[],r.resolve=r,r.id=65523,e.exports=r},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(53918));module.exports=s})();
(()=>{var e={};e.id=7220,e.ids=[7220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5152:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(60687);s(43210);var r=s(68367),i=s(30474),n=s(23877);function l(){return(0,a.jsx)(r.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden mb-12",children:[(0,a.jsx)("div",{className:"relative h-80 bg-gradient-to-r from-green-600 to-green-800",children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-white p-8",children:[(0,a.jsx)("div",{className:"flex justify-center mb-6",children:(0,a.jsx)(i.default,{src:"/logo.png",alt:"Extreme Life Herbal Products Logo",width:150,height:150,className:"rounded-full border-4 border-white shadow-lg"})}),(0,a.jsx)("h1",{className:"text-4xl font-bold mb-2",children:"Extreme Life Herbal Products"}),(0,a.jsx)("p",{className:"text-xl",children:"Nature's Healing Power in Every Product"})]})})}),(0,a.jsx)("div",{className:"p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-green-100 text-green-600",children:(0,a.jsx)(n.vq8,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Address"}),(0,a.jsxs)("p",{className:"mt-1 text-gray-500",children:["123 Herbal Street, Barangay Health",(0,a.jsx)("br",{}),"Quezon City, Metro Manila",(0,a.jsx)("br",{}),"Philippines 1100"]})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-green-100 text-green-600",children:(0,a.jsx)(n.Cab,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Contact"}),(0,a.jsxs)("p",{className:"mt-1 text-gray-500",children:["Phone: +63 (2) 8123 4567",(0,a.jsx)("br",{}),"Mobile: +63 ************",(0,a.jsx)("br",{}),"Fax: +63 (2) 8765 4321"]})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-md bg-green-100 text-green-600",children:(0,a.jsx)(n.maD,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Online"}),(0,a.jsxs)("p",{className:"mt-1 text-gray-500",children:["Email: <EMAIL>",(0,a.jsx)("br",{}),"Website: www.extremelifeherbal.ph"]}),(0,a.jsxs)("div",{className:"mt-2 flex space-x-4",children:[(0,a.jsx)("a",{href:"https://facebook.com/extremelifeherbalproducts",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:(0,a.jsx)(n.iYk,{className:"h-5 w-5"})}),(0,a.jsx)("a",{href:"https://instagram.com/extremelifeherbal",target:"_blank",rel:"noopener noreferrer",className:"text-pink-600 hover:text-pink-800",children:(0,a.jsx)(n.ao$,{className:"h-5 w-5"})}),(0,a.jsx)("a",{href:"https://twitter.com/extremelifeherb",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-600",children:(0,a.jsx)(n.feZ,{className:"h-5 w-5"})})]})]})]})]})})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8 text-center",children:"About Us"}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,a.jsxs)("div",{className:"md:flex",children:[(0,a.jsx)("div",{className:"md:flex-shrink-0 md:w-1/2",children:(0,a.jsx)(i.default,{src:"/about-image.jpg",alt:"About Extreme Life Herbal Products",width:800,height:600,className:"h-full w-full object-cover"})}),(0,a.jsxs)("div",{className:"p-8 md:w-1/2",children:[(0,a.jsx)("div",{className:"uppercase tracking-wide text-sm text-green-600 font-semibold",children:"Our Story"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600 leading-relaxed",children:"Founded in 2010, Extreme Life Herbal Products began with a simple mission: to harness the healing power of Philippine herbs and make them accessible to everyone. Our founder, Dr. Maria Gonzales, a respected herbalist and naturopathic doctor, recognized the incredible potential of local medicinal plants that had been used for generations."}),(0,a.jsx)("p",{className:"mt-4 text-gray-600 leading-relaxed",children:"Starting with just three products made in a small facility in Quezon City, we have grown to offer over 20 different herbal supplements and remedies, all made with locally-sourced, organic ingredients. Our commitment to quality, efficacy, and sustainability has made us one of the leading herbal product companies in the Philippines."}),(0,a.jsx)("p",{className:"mt-4 text-gray-600 leading-relaxed",children:"Today, Extreme Life Herbal Products continues to innovate while honoring traditional knowledge. We work directly with local farmers to ensure sustainable harvesting practices and fair compensation. Our state-of-the-art manufacturing facility meets international standards for quality and safety."})]})]})})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8 text-center",children:"Our Values"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"rounded-full bg-green-100 p-4",children:(0,a.jsx)(n.sHz,{className:"h-8 w-8 text-green-600"})})}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Natural Healing"}),(0,a.jsx)("p",{className:"text-gray-600",children:"We believe in the power of nature to heal and restore. All our products are made from 100% natural ingredients, with no artificial additives or preservatives."})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"rounded-full bg-green-100 p-4",children:(0,a.jsx)(n.YXz,{className:"h-8 w-8 text-green-600"})})}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Community Empowerment"}),(0,a.jsx)("p",{className:"text-gray-600",children:"We empower local communities through fair trade practices, sustainable farming, and creating economic opportunities through our MLM business model."})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"rounded-full bg-green-100 p-4",children:(0,a.jsx)(n._oH,{className:"h-8 w-8 text-green-600"})})}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Quality & Integrity"}),(0,a.jsx)("p",{className:"text-gray-600",children:"We maintain the highest standards of quality in all our products. Every batch is tested for purity and potency, ensuring you receive only the best."})]})]})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8 text-center",children:"Featured Products"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{id:1,name:"Premium Moringa Capsules",image:"/products/moringa.jpg",description:"High-potency Moringa Oleifera capsules packed with essential nutrients and antioxidants.",price:"₱1,200.00"},{id:2,name:"Sambong-Banaba Tea",image:"/products/tea.jpg",description:"Traditional herbal tea blend that helps support healthy blood sugar levels and kidney function.",price:"₱850.00"},{id:3,name:"Mangosteen Extract",image:"/products/mangosteen.jpg",description:"Pure mangosteen extract known for its powerful anti-inflammatory and antioxidant properties.",price:"₱1,500.00"}].map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,a.jsx)("div",{className:"relative h-64",children:(0,a.jsx)(i.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-green-600 font-bold",children:e.price}),(0,a.jsx)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:"Learn More"})]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8 text-center",children:"What Our Customers Say"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{id:1,name:"Maria Santos",location:"Quezon City",image:"/testimonials/testimonial1.jpg",text:"Extreme Life Herbal Products changed my life! After using their Moringa supplements for 3 months, my energy levels improved dramatically and my blood pressure is now under control.",rating:5},{id:2,name:"Juan Dela Cruz",location:"Cebu City",image:"/testimonials/testimonial2.jpg",text:"I've been a distributor for Extreme Life for 2 years now. Not only have their products helped my family's health, but the business opportunity has provided additional income for my children's education.",rating:5},{id:3,name:"Angelica Reyes",location:"Davao City",image:"/testimonials/testimonial3.jpg",text:"Their Sambong-Banaba tea is amazing! It helped me manage my blood sugar levels naturally. The customer service is also excellent - they always respond quickly to my questions.",rating:4}].map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(i.default,{src:e.image,alt:e.name,width:60,height:60,className:"rounded-full"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,a.jsx)("p",{className:"text-gray-500",children:e.location}),(0,a.jsx)("div",{className:"flex mt-1",children:[void 0,void 0,void 0,void 0,void 0].map((t,s)=>(0,a.jsx)(n.gt3,{className:`h-4 w-4 ${s<e.rating?"text-yellow-400":"text-gray-300"}`},s))})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.JwJ,{className:"absolute top-0 left-0 text-green-100 h-8 w-8"}),(0,a.jsx)("p",{className:"text-gray-600 pl-8 pr-8 py-2",children:e.text}),(0,a.jsx)(n.K9h,{className:"absolute bottom-0 right-0 text-green-100 h-8 w-8"})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-600 to-green-800 rounded-lg shadow-lg p-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Join Our Growing Family"}),(0,a.jsx)("p",{className:"text-white text-lg mb-6 max-w-3xl mx-auto",children:"Become a distributor today and start your journey towards health, wellness, and financial freedom with Extreme Life Herbal Products."}),(0,a.jsx)("button",{className:"px-8 py-3 bg-white text-green-700 font-bold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-green-700",children:"Learn About Our Business Opportunity"})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15129:(e,t,s)=>{Promise.resolve().then(s.bind(s,5152))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28770:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\about\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78681:(e,t,s)=>{Promise.resolve().then(s.bind(s,28770))},79551:e=>{"use strict";e.exports=require("url")},97708:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28770)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\about\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(97708));module.exports=a})();
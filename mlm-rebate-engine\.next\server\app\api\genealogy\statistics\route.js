(()=>{var e={};e.id=2733,e.ids=[2733],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>l,Nh:()=>c,aP:()=>u});var s=t(96330),n=t(13581),i=t(85663),o=t(55511),a=t.n(o);async function l(e){return await i.Ay.hash(e,10)}function u(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await i.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:o,...a}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(s,i,a):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(35426));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60223:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>d});var n=t(96559),i=t(48088),o=t(37719),a=t(32190),l=t(19854),u=t(12909),c=t(31183);async function d(e){try{let r,t=await (0,l.getServerSession)(u.Nh);if(!t||!t.user)return a.NextResponse.json({error:"You must be logged in to view genealogy statistics"},{status:401});let s=new URL(e.url).searchParams.get("userId");if(s){if(r=parseInt(s),isNaN(r))return a.NextResponse.json({error:"Invalid user ID"},{status:400})}else r=parseInt(t.user.id);if(!await c.z.user.findUnique({where:{id:r},include:{_count:{select:{downline:!0}}}}))return a.NextResponse.json({error:"User not found"},{status:404});let n=(await c.z.user.findMany({where:{uplineId:r},select:{id:!0}})).map(e=>e.id),i=(await c.z.user.findMany({where:{uplineId:{in:n}},select:{id:!0}})).map(e=>e.id),o=(await c.z.user.findMany({where:{uplineId:{in:i}},select:{id:!0}})).map(e=>e.id),d=(await c.z.user.findMany({where:{uplineId:{in:o}},select:{id:!0}})).map(e=>e.id),p=(await c.z.user.findMany({where:{uplineId:{in:d}},select:{id:!0}})).map(e=>e.id),g=await c.z.user.findMany({where:{uplineId:{in:p}},select:{id:!0}}),f=1+n.length+i.length+o.length+d.length+p.length+g.length,w=[...n,...i,...o,...d,...p,...g.map(e=>e.id)],h=(await c.z.purchase.findMany({where:{userId:{in:w},createdAt:{gte:new Date(Date.now()-2592e6)}},select:{userId:!0},distinct:["userId"]})).length,y=w.length>0?h/w.length*100:0;return a.NextResponse.json({totalUsers:f,directDownlineCount:n.length,levelCounts:{1:n.length,2:i.length,3:o.length,4:d.length,5:p.length,6:g.length},activeUsersLast30Days:h,activeUserPercentage:y})}catch(e){return console.error("Error fetching genealogy statistics:",e),a.NextResponse.json({error:"Failed to fetch genealogy statistics"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/genealogy/statistics/route",pathname:"/api/genealogy/statistics",filename:"route",bundlePath:"app/api/genealogy/statistics/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\statistics\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:w}=p;function h(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(60223));module.exports=s})();
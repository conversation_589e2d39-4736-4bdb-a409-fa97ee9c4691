(()=>{var e={};e.id=3686,e.ids=[3686],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24580:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),n=s(48088),a=s(88170),o=s.n(a),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c={children:["",{children:["admin",{children:["mlm-config",{children:["cutoff",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,88312)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\cutoff\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\cutoff\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/mlm-config/cutoff/page",pathname:"/admin/mlm-config/cutoff",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33135:(e,t,s)=>{Promise.resolve().then(s.bind(s,88312))},33873:e=>{"use strict";e.exports=require("path")},41383:(e,t,s)=>{Promise.resolve().then(s.bind(s,62734))},62734:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(60687),n=s(43210),a=s(82136),o=s(16189),i=s(68367),l=s(23877);function c(){let{data:e,status:t}=(0,a.useSession)();(0,o.useRouter)();let[s,c]=(0,n.useState)(!0),[d,u]=(0,n.useState)(!1),[m,f]=(0,n.useState)([]),[x,p]=(0,n.useState)(null),[h,g]=(0,n.useState)({type:"",text:""}),[y,b]=(0,n.useState)(!1),[j,v]=(0,n.useState)({year:new Date().getFullYear(),month:new Date().getMonth()+1,cutoffDay:25,notes:""}),N=async()=>{c(!0);try{let e=await fetch("/api/mlm-config/cutoff?action=list");if(!e.ok)throw Error(`Failed to fetch cutoffs: ${e.statusText}`);let t=await e.json();f(t.cutoffs||[])}catch(e){console.error("Error fetching cutoffs:",e),g({type:"error",text:"Failed to load cutoff data. Please try again."})}finally{c(!1)}},w=async()=>{try{let e=await fetch("/api/mlm-config/cutoff?action=current");if(!e.ok)throw Error(`Failed to fetch current cutoff: ${e.statusText}`);let t=await e.json();p(t.cutoff)}catch(e){console.error("Error fetching current cutoff:",e)}},C=async()=>{c(!0),g({type:"",text:""});try{let e=await fetch("/api/mlm-config/cutoff",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"create",...j})});if(!e.ok)throw Error(`Failed to create cutoff: ${e.statusText}`);await e.json(),g({type:"success",text:"Monthly cutoff created successfully."}),b(!1),v({year:new Date().getFullYear(),month:new Date().getMonth()+1,cutoffDay:25,notes:""}),N(),w()}catch(e){console.error("Error creating cutoff:",e),g({type:"error",text:e instanceof Error?e.message:"Failed to create cutoff. Please try again."})}finally{c(!1)}},P=async(e,t)=>{u(!0),g({type:"",text:""});try{let s=await fetch("/api/mlm-config/cutoff",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"process",year:e,month:t})});if(!s.ok)throw Error(`Failed to process cutoff: ${s.statusText}`);let r=await s.json();g({type:"success",text:`Monthly commissions processed successfully. ${r.result.summary.succeeded} users processed, ${r.result.summary.failed} failed.`}),N()}catch(e){console.error("Error processing cutoff:",e),g({type:"error",text:e instanceof Error?e.message:"Failed to process cutoff. Please try again."})}finally{u(!1)}},k=(e,t)=>{v({...j,[e]:t})},M=e=>["January","February","March","April","May","June","July","August","September","October","November","December"][e-1],A=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},S=e=>{switch(e){case"pending":return(0,r.jsx)(l.w_X,{});case"processing":return(0,r.jsx)(l.hW,{className:"animate-spin"});case"completed":return(0,r.jsx)(l.CMH,{});case"failed":return(0,r.jsx)(l.QCr,{});default:return null}};return"loading"===t||s?(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})}):(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Monthly Cutoff Management"}),h.text&&(0,r.jsx)("div",{className:`mb-6 p-4 rounded-md ${"error"===h.type?"bg-red-100 text-red-700":"success"===h.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:h.text}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden mb-6",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Current/Upcoming Cutoff"})}),(0,r.jsx)("div",{className:"p-6",children:x?(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-xl font-medium",children:[M(x.month)," ",x.year]}),(0,r.jsxs)("p",{className:"text-gray-600 mt-1",children:["Cutoff Day: ",x.cutoffDay]}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${A(x.status)}`,children:[S(x.status),(0,r.jsx)("span",{className:"ml-1",children:x.status.charAt(0).toUpperCase()+x.status.slice(1)})]})})]}),"pending"===x.status&&(0,r.jsx)("button",{type:"button",onClick:()=>P(x.year,x.month),disabled:d,className:"mt-4 md:mt-0 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.hW,{className:"animate-spin inline mr-2"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.gSK,{className:"inline mr-2"}),"Process Now"]})})]}):(0,r.jsx)("div",{className:"text-center py-4 text-gray-500",children:'No current or upcoming cutoff scheduled. Click "Add Cutoff" to create one.'})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Cutoff History"}),(0,r.jsxs)("button",{type:"button",onClick:()=>b(!0),className:"bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:[(0,r.jsx)(l.OiG,{className:"inline mr-1"}),"Add Cutoff"]})]}),(0,r.jsxs)("div",{className:"p-6",children:[y&&(0,r.jsxs)("div",{className:"mb-6 border rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Add New Cutoff"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year"}),(0,r.jsx)("select",{value:j.year,onChange:e=>k("year",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:Array.from({length:5},(e,t)=>new Date().getFullYear()-1+t).map(e=>(0,r.jsx)("option",{value:e,children:e},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Month"}),(0,r.jsx)("select",{value:j.month,onChange:e=>k("month",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:Array.from({length:12},(e,t)=>t+1).map(e=>(0,r.jsx)("option",{value:e,children:M(e)},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cutoff Day"}),(0,r.jsx)("input",{type:"number",min:"1",max:"31",value:j.cutoffDay,onChange:e=>k("cutoffDay",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,r.jsxs)("div",{className:"md:col-span-3",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Notes (optional)"}),(0,r.jsx)("textarea",{value:j.notes,onChange:e=>k("notes",e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",rows:2})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex justify-end space-x-3",children:[(0,r.jsx)("button",{type:"button",onClick:()=>b(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Cancel"}),(0,r.jsx)("button",{type:"button",onClick:C,disabled:s,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.dIn,{className:"inline mr-2"}),"Save Cutoff"]})})]})]}),0===m.length?(0,r.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No cutoff history found."}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Period"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Cutoff Day"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Processed At"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:[M(e.month)," ",e.year]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.cutoffDay})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${A(e.status)}`,children:[S(e.status),(0,r.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.processedAt?new Date(e.processedAt).toLocaleString():"-"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:"pending"===e.status&&(0,r.jsxs)("button",{type:"button",onClick:()=>P(e.year,e.month),disabled:d,className:"text-blue-600 hover:text-blue-900",children:[(0,r.jsx)(l.gSK,{className:"inline"}),(0,r.jsx)("span",{className:"ml-1",children:"Process"})]})})]},e.id))})]})})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},88312:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\mlm-config\\\\cutoff\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\cutoff\\page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(24580));module.exports=r})();
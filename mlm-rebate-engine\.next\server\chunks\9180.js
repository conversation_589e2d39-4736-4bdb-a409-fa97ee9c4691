exports.id=9180,exports.ids=[9180],exports.modules={231:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(60687);r(43210);let s=({productId:e})=>{let t=[{id:1,author:"<PERSON>",rating:5,date:"2023-04-15",content:"This product has significantly improved my energy levels and overall health. Highly recommended!"},{id:2,author:"Juan Dela Cruz",rating:4,date:"2023-03-22",content:"Great product, noticed improvements within a week of use. Would buy again."},{id:3,author:"<PERSON>",rating:5,date:"2023-02-10",content:"Amazing results! My family and friends have noticed the difference in my energy and appearance."}],r=e=>{let t=[];for(let r=1;r<=5;r++)t.push((0,i.jsx)("span",{className:r<=e?"text-yellow-500":"text-gray-300",children:"★"},r));return t};return(0,i.jsxs)("div",{className:"mt-10",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Customer Reviews"}),0===t.length?(0,i.jsx)("p",{className:"text-gray-500",children:"No reviews yet. Be the first to review this product!"}):(0,i.jsx)("div",{className:"space-y-6",children:t.map(e=>(0,i.jsxs)("div",{className:"border-b pb-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold",children:e.author}),(0,i.jsx)("div",{className:"flex text-xl",children:r(e.rating)})]}),(0,i.jsx)("span",{className:"text-sm text-gray-500",children:e.date})]}),(0,i.jsx)("p",{className:"mt-2 text-gray-700",children:e.content})]},e.id))}),(0,i.jsxs)("div",{className:"mt-8",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Write a Review"}),(0,i.jsxs)("form",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"rating",className:"block text-sm font-medium text-gray-700",children:"Rating"}),(0,i.jsxs)("select",{id:"rating",name:"rating",className:"mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",children:[(0,i.jsx)("option",{value:"5",children:"5 Stars - Excellent"}),(0,i.jsx)("option",{value:"4",children:"4 Stars - Very Good"}),(0,i.jsx)("option",{value:"3",children:"3 Stars - Good"}),(0,i.jsx)("option",{value:"2",children:"2 Stars - Fair"}),(0,i.jsx)("option",{value:"1",children:"1 Star - Poor"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"review",className:"block text-sm font-medium text-gray-700",children:"Your Review"}),(0,i.jsx)("textarea",{id:"review",name:"review",rows:"4",className:"mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",placeholder:"Share your experience with this product..."})]}),(0,i.jsx)("button",{type:"submit",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Submit Review"})]})]})]})}},23229:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>n,_:()=>a});var i=r(60687),s=r(43210);let o=(0,s.createContext)(void 0),a=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},n=({children:e})=>{let[t,r]=(0,s.useState)([]);(0,s.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{r(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,s.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let a=e=>{r(t=>t.filter(t=>t.id!==e))},n=t.reduce((e,t)=>e+t.quantity,0),l=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,i.jsx)(o.Provider,{value:{items:t,addItem:e=>{r(t=>{let r=t.findIndex(t=>t.id===e.id);if(!(r>=0))return[...t,e];{let i=[...t];return i[r]={...i[r],quantity:i[r].quantity+e.quantity},i}})},removeItem:a,updateQuantity:(e,t)=>{if(t<=0)return void a(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},itemCount:n,subtotal:l,totalPV:d},children:e})}},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>s});var i=r(12907);(0,i.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let s=(0,i.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},37590:(e,t,r)=>{"use strict";r.d(t,{oR:()=>I});var i,s=r(43210);let o={data:""},a=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,n=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,l=/\/\*[^]*?\*\/|  +/g,d=/\n+/g,c=(e,t)=>{let r="",i="",s="";for(let o in e){let a=e[o];"@"==o[0]?"i"==o[1]?r=o+" "+a+";":i+="f"==o[1]?c(a,o):o+"{"+c(a,"k"==o[1]?"":t)+"}":"object"==typeof a?i+=c(a,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=a&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=c.p?c.p(o,a):o+":"+a+";")}return r+(t&&s?t+"{"+s+"}":s)+i},m={},u=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+u(e[r]);return t}return e},p=(e,t,r,i,s)=>{let o=u(e),a=m[o]||(m[o]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(o));if(!m[a]){let t=o!==e?e:(e=>{let t,r,i=[{}];for(;t=n.exec(e.replace(l,""));)t[4]?i.shift():t[3]?(r=t[3].replace(d," ").trim(),i.unshift(i[0][r]=i[0][r]||{})):i[0][t[1]]=t[2].replace(d," ").trim();return i[0]})(e);m[a]=c(s?{["@keyframes "+a]:t}:t,r?"":"."+a)}let p=r&&m.g?m.g:null;return r&&(m.g=m[a]),((e,t,r,i)=>{i?t.data=t.data.replace(i,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(m[a],t,i,p),a},h=(e,t,r)=>e.reduce((e,i,s)=>{let o=t[s];if(o&&o.call){let e=o(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":c(e,""):!1===e?"":e}return e+i+(null==o?"":o)},"");function f(e){let t=this||{},r=e.call?e(t.p):e;return p(r.unshift?r.raw?h(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,a(t.target),t.g,t.o,t.k)}f.bind({g:1});let v,g,x,b=f.bind({k:1});function y(e,t){let r=this||{};return function(){let i=arguments;function s(o,a){let n=Object.assign({},o),l=n.className||s.className;r.p=Object.assign({theme:g&&g()},n),r.o=/ *go\d+/.test(l),n.className=f.apply(r,i)+(l?" "+l:""),t&&(n.ref=a);let d=e;return e[0]&&(d=n.as||e,delete n.as),x&&d[0]&&x(n),v(d,n)}return t?t(s):s}}var w=e=>"function"==typeof e,C=(e,t)=>w(e)?e(t):e,N=(()=>{let e=0;return()=>(++e).toString()})(),P=(()=>{let e;return()=>e})(),k=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return k(e,{type:+!!e.toasts.find(e=>e.id===r.id),toast:r});case 3:let{toastId:i}=t;return{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+s}))}}},A=[],S={toasts:[],pausedAt:void 0},L=e=>{S=k(S,e),A.forEach(e=>{e(S)})},E={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},M=(e={})=>{let[t,r]=j(S),i=Q(S);H(()=>(i.current!==S&&r(S),A.push(r),()=>{let e=A.indexOf(r);e>-1&&A.splice(e,1)}),[]);let s=t.toasts.map(t=>{var r,i,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(i=e[t.type])?void 0:i.duration)||(null==e?void 0:e.duration)||E[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}});return{...t,toasts:s}},$=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||N()}),R=e=>(t,r)=>{let i=$(t,e,r);return L({type:2,toast:i}),i.id},I=(e,t)=>R("blank")(e,t);I.error=R("error"),I.success=R("success"),I.loading=R("loading"),I.custom=R("custom"),I.dismiss=e=>{L({type:3,toastId:e})},I.remove=e=>L({type:4,toastId:e}),I.promise=(e,t,r)=>{let i=I.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let s=t.success?C(t.success,e):void 0;return s?I.success(s,{id:i,...r,...null==r?void 0:r.success}):I.dismiss(i),e}).catch(e=>{let s=t.error?C(t.error,e):void 0;s?I.error(s,{id:i,...r,...null==r?void 0:r.error}):I.dismiss(i)}),e};var F=(e,t)=>{L({type:1,toast:{id:e,height:t}})},q=()=>{L({type:5,time:Date.now()})},z=new Map,D=1e3,W=(e,t=D)=>{if(z.has(e))return;let r=setTimeout(()=>{z.delete(e),L({type:4,toastId:e})},t);z.set(e,r)},O=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,T=b`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,_=b`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=(y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${O} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${T} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${_} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,b`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`),U=(y("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${B} 1s linear infinite;
`,b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`),V=b`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,G=(y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${V} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,y("div")`
  position: absolute;
`,y("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,b`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`);y("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${G} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,y("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,y("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,i=s.createElement,c.p=void 0,v=i,g=void 0,x=void 0,f`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`},38458:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(60687);r(43210);var s=r(85814),o=r.n(s),a=r(30474);let n=({currentProductId:e})=>{let t=[{id:"veggie-coffee",name:"Veggie Coffee",price:1200,pv:120,image:"/images/products/veggie-coffee.jpg",description:"A healthy coffee alternative made with organic vegetables."},{id:"shield-soap",name:"Shield Soap",price:250,pv:25,image:"/images/products/shield-soap.jpg",description:"Antibacterial soap with natural ingredients for skin protection."},{id:"biogen-extreme",name:"Biogen Extreme",price:1800,pv:180,image:"/images/products/biogen-extreme.jpg",description:"Advanced health supplement for improved energy and vitality."}].filter(t=>t.id!==e);return(0,i.jsxs)("div",{className:"mt-16",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"You May Also Like"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:t.map(e=>(0,i.jsx)("div",{className:"border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow",children:(0,i.jsxs)(o(),{href:`/products/${e.id}`,children:[(0,i.jsx)("div",{className:"relative h-48 w-full",children:e.image?(0,i.jsx)(a.default,{src:e.image,alt:e.name,fill:!0,style:{objectFit:"cover"}}):(0,i.jsx)("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-gray-400",children:"No image"})})}),(0,i.jsxs)("div",{className:"p-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:e.name}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mt-1 line-clamp-2",children:e.description}),(0,i.jsxs)("div",{className:"mt-3 flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-lg font-bold",children:["₱",e.price.toLocaleString()]}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:[e.pv," PV"]})]}),(0,i.jsx)("button",{className:"text-indigo-600 hover:text-indigo-800 text-sm font-medium",children:"View Details"})]})]})]})},e.id))})]})}},41750:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},45851:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var i=r(60687),s=r(25217),o=r(8693),a=r(43210);function n({children:e}){let[t]=(0,a.useState)(()=>new s.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,i.jsx)(o.Ht,{client:t,children:e})}},49929:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(60687),s=r(43210),o=r(30474);let a=({images:e,productName:t})=>{let[r,a]=(0,s.useState)(0),n=e&&e.length>0?e:["/images/products/placeholder.jpg"];return(0,i.jsxs)("div",{className:"product-gallery",children:[(0,i.jsx)("div",{className:"relative w-full h-96 mb-4 border rounded-lg overflow-hidden",children:(0,i.jsx)(o.default,{src:n[r],alt:`${t} - Image ${r+1}`,fill:!0,style:{objectFit:"contain"},priority:!0})}),n.length>1&&(0,i.jsx)("div",{className:"flex space-x-2 overflow-x-auto pb-2",children:n.map((e,s)=>(0,i.jsx)("button",{onClick:()=>a(s),className:`relative w-20 h-20 border-2 rounded-md overflow-hidden flex-shrink-0 transition-all ${r===s?"border-indigo-600 opacity-100":"border-gray-200 opacity-70 hover:opacity-100"}`,children:(0,i.jsx)(o.default,{src:e,alt:`${t} - Thumbnail ${s+1}`,fill:!0,style:{objectFit:"cover"}})},s))}),n.length>1&&(0,i.jsxs)("div",{className:"flex justify-center mt-4 space-x-4",children:[(0,i.jsx)("button",{onClick:()=>a(e=>0===e?n.length-1:e-1),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors","aria-label":"Previous image",children:(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,i.jsx)("button",{onClick:()=>a(e=>e===n.length-1?0:e+1),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors","aria-label":"Next image",children:(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})})]})]})}},61135:()=>{},63345:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var i=r(60687),s=r(43210);let o=()=>"serviceWorker"in navigator,a=async()=>{if(!o())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),n(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},n=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,r]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{a();let e=()=>{r(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,i.jsxs)(i.Fragment,{children:[e,t&&(0,i.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,i.jsxs)("div",{className:"mr-4",children:[(0,i.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,i.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,i.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,r)=>{Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,23229)),Promise.resolve().then(r.bind(r,82113)),Promise.resolve().then(r.bind(r,41750))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,r)=>{Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,97695)),Promise.resolve().then(r.bind(r,45851)),Promise.resolve().then(r.bind(r,63345))},82113:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},83515:(e,t,r)=>{"use strict";r.d(t,{_:()=>s});var i=r(43210);function s(){let[e,t]=(0,i.useState)([]),[r,s]=(0,i.useState)(!0),o=e=>{t(t=>t.filter(t=>t.id!==e))},a=e.reduce((e,t)=>e+t.quantity,0),n=e.reduce((e,t)=>e+t.price*t.quantity,0);return{cart:e,addToCart:(e,r=1)=>{t(t=>t.find(t=>t.id===e.id)?t.map(t=>t.id===e.id?{...t,quantity:t.quantity+r}:t):[...t,{...e,quantity:r}])},removeFromCart:o,updateQuantity:(e,r)=>{if(r<=0)return void o(e);t(t=>t.map(t=>t.id===e?{...t,quantity:r}:t))},clearCart:()=>{t([])},totalItems:a,totalPrice:n,isLoading:r}}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>u});var i=r(37413),s=r(22376),o=r.n(s),a=r(68726),n=r.n(a);r(61135);var l=r(23229),d=r(37043),c=r(82113),m=r(41750);let u={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function p({children:e}){return(0,i.jsx)("html",{lang:"en",children:(0,i.jsx)("body",{className:`${o().variable} ${n().variable} antialiased`,children:(0,i.jsx)(l.AuthProvider,{children:(0,i.jsx)(c.default,{children:(0,i.jsx)(d.CartProvider,{children:(0,i.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},97695:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var i=r(60687),s=r(82136);function o({children:e}){return(0,i.jsx)(s.SessionProvider,{children:e})}}};
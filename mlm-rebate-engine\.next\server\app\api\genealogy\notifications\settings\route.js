(()=>{var e={};e.id=7056,e.ids=[7056],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,s)=>{"use strict";s.d(t,{Er:()=>u,Nh:()=>l,aP:()=>c});var r=s(96330),o=s(13581),n=s(85663),i=s(55511),a=s.n(i);async function u(e){return await n.Ay.hash(e,10)}function c(){let e=a().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new r.PrismaClient;let l={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new r.PrismaClient,s=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!s)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",s.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,s.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",s.id);let{password:i,...a}=s;return{id:s.id.toString(),email:s.email,name:s.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=s(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(r,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var s=i(t);if(s&&s.has(e))return s.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=o?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(r,n,a):r[n]=e[n]}return r.default=e,s&&s.set(e,r),r}(s(35426));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,s=new WeakMap;return(i=function(e){return e?s:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(r,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,s)=>{"use strict";s.d(t,{z:()=>o});var r=s(96330);let o=global.prisma||new r.PrismaClient({log:["query"]})},34667:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>b,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{GET:()=>f,POST:()=>g});var o=s(96559),n=s(48088),i=s(37719),a=s(32190),u=s(19854),c=s(12909),l=s(31183),p=s(70762);let d=p.z.object({userId:p.z.number(),settings:p.z.object({newMembers:p.z.boolean(),purchases:p.z.boolean(),rankAdvancements:p.z.boolean(),rebates:p.z.boolean(),system:p.z.boolean(),emailNotifications:p.z.boolean(),pushNotifications:p.z.boolean()})});async function f(e){try{let t=await (0,u.getServerSession)(c.Nh);if(!t||!t.user)return a.NextResponse.json({error:"You must be logged in to access notification settings"},{status:401});let s=new URL(e.url).searchParams.get("userId");if(!s)return a.NextResponse.json({error:"User ID is required"},{status:400});let r=parseInt(s);if(isNaN(r))return a.NextResponse.json({error:"Invalid user ID"},{status:400});let o=parseInt(t.user.id);if(r!==o)return a.NextResponse.json({error:"You do not have permission to access notification settings for this user"},{status:403});let n=await l.z.notificationSettings.findUnique({where:{userId:r}});if(!n)return a.NextResponse.json({newMembers:!0,purchases:!0,rankAdvancements:!0,rebates:!0,system:!0,emailNotifications:!1,pushNotifications:!1});return a.NextResponse.json({newMembers:n.newMembers,purchases:n.purchases,rankAdvancements:n.rankAdvancements,rebates:n.rebates,system:n.system,emailNotifications:n.emailNotifications,pushNotifications:n.pushNotifications})}catch(e){return console.error("Error fetching notification settings:",e),a.NextResponse.json({error:"Failed to fetch notification settings"},{status:500})}}async function g(e){try{let t=await (0,u.getServerSession)(c.Nh);if(!t||!t.user)return a.NextResponse.json({error:"You must be logged in to update notification settings"},{status:401});let s=await e.json(),r=d.safeParse(s);if(!r.success)return a.NextResponse.json({error:"Invalid request body",details:r.error.format()},{status:400});let{userId:o,settings:n}=r.data,i=parseInt(t.user.id);if(o!==i)return a.NextResponse.json({error:"You do not have permission to update notification settings for this user"},{status:403});return await l.z.notificationSettings.upsert({where:{userId:o},update:{newMembers:n.newMembers,purchases:n.purchases,rankAdvancements:n.rankAdvancements,rebates:n.rebates,system:n.system,emailNotifications:n.emailNotifications,pushNotifications:n.pushNotifications},create:{userId:o,newMembers:n.newMembers,purchases:n.purchases,rankAdvancements:n.rankAdvancements,rebates:n.rebates,system:n.system,emailNotifications:n.emailNotifications,pushNotifications:n.pushNotifications}}),a.NextResponse.json({success:!0})}catch(e){return console.error("Error updating notification settings:",e),a.NextResponse.json({error:"Failed to update notification settings"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/genealogy/notifications/settings/route",pathname:"/api/genealogy/notifications/settings",filename:"route",bundlePath:"app/api/genealogy/notifications/settings/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\settings\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:w,serverHooks:b}=m;function y(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,580,8044,3112,8381],()=>s(34667));module.exports=r})();
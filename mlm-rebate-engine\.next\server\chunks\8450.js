"use strict";exports.id=8450,exports.ids=[8450],exports.modules={98450:(e,s,t)=>{t.r(s),t.d(s,{default:()=>n});var r=t(60687),a=t(43210),l=t(30474),i=t(85814),c=t.n(i),d=t(23877);let n=()=>{let[e,s]=(0,a.useState)(!0),[t,i]=(0,a.useState)([]),[n,o]=(0,a.useState)(null);(0,a.useEffect)(()=>{m()},[]);let m=async()=>{try{s(!0);let e=await fetch("/api/products/top-performers");if(!e.ok)throw Error("Failed to fetch top products");let t=await e.json();i(t.products||[])}catch(e){console.error("Error fetching top products:",e),o("Failed to load top products. Please try again.");try{let e=await fetch("/api/products?limit=5");if(e.ok){let s=await e.json();i(s.products||[])}}catch(e){console.error("Error fetching fallback products:",e)}}finally{s(!1)}},x=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return e?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 flex items-center justify-center h-64",children:[(0,r.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading top products..."})]}):n&&0===t.length?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsx)("div",{className:"p-3 bg-red-50 text-red-700 rounded-md text-sm",children:n})}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border-b border-blue-100",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,r.jsx)(d.YYR,{className:"mr-2 text-blue-600"}),"Top Performing Products"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Products with the highest conversion rates and commission potential"})]}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:0===t.length?(0,r.jsx)("div",{className:"p-4 text-center text-gray-500",children:"No products available at the moment."}):t.map(e=>(0,r.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-16 w-16 bg-gray-100 rounded-md overflow-hidden mr-3",children:e.image?(0,r.jsx)(l.default,{src:e.image,alt:e.name,width:64,height:64,className:"object-cover w-full h-full"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(d.AsH,{className:"text-gray-400"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"mt-1 flex flex-wrap items-center gap-2",children:[(0,r.jsx)("span",{className:"text-green-600 font-medium",children:x(e.price)}),(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:[e.pv," PV"]}),void 0!==e.conversionRate&&(0,r.jsxs)("span",{className:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full",children:[e.conversionRate,"% Conversion"]})]}),(0,r.jsxs)("div",{className:"mt-2 flex space-x-2",children:[(0,r.jsxs)(c(),{href:`/shop/product/${e.id}`,className:"text-xs text-blue-600 hover:text-blue-800 flex items-center",children:[(0,r.jsx)(d.AsH,{className:"mr-1"}),"View Product"]}),(0,r.jsxs)(c(),{href:`/referrals/generate?productId=${e.id}`,className:"text-xs text-green-600 hover:text-green-800 flex items-center",children:[(0,r.jsx)(d.AnD,{className:"mr-1"}),"Generate Link"]})]})]})]})},e.id))}),(0,r.jsx)("div",{className:"p-4 bg-gray-50 border-t border-gray-200",children:(0,r.jsxs)(c(),{href:"/shop",className:"text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center justify-center",children:["Browse all products",(0,r.jsx)(d.Z0P,{className:"ml-1"})]})})]})}}};
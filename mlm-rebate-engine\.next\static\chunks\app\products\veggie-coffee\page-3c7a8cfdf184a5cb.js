(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7412],{17008:(e,s,a)=>{Promise.resolve().then(a.bind(a,84110))},84110:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var r=a(95155),t=a(12115),l=a(12108),n=a(87747),i=a(6874),d=a.n(i),c=a(29911),m=a(13568),o=a(13841),x=a(54853),h=a(8122),g=a(17349);function u(){let{data:e}=(0,l.useSession)(),{addToCart:s}=(0,o._)(),[a,i]=(0,t.useState)(1),[u,p]=(0,t.useState)(null),[j,b]=(0,t.useState)(!1),{data:N,isLoading:f,error:v}=(0,n.I)({queryKey:["veggie-coffee"],queryFn:async()=>{let e=await fetch("/api/products/veggie-coffee");if(!e.ok)throw Error("Failed to fetch product");return e.json()}});if((0,t.useEffect)(()=>{var e;(null==N||null==(e=N.product)?void 0:e.productVariants)&&p(N.product.productVariants.find(e=>e.isDefault)||N.product.productVariants[0])},[N]),f)return(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-2/3 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-6"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-6"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded mb-6"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-full mb-4"})]})]})]})});if(v||!N)return(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,r.jsx)("p",{children:"Failed to load product. Please try again later."})})});let{product:y,reviews:w,relatedProducts:k}=N,C=(null==N?void 0:N.reviews)&&0!==N.reviews.length?N.reviews.reduce((e,s)=>e+s.rating,0)/N.reviews.length:0;return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("nav",{className:"text-sm text-gray-500",children:(0,r.jsxs)("ol",{className:"list-none p-0 inline-flex",children:[(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(d(),{href:"/products",className:"hover:text-blue-600",children:"Products"}),(0,r.jsx)("span",{className:"mx-2",children:"/"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(d(),{href:"/products/category/health-supplements",className:"hover:text-blue-600",children:"Health Supplements"}),(0,r.jsx)("span",{className:"mx-2",children:"/"})]}),(0,r.jsx)("li",{className:"flex items-center text-gray-700",children:"Veggie Coffee 124 in 1"})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12",children:[(0,r.jsx)("div",{children:y.productImages&&y.productImages.length>0?(0,r.jsx)(g.A,{images:y.productImages}):(0,r.jsx)("div",{className:"bg-gray-100 rounded-lg flex items-center justify-center h-96",children:(0,r.jsx)(c.__w,{className:"text-gray-400 text-4xl"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:y.name}),(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"flex mr-2",children:(e=>{let s=[],a=Math.floor(e),t=e%1>=.5;for(let e=1;e<=5;e++)e<=a?s.push((0,r.jsx)(c.gt3,{className:"text-yellow-400"},e)):e===a+1&&t?s.push((0,r.jsx)(c.gVl,{className:"text-yellow-400"},e)):s.push((0,r.jsx)(c.wei,{className:"text-yellow-400"},e));return s})(C)}),(0,r.jsxs)("span",{className:"text-gray-600",children:[w.length," ",1===w.length?"review":"reviews"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[y.salePrice&&y.salePrice<y.price?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-blue-600 mr-2",children:["₱",(y.salePrice/100).toFixed(2)]}),(0,r.jsxs)("span",{className:"text-lg text-gray-500 line-through",children:["₱",(y.price/100).toFixed(2)]}),(0,r.jsxs)("span",{className:"ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded",children:["Save ",Math.round((y.price-y.salePrice)/y.price*100),"%"]})]}):(0,r.jsxs)("span",{className:"text-2xl font-bold text-blue-600",children:["₱",(y.price/100).toFixed(2)]}),(0,r.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:["Point Value: ",(0,r.jsxs)("span",{className:"font-medium text-green-600",children:[y.pointValue," PV"]})]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"text-gray-700",children:y.shortDescription})}),(0,r.jsxs)("div",{className:"mb-6 bg-green-50 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"Key Benefits"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)(c.sFP,{className:"text-green-600 mt-1 mr-2"}),(0,r.jsx)("span",{children:"Caffeine-free coffee alternative with natural flavor"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)(c.sHz,{className:"text-green-600 mt-1 mr-2"}),(0,r.jsx)("span",{children:"Contains 124 natural ingredients for comprehensive nutrition"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)(c.A7C,{className:"text-green-600 mt-1 mr-2"}),(0,r.jsx)("span",{children:"Supports detoxification when taken before meals"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)(c.vUD,{className:"text-green-600 mt-1 mr-2"}),(0,r.jsx)("span",{children:"Helps maintain good health when taken during meals"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)(c.egb,{className:"text-green-600 mt-1 mr-2"}),(0,r.jsx)("span",{children:"Aids in weight management when taken after meals"})]})]})]}),y.productVariants&&y.productVariants.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Size"}),(0,r.jsx)("div",{className:"flex space-x-2",children:y.productVariants.map(e=>(0,r.jsx)("button",{onClick:()=>p(e),className:"px-4 py-2 border rounded-md ".concat((null==u?void 0:u.id)===e.id?"border-green-500 bg-green-50 text-green-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:e.name},e.id))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quantity"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>a>1&&i(a-1),className:"px-3 py-1 border border-gray-300 rounded-l-md bg-gray-50 text-gray-600 hover:bg-gray-100",children:"-"}),(0,r.jsx)("input",{type:"number",min:"1",max:"10",value:a,onChange:e=>{let s=parseInt(e.target.value);s>0&&s<=10&&i(s)},className:"w-16 text-center border-t border-b border-gray-300 py-1"}),(0,r.jsx)("button",{onClick:()=>a<10&&i(a+1),className:"px-3 py-1 border border-gray-300 rounded-r-md bg-gray-50 text-gray-600 hover:bg-gray-100",children:"+"}),(0,r.jsxs)("span",{className:"ml-3 text-sm text-gray-500",children:[(null==u?void 0:u.stock)||y.stock," available"]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-4 mb-6",children:[(0,r.jsxs)("button",{onClick:()=>{var e;(null==N?void 0:N.product)&&u&&(s({id:N.product.id,name:N.product.name,price:u.salePrice||u.price,image:(null==(e=N.product.productImages[0])?void 0:e.url)||"/images/placeholder.png",quantity:a,variant:u.name,variantId:u.id,pointValue:N.product.pointValue}),m.oR.success("Added to cart!"))},className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-md font-medium flex items-center justify-center",children:[(0,r.jsx)(c.AsH,{className:"mr-2"}),"Add to Cart"]}),(0,r.jsx)("button",{onClick:()=>{b(!j),j?m.oR.success("Removed from favorites!"):m.oR.success("Added to favorites!")},className:"p-3 border border-gray-300 rounded-md hover:bg-gray-50","aria-label":"Add to favorites",children:j?(0,r.jsx)(c.Mbv,{className:"text-red-500"}):(0,r.jsx)(c.sOK,{className:"text-gray-600"})}),(0,r.jsx)("button",{className:"p-3 border border-gray-300 rounded-md hover:bg-gray-50","aria-label":"Share product",children:(0,r.jsx)(c.Zzu,{className:"text-gray-600"})})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,r.jsx)(c.A7C,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:(null==u?void 0:u.stock)||y.stock>0?"In Stock":"Out of Stock"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Free shipping on orders over ₱1,500"})]})]})]}),(0,r.jsxs)("div",{className:"mb-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Product Description"}),(0,r.jsx)("div",{className:"prose max-w-none",children:y.description.split("\n\n").map((e,s)=>(0,r.jsx)("p",{className:"mb-4",children:e},s))})]}),(0,r.jsxs)("div",{className:"mb-12 bg-gray-50 p-6 rounded-lg",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"How to Use"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm",children:[(0,r.jsxs)("h3",{className:"font-medium text-green-700 mb-2 flex items-center",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-700 rounded-full w-6 h-6 flex items-center justify-center mr-2",children:"1"}),"For Detoxification"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Take one sachet before meals to support your body's natural detoxification process."})]}),(0,r.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm",children:[(0,r.jsxs)("h3",{className:"font-medium text-green-700 mb-2 flex items-center",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-700 rounded-full w-6 h-6 flex items-center justify-center mr-2",children:"2"}),"For Health Maintenance"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Take one sachet during meals to help maintain good health and support your body's functions."})]}),(0,r.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm",children:[(0,r.jsxs)("h3",{className:"font-medium text-green-700 mb-2 flex items-center",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-700 rounded-full w-6 h-6 flex items-center justify-center mr-2",children:"3"}),"For Weight Management"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Take one sachet after meals to support your weight management goals and metabolism."})]})]}),(0,r.jsx)("div",{className:"mt-4 text-gray-600",children:(0,r.jsx)("p",{children:"Simply mix one sachet with hot water, stir well, and enjoy your healthy, delicious cup of Veggie Coffee."})})]}),(0,r.jsx)(x.A,{productId:y.id,reviews:w,averageRating:C}),k&&k.length>0&&(0,r.jsx)(h.A,{products:k})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,6967,7747,3568,9299,8441,1684,7358],()=>s(17008)),_N_E=e.O()}]);
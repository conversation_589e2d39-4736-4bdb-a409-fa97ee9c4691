[{"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\about\\page.tsx": "1", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\cutoff\\page.tsx": "2", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\page.tsx": "3", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\page.tsx": "4", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\products\\page.tsx": "5", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebate-configs\\page.tsx": "6", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebates\\page.tsx": "7", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\reports\\page.tsx": "8", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-data\\page.tsx": "9", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-users\\page.tsx": "10", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\users\\page.tsx": "11", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\notifications\\mark-all-read\\route.ts": "12", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\notifications\\route.ts": "13", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\notifications\\[id]\\mark-read\\route.ts": "14", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\route.ts": "15", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\adjust-inventory\\route.ts": "16", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\inventory-transactions\\route.ts": "17", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\route.ts": "18", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rank-advancements\\route.ts": "19", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebate-configs\\route.ts": "20", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebate-configs\\[id]\\route.ts": "21", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\export\\route.ts": "22", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\process\\route.ts": "23", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\route.ts": "24", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\stats\\route.ts": "25", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\reports\\route.ts": "26", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\stats\\route.ts": "27", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-data\\cleanup\\route.ts": "28", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-data\\generate\\route.ts": "29", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-users\\route.ts": "30", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\audit\\route.ts": "31", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\export\\route.ts": "32", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\import\\route.ts": "33", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\forgot-password\\route.ts": "34", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\reset-password\\route.ts": "35", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\validate-reset-token\\route.ts": "36", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "37", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\binary-mlm\\export\\route.ts": "38", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\binary-mlm\\route.ts": "39", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\dashboard\\route.ts": "40", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\compare\\route.ts": "41", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\export\\route.ts": "42", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\load-levels\\route.ts": "43", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\metrics\\route.ts": "44", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\read-all\\route.ts": "45", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\route.ts": "46", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\settings\\route.ts": "47", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\[id]\\read\\route.ts": "48", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\optimized\\route.ts": "49", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\performance\\route.ts": "50", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\route.ts": "51", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\search\\route.ts": "52", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\statistics\\route.ts": "53", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\generate-logo\\route.ts": "54", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\mlm-config\\cutoff\\route.ts": "55", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\mlm-config\\route.ts": "56", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\orders\\route.ts": "57", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\payment-methods\\route.ts": "58", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\payment-methods\\user\\route.ts": "59", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\product-categories\\route.ts": "60", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\biogen-extreme\\route.ts": "61", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\featured\\route.ts": "62", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\oxygen-extreme\\route.ts": "63", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\route.ts": "64", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\shield-soap\\route.ts": "65", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\top-performers\\route.ts": "66", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\veggie-coffee\\route.ts": "67", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\[id]\\route.ts": "68", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\[id]\\toggle-status\\route.ts": "69", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\purchases\\route.ts": "70", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\ranks\\check\\route.ts": "71", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rebates\\route.ts": "72", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rebates\\stats\\route.ts": "73", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\referrals\\activity\\route.ts": "74", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\referrals\\commissions\\route.ts": "75", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rewards\\bonus\\route.ts": "76", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rewards\\referral\\route.ts": "77", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shareable-links\\click\\route.ts": "78", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shareable-links\\route.ts": "79", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping\\addresses\\route.ts": "80", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping\\methods\\route.ts": "81", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping-methods\\route.ts": "82", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\upload\\route.ts": "83", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\genealogy\\route.ts": "84", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\me\\route.ts": "85", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\rank-advancement\\history\\route.ts": "86", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\rank-advancement\\route.ts": "87", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\route.ts": "88", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\payment-details\\route.ts": "89", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\performance\\route.ts": "90", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\route.ts": "91", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\wallet\\reset\\route.ts": "92", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\wallet\\route.ts": "93", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\__tests__\\products.test.ts": "94", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\__tests__\\purchases.test.ts": "95", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\binary-mlm\\page.tsx": "96", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\checkout\\page.tsx": "97", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\dashboard\\page.tsx": "98", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\forgot-password\\page.tsx": "99", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\basic-flow\\page.tsx": "100", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare\\page.tsx": "101", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare-users\\page.tsx": "102", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\enhanced-flow\\page.tsx": "103", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\export\\page.tsx": "104", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\integration\\page.tsx": "105", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\interactive\\page.tsx": "106", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\metrics\\page.tsx": "107", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\mobile\\page.tsx": "108", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\notifications\\page.tsx": "109", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\optimized\\page.tsx": "110", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\page.tsx": "111", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\search\\page.tsx": "112", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\virtualized\\page.tsx": "113", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx": "114", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\login\\page.tsx": "115", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\page.tsx": "116", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\biogen-extreme\\page.tsx": "117", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\oxygen-extreme\\page.tsx": "118", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\shield-soap\\page.tsx": "119", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\veggie-coffee\\page.tsx": "120", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\page.tsx": "121", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\payment-methods\\page.tsx": "122", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\purchases\\page.tsx": "123", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rank-advancement\\page.tsx": "124", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rebates\\page.tsx": "125", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\commissions\\page.tsx": "126", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\generate\\page.tsx": "127", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\page.tsx": "128", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\register\\page.tsx": "129", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\reset-password\\page.tsx": "130", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\s\\[code]\\route.ts": "131", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\shop\\page.tsx": "132", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\shop\\product\\[id]\\page.tsx": "133", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\wallet\\page.tsx": "134", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\ActivityFeed.tsx": "135", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\ChartPlaceholder.tsx": "136", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\MemberDistributionChart.tsx": "137", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\MonthlySalesChart.tsx": "138", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\RebatesByRankChart.tsx": "139", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\DashboardFilters.tsx": "140", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\GroupVolumeTracker.tsx": "141", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\notifications\\NotificationDropdown.tsx": "142", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\InventoryManagementModal.tsx": "143", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductBulkActions.tsx": "144", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductCloneModal.tsx": "145", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductCreateModal.tsx": "146", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductDetailsModal.tsx": "147", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductEditModal.tsx": "148", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductFilters.tsx": "149", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductImportModal.tsx": "150", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductRebateSimulatorModal.tsx": "151", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductSalesChart.tsx": "152", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductTable.tsx": "153", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\QuickAccessCard.tsx": "154", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\StatsCard.tsx": "155", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\TestUserManager.tsx": "156", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\TopEarnersTable.tsx": "157", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\UserManager.tsx": "158", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\users\\UserExportModal.tsx": "159", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\users\\UserImportHistory.tsx": "160", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\users\\UserImportModal.tsx": "161", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\binary-mlm\\BinaryTreeView.tsx": "162", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\binary-mlm\\EarningsReport.tsx": "163", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\cart\\CartButton.tsx": "164", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\cart\\CartDrawer.tsx": "165", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\GuestCheckoutForm.tsx": "166", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\OrderSummary.tsx": "167", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\PaymentMethodSelector.tsx": "168", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\ShippingAddressForm.tsx": "169", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\ShippingMethodSelector.tsx": "170", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\common\\OptimizedImage.tsx": "171", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\common\\PerformanceMonitor.tsx": "172", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\common\\VirtualizedList.tsx": "173", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\DashboardCharts.tsx": "174", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\GenealogyPreview.tsx": "175", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\PerformanceSummary.tsx": "176", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\QuickShareWidget.tsx": "177", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\RecentReferralsWidget.tsx": "178", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\TopProductsWidget.tsx": "179", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\UpcomingEvents.tsx": "180", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\AdvancedSearch.tsx": "181", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\BasicGenealogyFlow.tsx": "182", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\BasicUserNode.tsx": "183", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\CustomizableNode.tsx": "184", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\DraggableGenealogyFlow.tsx": "185", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\EnhancedGenealogyFlow.tsx": "186", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\EnhancedGenealogyTree.tsx": "187", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\EnhancedUserNode.tsx": "188", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\ExportOptions.tsx": "189", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyComparison.tsx": "190", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyControls.tsx": "191", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyFilters.tsx": "192", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyLegend.tsx": "193", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyMetricsDashboard.tsx": "194", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyNotifications.tsx": "195", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogySearch.tsx": "196", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyStatistics.tsx": "197", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyTree.tsx": "198", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyUserDetails.tsx": "199", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\ImportGenealogyData.tsx": "200", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\MobileGenealogyView.tsx": "201", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\PerformanceMetrics.tsx": "202", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SearchResults.tsx": "203", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SimpleFilters.tsx": "204", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SimpleStatistics.tsx": "205", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SyncExternalSystems.tsx": "206", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\UserDetailsPanel.tsx": "207", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\UserNodeComponent.tsx": "208", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\VirtualizedGenealogyFlow.tsx": "209", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\VisualizationSettings.tsx": "210", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\layout\\AdminLayout.tsx": "211", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\layout\\MainLayout.tsx": "212", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\layout\\__tests__\\MainLayout.test.tsx": "213", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\payment\\PaymentMethodSelector.tsx": "214", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\ProductPlaceholder.tsx": "215", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\FeaturedProducts.tsx": "216", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductBundles.tsx": "217", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductCarousel.tsx": "218", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductComparison.tsx": "219", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductFAQ.tsx": "220", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductIngredients.tsx": "221", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductTestimonials.tsx": "222", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductVideo.tsx": "223", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\purchases\\PurchaseDetailsModal.tsx": "224", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\rank\\RankBenefits.tsx": "225", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\sharing\\ProductShareButton.tsx": "226", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\shipping\\ShippingMethodSelector.tsx": "227", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\shop\\ProductCard.tsx": "228", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\shop\\PurchaseForm.tsx": "229", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\wallet\\WalletTransactionForm.tsx": "230", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx": "231", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\env.ts": "232", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\hooks\\useCart.ts": "233", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\auth.ts": "234", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\binaryMlmService.ts": "235", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\cache.ts": "236", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\csrf.ts": "237", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\email.ts": "238", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\emailService.ts": "239", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\excelService.ts": "240", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\genealogyService.ts": "241", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\inventoryService.ts": "242", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\mlmConfigService.ts": "243", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\optimizedGenealogyService.ts": "244", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\paymentMethodService.ts": "245", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\prisma.ts": "246", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\productService.ts": "247", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\purchaseService.ts": "248", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\pvRebateCalculator.ts": "249", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rankAdvancementService.ts": "250", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rankService.ts": "251", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rateLimit.ts": "252", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rebateCalculator.ts": "253", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\redisCache.ts": "254", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\referralService.ts": "255", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\serverCache.ts": "256", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\shareableLinkService.ts": "257", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\shippingMethodService.ts": "258", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\testDataGenerator.ts": "259", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\testDataGenerator2.ts": "260", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\testDataService.ts": "261", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\unifiedMlmService.ts": "262", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\unilevelMlmService.ts": "263", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\userExcelService.ts": "264", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\utils.ts": "265", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\validation.ts": "266", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\__tests__\\rebateCalculator.test.ts": "267", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\middleware.ts": "268", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx": "269", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx": "270", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx": "271", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\utils\\performance.ts": "272", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\utils\\serviceWorkerRegistration.ts": "273", "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\utils\\workerUtils.ts": "274"}, {"size": 14330, "mtime": 1746237864608, "results": "275", "hashOfConfig": "276"}, {"size": 18358, "mtime": 1746277688968, "results": "277", "hashOfConfig": "276"}, {"size": 29088, "mtime": 1746277619490, "results": "278", "hashOfConfig": "276"}, {"size": 11747, "mtime": 1748104575570, "results": "279", "hashOfConfig": "276"}, {"size": 42916, "mtime": 1746240667853, "results": "280", "hashOfConfig": "276"}, {"size": 24811, "mtime": 1746236795116, "results": "281", "hashOfConfig": "276"}, {"size": 25392, "mtime": 1746233864833, "results": "282", "hashOfConfig": "276"}, {"size": 27000, "mtime": 1746267084396, "results": "283", "hashOfConfig": "276"}, {"size": 26512, "mtime": 1746626407981, "results": "284", "hashOfConfig": "276"}, {"size": 2301, "mtime": 1746196161085, "results": "285", "hashOfConfig": "276"}, {"size": 2343, "mtime": 1748104590631, "results": "286", "hashOfConfig": "276"}, {"size": 1805, "mtime": 1746627697148, "results": "287", "hashOfConfig": "276"}, {"size": 2941, "mtime": 1746627622041, "results": "288", "hashOfConfig": "276"}, {"size": 2477, "mtime": 1746627677070, "results": "289", "hashOfConfig": "276"}, {"size": 14155, "mtime": 1748104623627, "results": "290", "hashOfConfig": "276"}, {"size": 3679, "mtime": 1746627536131, "results": "291", "hashOfConfig": "276"}, {"size": 2217, "mtime": 1746627475663, "results": "292", "hashOfConfig": "276"}, {"size": 3383, "mtime": 1746378350910, "results": "293", "hashOfConfig": "276"}, {"size": 4770, "mtime": 1746235837435, "results": "294", "hashOfConfig": "276"}, {"size": 5781, "mtime": 1746236824514, "results": "295", "hashOfConfig": "276"}, {"size": 8184, "mtime": 1746236856720, "results": "296", "hashOfConfig": "276"}, {"size": 4600, "mtime": 1746233979292, "results": "297", "hashOfConfig": "276"}, {"size": 1996, "mtime": 1746233946714, "results": "298", "hashOfConfig": "276"}, {"size": 3925, "mtime": 1746233904553, "results": "299", "hashOfConfig": "276"}, {"size": 2913, "mtime": 1746233934694, "results": "300", "hashOfConfig": "276"}, {"size": 8297, "mtime": 1746235349214, "results": "301", "hashOfConfig": "276"}, {"size": 4558, "mtime": 1746264873167, "results": "302", "hashOfConfig": "276"}, {"size": 1559, "mtime": 1746626466856, "results": "303", "hashOfConfig": "276"}, {"size": 2159, "mtime": 1746626441895, "results": "304", "hashOfConfig": "276"}, {"size": 7540, "mtime": 1746196137127, "results": "305", "hashOfConfig": "276"}, {"size": 2307, "mtime": 1746380853749, "results": "306", "hashOfConfig": "276"}, {"size": 2943, "mtime": 1746380669790, "results": "307", "hashOfConfig": "276"}, {"size": 5597, "mtime": 1746380645956, "results": "308", "hashOfConfig": "276"}, {"size": 1655, "mtime": 1748104698867, "results": "309", "hashOfConfig": "276"}, {"size": 1551, "mtime": 1748104727594, "results": "310", "hashOfConfig": "276"}, {"size": 1018, "mtime": 1748104832752, "results": "311", "hashOfConfig": "276"}, {"size": 161, "mtime": 1746187430147, "results": "312", "hashOfConfig": "276"}, {"size": 10964, "mtime": 1746276718602, "results": "313", "hashOfConfig": "276"}, {"size": 8619, "mtime": 1746276678384, "results": "314", "hashOfConfig": "276"}, {"size": 7890, "mtime": 1746713780897, "results": "315", "hashOfConfig": "276"}, {"size": 9441, "mtime": 1746536500726, "results": "316", "hashOfConfig": "276"}, {"size": 6171, "mtime": 1746270899708, "results": "317", "hashOfConfig": "276"}, {"size": 2054, "mtime": 1746270856514, "results": "318", "hashOfConfig": "276"}, {"size": 13127, "mtime": 1746535898231, "results": "319", "hashOfConfig": "276"}, {"size": 3563, "mtime": 1746536164900, "results": "320", "hashOfConfig": "276"}, {"size": 3742, "mtime": 1746536107814, "results": "321", "hashOfConfig": "276"}, {"size": 5291, "mtime": 1746536239663, "results": "322", "hashOfConfig": "276"}, {"size": 3313, "mtime": 1746536135166, "results": "323", "hashOfConfig": "276"}, {"size": 8777, "mtime": 1746381718579, "results": "324", "hashOfConfig": "276"}, {"size": 1946, "mtime": 1746270870944, "results": "325", "hashOfConfig": "276"}, {"size": 6748, "mtime": 1746713185903, "results": "326", "hashOfConfig": "276"}, {"size": 8570, "mtime": 1746534166387, "results": "327", "hashOfConfig": "276"}, {"size": 4885, "mtime": 1746531792643, "results": "328", "hashOfConfig": "276"}, {"size": 1319, "mtime": 1746237930924, "results": "329", "hashOfConfig": "276"}, {"size": 6878, "mtime": 1746277529936, "results": "330", "hashOfConfig": "276"}, {"size": 7417, "mtime": 1746277492165, "results": "331", "hashOfConfig": "276"}, {"size": 7951, "mtime": 1746624317331, "results": "332", "hashOfConfig": "276"}, {"size": 1775, "mtime": 1746278212024, "results": "333", "hashOfConfig": "276"}, {"size": 9087, "mtime": 1746278249552, "results": "334", "hashOfConfig": "276"}, {"size": 3621, "mtime": 1746541837955, "results": "335", "hashOfConfig": "276"}, {"size": 8231, "mtime": 1746540448027, "results": "336", "hashOfConfig": "276"}, {"size": 1741, "mtime": 1746543816735, "results": "337", "hashOfConfig": "276"}, {"size": 8147, "mtime": 1746540301528, "results": "338", "hashOfConfig": "276"}, {"size": 6790, "mtime": 1746624490244, "results": "339", "hashOfConfig": "276"}, {"size": 9510, "mtime": 1746541535097, "results": "340", "hashOfConfig": "276"}, {"size": 4868, "mtime": 1746625031288, "results": "341", "hashOfConfig": "276"}, {"size": 9101, "mtime": 1746541089985, "results": "342", "hashOfConfig": "276"}, {"size": 6970, "mtime": 1746233271267, "results": "343", "hashOfConfig": "276"}, {"size": 2382, "mtime": 1746233240409, "results": "344", "hashOfConfig": "276"}, {"size": 4187, "mtime": 1746379581332, "results": "345", "hashOfConfig": "276"}, {"size": 1491, "mtime": 1746189045182, "results": "346", "hashOfConfig": "276"}, {"size": 4272, "mtime": 1746234674126, "results": "347", "hashOfConfig": "276"}, {"size": 2727, "mtime": 1746234648363, "results": "348", "hashOfConfig": "276"}, {"size": 4205, "mtime": 1746625065878, "results": "349", "hashOfConfig": "276"}, {"size": 2060, "mtime": 1746377469664, "results": "350", "hashOfConfig": "276"}, {"size": 3922, "mtime": 1746236706577, "results": "351", "hashOfConfig": "276"}, {"size": 3667, "mtime": 1746236689100, "results": "352", "hashOfConfig": "276"}, {"size": 2958, "mtime": 1746377149412, "results": "353", "hashOfConfig": "276"}, {"size": 9297, "mtime": 1746377128409, "results": "354", "hashOfConfig": "276"}, {"size": 3503, "mtime": 1746620208706, "results": "355", "hashOfConfig": "276"}, {"size": 2594, "mtime": 1746620230623, "results": "356", "hashOfConfig": "276"}, {"size": 1826, "mtime": 1746379541861, "results": "357", "hashOfConfig": "276"}, {"size": 2785, "mtime": 1746240256957, "results": "358", "hashOfConfig": "276"}, {"size": 4612, "mtime": 1746199467173, "results": "359", "hashOfConfig": "276"}, {"size": 1505, "mtime": 1746195288212, "results": "360", "hashOfConfig": "276"}, {"size": 2356, "mtime": 1746236074312, "results": "361", "hashOfConfig": "276"}, {"size": 3233, "mtime": 1746235816287, "results": "362", "hashOfConfig": "276"}, {"size": 6059, "mtime": 1746629110844, "results": "363", "hashOfConfig": "276"}, {"size": 5658, "mtime": 1746629638446, "results": "364", "hashOfConfig": "276"}, {"size": 3721, "mtime": 1746531560445, "results": "365", "hashOfConfig": "276"}, {"size": 7775, "mtime": 1746229674605, "results": "366", "hashOfConfig": "276"}, {"size": 2644, "mtime": 1746232980458, "results": "367", "hashOfConfig": "276"}, {"size": 3332, "mtime": 1746187518080, "results": "368", "hashOfConfig": "276"}, {"size": 4456, "mtime": 1746188740942, "results": "369", "hashOfConfig": "276"}, {"size": 5504, "mtime": 1746188765297, "results": "370", "hashOfConfig": "276"}, {"size": 13623, "mtime": 1746276936885, "results": "371", "hashOfConfig": "276"}, {"size": 24640, "mtime": 1746630079600, "results": "372", "hashOfConfig": "276"}, {"size": 19951, "mtime": 1746711746540, "results": "373", "hashOfConfig": "276"}, {"size": 10440, "mtime": 1746618715527, "results": "374", "hashOfConfig": "276"}, {"size": 5642, "mtime": 1746531762642, "results": "375", "hashOfConfig": "276"}, {"size": 13711, "mtime": 1746533917370, "results": "376", "hashOfConfig": "276"}, {"size": 14936, "mtime": 1746536683227, "results": "377", "hashOfConfig": "276"}, {"size": 6510, "mtime": 1746531998444, "results": "378", "hashOfConfig": "276"}, {"size": 6439, "mtime": 1746534406732, "results": "379", "hashOfConfig": "276"}, {"size": 8677, "mtime": 1746535586927, "results": "380", "hashOfConfig": "276"}, {"size": 6552, "mtime": 1746534964445, "results": "381", "hashOfConfig": "276"}, {"size": 6505, "mtime": 1746535946951, "results": "382", "hashOfConfig": "276"}, {"size": 6843, "mtime": 1746535353609, "results": "383", "hashOfConfig": "276"}, {"size": 7423, "mtime": 1746536288218, "results": "384", "hashOfConfig": "276"}, {"size": 4197, "mtime": 1746531000719, "results": "385", "hashOfConfig": "276"}, {"size": 21402, "mtime": 1746538700926, "results": "386", "hashOfConfig": "276"}, {"size": 6817, "mtime": 1746534270965, "results": "387", "hashOfConfig": "276"}, {"size": 6936, "mtime": 1746535167048, "results": "388", "hashOfConfig": "276"}, {"size": 1366, "mtime": 1746714263262, "results": "389", "hashOfConfig": "276"}, {"size": 15552, "mtime": 1746544127730, "results": "390", "hashOfConfig": "276"}, {"size": 29670, "mtime": 1746543906657, "results": "391", "hashOfConfig": "276"}, {"size": 13640, "mtime": 1746540519454, "results": "392", "hashOfConfig": "276"}, {"size": 13640, "mtime": 1746539949483, "results": "393", "hashOfConfig": "276"}, {"size": 30377, "mtime": 1746542951061, "results": "394", "hashOfConfig": "276"}, {"size": 15628, "mtime": 1746541165069, "results": "395", "hashOfConfig": "276"}, {"size": 20094, "mtime": 1746376623611, "results": "396", "hashOfConfig": "276"}, {"size": 15596, "mtime": 1746376559674, "results": "397", "hashOfConfig": "276"}, {"size": 14510, "mtime": 1746379688312, "results": "398", "hashOfConfig": "276"}, {"size": 22139, "mtime": 1746618403369, "results": "399", "hashOfConfig": "276"}, {"size": 22128, "mtime": 1746714034903, "results": "400", "hashOfConfig": "276"}, {"size": 17571, "mtime": 1746377449625, "results": "401", "hashOfConfig": "276"}, {"size": 16975, "mtime": 1746625241480, "results": "402", "hashOfConfig": "276"}, {"size": 17747, "mtime": 1746377368276, "results": "403", "hashOfConfig": "276"}, {"size": 47321, "mtime": 1746629429420, "results": "404", "hashOfConfig": "276"}, {"size": 19884, "mtime": 1746618797585, "results": "405", "hashOfConfig": "276"}, {"size": 2029, "mtime": 1746377165901, "results": "406", "hashOfConfig": "276"}, {"size": 6597, "mtime": 1746624702056, "results": "407", "hashOfConfig": "276"}, {"size": 11263, "mtime": 1746624385986, "results": "408", "hashOfConfig": "276"}, {"size": 12075, "mtime": 1746200087252, "results": "409", "hashOfConfig": "276"}, {"size": 3965, "mtime": 1746265860496, "results": "410", "hashOfConfig": "276"}, {"size": 1046, "mtime": 1746265875859, "results": "411", "hashOfConfig": "276"}, {"size": 3470, "mtime": 1746266172654, "results": "412", "hashOfConfig": "276"}, {"size": 4435, "mtime": 1746266149995, "results": "413", "hashOfConfig": "276"}, {"size": 3250, "mtime": 1746266195304, "results": "414", "hashOfConfig": "276"}, {"size": 9273, "mtime": 1746266272796, "results": "415", "hashOfConfig": "276"}, {"size": 6930, "mtime": 1746266230387, "results": "416", "hashOfConfig": "276"}, {"size": 8949, "mtime": 1746627594270, "results": "417", "hashOfConfig": "276"}, {"size": 15971, "mtime": 1746627078660, "results": "418", "hashOfConfig": "276"}, {"size": 9843, "mtime": 1746378589796, "results": "419", "hashOfConfig": "276"}, {"size": 4461, "mtime": 1746378660266, "results": "420", "hashOfConfig": "276"}, {"size": 21191, "mtime": 1746378950138, "results": "421", "hashOfConfig": "276"}, {"size": 19720, "mtime": 1746378807182, "results": "422", "hashOfConfig": "276"}, {"size": 21736, "mtime": 1746627445740, "results": "423", "hashOfConfig": "276"}, {"size": 9544, "mtime": 1746378547999, "results": "424", "hashOfConfig": "276"}, {"size": 8929, "mtime": 1746378637607, "results": "425", "hashOfConfig": "276"}, {"size": 9070, "mtime": 1746378874726, "results": "426", "hashOfConfig": "276"}, {"size": 5145, "mtime": 1746378836632, "results": "427", "hashOfConfig": "276"}, {"size": 19814, "mtime": 1746627286667, "results": "428", "hashOfConfig": "276"}, {"size": 828, "mtime": 1746265886722, "results": "429", "hashOfConfig": "276"}, {"size": 1910, "mtime": 1746265815690, "results": "430", "hashOfConfig": "276"}, {"size": 20834, "mtime": 1746195250515, "results": "431", "hashOfConfig": "276"}, {"size": 4103, "mtime": 1746265838229, "results": "432", "hashOfConfig": "276"}, {"size": 29419, "mtime": 1746381006338, "results": "433", "hashOfConfig": "276"}, {"size": 10146, "mtime": 1746380806236, "results": "434", "hashOfConfig": "276"}, {"size": 4850, "mtime": 1746380834228, "results": "435", "hashOfConfig": "276"}, {"size": 28348, "mtime": 1746380762735, "results": "436", "hashOfConfig": "276"}, {"size": 6207, "mtime": 1746276838343, "results": "437", "hashOfConfig": "276"}, {"size": 10209, "mtime": 1746276882842, "results": "438", "hashOfConfig": "276"}, {"size": 963, "mtime": 1746619934199, "results": "439", "hashOfConfig": "276"}, {"size": 10885, "mtime": 1746619917441, "results": "440", "hashOfConfig": "276"}, {"size": 14751, "mtime": 1746629989539, "results": "441", "hashOfConfig": "276"}, {"size": 6546, "mtime": 1746630010509, "results": "442", "hashOfConfig": "276"}, {"size": 10625, "mtime": 1746629952295, "results": "443", "hashOfConfig": "276"}, {"size": 11169, "mtime": 1746620047200, "results": "444", "hashOfConfig": "276"}, {"size": 3798, "mtime": 1746620077153, "results": "445", "hashOfConfig": "276"}, {"size": 2853, "mtime": 1746713324896, "results": "446", "hashOfConfig": "276"}, {"size": 6097, "mtime": 1746713642143, "results": "447", "hashOfConfig": "276"}, {"size": 3365, "mtime": 1746713909359, "results": "448", "hashOfConfig": "276"}, {"size": 8620, "mtime": 1746575200194, "results": "449", "hashOfConfig": "276"}, {"size": 6561, "mtime": 1748104553065, "results": "450", "hashOfConfig": "276"}, {"size": 5219, "mtime": 1746575303419, "results": "451", "hashOfConfig": "276"}, {"size": 10696, "mtime": 1746624926438, "results": "452", "hashOfConfig": "276"}, {"size": 6052, "mtime": 1746624996380, "results": "453", "hashOfConfig": "276"}, {"size": 5949, "mtime": 1746624963522, "results": "454", "hashOfConfig": "276"}, {"size": 6612, "mtime": 1746575344435, "results": "455", "hashOfConfig": "276"}, {"size": 15178, "mtime": 1746534115857, "results": "456", "hashOfConfig": "276"}, {"size": 9726, "mtime": 1746531698700, "results": "457", "hashOfConfig": "276"}, {"size": 4300, "mtime": 1746531224868, "results": "458", "hashOfConfig": "276"}, {"size": 13469, "mtime": 1746534741245, "results": "459", "hashOfConfig": "276"}, {"size": 22206, "mtime": 1746534887932, "results": "460", "hashOfConfig": "276"}, {"size": 13812, "mtime": 1746531961129, "results": "461", "hashOfConfig": "276"}, {"size": 20389, "mtime": 1746239103356, "results": "462", "hashOfConfig": "276"}, {"size": 8254, "mtime": 1746531897343, "results": "463", "hashOfConfig": "276"}, {"size": 11956, "mtime": 1746534360242, "results": "464", "hashOfConfig": "276"}, {"size": 22396, "mtime": 1746536441331, "results": "465", "hashOfConfig": "276"}, {"size": 2390, "mtime": 1746531584427, "results": "466", "hashOfConfig": "276"}, {"size": 7661, "mtime": 1746530713150, "results": "467", "hashOfConfig": "276"}, {"size": 6537, "mtime": 1746530902087, "results": "468", "hashOfConfig": "276"}, {"size": 14140, "mtime": 1746535820537, "results": "469", "hashOfConfig": "276"}, {"size": 18324, "mtime": 1746536079554, "results": "470", "hashOfConfig": "276"}, {"size": 2074, "mtime": 1746530925580, "results": "471", "hashOfConfig": "276"}, {"size": 7226, "mtime": 1746530856465, "results": "472", "hashOfConfig": "276"}, {"size": 7707, "mtime": 1746189088951, "results": "473", "hashOfConfig": "276"}, {"size": 11841, "mtime": 1746530975456, "results": "474", "hashOfConfig": "276"}, {"size": 17427, "mtime": 1746535469509, "results": "475", "hashOfConfig": "276"}, {"size": 26959, "mtime": 1746535310475, "results": "476", "hashOfConfig": "276"}, {"size": 4230, "mtime": 1746531346758, "results": "477", "hashOfConfig": "276"}, {"size": 16048, "mtime": 1746534231963, "results": "478", "hashOfConfig": "276"}, {"size": 5066, "mtime": 1746531609780, "results": "479", "hashOfConfig": "276"}, {"size": 4669, "mtime": 1746531729063, "results": "480", "hashOfConfig": "276"}, {"size": 17372, "mtime": 1746535545661, "results": "481", "hashOfConfig": "276"}, {"size": 4745, "mtime": 1746531451247, "results": "482", "hashOfConfig": "276"}, {"size": 6385, "mtime": 1746530675652, "results": "483", "hashOfConfig": "276"}, {"size": 25005, "mtime": 1746535118971, "results": "484", "hashOfConfig": "276"}, {"size": 19591, "mtime": 1746534661488, "results": "485", "hashOfConfig": "276"}, {"size": 9194, "mtime": 1746627846881, "results": "486", "hashOfConfig": "276"}, {"size": 7460, "mtime": 1746713684526, "results": "487", "hashOfConfig": "276"}, {"size": 2754, "mtime": 1746188780194, "results": "488", "hashOfConfig": "276"}, {"size": 16225, "mtime": 1746278503193, "results": "489", "hashOfConfig": "276"}, {"size": 566, "mtime": 1746240587069, "results": "490", "hashOfConfig": "276"}, {"size": 5725, "mtime": 1746540649222, "results": "491", "hashOfConfig": "276"}, {"size": 6758, "mtime": 1746542864648, "results": "492", "hashOfConfig": "276"}, {"size": 7498, "mtime": 1746543793354, "results": "493", "hashOfConfig": "276"}, {"size": 6609, "mtime": 1746541970139, "results": "494", "hashOfConfig": "276"}, {"size": 1633, "mtime": 1746541994775, "results": "495", "hashOfConfig": "276"}, {"size": 3399, "mtime": 1746542043108, "results": "496", "hashOfConfig": "276"}, {"size": 5274, "mtime": 1746542187492, "results": "497", "hashOfConfig": "276"}, {"size": 5164, "mtime": 1746542662191, "results": "498", "hashOfConfig": "276"}, {"size": 19997, "mtime": 1746379769845, "results": "499", "hashOfConfig": "276"}, {"size": 11376, "mtime": 1746618330812, "results": "500", "hashOfConfig": "276"}, {"size": 7906, "mtime": 1746377292745, "results": "501", "hashOfConfig": "276"}, {"size": 9219, "mtime": 1746379362160, "results": "502", "hashOfConfig": "276"}, {"size": 4794, "mtime": 1746713589205, "results": "503", "hashOfConfig": "276"}, {"size": 9771, "mtime": 1746379524692, "results": "504", "hashOfConfig": "276"}, {"size": 7993, "mtime": 1746376497580, "results": "505", "hashOfConfig": "276"}, {"size": 3183, "mtime": 1746624178306, "results": "506", "hashOfConfig": "276"}, {"size": 2085, "mtime": 1746381404274, "results": "507", "hashOfConfig": "276"}, {"size": 2127, "mtime": 1748104464746, "results": "508", "hashOfConfig": "276"}, {"size": 4804, "mtime": 1746618867435, "results": "509", "hashOfConfig": "276"}, {"size": 21422, "mtime": 1746276529057, "results": "510", "hashOfConfig": "276"}, {"size": 3935, "mtime": 1746189457117, "results": "511", "hashOfConfig": "276"}, {"size": 991, "mtime": 1746196833045, "results": "512", "hashOfConfig": "276"}, {"size": 4351, "mtime": 1746618896655, "results": "513", "hashOfConfig": "276"}, {"size": 4599, "mtime": 1746188946365, "results": "514", "hashOfConfig": "276"}, {"size": 5302, "mtime": 1746378266640, "results": "515", "hashOfConfig": "276"}, {"size": 16250, "mtime": 1746713293136, "results": "516", "hashOfConfig": "276"}, {"size": 9233, "mtime": 1746626895396, "results": "517", "hashOfConfig": "276"}, {"size": 8652, "mtime": 1746277355067, "results": "518", "hashOfConfig": "276"}, {"size": 24044, "mtime": 1746381670493, "results": "519", "hashOfConfig": "276"}, {"size": 7522, "mtime": 1746278147096, "results": "520", "hashOfConfig": "276"}, {"size": 294, "mtime": 1746187435510, "results": "521", "hashOfConfig": "276"}, {"size": 22146, "mtime": 1746378234815, "results": "522", "hashOfConfig": "276"}, {"size": 7233, "mtime": 1746379299907, "results": "523", "hashOfConfig": "276"}, {"size": 9994, "mtime": 1746276574604, "results": "524", "hashOfConfig": "276"}, {"size": 10604, "mtime": 1746235799718, "results": "525", "hashOfConfig": "276"}, {"size": 5730, "mtime": 1746189025774, "results": "526", "hashOfConfig": "276"}, {"size": 3457, "mtime": 1746190548009, "results": "527", "hashOfConfig": "276"}, {"size": 9469, "mtime": 1746236617936, "results": "528", "hashOfConfig": "276"}, {"size": 8607, "mtime": 1746381385897, "results": "529", "hashOfConfig": "276"}, {"size": 8370, "mtime": 1746236666307, "results": "530", "hashOfConfig": "276"}, {"size": 4135, "mtime": 1746713720453, "results": "531", "hashOfConfig": "276"}, {"size": 11583, "mtime": 1746377084691, "results": "532", "hashOfConfig": "276"}, {"size": 5015, "mtime": 1746379234291, "results": "533", "hashOfConfig": "276"}, {"size": 17603, "mtime": 1748104639220, "results": "534", "hashOfConfig": "276"}, {"size": 8032, "mtime": 1746626248495, "results": "535", "hashOfConfig": "276"}, {"size": 16984, "mtime": 1746625772501, "results": "536", "hashOfConfig": "276"}, {"size": 6553, "mtime": 1746277443163, "results": "537", "hashOfConfig": "276"}, {"size": 12814, "mtime": 1746277411669, "results": "538", "hashOfConfig": "276"}, {"size": 16090, "mtime": 1746380469173, "results": "539", "hashOfConfig": "276"}, {"size": 3454, "mtime": 1746620273201, "results": "540", "hashOfConfig": "276"}, {"size": 5830, "mtime": 1746629075523, "results": "541", "hashOfConfig": "276"}, {"size": 7007, "mtime": 1746188716123, "results": "542", "hashOfConfig": "276"}, {"size": 4458, "mtime": 1746381772513, "results": "543", "hashOfConfig": "276"}, {"size": 201, "mtime": 1746187862310, "results": "544", "hashOfConfig": "276"}, {"size": 626, "mtime": 1746711826012, "results": "545", "hashOfConfig": "276"}, {"size": 1746, "mtime": 1746714285756, "results": "546", "hashOfConfig": "276"}, {"size": 4056, "mtime": 1746712147974, "results": "547", "hashOfConfig": "276"}, {"size": 4824, "mtime": 1746714219620, "results": "548", "hashOfConfig": "276"}, {"size": 4519, "mtime": 1746714144727, "results": "549", "hashOfConfig": "276"}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "x9m519", {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1363", "messages": "1364", "suppressedMessages": "1365", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1366", "messages": "1367", "suppressedMessages": "1368", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1369", "messages": "1370", "suppressedMessages": "1371", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\about\\page.tsx", ["1372"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\cutoff\\page.tsx", ["1373", "1374", "1375", "1376", "1377", "1378"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\page.tsx", ["1379", "1380", "1381", "1382", "1383", "1384", "1385"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\page.tsx", ["1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\products\\page.tsx", ["1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebate-configs\\page.tsx", ["1413", "1414", "1415"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebates\\page.tsx", ["1416", "1417", "1418", "1419", "1420", "1421"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\reports\\page.tsx", ["1422", "1423", "1424"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-data\\page.tsx", ["1425", "1426"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-users\\page.tsx", ["1427"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\users\\page.tsx", ["1428"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\notifications\\mark-all-read\\route.ts", ["1429"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\notifications\\route.ts", ["1430"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\notifications\\[id]\\mark-read\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\route.ts", ["1431"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\adjust-inventory\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\inventory-transactions\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\route.ts", ["1432"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rank-advancements\\route.ts", ["1433", "1434"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebate-configs\\route.ts", ["1435"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebate-configs\\[id]\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\export\\route.ts", ["1436"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\process\\route.ts", ["1437"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\route.ts", ["1438"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\stats\\route.ts", ["1439"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\reports\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\stats\\route.ts", ["1440"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-data\\cleanup\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-data\\generate\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-users\\route.ts", ["1441"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\audit\\route.ts", ["1442"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\export\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\import\\route.ts", ["1443", "1444"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\forgot-password\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\reset-password\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\validate-reset-token\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\binary-mlm\\export\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\binary-mlm\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\dashboard\\route.ts", ["1445", "1446"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\compare\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\export\\route.ts", ["1447"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\load-levels\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\metrics\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\read-all\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\settings\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\notifications\\[id]\\read\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\optimized\\route.ts", ["1448", "1449", "1450", "1451", "1452", "1453"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\performance\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\route.ts", ["1454", "1455"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\search\\route.ts", ["1456"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\statistics\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\generate-logo\\route.ts", ["1457"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\mlm-config\\cutoff\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\mlm-config\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\orders\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\payment-methods\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\payment-methods\\user\\route.ts", ["1458", "1459"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\product-categories\\route.ts", ["1460"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\biogen-extreme\\route.ts", ["1461", "1462"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\featured\\route.ts", ["1463"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\oxygen-extreme\\route.ts", ["1464", "1465"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\route.ts", ["1466", "1467", "1468", "1469", "1470"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\shield-soap\\route.ts", ["1471", "1472"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\top-performers\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\veggie-coffee\\route.ts", ["1473", "1474"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\[id]\\route.ts", ["1475"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\[id]\\toggle-status\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\purchases\\route.ts", ["1476"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\ranks\\check\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rebates\\route.ts", ["1477", "1478", "1479"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rebates\\stats\\route.ts", ["1480"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\referrals\\activity\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\referrals\\commissions\\route.ts", ["1481"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rewards\\bonus\\route.ts", ["1482"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rewards\\referral\\route.ts", ["1483"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shareable-links\\click\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shareable-links\\route.ts", ["1484"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping\\addresses\\route.ts", ["1485"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping\\methods\\route.ts", ["1486"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping-methods\\route.ts", ["1487"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\upload\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\genealogy\\route.ts", ["1488", "1489", "1490", "1491"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\me\\route.ts", ["1492"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\rank-advancement\\history\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\rank-advancement\\route.ts", ["1493"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\route.ts", ["1494", "1495", "1496", "1497"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\payment-details\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\performance\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\route.ts", ["1498", "1499", "1500"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\wallet\\reset\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\wallet\\route.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\__tests__\\products.test.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\__tests__\\purchases.test.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\binary-mlm\\page.tsx", ["1501", "1502", "1503", "1504", "1505"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\checkout\\page.tsx", ["1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\dashboard\\page.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\forgot-password\\page.tsx", ["1519", "1520", "1521", "1522", "1523", "1524", "1525"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\basic-flow\\page.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare\\page.tsx", ["1526", "1527"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare-users\\page.tsx", ["1528", "1529", "1530", "1531", "1532", "1533"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\enhanced-flow\\page.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\export\\page.tsx", ["1534", "1535"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\integration\\page.tsx", ["1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\interactive\\page.tsx", ["1546"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\metrics\\page.tsx", ["1547", "1548", "1549", "1550"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\mobile\\page.tsx", ["1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\notifications\\page.tsx", ["1559", "1560", "1561", "1562"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\optimized\\page.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\page.tsx", ["1563", "1564", "1565"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\search\\page.tsx", ["1566"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\virtualized\\page.tsx", ["1567"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\login\\page.tsx", ["1568", "1569", "1570", "1571"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\page.tsx", ["1572", "1573", "1574", "1575", "1576", "1577"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\biogen-extreme\\page.tsx", ["1578", "1579", "1580", "1581", "1582", "1583"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\oxygen-extreme\\page.tsx", ["1584", "1585", "1586", "1587", "1588", "1589"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\shield-soap\\page.tsx", ["1590", "1591", "1592", "1593", "1594", "1595"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\veggie-coffee\\page.tsx", ["1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\page.tsx", ["1604", "1605", "1606", "1607", "1608", "1609"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\payment-methods\\page.tsx", ["1610", "1611", "1612", "1613", "1614"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\purchases\\page.tsx", ["1615", "1616", "1617", "1618", "1619", "1620"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rank-advancement\\page.tsx", ["1621", "1622"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rebates\\page.tsx", ["1623", "1624", "1625", "1626"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\commissions\\page.tsx", ["1627", "1628"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\generate\\page.tsx", ["1629", "1630", "1631", "1632", "1633", "1634", "1635"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\page.tsx", ["1636", "1637", "1638"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\register\\page.tsx", ["1639", "1640", "1641", "1642", "1643", "1644"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\reset-password\\page.tsx", ["1645", "1646", "1647", "1648", "1649", "1650"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\s\\[code]\\route.ts", ["1651"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\shop\\page.tsx", ["1652", "1653", "1654", "1655", "1656", "1657"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\shop\\product\\[id]\\page.tsx", ["1658", "1659", "1660"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\wallet\\page.tsx", ["1661"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\ActivityFeed.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\ChartPlaceholder.tsx", ["1662"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\MemberDistributionChart.tsx", ["1663", "1664", "1665"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\MonthlySalesChart.tsx", ["1666", "1667", "1668"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\charts\\RebatesByRankChart.tsx", ["1669", "1670", "1671"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\DashboardFilters.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\GroupVolumeTracker.tsx", ["1672"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\notifications\\NotificationDropdown.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\InventoryManagementModal.tsx", ["1673", "1674", "1675", "1676", "1677"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductBulkActions.tsx", ["1678", "1679", "1680", "1681"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductCloneModal.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductCreateModal.tsx", ["1682", "1683", "1684"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductDetailsModal.tsx", ["1685", "1686", "1687", "1688"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductEditModal.tsx", ["1689", "1690", "1691", "1692", "1693", "1694", "1695"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductFilters.tsx", ["1696"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductImportModal.tsx", ["1697", "1698"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductRebateSimulatorModal.tsx", ["1699", "1700"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductSalesChart.tsx", ["1701", "1702"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\products\\ProductTable.tsx", ["1703", "1704"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\QuickAccessCard.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\StatsCard.tsx", ["1705"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\TestUserManager.tsx", ["1706", "1707", "1708", "1709"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\TopEarnersTable.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\UserManager.tsx", ["1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\users\\UserExportModal.tsx", ["1718"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\users\\UserImportHistory.tsx", ["1719", "1720"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\admin\\users\\UserImportModal.tsx", ["1721", "1722"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\binary-mlm\\BinaryTreeView.tsx", ["1723"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\binary-mlm\\EarningsReport.tsx", ["1724", "1725", "1726"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\cart\\CartButton.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\cart\\CartDrawer.tsx", ["1727", "1728"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\GuestCheckoutForm.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\OrderSummary.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\PaymentMethodSelector.tsx", ["1729"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\ShippingAddressForm.tsx", ["1730", "1731"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\checkout\\ShippingMethodSelector.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\common\\OptimizedImage.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\common\\PerformanceMonitor.tsx", ["1732"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\common\\VirtualizedList.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\DashboardCharts.tsx", ["1733", "1734", "1735"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\GenealogyPreview.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\PerformanceSummary.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\QuickShareWidget.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\RecentReferralsWidget.tsx", ["1736"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\TopProductsWidget.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\dashboard\\UpcomingEvents.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\AdvancedSearch.tsx", ["1737"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\BasicGenealogyFlow.tsx", ["1738", "1739", "1740", "1741", "1742", "1743"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\BasicUserNode.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\CustomizableNode.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\DraggableGenealogyFlow.tsx", ["1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\EnhancedGenealogyFlow.tsx", ["1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\EnhancedGenealogyTree.tsx", ["1764", "1765", "1766", "1767"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\EnhancedUserNode.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\ExportOptions.tsx", ["1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyComparison.tsx", ["1776", "1777", "1778", "1779", "1780"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyControls.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyFilters.tsx", ["1781", "1782", "1783", "1784"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyLegend.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyMetricsDashboard.tsx", ["1785"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyNotifications.tsx", ["1786", "1787", "1788", "1789"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogySearch.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyStatistics.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyTree.tsx", ["1790"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\GenealogyUserDetails.tsx", ["1791"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\ImportGenealogyData.tsx", ["1792", "1793", "1794", "1795", "1796", "1797"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\MobileGenealogyView.tsx", ["1798", "1799", "1800", "1801", "1802"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\PerformanceMetrics.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SearchResults.tsx", ["1803"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SimpleFilters.tsx", ["1804", "1805"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SimpleStatistics.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\SyncExternalSystems.tsx", ["1806", "1807"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\UserDetailsPanel.tsx", ["1808", "1809"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\UserNodeComponent.tsx", ["1810"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\VirtualizedGenealogyFlow.tsx", ["1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\genealogy\\VisualizationSettings.tsx", ["1819", "1820", "1821", "1822"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\layout\\AdminLayout.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\layout\\MainLayout.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\layout\\__tests__\\MainLayout.test.tsx", ["1823"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\payment\\PaymentMethodSelector.tsx", ["1824", "1825", "1826", "1827"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\ProductPlaceholder.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\FeaturedProducts.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductBundles.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductCarousel.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductComparison.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductFAQ.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductIngredients.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductTestimonials.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\products\\ProductVideo.tsx", ["1828"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\purchases\\PurchaseDetailsModal.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\rank\\RankBenefits.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\sharing\\ProductShareButton.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\shipping\\ShippingMethodSelector.tsx", ["1829", "1830", "1831", "1832", "1833"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\shop\\ProductCard.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\shop\\PurchaseForm.tsx", ["1834", "1835", "1836", "1837", "1838", "1839"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\components\\wallet\\WalletTransactionForm.tsx", ["1840", "1841", "1842", "1843"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\env.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\hooks\\useCart.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\auth.ts", ["1844", "1845", "1846", "1847"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\binaryMlmService.ts", ["1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\cache.ts", ["1859", "1860", "1861", "1862", "1863", "1864", "1865"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\csrf.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\email.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\emailService.ts", ["1866"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\excelService.ts", ["1867", "1868"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\genealogyService.ts", ["1869", "1870", "1871", "1872", "1873", "1874"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\inventoryService.ts", ["1875"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\mlmConfigService.ts", ["1876"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\optimizedGenealogyService.ts", ["1877", "1878", "1879", "1880", "1881"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\paymentMethodService.ts", ["1882"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\prisma.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\productService.ts", ["1883", "1884", "1885"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\purchaseService.ts", ["1886", "1887"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\pvRebateCalculator.ts", ["1888", "1889", "1890"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rankAdvancementService.ts", ["1891", "1892", "1893", "1894"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rankService.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rateLimit.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\rebateCalculator.ts", ["1895", "1896"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\redisCache.ts", ["1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\referralService.ts", ["1906"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\serverCache.ts", ["1907"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\shareableLinkService.ts", ["1908", "1909"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\shippingMethodService.ts", ["1910", "1911", "1912", "1913", "1914", "1915"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\testDataGenerator.ts", ["1916", "1917", "1918", "1919", "1920", "1921"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\testDataGenerator2.ts", ["1922", "1923", "1924", "1925", "1926", "1927", "1928"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\testDataService.ts", ["1929", "1930", "1931", "1932", "1933", "1934"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\unifiedMlmService.ts", ["1935", "1936", "1937", "1938", "1939", "1940", "1941", "1942"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\unilevelMlmService.ts", ["1943", "1944", "1945", "1946", "1947"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\userExcelService.ts", ["1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\utils.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\validation.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\lib\\__tests__\\rebateCalculator.test.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\middleware.ts", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx", [], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\utils\\performance.ts", ["1958", "1959", "1960", "1961", "1962", "1963"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\utils\\serviceWorkerRegistration.ts", ["1964"], [], "C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\utils\\workerUtils.ts", ["1965", "1966", "1967", "1968", "1969", "1970", "1971", "1972", "1973", "1974", "1975", "1976", "1977", "1978"], [], {"ruleId": "1979", "severity": 2, "message": "1980", "line": 91, "column": 46, "nodeType": "1981", "messageId": "1982", "suggestions": "1983"}, {"ruleId": "1984", "severity": 2, "message": "1985", "line": 9, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 16}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 31, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 31, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "1988", "line": 121, "column": 13, "nodeType": null, "messageId": "1986", "endLine": 121, "endColumn": 17}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 191, "column": 50, "nodeType": "1991", "messageId": "1992", "endLine": 191, "endColumn": 53, "suggestions": "1993"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 314, "column": 64, "nodeType": "1981", "messageId": "1982", "suggestions": "1995"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 314, "column": 75, "nodeType": "1981", "messageId": "1982", "suggestions": "1996"}, {"ruleId": "1984", "severity": 2, "message": "1997", "line": 17, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 17, "endColumn": 10}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 41, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 41, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "1988", "line": 157, "column": 13, "nodeType": null, "messageId": "1986", "endLine": 157, "endColumn": 17}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 181, "column": 67, "nodeType": "1991", "messageId": "1992", "endLine": 181, "endColumn": 70, "suggestions": "1998"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 190, "column": 69, "nodeType": "1991", "messageId": "1992", "endLine": 190, "endColumn": 72, "suggestions": "1999"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 473, "column": 65, "nodeType": "1981", "messageId": "1982", "suggestions": "2000"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 473, "column": 74, "nodeType": "1981", "messageId": "1982", "suggestions": "2001"}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 9, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "2003", "line": 20, "column": 51, "nodeType": null, "messageId": "1986", "endLine": 20, "endColumn": 56}, {"ruleId": "1984", "severity": 2, "message": "2004", "line": 21, "column": 15, "nodeType": null, "messageId": "1986", "endLine": 21, "endColumn": 27}, {"ruleId": "1984", "severity": 2, "message": "2005", "line": 21, "column": 29, "nodeType": null, "messageId": "1986", "endLine": 21, "endColumn": 41}, {"ruleId": "1984", "severity": 2, "message": "2006", "line": 22, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 22, "endColumn": 12}, {"ruleId": "1984", "severity": 2, "message": "2007", "line": 22, "column": 14, "nodeType": null, "messageId": "1986", "endLine": 22, "endColumn": 25}, {"ruleId": "1984", "severity": 2, "message": "1985", "line": 22, "column": 50, "nodeType": null, "messageId": "1986", "endLine": 22, "endColumn": 63}, {"ruleId": "1984", "severity": 2, "message": "2008", "line": 23, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 23, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "2009", "line": 23, "column": 49, "nodeType": null, "messageId": "1986", "endLine": 23, "endColumn": 57}, {"ruleId": "1984", "severity": 2, "message": "2010", "line": 24, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 24, "endColumn": 11}, {"ruleId": "1984", "severity": 2, "message": "2011", "line": 24, "column": 13, "nodeType": null, "messageId": "1986", "endLine": 24, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "2012", "line": 24, "column": 23, "nodeType": null, "messageId": "1986", "endLine": 24, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2013", "line": 24, "column": 36, "nodeType": null, "messageId": "1986", "endLine": 24, "endColumn": 41}, {"ruleId": "1984", "severity": 2, "message": "2014", "line": 24, "column": 43, "nodeType": null, "messageId": "1986", "endLine": 24, "endColumn": 49}, {"ruleId": "1984", "severity": 2, "message": "1997", "line": 24, "column": 51, "nodeType": null, "messageId": "1986", "endLine": 24, "endColumn": 58}, {"ruleId": "1984", "severity": 2, "message": "2015", "line": 25, "column": 11, "nodeType": null, "messageId": "1986", "endLine": 25, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 29, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 29, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 107, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 107, "endColumn": 24, "suggestions": "2016"}, {"ruleId": "1984", "severity": 2, "message": "2017", "line": 12, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 12, "endColumn": 17}, {"ruleId": "1984", "severity": 2, "message": "2018", "line": 12, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 12, "endColumn": 35}, {"ruleId": "1984", "severity": 2, "message": "2019", "line": 12, "column": 47, "nodeType": null, "messageId": "1986", "endLine": 12, "endColumn": 55}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 35, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 35, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2021", "line": 83, "column": 6, "nodeType": "2022", "endLine": 83, "endColumn": 55, "suggestions": "2023"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 182, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 182, "endColumn": 24, "suggestions": "2024"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 347, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 347, "endColumn": 24, "suggestions": "2025"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 1047, "column": 61, "nodeType": "1981", "messageId": "1982", "suggestions": "2026"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 1047, "column": 84, "nodeType": "1981", "messageId": "1982", "suggestions": "2027"}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 37, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 37, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2028", "line": 59, "column": 6, "nodeType": "2022", "endLine": 59, "endColumn": 14, "suggestions": "2029"}, {"ruleId": "2030", "severity": 2, "message": "2031", "line": 301, "column": 14, "nodeType": "2032", "messageId": "2033", "endLine": 301, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "2034", "line": 13, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 13, "endColumn": 10}, {"ruleId": "1984", "severity": 2, "message": "2035", "line": 16, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 16, "endColumn": 14}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 62, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 62, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2036", "line": 128, "column": 6, "nodeType": "2022", "endLine": 128, "endColumn": 14, "suggestions": "2037"}, {"ruleId": "2020", "severity": 1, "message": "2036", "line": 187, "column": 6, "nodeType": "2022", "endLine": 187, "endColumn": 64, "suggestions": "2038"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 209, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 209, "endColumn": 24, "suggestions": "2039"}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 87, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 87, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2040", "line": 129, "column": 6, "nodeType": "2022", "endLine": 129, "endColumn": 14, "suggestions": "2041"}, {"ruleId": "2020", "severity": 1, "message": "2040", "line": 135, "column": 6, "nodeType": "2022", "endLine": 135, "endColumn": 34, "suggestions": "2042"}, {"ruleId": "1984", "severity": 2, "message": "2043", "line": 15, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 15, "endColumn": 16}, {"ruleId": "1984", "severity": 2, "message": "2044", "line": 16, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 16, "endColumn": 16}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 11, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 11, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 12, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 12, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 10, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 35}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 49, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 49, "endColumn": 21, "suggestions": "2046"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 75, "column": 40, "nodeType": "1991", "messageId": "1992", "endLine": 75, "endColumn": 43, "suggestions": "2047"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 18, "column": 40, "nodeType": "1991", "messageId": "1992", "endLine": 18, "endColumn": 43, "suggestions": "2048"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 8, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 35}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 122, "column": 24, "nodeType": "1991", "messageId": "1992", "endLine": 122, "endColumn": 27, "suggestions": "2049"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 55, "column": 24, "nodeType": "1991", "messageId": "1992", "endLine": 55, "endColumn": 27, "suggestions": "2050"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 56, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 56, "endColumn": 21, "suggestions": "2051"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 7, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 7, "endColumn": 35}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 58, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 58, "endColumn": 21, "suggestions": "2052"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 6, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 6, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 34}, {"ruleId": "2053", "severity": 2, "message": "2054", "line": 51, "column": 16, "nodeType": "2055", "messageId": "2056", "endLine": 51, "endColumn": 29}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 50, "column": 24, "nodeType": "1991", "messageId": "1992", "endLine": 50, "endColumn": 27, "suggestions": "2057"}, {"ruleId": "1984", "severity": 2, "message": "2058", "line": 71, "column": 13, "nodeType": null, "messageId": "1986", "endLine": 71, "endColumn": 20}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 141, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 141, "endColumn": 34}, {"ruleId": "2059", "severity": 2, "message": "2060", "line": 68, "column": 13, "nodeType": "2061", "messageId": "2062", "endLine": 68, "endColumn": 22, "fix": "2063"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 231, "column": 38, "nodeType": "1991", "messageId": "1992", "endLine": 231, "endColumn": 41, "suggestions": "2064"}, {"ruleId": "1984", "severity": 2, "message": "2065", "line": 5, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 5, "endColumn": 30}, {"ruleId": "1984", "severity": 2, "message": "2066", "line": 174, "column": 16, "nodeType": null, "messageId": "1986", "endLine": 174, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "2066", "line": 182, "column": 16, "nodeType": null, "messageId": "1986", "endLine": 182, "endColumn": 21}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 189, "column": 47, "nodeType": "1991", "messageId": "1992", "endLine": 189, "endColumn": 50, "suggestions": "2067"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 190, "column": 41, "nodeType": "1991", "messageId": "1992", "endLine": 190, "endColumn": 44, "suggestions": "2068"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 196, "column": 61, "nodeType": "1991", "messageId": "1992", "endLine": 196, "endColumn": 64, "suggestions": "2069"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 197, "column": 55, "nodeType": "1991", "messageId": "1992", "endLine": 197, "endColumn": 58, "suggestions": "2070"}, {"ruleId": "1984", "severity": 2, "message": "2071", "line": 9, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 28}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 92, "column": 20, "nodeType": "1991", "messageId": "1992", "endLine": 92, "endColumn": 23, "suggestions": "2072"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 70, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 70, "endColumn": 21, "suggestions": "2073"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 5, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 5, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2074", "line": 10, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 30}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 30, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 30, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 44, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 44, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 10, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 82, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 82, "endColumn": 35}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 9, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 10, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 82, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 82, "endColumn": 35}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 80, "column": 55, "nodeType": "1991", "messageId": "1992", "endLine": 80, "endColumn": 58, "suggestions": "2075"}, {"ruleId": "2059", "severity": 2, "message": "2076", "line": 153, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 153, "endColumn": 25, "fix": "2077"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 153, "column": 22, "nodeType": "1991", "messageId": "1992", "endLine": 153, "endColumn": 25, "suggestions": "2078"}, {"ruleId": "2059", "severity": 2, "message": "2079", "line": 167, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 167, "endColumn": 27, "fix": "2080"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 167, "column": 24, "nodeType": "1991", "messageId": "1992", "endLine": 167, "endColumn": 27, "suggestions": "2081"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 10, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 82, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 82, "endColumn": 35}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 10, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 82, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 82, "endColumn": 35}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 143, "column": 41, "nodeType": "1991", "messageId": "1992", "endLine": 143, "endColumn": 44, "suggestions": "2082"}, {"ruleId": "1984", "severity": 2, "message": "2083", "line": 1, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 1, "endColumn": 16}, {"ruleId": "2059", "severity": 2, "message": "2076", "line": 53, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 53, "endColumn": 25, "fix": "2084"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 53, "column": 22, "nodeType": "1991", "messageId": "1992", "endLine": 53, "endColumn": 25, "suggestions": "2085"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 140, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 140, "endColumn": 35}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 6, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 34}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 47, "column": 20, "nodeType": "1991", "messageId": "1992", "endLine": 47, "endColumn": 23, "suggestions": "2086"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 126, "column": 24, "nodeType": "1991", "messageId": "1992", "endLine": 126, "endColumn": 27, "suggestions": "2087"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 77, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 77, "endColumn": 34}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 76, "column": 20, "nodeType": "1991", "messageId": "1992", "endLine": 76, "endColumn": 23, "suggestions": "2088"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 10, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 10, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2083", "line": 1, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 1, "endColumn": 16}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 123, "column": 35, "nodeType": "1991", "messageId": "1992", "endLine": 123, "endColumn": 38, "suggestions": "2089"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 127, "column": 41, "nodeType": "1991", "messageId": "1992", "endLine": 127, "endColumn": 44, "suggestions": "2090"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 141, "column": 44, "nodeType": "1991", "messageId": "1992", "endLine": 141, "endColumn": 47, "suggestions": "2091"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 143, "column": 41, "nodeType": "1991", "messageId": "1992", "endLine": 143, "endColumn": 44, "suggestions": "2092"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 6, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 34}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 8, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 34}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 83, "column": 42, "nodeType": "1991", "messageId": "1992", "endLine": 83, "endColumn": 45, "suggestions": "2093"}, {"ruleId": "1984", "severity": 2, "message": "2094", "line": 120, "column": 23, "nodeType": null, "messageId": "1986", "endLine": 120, "endColumn": 24}, {"ruleId": "2059", "severity": 2, "message": "2076", "line": 146, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 146, "endColumn": 25, "fix": "2095"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 146, "column": 22, "nodeType": "1991", "messageId": "1992", "endLine": 146, "endColumn": 25, "suggestions": "2096"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 202, "column": 23, "nodeType": "1991", "messageId": "1992", "endLine": 202, "endColumn": 26, "suggestions": "2097"}, {"ruleId": "1984", "severity": 2, "message": "2045", "line": 279, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 279, "endColumn": 10}, {"ruleId": "1984", "severity": 2, "message": "2098", "line": 280, "column": 5, "nodeType": null, "messageId": "1986", "endLine": 280, "endColumn": 11}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 53, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 53, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2099", "line": 82, "column": 6, "nodeType": "2022", "endLine": 82, "endColumn": 43, "suggestions": "2100"}, {"ruleId": "1984", "severity": 2, "message": "1988", "line": 159, "column": 13, "nodeType": null, "messageId": "1986", "endLine": 159, "endColumn": 17}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 382, "column": 23, "nodeType": "1981", "messageId": "1982", "suggestions": "2101"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 382, "column": 41, "nodeType": "1981", "messageId": "1982", "suggestions": "2102"}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 6, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "2103", "line": 10, "column": 23, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 37}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 26, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 26, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 31, "column": 62, "nodeType": "1991", "messageId": "1992", "endLine": 31, "endColumn": 65, "suggestions": "2104"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 33, "column": 58, "nodeType": "1991", "messageId": "1992", "endLine": 33, "endColumn": 61, "suggestions": "2105"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 43, "column": 68, "nodeType": "1991", "messageId": "1992", "endLine": 43, "endColumn": 71, "suggestions": "2106"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 76, "column": 50, "nodeType": "1991", "messageId": "1992", "endLine": 76, "endColumn": 53, "suggestions": "2107"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 143, "column": 45, "nodeType": "1991", "messageId": "1992", "endLine": 143, "endColumn": 48, "suggestions": "2108"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 205, "column": 24, "nodeType": "1991", "messageId": "1992", "endLine": 205, "endColumn": 27, "suggestions": "2109"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 260, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 260, "endColumn": 24, "suggestions": "2110"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 297, "column": 21, "nodeType": "1981", "messageId": "1982", "suggestions": "2111"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 316, "column": 19, "nodeType": "1981", "messageId": "1982", "suggestions": "2112"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 422, "column": 43, "nodeType": "1981", "messageId": "1982", "suggestions": "2113"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 95, "column": 17, "nodeType": "1981", "messageId": "1982", "suggestions": "2114"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 99, "column": 17, "nodeType": "1981", "messageId": "1982", "suggestions": "2115"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 99, "column": 90, "nodeType": "1981", "messageId": "1982", "suggestions": "2116"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 99, "column": 159, "nodeType": "1981", "messageId": "1982", "suggestions": "2117"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 151, "column": 48, "nodeType": "1981", "messageId": "1982", "suggestions": "2118"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 220, "column": 19, "nodeType": "1981", "messageId": "1982", "suggestions": "2119"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 225, "column": 27, "nodeType": "1981", "messageId": "1982", "suggestions": "2120"}, {"ruleId": "1984", "severity": 2, "message": "2121", "line": 6, "column": 57, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 67}, {"ruleId": "1984", "severity": 2, "message": "2122", "line": 6, "column": 69, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 81}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 37, "column": 23, "nodeType": "1991", "messageId": "1992", "endLine": 37, "endColumn": 26, "suggestions": "2123"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 43, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 43, "endColumn": 59, "suggestions": "2124"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 44, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 44, "endColumn": 59, "suggestions": "2125"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 137, "column": 36, "nodeType": "1991", "messageId": "1992", "endLine": 137, "endColumn": 39, "suggestions": "2126"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 144, "column": 36, "nodeType": "1991", "messageId": "1992", "endLine": 144, "endColumn": 39, "suggestions": "2127"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 152, "column": 27, "nodeType": "1991", "messageId": "1992", "endLine": 152, "endColumn": 30, "suggestions": "2128"}, {"ruleId": "1984", "severity": 2, "message": "2129", "line": 6, "column": 57, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 69}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 176, "column": 134, "nodeType": "1981", "messageId": "1982", "suggestions": "2130"}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 16, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 16, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 20, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 20, "endColumn": 42, "suggestions": "2131"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 26, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 26, "endColumn": 42, "suggestions": "2132"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 143, "column": 40, "nodeType": "1981", "messageId": "1982", "suggestions": "2133"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 144, "column": 41, "nodeType": "1981", "messageId": "1982", "suggestions": "2134"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 145, "column": 55, "nodeType": "1981", "messageId": "1982", "suggestions": "2135"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 146, "column": 45, "nodeType": "1981", "messageId": "1982", "suggestions": "2136"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 153, "column": 50, "nodeType": "1981", "messageId": "1982", "suggestions": "2137"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 155, "column": 41, "nodeType": "1981", "messageId": "1982", "suggestions": "2138"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 156, "column": 43, "nodeType": "1981", "messageId": "1982", "suggestions": "2139"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 57, "column": 45, "nodeType": "1991", "messageId": "1992", "endLine": 57, "endColumn": 48, "suggestions": "2140"}, {"ruleId": "1984", "severity": 2, "message": "2035", "line": 6, "column": 57, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 68}, {"ruleId": "1984", "severity": 2, "message": "2141", "line": 18, "column": 21, "nodeType": null, "messageId": "1986", "endLine": 18, "endColumn": 33}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 106, "column": 91, "nodeType": "1981", "messageId": "1982", "suggestions": "2142"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 126, "column": 59, "nodeType": "1981", "messageId": "1982", "suggestions": "2143"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 122, "column": 172, "nodeType": "1981", "messageId": "1982", "suggestions": "2144"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 140, "column": 40, "nodeType": "1981", "messageId": "1982", "suggestions": "2145"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 141, "column": 33, "nodeType": "1981", "messageId": "1982", "suggestions": "2146"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 141, "column": 46, "nodeType": "1981", "messageId": "1982", "suggestions": "2147"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 142, "column": 29, "nodeType": "1981", "messageId": "1982", "suggestions": "2148"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 142, "column": 43, "nodeType": "1981", "messageId": "1982", "suggestions": "2149"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 142, "column": 68, "nodeType": "1981", "messageId": "1982", "suggestions": "2150"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 163, "column": 117, "nodeType": "1981", "messageId": "1982", "suggestions": "2151"}, {"ruleId": "1984", "severity": 2, "message": "2152", "line": 6, "column": 57, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 63}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 42, "column": 50, "nodeType": "1991", "messageId": "1992", "endLine": 42, "endColumn": 53, "suggestions": "2153"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 107, "column": 41, "nodeType": "1981", "messageId": "1982", "suggestions": "2154"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 154, "column": 89, "nodeType": "1981", "messageId": "1982", "suggestions": "2155"}, {"ruleId": "1984", "severity": 2, "message": "2156", "line": 21, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 21, "endColumn": 11}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 67, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 67, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2157", "line": 100, "column": 6, "nodeType": "2022", "endLine": 100, "endColumn": 38, "suggestions": "2158"}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 64, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 64, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "2121", "line": 6, "column": 57, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 67}, {"ruleId": "1984", "severity": 2, "message": "2159", "line": 8, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 27}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 162, "column": 17, "nodeType": "1981", "messageId": "1982", "suggestions": "2160"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 162, "column": 170, "nodeType": "1981", "messageId": "1982", "suggestions": "2161"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 370, "column": 16, "nodeType": "1981", "messageId": "1982", "suggestions": "2162"}, {"ruleId": "1984", "severity": 2, "message": "2163", "line": 11, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 11, "endColumn": 9}, {"ruleId": "1984", "severity": 2, "message": "2164", "line": 12, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 12, "endColumn": 21}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 167, "column": 56, "nodeType": "1981", "messageId": "1982", "suggestions": "2165"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 420, "column": 23, "nodeType": "1981", "messageId": "1982", "suggestions": "2166"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 420, "column": 63, "nodeType": "1981", "messageId": "1982", "suggestions": "2167"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 561, "column": 23, "nodeType": "1981", "messageId": "1982", "suggestions": "2168"}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 6, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 30, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 30, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 33, "column": 58, "nodeType": "1991", "messageId": "1992", "endLine": 33, "endColumn": 61, "suggestions": "2169"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 52, "column": 19, "nodeType": "1991", "messageId": "1992", "endLine": 52, "endColumn": 22, "suggestions": "2170"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 120, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 120, "endColumn": 32, "suggestions": "2171"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 273, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 273, "endColumn": 59, "suggestions": "2172"}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 6, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 30, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 30, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 33, "column": 58, "nodeType": "1991", "messageId": "1992", "endLine": 33, "endColumn": 61, "suggestions": "2173"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 52, "column": 19, "nodeType": "1991", "messageId": "1992", "endLine": 52, "endColumn": 22, "suggestions": "2174"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 120, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 120, "endColumn": 32, "suggestions": "2175"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 273, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 273, "endColumn": 59, "suggestions": "2176"}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 6, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 36, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 36, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 39, "column": 58, "nodeType": "1991", "messageId": "1992", "endLine": 39, "endColumn": 61, "suggestions": "2177"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 58, "column": 19, "nodeType": "1991", "messageId": "1992", "endLine": 58, "endColumn": 22, "suggestions": "2178"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 126, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 126, "endColumn": 32, "suggestions": "2179"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 283, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 283, "endColumn": 59, "suggestions": "2180"}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 6, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 30, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 30, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 33, "column": 58, "nodeType": "1991", "messageId": "1992", "endLine": 33, "endColumn": 61, "suggestions": "2181"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 52, "column": 19, "nodeType": "1991", "messageId": "1992", "endLine": 52, "endColumn": 22, "suggestions": "2182"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 120, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 120, "endColumn": 32, "suggestions": "2183"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 273, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 273, "endColumn": 59, "suggestions": "2184"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 389, "column": 91, "nodeType": "1981", "messageId": "1982", "suggestions": "2185"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 396, "column": 121, "nodeType": "1981", "messageId": "1982", "suggestions": "2186"}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 9, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 13}, {"ruleId": "2020", "severity": 1, "message": "2187", "line": 74, "column": 6, "nodeType": "2022", "endLine": 74, "endColumn": 14, "suggestions": "2188"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 115, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 115, "endColumn": 24, "suggestions": "2189"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 200, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 200, "endColumn": 24, "suggestions": "2190"}, {"ruleId": "2191", "severity": 1, "message": "2192", "line": 269, "column": 25, "nodeType": "2193", "endLine": 273, "endColumn": 27}, {"ruleId": "2191", "severity": 1, "message": "2192", "line": 290, "column": 25, "nodeType": "2193", "endLine": 294, "endColumn": 27}, {"ruleId": "1984", "severity": 2, "message": "2014", "line": 7, "column": 29, "nodeType": null, "messageId": "1986", "endLine": 7, "endColumn": 35}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 31, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 31, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 40, "column": 75, "nodeType": "1991", "messageId": "1992", "endLine": 40, "endColumn": 78, "suggestions": "2194"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 232, "column": 73, "nodeType": "1991", "messageId": "1992", "endLine": 232, "endColumn": 76, "suggestions": "2195"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 364, "column": 27, "nodeType": "1981", "messageId": "1982", "suggestions": "2196"}, {"ruleId": "1984", "severity": 2, "message": "2197", "line": 11, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 11, "endColumn": 18}, {"ruleId": "1984", "severity": 2, "message": "2198", "line": 16, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 16, "endColumn": 17}, {"ruleId": "1984", "severity": 2, "message": "2199", "line": 17, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 17, "endColumn": 15}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 66, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 66, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2200", "line": 93, "column": 6, "nodeType": "2022", "endLine": 93, "endColumn": 51, "suggestions": "2201"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 226, "column": 24, "nodeType": "1981", "messageId": "1982", "suggestions": "2202"}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 72, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 72, "endColumn": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 78, "column": 62, "nodeType": "1991", "messageId": "1992", "endLine": 78, "endColumn": 65, "suggestions": "2203"}, {"ruleId": "1984", "severity": 2, "message": "2204", "line": 3, "column": 44, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 51}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 50, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 50, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "2205", "line": 149, "column": 12, "nodeType": null, "messageId": "1986", "endLine": 149, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "2206", "line": 208, "column": 9, "nodeType": null, "messageId": "1986", "endLine": 208, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 58, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 58, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2207", "line": 78, "column": 6, "nodeType": "2022", "endLine": 78, "endColumn": 22, "suggestions": "2208"}, {"ruleId": "1984", "severity": 2, "message": "2209", "line": 20, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 20, "endColumn": 11}, {"ruleId": "1984", "severity": 2, "message": "2018", "line": 21, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 21, "endColumn": 10}, {"ruleId": "1984", "severity": 2, "message": "2210", "line": 22, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 22, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "2211", "line": 23, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 23, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 50, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 50, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "2212", "line": 61, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 61, "endColumn": 20}, {"ruleId": "1984", "severity": 2, "message": "2213", "line": 61, "column": 22, "nodeType": null, "messageId": "1986", "endLine": 61, "endColumn": 35}, {"ruleId": "1984", "severity": 2, "message": "2214", "line": 12, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 12, "endColumn": 10}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 61, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 61, "endColumn": 24}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 306, "column": 29, "nodeType": "1981", "messageId": "1982", "suggestions": "2215"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 70, "column": 47, "nodeType": "1991", "messageId": "1992", "endLine": 70, "endColumn": 50, "suggestions": "2216"}, {"ruleId": "2059", "severity": 2, "message": "2217", "line": 174, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 174, "endColumn": 42, "fix": "2218"}, {"ruleId": "2020", "severity": 1, "message": "2219", "line": 214, "column": 6, "nodeType": "2022", "endLine": 214, "endColumn": 29, "suggestions": "2220"}, {"ruleId": "1984", "severity": 2, "message": "2066", "line": 245, "column": 14, "nodeType": null, "messageId": "1986", "endLine": 245, "endColumn": 19}, {"ruleId": "2059", "severity": 2, "message": "2221", "line": 258, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 258, "endColumn": 42, "fix": "2222"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 346, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 346, "endColumn": 24, "suggestions": "2223"}, {"ruleId": "1984", "severity": 2, "message": "2224", "line": 10, "column": 9, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 15}, {"ruleId": "1984", "severity": 2, "message": "2225", "line": 13, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 13, "endColumn": 15}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 206, "column": 17, "nodeType": "1981", "messageId": "1982", "suggestions": "2226"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 206, "column": 130, "nodeType": "1981", "messageId": "1982", "suggestions": "2227"}, {"ruleId": "1979", "severity": 2, "message": "1994", "line": 206, "column": 156, "nodeType": "1981", "messageId": "1982", "suggestions": "2228"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 258, "column": 69, "nodeType": "1981", "messageId": "1982", "suggestions": "2229"}, {"ruleId": "1984", "severity": 2, "message": "2083", "line": 3, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 16}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 6, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 6, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "2103", "line": 8, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 24}, {"ruleId": "1984", "severity": 2, "message": "2230", "line": 9, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 26}, {"ruleId": "1984", "severity": 2, "message": "2231", "line": 30, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 30, "endColumn": 25}, {"ruleId": "1984", "severity": 2, "message": "2232", "line": 62, "column": 9, "nodeType": null, "messageId": "1986", "endLine": 62, "endColumn": 23}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 105, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 105, "endColumn": 24, "suggestions": "2233"}, {"ruleId": "1984", "severity": 2, "message": "1987", "line": 26, "column": 17, "nodeType": null, "messageId": "1986", "endLine": 26, "endColumn": 24}, {"ruleId": "2020", "severity": 1, "message": "2234", "line": 44, "column": 6, "nodeType": "2022", "endLine": 44, "endColumn": 17, "suggestions": "2235"}, {"ruleId": "2030", "severity": 2, "message": "2031", "line": 299, "column": 24, "nodeType": "2032", "messageId": "2033", "endLine": 299, "endColumn": 31}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 117, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 117, "endColumn": 24, "suggestions": "2236"}, {"ruleId": "1984", "severity": 2, "message": "2237", "line": 2, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 2, "endColumn": 18}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 68, "column": 43, "nodeType": "1991", "messageId": "1992", "endLine": 68, "endColumn": 46, "suggestions": "2238"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 101, "column": 36, "nodeType": "1991", "messageId": "1992", "endLine": 101, "endColumn": 39, "suggestions": "2239"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 117, "column": 49, "nodeType": "1991", "messageId": "1992", "endLine": 117, "endColumn": 52, "suggestions": "2240"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 88, "column": 36, "nodeType": "1991", "messageId": "1992", "endLine": 88, "endColumn": 39, "suggestions": "2241"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 121, "column": 37, "nodeType": "1991", "messageId": "1992", "endLine": 121, "endColumn": 40, "suggestions": "2242"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 158, "column": 47, "nodeType": "1991", "messageId": "1992", "endLine": 158, "endColumn": 50, "suggestions": "2243"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 81, "column": 36, "nodeType": "1991", "messageId": "1992", "endLine": 81, "endColumn": 39, "suggestions": "2244"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 114, "column": 37, "nodeType": "1991", "messageId": "1992", "endLine": 114, "endColumn": 40, "suggestions": "2245"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 127, "column": 49, "nodeType": "1991", "messageId": "1992", "endLine": 127, "endColumn": 52, "suggestions": "2246"}, {"ruleId": "1984", "severity": 2, "message": "2214", "line": 2, "column": 41, "nodeType": null, "messageId": "1986", "endLine": 2, "endColumn": 48}, {"ruleId": "1984", "severity": 2, "message": "2199", "line": 12, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 12, "endColumn": 15}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 37, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 37, "endColumn": 42, "suggestions": "2247"}, {"ruleId": "2020", "severity": 1, "message": "2248", "line": 61, "column": 6, "nodeType": "2022", "endLine": 61, "endColumn": 8, "suggestions": "2249"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 131, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 131, "endColumn": 24, "suggestions": "2250"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 161, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 161, "endColumn": 24, "suggestions": "2251"}, {"ruleId": "1984", "severity": 2, "message": "2252", "line": 11, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 11, "endColumn": 12}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 16, "column": 37, "nodeType": "1991", "messageId": "1992", "endLine": 16, "endColumn": 40, "suggestions": "2253"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 35, "column": 55, "nodeType": "1991", "messageId": "1992", "endLine": 35, "endColumn": 58, "suggestions": "2254"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 44, "column": 32, "nodeType": "1991", "messageId": "1992", "endLine": 44, "endColumn": 35, "suggestions": "2255"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 16, "column": 27, "nodeType": "1991", "messageId": "1992", "endLine": 16, "endColumn": 30, "suggestions": "2256"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 121, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 121, "endColumn": 24, "suggestions": "2257"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 513, "column": 40, "nodeType": "1981", "messageId": "1982", "suggestions": "2258"}, {"ruleId": "1984", "severity": 2, "message": "2259", "line": 10, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 8}, {"ruleId": "2020", "severity": 1, "message": "2260", "line": 87, "column": 6, "nodeType": "2022", "endLine": 87, "endColumn": 17, "suggestions": "2261"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 192, "column": 70, "nodeType": "1991", "messageId": "1992", "endLine": 192, "endColumn": 73, "suggestions": "2262"}, {"ruleId": "1984", "severity": 2, "message": "2066", "line": 206, "column": 14, "nodeType": null, "messageId": "1986", "endLine": 206, "endColumn": 19}, {"ruleId": "1984", "severity": 2, "message": "2263", "line": 3, "column": 28, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 37}, {"ruleId": "1984", "severity": 2, "message": "2018", "line": 10, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 10}, {"ruleId": "1984", "severity": 2, "message": "2264", "line": 11, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 11, "endColumn": 9}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 39, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 39, "endColumn": 42, "suggestions": "2265"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 130, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 130, "endColumn": 24, "suggestions": "2266"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 337, "column": 26, "nodeType": "1981", "messageId": "1982", "suggestions": "2267"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 532, "column": 40, "nodeType": "1981", "messageId": "1982", "suggestions": "2268"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 20, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 20, "endColumn": 32, "suggestions": "2269"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 28, "column": 40, "nodeType": "1991", "messageId": "1992", "endLine": 28, "endColumn": 43, "suggestions": "2270"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 232, "column": 52, "nodeType": "1991", "messageId": "1992", "endLine": 232, "endColumn": 55, "suggestions": "2271"}, {"ruleId": "1984", "severity": 2, "message": "2035", "line": 9, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 14}, {"ruleId": "2020", "severity": 1, "message": "2272", "line": 54, "column": 6, "nodeType": "2022", "endLine": 54, "endColumn": 26, "suggestions": "2273"}, {"ruleId": "1984", "severity": 2, "message": "2274", "line": 45, "column": 9, "nodeType": null, "messageId": "1986", "endLine": 45, "endColumn": 17}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 46, "column": 46, "nodeType": "1991", "messageId": "1992", "endLine": 46, "endColumn": 49, "suggestions": "2275"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 69, "column": 46, "nodeType": "1991", "messageId": "1992", "endLine": 69, "endColumn": 49, "suggestions": "2276"}, {"ruleId": "2059", "severity": 2, "message": "2277", "line": 165, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 165, "endColumn": 16, "fix": "2278"}, {"ruleId": "1984", "severity": 2, "message": "2237", "line": 2, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 2, "endColumn": 18}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 71, "column": 19, "nodeType": "1991", "messageId": "1992", "endLine": 71, "endColumn": 22, "suggestions": "2279"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 112, "column": 19, "nodeType": "1991", "messageId": "1992", "endLine": 112, "endColumn": 22, "suggestions": "2280"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 152, "column": 19, "nodeType": "1991", "messageId": "1992", "endLine": 152, "endColumn": 22, "suggestions": "2281"}, {"ruleId": "2020", "severity": 1, "message": "2282", "line": 162, "column": 6, "nodeType": "2022", "endLine": 162, "endColumn": 19, "suggestions": "2283"}, {"ruleId": "1984", "severity": 2, "message": "1997", "line": 5, "column": 31, "nodeType": null, "messageId": "1986", "endLine": 5, "endColumn": 38}, {"ruleId": "1984", "severity": 2, "message": "2008", "line": 5, "column": 40, "nodeType": null, "messageId": "1986", "endLine": 5, "endColumn": 50}, {"ruleId": "1984", "severity": 2, "message": "2017", "line": 7, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 7, "endColumn": 10}, {"ruleId": "2020", "severity": 1, "message": "2284", "line": 91, "column": 6, "nodeType": "2022", "endLine": 91, "endColumn": 64, "suggestions": "2285"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 247, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 247, "endColumn": 24, "suggestions": "2286"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 283, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 283, "endColumn": 24, "suggestions": "2287"}, {"ruleId": "2191", "severity": 1, "message": "2192", "line": 463, "column": 27, "nodeType": "2193", "endLine": 467, "endColumn": 29}, {"ruleId": "2191", "severity": 1, "message": "2192", "line": 610, "column": 21, "nodeType": "2193", "endLine": 614, "endColumn": 23}, {"ruleId": "1984", "severity": 2, "message": "2210", "line": 4, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 20}, {"ruleId": "2020", "severity": 1, "message": "2288", "line": 31, "column": 6, "nodeType": "2022", "endLine": 31, "endColumn": 8, "suggestions": "2289"}, {"ruleId": "1984", "severity": 2, "message": "2066", "line": 66, "column": 14, "nodeType": null, "messageId": "1986", "endLine": 66, "endColumn": 19}, {"ruleId": "1984", "severity": 2, "message": "2290", "line": 56, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 56, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "2291", "line": 56, "column": 23, "nodeType": null, "messageId": "1986", "endLine": 56, "endColumn": 37}, {"ruleId": "1984", "severity": 2, "message": "2292", "line": 4, "column": 58, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 64}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 9, "column": 14, "nodeType": "1991", "messageId": "1992", "endLine": 9, "endColumn": 17, "suggestions": "2293"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 14, "column": 14, "nodeType": "1991", "messageId": "1992", "endLine": 14, "endColumn": 17, "suggestions": "2294"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 18, "column": 14, "nodeType": "1991", "messageId": "1992", "endLine": 18, "endColumn": 17, "suggestions": "2295"}, {"ruleId": "1984", "severity": 2, "message": "2296", "line": 3, "column": 20, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 28}, {"ruleId": "1984", "severity": 2, "message": "2297", "line": 8, "column": 19, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 27}, {"ruleId": "1984", "severity": 2, "message": "2002", "line": 4, "column": 8, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "2198", "line": 4, "column": 18, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 32}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 22, "column": 27, "nodeType": "1991", "messageId": "1992", "endLine": 22, "endColumn": 30, "suggestions": "2298"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 105, "column": 44, "nodeType": "1991", "messageId": "1992", "endLine": 105, "endColumn": 47, "suggestions": "2299"}, {"ruleId": "1984", "severity": 2, "message": "2263", "line": 3, "column": 20, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 29}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 191, "column": 38, "nodeType": "1991", "messageId": "1992", "endLine": 191, "endColumn": 41, "suggestions": "2300"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 215, "column": 40, "nodeType": "1991", "messageId": "1992", "endLine": 215, "endColumn": 43, "suggestions": "2301"}, {"ruleId": "1984", "severity": 2, "message": "2302", "line": 23, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 23, "endColumn": 15}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 41, "column": 64, "nodeType": "1991", "messageId": "1992", "endLine": 41, "endColumn": 67, "suggestions": "2303"}, {"ruleId": "1984", "severity": 2, "message": "2010", "line": 18, "column": 21, "nodeType": null, "messageId": "1986", "endLine": 18, "endColumn": 29}, {"ruleId": "1984", "severity": 2, "message": "2011", "line": 18, "column": 31, "nodeType": null, "messageId": "1986", "endLine": 18, "endColumn": 39}, {"ruleId": "1984", "severity": 2, "message": "2035", "line": 18, "column": 41, "nodeType": null, "messageId": "1986", "endLine": 18, "endColumn": 52}, {"ruleId": "2020", "severity": 1, "message": "2304", "line": 138, "column": 6, "nodeType": "2022", "endLine": 138, "endColumn": 72, "suggestions": "2305"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 146, "column": 38, "nodeType": "1991", "messageId": "1992", "endLine": 146, "endColumn": 41, "suggestions": "2306"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 186, "column": 15, "nodeType": "1991", "messageId": "1992", "endLine": 186, "endColumn": 18, "suggestions": "2307"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 78, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 78, "endColumn": 32, "suggestions": "2308"}, {"ruleId": "2020", "severity": 1, "message": "2304", "line": 167, "column": 6, "nodeType": "2022", "endLine": 167, "endColumn": 63, "suggestions": "2309"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 175, "column": 50, "nodeType": "1991", "messageId": "1992", "endLine": 175, "endColumn": 53, "suggestions": "2310"}, {"ruleId": "2020", "severity": 1, "message": "2311", "line": 216, "column": 6, "nodeType": "2022", "endLine": 216, "endColumn": 35, "suggestions": "2312"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 220, "column": 15, "nodeType": "1991", "messageId": "1992", "endLine": 220, "endColumn": 18, "suggestions": "2313"}, {"ruleId": "2020", "severity": 1, "message": "2314", "line": 267, "column": 6, "nodeType": "2022", "endLine": 267, "endColumn": 21, "suggestions": "2315"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 271, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 271, "endColumn": 15, "suggestions": "2316"}, {"ruleId": "2020", "severity": 1, "message": "2317", "line": 338, "column": 6, "nodeType": "2022", "endLine": 338, "endColumn": 64, "suggestions": "2318"}, {"ruleId": "2020", "severity": 1, "message": "2319", "line": 477, "column": 6, "nodeType": "2022", "endLine": 477, "endColumn": 78, "suggestions": "2320"}, {"ruleId": "1984", "severity": 2, "message": "2204", "line": 3, "column": 44, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 51}, {"ruleId": "1984", "severity": 2, "message": "2010", "line": 19, "column": 21, "nodeType": null, "messageId": "1986", "endLine": 19, "endColumn": 29}, {"ruleId": "1984", "severity": 2, "message": "2011", "line": 19, "column": 31, "nodeType": null, "messageId": "1986", "endLine": 19, "endColumn": 39}, {"ruleId": "1984", "severity": 2, "message": "2035", "line": 19, "column": 41, "nodeType": null, "messageId": "1986", "endLine": 19, "endColumn": 52}, {"ruleId": "2020", "severity": 1, "message": "2304", "line": 165, "column": 6, "nodeType": "2022", "endLine": 165, "endColumn": 91, "suggestions": "2321"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 173, "column": 50, "nodeType": "1991", "messageId": "1992", "endLine": 173, "endColumn": 53, "suggestions": "2322"}, {"ruleId": "2020", "severity": 1, "message": "2323", "line": 210, "column": 6, "nodeType": "2022", "endLine": 210, "endColumn": 8, "suggestions": "2324"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 214, "column": 15, "nodeType": "1991", "messageId": "1992", "endLine": 214, "endColumn": 18, "suggestions": "2325"}, {"ruleId": "2020", "severity": 1, "message": "2314", "line": 250, "column": 6, "nodeType": "2022", "endLine": 250, "endColumn": 14, "suggestions": "2326"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 254, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 254, "endColumn": 15, "suggestions": "2327"}, {"ruleId": "2020", "severity": 1, "message": "2328", "line": 312, "column": 6, "nodeType": "2022", "endLine": 312, "endColumn": 46, "suggestions": "2329"}, {"ruleId": "1984", "severity": 2, "message": "2330", "line": 15, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 15, "endColumn": 15}, {"ruleId": "1984", "severity": 2, "message": "2331", "line": 16, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 16, "endColumn": 14}, {"ruleId": "1984", "severity": 2, "message": "2332", "line": 249, "column": 9, "nodeType": null, "messageId": "1986", "endLine": 249, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "2333", "line": 261, "column": 9, "nodeType": null, "messageId": "1986", "endLine": 261, "endColumn": 25}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 68, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 68, "endColumn": 42, "suggestions": "2334"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 69, "column": 26, "nodeType": "1991", "messageId": "1992", "endLine": 69, "endColumn": 29, "suggestions": "2335"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 72, "column": 32, "nodeType": "1991", "messageId": "1992", "endLine": 72, "endColumn": 35, "suggestions": "2336"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 73, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 73, "endColumn": 21, "suggestions": "2337"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 103, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 103, "endColumn": 42, "suggestions": "2338"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 116, "column": 32, "nodeType": "1991", "messageId": "1992", "endLine": 116, "endColumn": 35, "suggestions": "2339"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 137, "column": 30, "nodeType": "1991", "messageId": "1992", "endLine": 137, "endColumn": 33, "suggestions": "2340"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 153, "column": 36, "nodeType": "1991", "messageId": "1992", "endLine": 153, "endColumn": 39, "suggestions": "2341"}, {"ruleId": "1984", "severity": 2, "message": "2010", "line": 8, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 11}, {"ruleId": "1984", "severity": 2, "message": "2342", "line": 13, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 13, "endColumn": 14}, {"ruleId": "1984", "severity": 2, "message": "2011", "line": 18, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 18, "endColumn": 11}, {"ruleId": "2030", "severity": 2, "message": "2343", "line": 370, "column": 22, "nodeType": "2032", "messageId": "2033", "endLine": 370, "endColumn": 29}, {"ruleId": "2030", "severity": 2, "message": "2343", "line": 523, "column": 12, "nodeType": "2032", "messageId": "2033", "endLine": 523, "endColumn": 19}, {"ruleId": "1984", "severity": 2, "message": "2296", "line": 3, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 18}, {"ruleId": "1984", "severity": 2, "message": "2263", "line": 3, "column": 20, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 29}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 48, "column": 64, "nodeType": "1991", "messageId": "1992", "endLine": 48, "endColumn": 67, "suggestions": "2344"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 135, "column": 77, "nodeType": "1991", "messageId": "1992", "endLine": 135, "endColumn": 80, "suggestions": "2345"}, {"ruleId": "1984", "severity": 2, "message": "2011", "line": 16, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 16, "endColumn": 11}, {"ruleId": "1984", "severity": 2, "message": "2017", "line": 11, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 11, "endColumn": 10}, {"ruleId": "1984", "severity": 2, "message": "2012", "line": 13, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 13, "endColumn": 14}, {"ruleId": "1984", "severity": 2, "message": "2011", "line": 14, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 14, "endColumn": 11}, {"ruleId": "2030", "severity": 2, "message": "2346", "line": 201, "column": 17, "nodeType": "2032", "messageId": "2033", "endLine": 201, "endColumn": 25}, {"ruleId": "1984", "severity": 2, "message": "2292", "line": 4, "column": 58, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 64}, {"ruleId": "1984", "severity": 2, "message": "2347", "line": 4, "column": 25, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 47}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 20, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 20, "endColumn": 32, "suggestions": "2348"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 35, "column": 52, "nodeType": "1991", "messageId": "1992", "endLine": 35, "endColumn": 55, "suggestions": "2349"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 85, "column": 17, "nodeType": "1991", "messageId": "1992", "endLine": 85, "endColumn": 20, "suggestions": "2350"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 121, "column": 49, "nodeType": "1991", "messageId": "1992", "endLine": 121, "endColumn": 52, "suggestions": "2351"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 156, "column": 43, "nodeType": "1991", "messageId": "1992", "endLine": 156, "endColumn": 46, "suggestions": "2352"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 208, "column": 42, "nodeType": "1991", "messageId": "1992", "endLine": 208, "endColumn": 45, "suggestions": "2353"}, {"ruleId": "1984", "severity": 2, "message": "2011", "line": 15, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 15, "endColumn": 11}, {"ruleId": "1984", "severity": 2, "message": "2354", "line": 54, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 54, "endColumn": 11}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 110, "column": 48, "nodeType": "1991", "messageId": "1992", "endLine": 110, "endColumn": 51, "suggestions": "2355"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 157, "column": 51, "nodeType": "1991", "messageId": "1992", "endLine": 157, "endColumn": 54, "suggestions": "2356"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 340, "column": 46, "nodeType": "1991", "messageId": "1992", "endLine": 340, "endColumn": 49, "suggestions": "2357"}, {"ruleId": "2030", "severity": 2, "message": "2358", "line": 150, "column": 16, "nodeType": "2032", "messageId": "2033", "endLine": 150, "endColumn": 21}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 56, "column": 64, "nodeType": "1991", "messageId": "1992", "endLine": 56, "endColumn": 67, "suggestions": "2359"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 126, "column": 77, "nodeType": "1991", "messageId": "1992", "endLine": 126, "endColumn": 80, "suggestions": "2360"}, {"ruleId": "1984", "severity": 2, "message": "2361", "line": 10, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 10, "endColumn": 24}, {"ruleId": "2030", "severity": 2, "message": "2362", "line": 365, "column": 16, "nodeType": "2032", "messageId": "2033", "endLine": 365, "endColumn": 22}, {"ruleId": "1984", "severity": 2, "message": "2363", "line": 3, "column": 19, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 25}, {"ruleId": "1984", "severity": 2, "message": "2364", "line": 3, "column": 27, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 37}, {"ruleId": "1984", "severity": 2, "message": "2214", "line": 5, "column": 18, "nodeType": null, "messageId": "1986", "endLine": 5, "endColumn": 25}, {"ruleId": "1984", "severity": 2, "message": "2354", "line": 85, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 85, "endColumn": 11}, {"ruleId": "2020", "severity": 1, "message": "2304", "line": 197, "column": 6, "nodeType": "2022", "endLine": 197, "endColumn": 70, "suggestions": "2365"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 257, "column": 41, "nodeType": "1991", "messageId": "1992", "endLine": 257, "endColumn": 44, "suggestions": "2366"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 314, "column": 41, "nodeType": "1991", "messageId": "1992", "endLine": 314, "endColumn": 44, "suggestions": "2367"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 371, "column": 41, "nodeType": "1991", "messageId": "1992", "endLine": 371, "endColumn": 44, "suggestions": "2368"}, {"ruleId": "2020", "severity": 1, "message": "2328", "line": 454, "column": 6, "nodeType": "2022", "endLine": 454, "endColumn": 119, "suggestions": "2369"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 457, "column": 50, "nodeType": "1991", "messageId": "1992", "endLine": 457, "endColumn": 53, "suggestions": "2370"}, {"ruleId": "2020", "severity": 1, "message": "2328", "line": 489, "column": 6, "nodeType": "2022", "endLine": 489, "endColumn": 36, "suggestions": "2371"}, {"ruleId": "1984", "severity": 2, "message": "2013", "line": 9, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 9, "endColumn": 8}, {"ruleId": "1984", "severity": 2, "message": "2372", "line": 38, "column": 7, "nodeType": null, "messageId": "1986", "endLine": 38, "endColumn": 21}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 355, "column": 91, "nodeType": "1991", "messageId": "1992", "endLine": 355, "endColumn": 94, "suggestions": "2373"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 373, "column": 94, "nodeType": "1991", "messageId": "1992", "endLine": 373, "endColumn": 97, "suggestions": "2374"}, {"ruleId": "2375", "severity": 2, "message": "2376", "line": 12, "column": 10, "nodeType": "2377", "messageId": "2378", "endLine": 14, "endColumn": 4}, {"ruleId": "1984", "severity": 2, "message": "2014", "line": 4, "column": 29, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 35}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 43, "column": 75, "nodeType": "1991", "messageId": "1992", "endLine": 43, "endColumn": 78, "suggestions": "2379"}, {"ruleId": "2020", "severity": 1, "message": "2380", "line": 49, "column": 6, "nodeType": "2022", "endLine": 49, "endColumn": 8, "suggestions": "2381"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 233, "column": 73, "nodeType": "1991", "messageId": "1992", "endLine": 233, "endColumn": 76, "suggestions": "2382"}, {"ruleId": "1979", "severity": 2, "message": "1980", "line": 148, "column": 18, "nodeType": "1981", "messageId": "1982", "suggestions": "2383"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 18, "column": 69, "nodeType": "1991", "messageId": "1992", "endLine": 18, "endColumn": 72, "suggestions": "2384"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 31, "column": 73, "nodeType": "1991", "messageId": "1992", "endLine": 31, "endColumn": 76, "suggestions": "2385"}, {"ruleId": "2020", "severity": 1, "message": "2386", "line": 38, "column": 6, "nodeType": "2022", "endLine": 38, "endColumn": 8, "suggestions": "2387"}, {"ruleId": "2020", "severity": 1, "message": "2388", "line": 48, "column": 6, "nodeType": "2022", "endLine": 48, "endColumn": 41, "suggestions": "2389"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 126, "column": 73, "nodeType": "1991", "messageId": "1992", "endLine": 126, "endColumn": 76, "suggestions": "2390"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 60, "column": 71, "nodeType": "1991", "messageId": "1992", "endLine": 60, "endColumn": 74, "suggestions": "2391"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 63, "column": 73, "nodeType": "1991", "messageId": "1992", "endLine": 63, "endColumn": 76, "suggestions": "2392"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 82, "column": 33, "nodeType": "1991", "messageId": "1992", "endLine": 82, "endColumn": 36, "suggestions": "2393"}, {"ruleId": "1984", "severity": 2, "message": "2394", "line": 101, "column": 20, "nodeType": null, "messageId": "1986", "endLine": 101, "endColumn": 21}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 160, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 160, "endColumn": 32, "suggestions": "2395"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 188, "column": 73, "nodeType": "1991", "messageId": "1992", "endLine": 188, "endColumn": 76, "suggestions": "2396"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 41, "column": 71, "nodeType": "1991", "messageId": "1992", "endLine": 41, "endColumn": 74, "suggestions": "2397"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 57, "column": 33, "nodeType": "1991", "messageId": "1992", "endLine": 57, "endColumn": 36, "suggestions": "2398"}, {"ruleId": "1984", "severity": 2, "message": "2394", "line": 69, "column": 20, "nodeType": null, "messageId": "1986", "endLine": 69, "endColumn": 21}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 133, "column": 73, "nodeType": "1991", "messageId": "1992", "endLine": 133, "endColumn": 76, "suggestions": "2399"}, {"ruleId": "1984", "severity": 2, "message": "2400", "line": 8, "column": 7, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 13}, {"ruleId": "1984", "severity": 2, "message": "2401", "line": 53, "column": 36, "nodeType": null, "messageId": "1986", "endLine": 53, "endColumn": 39}, {"ruleId": "1984", "severity": 2, "message": "2402", "line": 104, "column": 19, "nodeType": null, "messageId": "1986", "endLine": 104, "endColumn": 27}, {"ruleId": "1984", "severity": 2, "message": "2403", "line": 104, "column": 32, "nodeType": null, "messageId": "1986", "endLine": 104, "endColumn": 51}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 45, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 45, "endColumn": 19, "suggestions": "2404"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 50, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 50, "endColumn": 19, "suggestions": "2405"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 54, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 54, "endColumn": 19, "suggestions": "2406"}, {"ruleId": "2059", "severity": 2, "message": "2407", "line": 104, "column": 7, "nodeType": "2061", "messageId": "2062", "endLine": 104, "endColumn": 20, "fix": "2408"}, {"ruleId": "1984", "severity": 2, "message": "2409", "line": 104, "column": 7, "nodeType": null, "messageId": "1986", "endLine": 104, "endColumn": 20}, {"ruleId": "2059", "severity": 2, "message": "2410", "line": 105, "column": 7, "nodeType": "2061", "messageId": "2062", "endLine": 105, "endColumn": 62, "fix": "2411"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 581, "column": 23, "nodeType": "1991", "messageId": "1992", "endLine": 581, "endColumn": 26, "suggestions": "2412"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 706, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 706, "endColumn": 15, "suggestions": "2413"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 737, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 737, "endColumn": 15, "suggestions": "2414"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 738, "column": 22, "nodeType": "1991", "messageId": "1992", "endLine": 738, "endColumn": 25, "suggestions": "2415"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 769, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 769, "endColumn": 15, "suggestions": "2416"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 5, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 5, "endColumn": 42, "suggestions": "2417"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 23, "column": 27, "nodeType": "1991", "messageId": "1992", "endLine": 23, "endColumn": 30, "suggestions": "2418"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 33, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 33, "endColumn": 24, "suggestions": "2419"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 88, "column": 54, "nodeType": "1991", "messageId": "1992", "endLine": 88, "endColumn": 57, "suggestions": "2420"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 88, "column": 83, "nodeType": "1991", "messageId": "1992", "endLine": 88, "endColumn": 86, "suggestions": "2421"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 120, "column": 31, "nodeType": "1991", "messageId": "1992", "endLine": 120, "endColumn": 34, "suggestions": "2422"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 132, "column": 52, "nodeType": "1991", "messageId": "1992", "endLine": 132, "endColumn": 55, "suggestions": "2423"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 83, "column": 9, "nodeType": "1991", "messageId": "1992", "endLine": 83, "endColumn": 12, "suggestions": "2424"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 53, "column": 34, "nodeType": "1991", "messageId": "1992", "endLine": 53, "endColumn": 37, "suggestions": "2425"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 152, "column": 49, "nodeType": "1991", "messageId": "1992", "endLine": 152, "endColumn": 52, "suggestions": "2426"}, {"ruleId": "2059", "severity": 2, "message": "2076", "line": 110, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 110, "endColumn": 25, "fix": "2427"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 110, "column": 22, "nodeType": "1991", "messageId": "1992", "endLine": 110, "endColumn": 25, "suggestions": "2428"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 129, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 129, "endColumn": 21, "suggestions": "2429"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 408, "column": 33, "nodeType": "1991", "messageId": "1992", "endLine": 408, "endColumn": 36, "suggestions": "2430"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 515, "column": 31, "nodeType": "1991", "messageId": "1992", "endLine": 515, "endColumn": 34, "suggestions": "2431"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 527, "column": 38, "nodeType": "1991", "messageId": "1992", "endLine": 527, "endColumn": 41, "suggestions": "2432"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2433", "line": 74, "column": 24}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 177, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 177, "endColumn": 19, "suggestions": "2434"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 27, "column": 29, "nodeType": "1991", "messageId": "1992", "endLine": 27, "endColumn": 32, "suggestions": "2435"}, {"ruleId": "2059", "severity": 2, "message": "2076", "line": 137, "column": 9, "nodeType": "2061", "messageId": "2062", "endLine": 137, "endColumn": 25, "fix": "2436"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 137, "column": 22, "nodeType": "1991", "messageId": "1992", "endLine": 137, "endColumn": 25, "suggestions": "2437"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 156, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 156, "endColumn": 21, "suggestions": "2438"}, {"ruleId": "1984", "severity": 2, "message": "2439", "line": 860, "column": 9, "nodeType": null, "messageId": "1986", "endLine": 860, "endColumn": 16}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 277, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 277, "endColumn": 15, "suggestions": "2440"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 185, "column": 18, "nodeType": "1991", "messageId": "1992", "endLine": 185, "endColumn": 21, "suggestions": "2441"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 350, "column": 43, "nodeType": "1991", "messageId": "1992", "endLine": 350, "endColumn": 46, "suggestions": "2442"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 350, "column": 52, "nodeType": "1991", "messageId": "1992", "endLine": 350, "endColumn": 55, "suggestions": "2443"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 15, "column": 20, "nodeType": "1991", "messageId": "1992", "endLine": 15, "endColumn": 23, "suggestions": "2444"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 19, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 19, "endColumn": 24, "suggestions": "2445"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 204, "column": 31, "nodeType": "1991", "messageId": "1992", "endLine": 204, "endColumn": 34, "suggestions": "2446"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 205, "column": 28, "nodeType": "1991", "messageId": "1992", "endLine": 205, "endColumn": 31, "suggestions": "2447"}, {"ruleId": "1984", "severity": 2, "message": "2448", "line": 223, "column": 15, "nodeType": null, "messageId": "1986", "endLine": 223, "endColumn": 28}, {"ruleId": "1984", "severity": 2, "message": "2449", "line": 3, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 3, "endColumn": 32}, {"ruleId": "1984", "severity": 2, "message": "2450", "line": 8, "column": 11, "nodeType": null, "messageId": "1986", "endLine": 8, "endColumn": 27}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 238, "column": 28, "nodeType": "1991", "messageId": "1992", "endLine": 238, "endColumn": 31, "suggestions": "2451"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 239, "column": 26, "nodeType": "1991", "messageId": "1992", "endLine": 239, "endColumn": 29, "suggestions": "2452"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 178, "column": 31, "nodeType": "1991", "messageId": "1992", "endLine": 178, "endColumn": 34, "suggestions": "2453"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 179, "column": 28, "nodeType": "1991", "messageId": "1992", "endLine": 179, "endColumn": 31, "suggestions": "2454"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 55, "column": 33, "nodeType": "1991", "messageId": "1992", "endLine": 55, "endColumn": 36, "suggestions": "2455"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 78, "column": 35, "nodeType": "1991", "messageId": "1992", "endLine": 78, "endColumn": 38, "suggestions": "2456"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 152, "column": 54, "nodeType": "1991", "messageId": "1992", "endLine": 152, "endColumn": 57, "suggestions": "2457"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 152, "column": 83, "nodeType": "1991", "messageId": "1992", "endLine": 152, "endColumn": 86, "suggestions": "2458"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 169, "column": 32, "nodeType": "1991", "messageId": "1992", "endLine": 169, "endColumn": 35, "suggestions": "2459"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 196, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 196, "endColumn": 42, "suggestions": "2460"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 249, "column": 39, "nodeType": "1991", "messageId": "1992", "endLine": 249, "endColumn": 42, "suggestions": "2461"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 255, "column": 27, "nodeType": "1991", "messageId": "1992", "endLine": 255, "endColumn": 30, "suggestions": "2462"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 260, "column": 21, "nodeType": "1991", "messageId": "1992", "endLine": 260, "endColumn": 24, "suggestions": "2463"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 142, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 142, "endColumn": 19, "suggestions": "2464"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 15, "column": 40, "nodeType": "1991", "messageId": "1992", "endLine": 15, "endColumn": 43, "suggestions": "2465"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 164, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 164, "endColumn": 19, "suggestions": "2466"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 416, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 416, "endColumn": 19, "suggestions": "2467"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 65, "column": 27, "nodeType": "1991", "messageId": "1992", "endLine": 65, "endColumn": 30, "suggestions": "2468"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 101, "column": 50, "nodeType": "1991", "messageId": "1992", "endLine": 101, "endColumn": 53, "suggestions": "2469"}, {"ruleId": "1984", "severity": 2, "message": "2470", "line": 136, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 136, "endColumn": 10}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 136, "column": 27, "nodeType": "1991", "messageId": "1992", "endLine": 136, "endColumn": 30, "suggestions": "2471"}, {"ruleId": "1984", "severity": 2, "message": "2472", "line": 137, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 137, "endColumn": 12}, {"ruleId": "1984", "severity": 2, "message": "2473", "line": 138, "column": 3, "nodeType": null, "messageId": "1986", "endLine": 138, "endColumn": 11}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 227, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 227, "endColumn": 15, "suggestions": "2474"}, {"ruleId": "1984", "severity": 2, "message": "2475", "line": 240, "column": 11, "nodeType": null, "messageId": "1986", "endLine": 240, "endColumn": 19}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 309, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 309, "endColumn": 15, "suggestions": "2476"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 354, "column": 14, "nodeType": "1991", "messageId": "1992", "endLine": 354, "endColumn": 17, "suggestions": "2477"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 357, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 357, "endColumn": 15, "suggestions": "2478"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 434, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 434, "endColumn": 15, "suggestions": "2479"}, {"ruleId": "1984", "severity": 2, "message": "2480", "line": 4, "column": 16, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 31}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 9, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 9, "endColumn": 15, "suggestions": "2481"}, {"ruleId": "1984", "severity": 2, "message": "2482", "line": 52, "column": 16, "nodeType": null, "messageId": "1986", "endLine": 52, "endColumn": 29}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 54, "column": 14, "nodeType": "1991", "messageId": "1992", "endLine": 54, "endColumn": 17, "suggestions": "2483"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 57, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 57, "endColumn": 15, "suggestions": "2484"}, {"ruleId": "1984", "severity": 2, "message": "2485", "line": 130, "column": 16, "nodeType": null, "messageId": "1986", "endLine": 130, "endColumn": 35}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 134, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 134, "endColumn": 15, "suggestions": "2486"}, {"ruleId": "1984", "severity": 2, "message": "2487", "line": 4, "column": 10, "nodeType": null, "messageId": "1986", "endLine": 4, "endColumn": 16}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 246, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 246, "endColumn": 15, "suggestions": "2488"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 334, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 334, "endColumn": 15, "suggestions": "2489"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 378, "column": 14, "nodeType": "1991", "messageId": "1992", "endLine": 378, "endColumn": 17, "suggestions": "2490"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 381, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 381, "endColumn": 15, "suggestions": "2491"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 456, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 456, "endColumn": 15, "suggestions": "2492"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 18, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 18, "endColumn": 15, "suggestions": "2493"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 40, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 40, "endColumn": 15, "suggestions": "2494"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 69, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 69, "endColumn": 15, "suggestions": "2495"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 100, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 100, "endColumn": 15, "suggestions": "2496"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 118, "column": 77, "nodeType": "1991", "messageId": "1992", "endLine": 118, "endColumn": 80, "suggestions": "2497"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 139, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 139, "endColumn": 15, "suggestions": "2498"}, {"ruleId": "1984", "severity": 2, "message": "2499", "line": 174, "column": 11, "nodeType": null, "messageId": "1986", "endLine": 174, "endColumn": 20}, {"ruleId": "1984", "severity": 2, "message": "2500", "line": 175, "column": 11, "nodeType": null, "messageId": "1986", "endLine": 175, "endColumn": 18}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 41, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 41, "endColumn": 19, "suggestions": "2501"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 46, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 46, "endColumn": 19, "suggestions": "2502"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 50, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 50, "endColumn": 19, "suggestions": "2503"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 345, "column": 23, "nodeType": "1991", "messageId": "1992", "endLine": 345, "endColumn": 26, "suggestions": "2504"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 468, "column": 12, "nodeType": "1991", "messageId": "1992", "endLine": 468, "endColumn": 15, "suggestions": "2505"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 102, "column": 30, "nodeType": "1991", "messageId": "1992", "endLine": 102, "endColumn": 33, "suggestions": "2506"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 127, "column": 8, "nodeType": "1991", "messageId": "1992", "endLine": 127, "endColumn": 11, "suggestions": "2507"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 134, "column": 33, "nodeType": "1991", "messageId": "1992", "endLine": 134, "endColumn": 36, "suggestions": "2508"}, {"ruleId": "2059", "severity": 2, "message": "2509", "line": 144, "column": 7, "nodeType": "2061", "messageId": "2062", "endLine": 144, "endColumn": 15, "fix": "2510"}, {"ruleId": "2059", "severity": 2, "message": "2511", "line": 155, "column": 7, "nodeType": "2061", "messageId": "2062", "endLine": 155, "endColumn": 18, "fix": "2512"}, {"ruleId": "1984", "severity": 2, "message": "2066", "line": 202, "column": 16, "nodeType": null, "messageId": "1986", "endLine": 202, "endColumn": 21}, {"ruleId": "1984", "severity": 2, "message": "2066", "line": 324, "column": 20, "nodeType": null, "messageId": "1986", "endLine": 324, "endColumn": 25}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 395, "column": 22, "nodeType": "1991", "messageId": "1992", "endLine": 395, "endColumn": 25, "suggestions": "2513"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 431, "column": 36, "nodeType": "1991", "messageId": "1992", "endLine": 431, "endColumn": 39, "suggestions": "2514"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 464, "column": 49, "nodeType": "1991", "messageId": "1992", "endLine": 464, "endColumn": 52, "suggestions": "2515"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 31, "column": 46, "nodeType": "1991", "messageId": "1992", "endLine": 31, "endColumn": 49, "suggestions": "2516"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 31, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 31, "endColumn": 59, "suggestions": "2517"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 50, "column": 46, "nodeType": "1991", "messageId": "1992", "endLine": 50, "endColumn": 49, "suggestions": "2518"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 50, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 50, "endColumn": 59, "suggestions": "2519"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 116, "column": 16, "nodeType": "1991", "messageId": "1992", "endLine": 116, "endColumn": 19, "suggestions": "2520"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 144, "column": 45, "nodeType": "1991", "messageId": "1992", "endLine": 144, "endColumn": 48, "suggestions": "2521"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 93, "column": 31, "nodeType": "1991", "messageId": "1992", "endLine": 93, "endColumn": 34, "suggestions": "2522"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 20, "column": 9, "nodeType": "1991", "messageId": "1992", "endLine": 20, "endColumn": 12, "suggestions": "2523"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 26, "column": 11, "nodeType": "1991", "messageId": "1992", "endLine": 26, "endColumn": 14, "suggestions": "2524"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 35, "column": 20, "nodeType": "1991", "messageId": "1992", "endLine": 35, "endColumn": 23, "suggestions": "2525"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 36, "column": 20, "nodeType": "1991", "messageId": "1992", "endLine": 36, "endColumn": 23, "suggestions": "2526"}, {"ruleId": "1984", "severity": 2, "message": "2527", "line": 49, "column": 19, "nodeType": null, "messageId": "1986", "endLine": 49, "endColumn": 23}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 97, "column": 62, "nodeType": "1991", "messageId": "1992", "endLine": 97, "endColumn": 65, "suggestions": "2528"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 131, "column": 40, "nodeType": "1991", "messageId": "1992", "endLine": 131, "endColumn": 43, "suggestions": "2529"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 131, "column": 54, "nodeType": "1991", "messageId": "1992", "endLine": 131, "endColumn": 57, "suggestions": "2530"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 141, "column": 44, "nodeType": "1991", "messageId": "1992", "endLine": 141, "endColumn": 47, "suggestions": "2531"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 141, "column": 58, "nodeType": "1991", "messageId": "1992", "endLine": 141, "endColumn": 61, "suggestions": "2532"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 151, "column": 44, "nodeType": "1991", "messageId": "1992", "endLine": 151, "endColumn": 47, "suggestions": "2533"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 151, "column": 58, "nodeType": "1991", "messageId": "1992", "endLine": 151, "endColumn": 61, "suggestions": "2534"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 161, "column": 42, "nodeType": "1991", "messageId": "1992", "endLine": 161, "endColumn": 45, "suggestions": "2535"}, {"ruleId": "1989", "severity": 2, "message": "1990", "line": 161, "column": 56, "nodeType": "1991", "messageId": "1992", "endLine": 161, "endColumn": 59, "suggestions": "2536"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["2537", "2538", "2539", "2540"], "@typescript-eslint/no-unused-vars", "'FaCalendarAlt' is defined but never used.", "unusedVar", "'session' is assigned a value but never used.", "'data' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["2541", "2542"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["2543", "2544", "2545", "2546"], ["2547", "2548", "2549", "2550"], "'FaTrash' is defined but never used.", ["2551", "2552"], ["2553", "2554"], ["2555", "2556", "2557", "2558"], ["2559", "2560", "2561", "2562"], "'Image' is defined but never used.", "'FaCog' is defined but never used.", "'FaPercentage' is defined but never used.", "'FaDollarSign' is defined but never used.", "'FaArrowUp' is defined but never used.", "'FaArrowDown' is defined but never used.", "'FaUserPlus' is defined but never used.", "'FaTrophy' is defined but never used.", "'FaSearch' is defined but never used.", "'FaFilter' is defined but never used.", "'FaEllipsisH' is defined but never used.", "'FaEye' is defined but never used.", "'FaEdit' is defined but never used.", "'FaChartPie' is defined but never used.", ["2563", "2564"], "'FaCheck' is defined but never used.", "'FaImage' is defined but never used.", "'FaCamera' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["2565"], ["2566", "2567"], ["2568", "2569"], ["2570", "2571", "2572", "2573"], ["2574", "2575", "2576", "2577"], "React Hook useEffect has a missing dependency: 'checkAdminStatus'. Either include it or remove the dependency array.", ["2578"], "react/jsx-no-undef", "'FaCheck' is not defined.", "JSXIdentifier", "undefined", "'FaTimes' is defined but never used.", "'FaChartLine' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRebates'. Either include it or remove the dependency array.", ["2579"], ["2580"], ["2581", "2582"], "React Hook useEffect has a missing dependency: 'fetchReportData'. Either include it or remove the dependency array.", ["2583"], ["2584"], "'FaCheckCircle' is defined but never used.", "'FaTimesCircle' is defined but never used.", "'request' is defined but never used.", ["2585", "2586"], ["2587", "2588"], ["2589", "2590"], ["2591", "2592"], ["2593", "2594"], ["2595", "2596"], ["2597", "2598"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["2599", "2600"], "'options' is assigned a value but never used.", "prefer-const", "'startDate' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "2601", "text": "2602"}, ["2603", "2604"], "'getPaginatedDownline' is defined but never used.", "'error' is defined but never used.", ["2605", "2606"], ["2607", "2608"], ["2609", "2610"], ["2611", "2612"], "'getUserPerformanceMetrics' is defined but never used.", ["2613", "2614"], ["2615", "2616"], "'setDefaultUserPaymentMethod' is defined but never used.", ["2617", "2618"], "'where<PERSON><PERSON><PERSON>' is never reassigned. Use 'const' instead.", {"range": "2619", "text": "2620"}, ["2621", "2622"], "'orderBy<PERSON><PERSON><PERSON>' is never reassigned. Use 'const' instead.", {"range": "2623", "text": "2624"}, ["2625", "2626"], ["2627", "2628"], "'prisma' is defined but never used.", {"range": "2629", "text": "2620"}, ["2630", "2631"], ["2632", "2633"], ["2634", "2635"], ["2636", "2637"], ["2638", "2639"], ["2640", "2641"], ["2642", "2643"], ["2644", "2645"], ["2646", "2647"], "'_' is assigned a value but never used.", {"range": "2648", "text": "2620"}, ["2649", "2650"], ["2651", "2652"], "'params' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPerformanceData'. Either include it or remove the dependency array.", ["2653"], ["2654", "2655", "2656", "2657"], ["2658", "2659", "2660", "2661"], "'FaShoppingCart' is defined but never used.", ["2662", "2663"], ["2664", "2665"], ["2666", "2667"], ["2668", "2669"], ["2670", "2671"], ["2672", "2673"], ["2674", "2675"], ["2676", "2677", "2678", "2679"], ["2680", "2681", "2682", "2683"], ["2684", "2685", "2686", "2687"], ["2688", "2689", "2690", "2691"], ["2692", "2693", "2694", "2695"], ["2696", "2697", "2698", "2699"], ["2700", "2701", "2702", "2703"], ["2704", "2705", "2706", "2707"], ["2708", "2709", "2710", "2711"], ["2712", "2713", "2714", "2715"], "'FaChartBar' is defined but never used.", "'FaLayerGroup' is defined but never used.", ["2716", "2717"], ["2718", "2719"], ["2720", "2721"], ["2722", "2723"], ["2724", "2725"], ["2726", "2727"], "'FaFileExport' is defined but never used.", ["2728", "2729", "2730", "2731"], ["2732", "2733"], ["2734", "2735"], ["2736", "2737", "2738", "2739"], ["2740", "2741", "2742", "2743"], ["2744", "2745", "2746", "2747"], ["2748", "2749", "2750", "2751"], ["2752", "2753", "2754", "2755"], ["2756", "2757", "2758", "2759"], ["2760", "2761", "2762", "2763"], ["2764", "2765"], "'setTimeRange' is assigned a value but never used.", ["2766", "2767", "2768", "2769"], ["2770", "2771", "2772", "2773"], ["2774", "2775", "2776", "2777"], ["2778", "2779", "2780", "2781"], ["2782", "2783", "2784", "2785"], ["2786", "2787", "2788", "2789"], ["2790", "2791", "2792", "2793"], ["2794", "2795", "2796", "2797"], ["2798", "2799", "2800", "2801"], ["2802", "2803", "2804", "2805"], "'FaBell' is defined but never used.", ["2806", "2807"], ["2808", "2809", "2810", "2811"], ["2812", "2813", "2814", "2815"], "'FaWallet' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGenealogy'. Either include it or remove the dependency array.", ["2816"], "'generateCsrfToken' is defined but never used.", ["2817", "2818", "2819", "2820"], ["2821", "2822", "2823", "2824"], ["2825", "2826", "2827", "2828"], "'FaLeaf' is defined but never used.", "'FaHandHoldingHeart' is defined but never used.", ["2829", "2830", "2831", "2832"], ["2833", "2834", "2835", "2836"], ["2837", "2838", "2839", "2840"], ["2841", "2842", "2843", "2844"], ["2845", "2846"], ["2847", "2848"], ["2849", "2850"], ["2851", "2852"], ["2853", "2854"], ["2855", "2856"], ["2857", "2858"], ["2859", "2860"], ["2861", "2862"], ["2863", "2864"], ["2865", "2866"], ["2867", "2868"], ["2869", "2870"], ["2871", "2872"], ["2873", "2874"], ["2875", "2876"], ["2877", "2878", "2879", "2880"], ["2881", "2882", "2883", "2884"], "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", ["2885"], ["2886", "2887"], ["2888", "2889"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["2890", "2891"], ["2892", "2893"], ["2894", "2895", "2896", "2897"], "'FaMoneyBillWave' is defined but never used.", "'FaMapMarkerAlt' is defined but never used.", "'FaInfoCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPurchases'. Either include it or remove the dependency array.", ["2898"], ["2899", "2900", "2901", "2902"], ["2903", "2904"], "'useMemo' is defined but never used.", "'rebatesError' is assigned a value but never used.", "'totalRebates' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchReferralCommissions'. Either include it or remove the dependency array.", ["2905"], "'FaQrCode' is defined but never used.", "'FaDownload' is defined but never used.", "'FaShareAlt' is defined but never used.", "'showQRCode' is assigned a value but never used.", "'setShowQRCode' is assigned a value but never used.", "'FaUsers' is defined but never used.", ["2906", "2907", "2908", "2909"], ["2910", "2911"], "'newErrors' is never reassigned. Use 'const' instead.", {"range": "2912", "text": "2913"}, "React Hook useEffect has a missing dependency: 'validateStep'. Either include it or remove the dependency array.", ["2914"], "'allErrors' is never reassigned. Use 'const' instead.", {"range": "2915", "text": "2916"}, ["2917", "2918"], "'router' is assigned a value but never used.", "'token' is assigned a value but never used.", ["2919", "2920", "2921", "2922"], ["2923", "2924", "2925", "2926"], ["2927", "2928", "2929", "2930"], ["2931", "2932", "2933", "2934"], "'ProductPlaceholder' is defined but never used.", "'purchaseLoading' is assigned a value but never used.", "'handlePurchase' is assigned a value but never used.", ["2935", "2936"], "React Hook useEffect has a missing dependency: 'fetchProduct'. Either include it or remove the dependency array.", ["2937"], ["2938", "2939"], "'IconType' is defined but never used.", ["2940", "2941"], ["2942", "2943"], ["2944", "2945"], ["2946", "2947"], ["2948", "2949"], ["2950", "2951"], ["2952", "2953"], ["2954", "2955"], ["2956", "2957"], ["2958", "2959"], "React Hook useEffect has a missing dependency: 'fetchInventoryTransactions'. Either include it or remove the dependency array.", ["2960"], ["2961", "2962"], ["2963", "2964"], "'FaBoxOpen' is defined but never used.", ["2965", "2966"], ["2967", "2968"], ["2969", "2970"], ["2971", "2972"], ["2973", "2974"], ["2975", "2976", "2977", "2978"], "'FaTag' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAuditLogs' and 'fetchSalesHistory'. Either include them or remove the dependency array.", ["2979"], ["2980", "2981"], "'useEffect' is defined but never used.", "'FaPlus' is defined but never used.", ["2982", "2983"], ["2984", "2985"], ["2986", "2987", "2988", "2989"], ["2990", "2991", "2992", "2993"], ["2994", "2995"], ["2996", "2997"], ["2998", "2999"], "React Hook useEffect has a missing dependency: 'simulateRebates'. Either include it or remove the dependency array.", ["3000"], "'chartRef' is assigned a value but never used.", ["3001", "3002"], ["3003", "3004"], "'endPage' is never reassigned. Use 'const' instead.", {"range": "3005", "text": "3006"}, ["3007", "3008"], ["3009", "3010"], ["3011", "3012"], "React Hook useEffect has a missing dependency: 'fetchTestUsers'. Either include it or remove the dependency array.", ["3013"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["3014"], ["3015", "3016"], ["3017", "3018"], "React Hook useEffect has a missing dependency: 'fetchAuditLogs'. Either include it or remove the dependency array.", ["3019"], "'isUploading' is assigned a value but never used.", "'setIsUploading' is assigned a value but never used.", "'FaStar' is defined but never used.", ["3020", "3021"], ["3022", "3023"], ["3024", "3025"], "'useState' is defined but never used.", "'CartItem' is defined but never used.", ["3026", "3027"], ["3028", "3029"], ["3030", "3031"], ["3032", "3033"], "'error' is assigned a value but never used.", ["3034", "3035"], "React Hook useCallback has a missing dependency: 'transformDataToFlow'. Either include it or remove the dependency array.", ["3036"], ["3037", "3038"], ["3039", "3040"], ["3041", "3042"], ["3043"], ["3044", "3045"], "React Hook useCallback has missing dependencies: 'handleAddUser', 'handleDeleteUser', 'handleEditUser', 'handleExpandNode', and 'processChildren'. Either include them or remove the dependency array.", ["3046"], ["3047", "3048"], "React Hook useCallback has a missing dependency: 'processChild'. Either include it or remove the dependency array.", ["3049"], ["3050", "3051"], "React Hook useCallback has missing dependencies: 'getConnectionType', 'handleAddUser', 'handleDeleteUser', 'handleEditUser', 'handleExpandNode', and 'processChildren'. Either include them or remove the dependency array.", ["3052"], "React Hook useCallback has a missing dependency: 'isDescendantOf'. Either include it or remove the dependency array.", ["3053"], ["3054"], ["3055", "3056"], "React Hook useCallback has missing dependencies: 'handleExpandNode' and 'processChildren'. Either include them or remove the dependency array.", ["3057"], ["3058", "3059"], ["3060"], ["3061", "3062"], "React Hook useCallback has a missing dependency: 'handleExpandNode'. Either include it or remove the dependency array.", ["3063"], "'FaArrowRight' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'nodeVariants' is assigned a value but never used.", "'childrenVariants' is assigned a value but never used.", ["3064", "3065"], ["3066", "3067"], ["3068", "3069"], ["3070", "3071"], ["3072", "3073"], ["3074", "3075"], ["3076", "3077"], ["3078", "3079"], "'FaUserMinus' is defined but never used.", "'FaUsers' is not defined.", ["3080", "3081"], ["3082", "3083"], "'FaWallet' is not defined.", "'UserPerformanceMetrics' is defined but never used.", ["3084", "3085"], ["3086", "3087"], ["3088", "3089"], ["3090", "3091"], ["3092", "3093"], ["3094", "3095"], "'maxLevel' is assigned a value but never used.", ["3096", "3097"], ["3098", "3099"], ["3100", "3101"], "'React' is not defined.", ["3102", "3103"], ["3104", "3105"], "'FaExclamationTriangle' is defined but never used.", "'FaPlus' is not defined.", "'FaUser' is defined but never used.", "'FaEnvelope' is defined but never used.", ["3106"], ["3107", "3108"], ["3109", "3110"], ["3111", "3112"], ["3113"], ["3114", "3115"], ["3116"], "'defaultOptions' is assigned a value but never used.", ["3117", "3118"], ["3119", "3120"], "react/display-name", "Component definition is missing display name", "ArrowFunctionExpression", "noDisplayName", ["3121", "3122"], "React Hook useEffect has a missing dependency: 'fetchPaymentMethods'. Either include it or remove the dependency array.", ["3123"], ["3124", "3125"], ["3126", "3127", "3128", "3129"], ["3130", "3131"], ["3132", "3133"], "React Hook useEffect has a missing dependency: 'fetchShippingMethods'. Either include it or remove the dependency array.", ["3134"], "React Hook useEffect has a missing dependency: 'handleSelectMethod'. Either include it or remove the dependency array.", ["3135"], ["3136", "3137"], ["3138", "3139"], ["3140", "3141"], ["3142", "3143"], "'e' is defined but never used.", ["3144", "3145"], ["3146", "3147"], ["3148", "3149"], ["3150", "3151"], ["3152", "3153"], "'prisma' is assigned a value but never used.", "'req' is defined but never used.", "'password' is assigned a value but never used.", "'userWithoutPassword' is assigned a value but never used.", ["3154", "3155"], ["3156", "3157"], ["3158", "3159"], "'currentUserId' is never reassigned. Use 'const' instead.", {"range": "3160", "text": "3161"}, "'currentUserId' is assigned a value but never used.", "'queue' is never reassigned. Use 'const' instead.", {"range": "3162", "text": "3163"}, ["3164", "3165"], ["3166", "3167"], ["3168", "3169"], ["3170", "3171"], ["3172", "3173"], ["3174", "3175"], ["3176", "3177"], ["3178", "3179"], ["3180", "3181"], ["3182", "3183"], ["3184", "3185"], ["3186", "3187"], ["3188", "3189"], ["3190", "3191"], ["3192", "3193"], {"range": "3194", "text": "3195"}, ["3196", "3197"], ["3198", "3199"], ["3200", "3201"], ["3202", "3203"], ["3204", "3205"], "Parsing error: ',' expected.", ["3206", "3207"], ["3208", "3209"], {"range": "3210", "text": "3195"}, ["3211", "3212"], ["3213", "3214"], "'pattern' is assigned a value but never used.", ["3215", "3216"], ["3217", "3218"], ["3219", "3220"], ["3221", "3222"], ["3223", "3224"], ["3225", "3226"], ["3227", "3228"], ["3229", "3230"], "'totalPvAmount' is assigned a value but never used.", "'getDownlineLevelCounts' is defined but never used.", "'RankRequirements' is defined but never used.", ["3231", "3232"], ["3233", "3234"], ["3235", "3236"], ["3237", "3238"], ["3239", "3240"], ["3241", "3242"], ["3243", "3244"], ["3245", "3246"], ["3247", "3248"], ["3249", "3250"], ["3251", "3252"], ["3253", "3254"], ["3255", "3256"], ["3257", "3258"], ["3259", "3260"], ["3261", "3262"], ["3263", "3264"], ["3265", "3266"], ["3267", "3268"], "'details' is defined but never used.", ["3269", "3270"], "'productId' is defined but never used.", "'quantity' is defined but never used.", ["3271", "3272"], "'position' is assigned a value but never used.", ["3273", "3274"], ["3275", "3276"], ["3277", "3278"], ["3279", "3280"], "'createPurchases' is defined but never used.", ["3281", "3282"], "'createRebates' is defined but never used.", ["3283", "3284"], ["3285", "3286"], "'createReferralLinks' is defined but never used.", ["3287", "3288"], "'Prisma' is defined but never used.", ["3289", "3290"], ["3291", "3292"], ["3293", "3294"], ["3295", "3296"], ["3297", "3298"], ["3299", "3300"], ["3301", "3302"], ["3303", "3304"], ["3305", "3306"], ["3307", "3308"], ["3309", "3310"], "'startDate' is assigned a value but never used.", "'endDate' is assigned a value but never used.", ["3311", "3312"], ["3313", "3314"], ["3315", "3316"], ["3317", "3318"], ["3319", "3320"], ["3321", "3322"], ["3323", "3324"], ["3325", "3326"], "'memberId' is never reassigned. Use 'const' instead.", {"range": "3327", "text": "3328"}, "'rowUplineId' is never reassigned. Use 'const' instead.", {"range": "3329", "text": "3330"}, ["3331", "3332"], ["3333", "3334"], ["3335", "3336"], ["3337", "3338"], ["3339", "3340"], ["3341", "3342"], ["3343", "3344"], ["3345", "3346"], ["3347", "3348"], ["3349", "3350"], ["3351", "3352"], ["3353", "3354"], ["3355", "3356"], ["3357", "3358"], "'type' is assigned a value but never used.", ["3359", "3360"], ["3361", "3362"], ["3363", "3364"], ["3365", "3366"], ["3367", "3368"], ["3369", "3370"], ["3371", "3372"], ["3373", "3374"], ["3375", "3376"], {"messageId": "3377", "data": "3378", "fix": "3379", "desc": "3380"}, {"messageId": "3377", "data": "3381", "fix": "3382", "desc": "3383"}, {"messageId": "3377", "data": "3384", "fix": "3385", "desc": "3386"}, {"messageId": "3377", "data": "3387", "fix": "3388", "desc": "3389"}, {"messageId": "3390", "fix": "3391", "desc": "3392"}, {"messageId": "3393", "fix": "3394", "desc": "3395"}, {"messageId": "3377", "data": "3396", "fix": "3397", "desc": "3398"}, {"messageId": "3377", "data": "3399", "fix": "3400", "desc": "3401"}, {"messageId": "3377", "data": "3402", "fix": "3403", "desc": "3404"}, {"messageId": "3377", "data": "3405", "fix": "3406", "desc": "3407"}, {"messageId": "3377", "data": "3408", "fix": "3409", "desc": "3398"}, {"messageId": "3377", "data": "3410", "fix": "3411", "desc": "3401"}, {"messageId": "3377", "data": "3412", "fix": "3413", "desc": "3404"}, {"messageId": "3377", "data": "3414", "fix": "3415", "desc": "3407"}, {"messageId": "3390", "fix": "3416", "desc": "3392"}, {"messageId": "3393", "fix": "3417", "desc": "3395"}, {"messageId": "3390", "fix": "3418", "desc": "3392"}, {"messageId": "3393", "fix": "3419", "desc": "3395"}, {"messageId": "3377", "data": "3420", "fix": "3421", "desc": "3398"}, {"messageId": "3377", "data": "3422", "fix": "3423", "desc": "3401"}, {"messageId": "3377", "data": "3424", "fix": "3425", "desc": "3404"}, {"messageId": "3377", "data": "3426", "fix": "3427", "desc": "3407"}, {"messageId": "3377", "data": "3428", "fix": "3429", "desc": "3398"}, {"messageId": "3377", "data": "3430", "fix": "3431", "desc": "3401"}, {"messageId": "3377", "data": "3432", "fix": "3433", "desc": "3404"}, {"messageId": "3377", "data": "3434", "fix": "3435", "desc": "3407"}, {"messageId": "3390", "fix": "3436", "desc": "3392"}, {"messageId": "3393", "fix": "3437", "desc": "3395"}, {"desc": "3438", "fix": "3439"}, {"messageId": "3390", "fix": "3440", "desc": "3392"}, {"messageId": "3393", "fix": "3441", "desc": "3395"}, {"messageId": "3390", "fix": "3442", "desc": "3392"}, {"messageId": "3393", "fix": "3443", "desc": "3395"}, {"messageId": "3377", "data": "3444", "fix": "3445", "desc": "3398"}, {"messageId": "3377", "data": "3446", "fix": "3447", "desc": "3401"}, {"messageId": "3377", "data": "3448", "fix": "3449", "desc": "3404"}, {"messageId": "3377", "data": "3450", "fix": "3451", "desc": "3407"}, {"messageId": "3377", "data": "3452", "fix": "3453", "desc": "3398"}, {"messageId": "3377", "data": "3454", "fix": "3455", "desc": "3401"}, {"messageId": "3377", "data": "3456", "fix": "3457", "desc": "3404"}, {"messageId": "3377", "data": "3458", "fix": "3459", "desc": "3407"}, {"desc": "3460", "fix": "3461"}, {"desc": "3462", "fix": "3463"}, {"desc": "3464", "fix": "3465"}, {"messageId": "3390", "fix": "3466", "desc": "3392"}, {"messageId": "3393", "fix": "3467", "desc": "3395"}, {"desc": "3468", "fix": "3469"}, {"desc": "3470", "fix": "3471"}, {"messageId": "3390", "fix": "3472", "desc": "3392"}, {"messageId": "3393", "fix": "3473", "desc": "3395"}, {"messageId": "3390", "fix": "3474", "desc": "3392"}, {"messageId": "3393", "fix": "3475", "desc": "3395"}, {"messageId": "3390", "fix": "3476", "desc": "3392"}, {"messageId": "3393", "fix": "3477", "desc": "3395"}, {"messageId": "3390", "fix": "3478", "desc": "3392"}, {"messageId": "3393", "fix": "3479", "desc": "3395"}, {"messageId": "3390", "fix": "3480", "desc": "3392"}, {"messageId": "3393", "fix": "3481", "desc": "3395"}, {"messageId": "3390", "fix": "3482", "desc": "3392"}, {"messageId": "3393", "fix": "3483", "desc": "3395"}, {"messageId": "3390", "fix": "3484", "desc": "3392"}, {"messageId": "3393", "fix": "3485", "desc": "3395"}, {"messageId": "3390", "fix": "3486", "desc": "3392"}, {"messageId": "3393", "fix": "3487", "desc": "3395"}, [1835, 1862], "const startDate = new Date();", {"messageId": "3390", "fix": "3488", "desc": "3392"}, {"messageId": "3393", "fix": "3489", "desc": "3395"}, {"messageId": "3390", "fix": "3490", "desc": "3392"}, {"messageId": "3393", "fix": "3491", "desc": "3395"}, {"messageId": "3390", "fix": "3492", "desc": "3392"}, {"messageId": "3393", "fix": "3493", "desc": "3395"}, {"messageId": "3390", "fix": "3494", "desc": "3392"}, {"messageId": "3393", "fix": "3495", "desc": "3395"}, {"messageId": "3390", "fix": "3496", "desc": "3392"}, {"messageId": "3393", "fix": "3497", "desc": "3395"}, {"messageId": "3390", "fix": "3498", "desc": "3392"}, {"messageId": "3393", "fix": "3499", "desc": "3395"}, {"messageId": "3390", "fix": "3500", "desc": "3392"}, {"messageId": "3393", "fix": "3501", "desc": "3395"}, {"messageId": "3390", "fix": "3502", "desc": "3392"}, {"messageId": "3393", "fix": "3503", "desc": "3395"}, [4931, 4957], "const whereClause: any = {};", {"messageId": "3390", "fix": "3504", "desc": "3392"}, {"messageId": "3393", "fix": "3505", "desc": "3395"}, [5287, 5315], "const orderByClause: any = {};", {"messageId": "3390", "fix": "3506", "desc": "3392"}, {"messageId": "3393", "fix": "3507", "desc": "3395"}, {"messageId": "3390", "fix": "3508", "desc": "3392"}, {"messageId": "3393", "fix": "3509", "desc": "3395"}, [1600, 1626], {"messageId": "3390", "fix": "3510", "desc": "3392"}, {"messageId": "3393", "fix": "3511", "desc": "3395"}, {"messageId": "3390", "fix": "3512", "desc": "3392"}, {"messageId": "3393", "fix": "3513", "desc": "3395"}, {"messageId": "3390", "fix": "3514", "desc": "3392"}, {"messageId": "3393", "fix": "3515", "desc": "3395"}, {"messageId": "3390", "fix": "3516", "desc": "3392"}, {"messageId": "3393", "fix": "3517", "desc": "3395"}, {"messageId": "3390", "fix": "3518", "desc": "3392"}, {"messageId": "3393", "fix": "3519", "desc": "3395"}, {"messageId": "3390", "fix": "3520", "desc": "3392"}, {"messageId": "3393", "fix": "3521", "desc": "3395"}, {"messageId": "3390", "fix": "3522", "desc": "3392"}, {"messageId": "3393", "fix": "3523", "desc": "3395"}, {"messageId": "3390", "fix": "3524", "desc": "3392"}, {"messageId": "3393", "fix": "3525", "desc": "3395"}, {"messageId": "3390", "fix": "3526", "desc": "3392"}, {"messageId": "3393", "fix": "3527", "desc": "3395"}, [4075, 4101], {"messageId": "3390", "fix": "3528", "desc": "3392"}, {"messageId": "3393", "fix": "3529", "desc": "3395"}, {"messageId": "3390", "fix": "3530", "desc": "3392"}, {"messageId": "3393", "fix": "3531", "desc": "3395"}, {"desc": "3532", "fix": "3533"}, {"messageId": "3377", "data": "3534", "fix": "3535", "desc": "3398"}, {"messageId": "3377", "data": "3536", "fix": "3537", "desc": "3401"}, {"messageId": "3377", "data": "3538", "fix": "3539", "desc": "3404"}, {"messageId": "3377", "data": "3540", "fix": "3541", "desc": "3407"}, {"messageId": "3377", "data": "3542", "fix": "3543", "desc": "3398"}, {"messageId": "3377", "data": "3544", "fix": "3545", "desc": "3401"}, {"messageId": "3377", "data": "3546", "fix": "3547", "desc": "3404"}, {"messageId": "3377", "data": "3548", "fix": "3549", "desc": "3407"}, {"messageId": "3390", "fix": "3550", "desc": "3392"}, {"messageId": "3393", "fix": "3551", "desc": "3395"}, {"messageId": "3390", "fix": "3552", "desc": "3392"}, {"messageId": "3393", "fix": "3553", "desc": "3395"}, {"messageId": "3390", "fix": "3554", "desc": "3392"}, {"messageId": "3393", "fix": "3555", "desc": "3395"}, {"messageId": "3390", "fix": "3556", "desc": "3392"}, {"messageId": "3393", "fix": "3557", "desc": "3395"}, {"messageId": "3390", "fix": "3558", "desc": "3392"}, {"messageId": "3393", "fix": "3559", "desc": "3395"}, {"messageId": "3390", "fix": "3560", "desc": "3392"}, {"messageId": "3393", "fix": "3561", "desc": "3395"}, {"messageId": "3390", "fix": "3562", "desc": "3392"}, {"messageId": "3393", "fix": "3563", "desc": "3395"}, {"messageId": "3377", "data": "3564", "fix": "3565", "desc": "3380"}, {"messageId": "3377", "data": "3566", "fix": "3567", "desc": "3383"}, {"messageId": "3377", "data": "3568", "fix": "3569", "desc": "3386"}, {"messageId": "3377", "data": "3570", "fix": "3571", "desc": "3389"}, {"messageId": "3377", "data": "3572", "fix": "3573", "desc": "3380"}, {"messageId": "3377", "data": "3574", "fix": "3575", "desc": "3383"}, {"messageId": "3377", "data": "3576", "fix": "3577", "desc": "3386"}, {"messageId": "3377", "data": "3578", "fix": "3579", "desc": "3389"}, {"messageId": "3377", "data": "3580", "fix": "3581", "desc": "3380"}, {"messageId": "3377", "data": "3582", "fix": "3583", "desc": "3383"}, {"messageId": "3377", "data": "3584", "fix": "3585", "desc": "3386"}, {"messageId": "3377", "data": "3586", "fix": "3587", "desc": "3389"}, {"messageId": "3377", "data": "3588", "fix": "3589", "desc": "3380"}, {"messageId": "3377", "data": "3590", "fix": "3591", "desc": "3383"}, {"messageId": "3377", "data": "3592", "fix": "3593", "desc": "3386"}, {"messageId": "3377", "data": "3594", "fix": "3595", "desc": "3389"}, {"messageId": "3377", "data": "3596", "fix": "3597", "desc": "3398"}, {"messageId": "3377", "data": "3598", "fix": "3599", "desc": "3401"}, {"messageId": "3377", "data": "3600", "fix": "3601", "desc": "3404"}, {"messageId": "3377", "data": "3602", "fix": "3603", "desc": "3407"}, {"messageId": "3377", "data": "3604", "fix": "3605", "desc": "3380"}, {"messageId": "3377", "data": "3606", "fix": "3607", "desc": "3383"}, {"messageId": "3377", "data": "3608", "fix": "3609", "desc": "3386"}, {"messageId": "3377", "data": "3610", "fix": "3611", "desc": "3389"}, {"messageId": "3377", "data": "3612", "fix": "3613", "desc": "3398"}, {"messageId": "3377", "data": "3614", "fix": "3615", "desc": "3401"}, {"messageId": "3377", "data": "3616", "fix": "3617", "desc": "3404"}, {"messageId": "3377", "data": "3618", "fix": "3619", "desc": "3407"}, {"messageId": "3377", "data": "3620", "fix": "3621", "desc": "3380"}, {"messageId": "3377", "data": "3622", "fix": "3623", "desc": "3383"}, {"messageId": "3377", "data": "3624", "fix": "3625", "desc": "3386"}, {"messageId": "3377", "data": "3626", "fix": "3627", "desc": "3389"}, {"messageId": "3377", "data": "3628", "fix": "3629", "desc": "3380"}, {"messageId": "3377", "data": "3630", "fix": "3631", "desc": "3383"}, {"messageId": "3377", "data": "3632", "fix": "3633", "desc": "3386"}, {"messageId": "3377", "data": "3634", "fix": "3635", "desc": "3389"}, {"messageId": "3377", "data": "3636", "fix": "3637", "desc": "3380"}, {"messageId": "3377", "data": "3638", "fix": "3639", "desc": "3383"}, {"messageId": "3377", "data": "3640", "fix": "3641", "desc": "3386"}, {"messageId": "3377", "data": "3642", "fix": "3643", "desc": "3389"}, {"messageId": "3390", "fix": "3644", "desc": "3392"}, {"messageId": "3393", "fix": "3645", "desc": "3395"}, {"messageId": "3390", "fix": "3646", "desc": "3392"}, {"messageId": "3393", "fix": "3647", "desc": "3395"}, {"messageId": "3390", "fix": "3648", "desc": "3392"}, {"messageId": "3393", "fix": "3649", "desc": "3395"}, {"messageId": "3390", "fix": "3650", "desc": "3392"}, {"messageId": "3393", "fix": "3651", "desc": "3395"}, {"messageId": "3390", "fix": "3652", "desc": "3392"}, {"messageId": "3393", "fix": "3653", "desc": "3395"}, {"messageId": "3390", "fix": "3654", "desc": "3392"}, {"messageId": "3393", "fix": "3655", "desc": "3395"}, {"messageId": "3377", "data": "3656", "fix": "3657", "desc": "3380"}, {"messageId": "3377", "data": "3658", "fix": "3659", "desc": "3383"}, {"messageId": "3377", "data": "3660", "fix": "3661", "desc": "3386"}, {"messageId": "3377", "data": "3662", "fix": "3663", "desc": "3389"}, {"messageId": "3390", "fix": "3664", "desc": "3392"}, {"messageId": "3393", "fix": "3665", "desc": "3395"}, {"messageId": "3390", "fix": "3666", "desc": "3392"}, {"messageId": "3393", "fix": "3667", "desc": "3395"}, {"messageId": "3377", "data": "3668", "fix": "3669", "desc": "3380"}, {"messageId": "3377", "data": "3670", "fix": "3671", "desc": "3383"}, {"messageId": "3377", "data": "3672", "fix": "3673", "desc": "3386"}, {"messageId": "3377", "data": "3674", "fix": "3675", "desc": "3389"}, {"messageId": "3377", "data": "3676", "fix": "3677", "desc": "3380"}, {"messageId": "3377", "data": "3678", "fix": "3679", "desc": "3383"}, {"messageId": "3377", "data": "3680", "fix": "3681", "desc": "3386"}, {"messageId": "3377", "data": "3682", "fix": "3683", "desc": "3389"}, {"messageId": "3377", "data": "3684", "fix": "3685", "desc": "3380"}, {"messageId": "3377", "data": "3686", "fix": "3687", "desc": "3383"}, {"messageId": "3377", "data": "3688", "fix": "3689", "desc": "3386"}, {"messageId": "3377", "data": "3690", "fix": "3691", "desc": "3389"}, {"messageId": "3377", "data": "3692", "fix": "3693", "desc": "3380"}, {"messageId": "3377", "data": "3694", "fix": "3695", "desc": "3383"}, {"messageId": "3377", "data": "3696", "fix": "3697", "desc": "3386"}, {"messageId": "3377", "data": "3698", "fix": "3699", "desc": "3389"}, {"messageId": "3377", "data": "3700", "fix": "3701", "desc": "3380"}, {"messageId": "3377", "data": "3702", "fix": "3703", "desc": "3383"}, {"messageId": "3377", "data": "3704", "fix": "3705", "desc": "3386"}, {"messageId": "3377", "data": "3706", "fix": "3707", "desc": "3389"}, {"messageId": "3377", "data": "3708", "fix": "3709", "desc": "3380"}, {"messageId": "3377", "data": "3710", "fix": "3711", "desc": "3383"}, {"messageId": "3377", "data": "3712", "fix": "3713", "desc": "3386"}, {"messageId": "3377", "data": "3714", "fix": "3715", "desc": "3389"}, {"messageId": "3377", "data": "3716", "fix": "3717", "desc": "3380"}, {"messageId": "3377", "data": "3718", "fix": "3719", "desc": "3383"}, {"messageId": "3377", "data": "3720", "fix": "3721", "desc": "3386"}, {"messageId": "3377", "data": "3722", "fix": "3723", "desc": "3389"}, {"messageId": "3390", "fix": "3724", "desc": "3392"}, {"messageId": "3393", "fix": "3725", "desc": "3395"}, {"messageId": "3377", "data": "3726", "fix": "3727", "desc": "3380"}, {"messageId": "3377", "data": "3728", "fix": "3729", "desc": "3383"}, {"messageId": "3377", "data": "3730", "fix": "3731", "desc": "3386"}, {"messageId": "3377", "data": "3732", "fix": "3733", "desc": "3389"}, {"messageId": "3377", "data": "3734", "fix": "3735", "desc": "3380"}, {"messageId": "3377", "data": "3736", "fix": "3737", "desc": "3383"}, {"messageId": "3377", "data": "3738", "fix": "3739", "desc": "3386"}, {"messageId": "3377", "data": "3740", "fix": "3741", "desc": "3389"}, {"messageId": "3377", "data": "3742", "fix": "3743", "desc": "3380"}, {"messageId": "3377", "data": "3744", "fix": "3745", "desc": "3383"}, {"messageId": "3377", "data": "3746", "fix": "3747", "desc": "3386"}, {"messageId": "3377", "data": "3748", "fix": "3749", "desc": "3389"}, {"messageId": "3377", "data": "3750", "fix": "3751", "desc": "3380"}, {"messageId": "3377", "data": "3752", "fix": "3753", "desc": "3383"}, {"messageId": "3377", "data": "3754", "fix": "3755", "desc": "3386"}, {"messageId": "3377", "data": "3756", "fix": "3757", "desc": "3389"}, {"messageId": "3377", "data": "3758", "fix": "3759", "desc": "3398"}, {"messageId": "3377", "data": "3760", "fix": "3761", "desc": "3401"}, {"messageId": "3377", "data": "3762", "fix": "3763", "desc": "3404"}, {"messageId": "3377", "data": "3764", "fix": "3765", "desc": "3407"}, {"messageId": "3377", "data": "3766", "fix": "3767", "desc": "3398"}, {"messageId": "3377", "data": "3768", "fix": "3769", "desc": "3401"}, {"messageId": "3377", "data": "3770", "fix": "3771", "desc": "3404"}, {"messageId": "3377", "data": "3772", "fix": "3773", "desc": "3407"}, {"messageId": "3377", "data": "3774", "fix": "3775", "desc": "3398"}, {"messageId": "3377", "data": "3776", "fix": "3777", "desc": "3401"}, {"messageId": "3377", "data": "3778", "fix": "3779", "desc": "3404"}, {"messageId": "3377", "data": "3780", "fix": "3781", "desc": "3407"}, {"messageId": "3377", "data": "3782", "fix": "3783", "desc": "3398"}, {"messageId": "3377", "data": "3784", "fix": "3785", "desc": "3401"}, {"messageId": "3377", "data": "3786", "fix": "3787", "desc": "3404"}, {"messageId": "3377", "data": "3788", "fix": "3789", "desc": "3407"}, {"messageId": "3377", "data": "3790", "fix": "3791", "desc": "3380"}, {"messageId": "3377", "data": "3792", "fix": "3793", "desc": "3383"}, {"messageId": "3377", "data": "3794", "fix": "3795", "desc": "3386"}, {"messageId": "3377", "data": "3796", "fix": "3797", "desc": "3389"}, {"messageId": "3377", "data": "3798", "fix": "3799", "desc": "3380"}, {"messageId": "3377", "data": "3800", "fix": "3801", "desc": "3383"}, {"messageId": "3377", "data": "3802", "fix": "3803", "desc": "3386"}, {"messageId": "3377", "data": "3804", "fix": "3805", "desc": "3389"}, {"messageId": "3390", "fix": "3806", "desc": "3392"}, {"messageId": "3393", "fix": "3807", "desc": "3395"}, {"messageId": "3377", "data": "3808", "fix": "3809", "desc": "3380"}, {"messageId": "3377", "data": "3810", "fix": "3811", "desc": "3383"}, {"messageId": "3377", "data": "3812", "fix": "3813", "desc": "3386"}, {"messageId": "3377", "data": "3814", "fix": "3815", "desc": "3389"}, {"messageId": "3377", "data": "3816", "fix": "3817", "desc": "3380"}, {"messageId": "3377", "data": "3818", "fix": "3819", "desc": "3383"}, {"messageId": "3377", "data": "3820", "fix": "3821", "desc": "3386"}, {"messageId": "3377", "data": "3822", "fix": "3823", "desc": "3389"}, {"desc": "3824", "fix": "3825"}, {"messageId": "3377", "data": "3826", "fix": "3827", "desc": "3398"}, {"messageId": "3377", "data": "3828", "fix": "3829", "desc": "3401"}, {"messageId": "3377", "data": "3830", "fix": "3831", "desc": "3404"}, {"messageId": "3377", "data": "3832", "fix": "3833", "desc": "3407"}, {"messageId": "3377", "data": "3834", "fix": "3835", "desc": "3398"}, {"messageId": "3377", "data": "3836", "fix": "3837", "desc": "3401"}, {"messageId": "3377", "data": "3838", "fix": "3839", "desc": "3404"}, {"messageId": "3377", "data": "3840", "fix": "3841", "desc": "3407"}, {"messageId": "3377", "data": "3842", "fix": "3843", "desc": "3380"}, {"messageId": "3377", "data": "3844", "fix": "3845", "desc": "3383"}, {"messageId": "3377", "data": "3846", "fix": "3847", "desc": "3386"}, {"messageId": "3377", "data": "3848", "fix": "3849", "desc": "3389"}, {"messageId": "3377", "data": "3850", "fix": "3851", "desc": "3380"}, {"messageId": "3377", "data": "3852", "fix": "3853", "desc": "3383"}, {"messageId": "3377", "data": "3854", "fix": "3855", "desc": "3386"}, {"messageId": "3377", "data": "3856", "fix": "3857", "desc": "3389"}, {"messageId": "3377", "data": "3858", "fix": "3859", "desc": "3398"}, {"messageId": "3377", "data": "3860", "fix": "3861", "desc": "3401"}, {"messageId": "3377", "data": "3862", "fix": "3863", "desc": "3404"}, {"messageId": "3377", "data": "3864", "fix": "3865", "desc": "3407"}, {"messageId": "3377", "data": "3866", "fix": "3867", "desc": "3398"}, {"messageId": "3377", "data": "3868", "fix": "3869", "desc": "3401"}, {"messageId": "3377", "data": "3870", "fix": "3871", "desc": "3404"}, {"messageId": "3377", "data": "3872", "fix": "3873", "desc": "3407"}, {"messageId": "3377", "data": "3874", "fix": "3875", "desc": "3380"}, {"messageId": "3377", "data": "3876", "fix": "3877", "desc": "3383"}, {"messageId": "3377", "data": "3878", "fix": "3879", "desc": "3386"}, {"messageId": "3377", "data": "3880", "fix": "3881", "desc": "3389"}, {"messageId": "3390", "fix": "3882", "desc": "3392"}, {"messageId": "3393", "fix": "3883", "desc": "3395"}, {"messageId": "3390", "fix": "3884", "desc": "3392"}, {"messageId": "3393", "fix": "3885", "desc": "3395"}, {"messageId": "3390", "fix": "3886", "desc": "3392"}, {"messageId": "3393", "fix": "3887", "desc": "3395"}, {"messageId": "3390", "fix": "3888", "desc": "3392"}, {"messageId": "3393", "fix": "3889", "desc": "3395"}, {"messageId": "3390", "fix": "3890", "desc": "3392"}, {"messageId": "3393", "fix": "3891", "desc": "3395"}, {"messageId": "3390", "fix": "3892", "desc": "3392"}, {"messageId": "3393", "fix": "3893", "desc": "3395"}, {"messageId": "3390", "fix": "3894", "desc": "3392"}, {"messageId": "3393", "fix": "3895", "desc": "3395"}, {"messageId": "3390", "fix": "3896", "desc": "3392"}, {"messageId": "3393", "fix": "3897", "desc": "3395"}, {"messageId": "3390", "fix": "3898", "desc": "3392"}, {"messageId": "3393", "fix": "3899", "desc": "3395"}, {"messageId": "3390", "fix": "3900", "desc": "3392"}, {"messageId": "3393", "fix": "3901", "desc": "3395"}, {"messageId": "3390", "fix": "3902", "desc": "3392"}, {"messageId": "3393", "fix": "3903", "desc": "3395"}, {"messageId": "3390", "fix": "3904", "desc": "3392"}, {"messageId": "3393", "fix": "3905", "desc": "3395"}, {"messageId": "3390", "fix": "3906", "desc": "3392"}, {"messageId": "3393", "fix": "3907", "desc": "3395"}, {"messageId": "3390", "fix": "3908", "desc": "3392"}, {"messageId": "3393", "fix": "3909", "desc": "3395"}, {"messageId": "3390", "fix": "3910", "desc": "3392"}, {"messageId": "3393", "fix": "3911", "desc": "3395"}, {"messageId": "3390", "fix": "3912", "desc": "3392"}, {"messageId": "3393", "fix": "3913", "desc": "3395"}, {"messageId": "3377", "data": "3914", "fix": "3915", "desc": "3380"}, {"messageId": "3377", "data": "3916", "fix": "3917", "desc": "3383"}, {"messageId": "3377", "data": "3918", "fix": "3919", "desc": "3386"}, {"messageId": "3377", "data": "3920", "fix": "3921", "desc": "3389"}, {"messageId": "3377", "data": "3922", "fix": "3923", "desc": "3380"}, {"messageId": "3377", "data": "3924", "fix": "3925", "desc": "3383"}, {"messageId": "3377", "data": "3926", "fix": "3927", "desc": "3386"}, {"messageId": "3377", "data": "3928", "fix": "3929", "desc": "3389"}, {"desc": "3930", "fix": "3931"}, {"messageId": "3390", "fix": "3932", "desc": "3392"}, {"messageId": "3393", "fix": "3933", "desc": "3395"}, {"messageId": "3390", "fix": "3934", "desc": "3392"}, {"messageId": "3393", "fix": "3935", "desc": "3395"}, {"messageId": "3390", "fix": "3936", "desc": "3392"}, {"messageId": "3393", "fix": "3937", "desc": "3395"}, {"messageId": "3390", "fix": "3938", "desc": "3392"}, {"messageId": "3393", "fix": "3939", "desc": "3395"}, {"messageId": "3377", "data": "3940", "fix": "3941", "desc": "3380"}, {"messageId": "3377", "data": "3942", "fix": "3943", "desc": "3383"}, {"messageId": "3377", "data": "3944", "fix": "3945", "desc": "3386"}, {"messageId": "3377", "data": "3946", "fix": "3947", "desc": "3389"}, {"desc": "3948", "fix": "3949"}, {"messageId": "3377", "data": "3950", "fix": "3951", "desc": "3380"}, {"messageId": "3377", "data": "3952", "fix": "3953", "desc": "3383"}, {"messageId": "3377", "data": "3954", "fix": "3955", "desc": "3386"}, {"messageId": "3377", "data": "3956", "fix": "3957", "desc": "3389"}, {"messageId": "3390", "fix": "3958", "desc": "3392"}, {"messageId": "3393", "fix": "3959", "desc": "3395"}, {"desc": "3960", "fix": "3961"}, {"messageId": "3377", "data": "3962", "fix": "3963", "desc": "3380"}, {"messageId": "3377", "data": "3964", "fix": "3965", "desc": "3383"}, {"messageId": "3377", "data": "3966", "fix": "3967", "desc": "3386"}, {"messageId": "3377", "data": "3968", "fix": "3969", "desc": "3389"}, {"messageId": "3390", "fix": "3970", "desc": "3392"}, {"messageId": "3393", "fix": "3971", "desc": "3395"}, [5968, 6011], "const newErrors: Record<string, string> = {};", {"desc": "3972", "fix": "3973"}, [8640, 8683], "const allErrors: Record<string, string> = {};", {"messageId": "3390", "fix": "3974", "desc": "3392"}, {"messageId": "3393", "fix": "3975", "desc": "3395"}, {"messageId": "3377", "data": "3976", "fix": "3977", "desc": "3398"}, {"messageId": "3377", "data": "3978", "fix": "3979", "desc": "3401"}, {"messageId": "3377", "data": "3980", "fix": "3981", "desc": "3404"}, {"messageId": "3377", "data": "3982", "fix": "3983", "desc": "3407"}, {"messageId": "3377", "data": "3984", "fix": "3985", "desc": "3380"}, {"messageId": "3377", "data": "3986", "fix": "3987", "desc": "3383"}, {"messageId": "3377", "data": "3988", "fix": "3989", "desc": "3386"}, {"messageId": "3377", "data": "3990", "fix": "3991", "desc": "3389"}, {"messageId": "3377", "data": "3992", "fix": "3993", "desc": "3398"}, {"messageId": "3377", "data": "3994", "fix": "3995", "desc": "3401"}, {"messageId": "3377", "data": "3996", "fix": "3997", "desc": "3404"}, {"messageId": "3377", "data": "3998", "fix": "3999", "desc": "3407"}, {"messageId": "3377", "data": "4000", "fix": "4001", "desc": "3380"}, {"messageId": "3377", "data": "4002", "fix": "4003", "desc": "3383"}, {"messageId": "3377", "data": "4004", "fix": "4005", "desc": "3386"}, {"messageId": "3377", "data": "4006", "fix": "4007", "desc": "3389"}, {"messageId": "3390", "fix": "4008", "desc": "3392"}, {"messageId": "3393", "fix": "4009", "desc": "3395"}, {"desc": "4010", "fix": "4011"}, {"messageId": "3390", "fix": "4012", "desc": "3392"}, {"messageId": "3393", "fix": "4013", "desc": "3395"}, {"messageId": "3390", "fix": "4014", "desc": "3392"}, {"messageId": "3393", "fix": "4015", "desc": "3395"}, {"messageId": "3390", "fix": "4016", "desc": "3392"}, {"messageId": "3393", "fix": "4017", "desc": "3395"}, {"messageId": "3390", "fix": "4018", "desc": "3392"}, {"messageId": "3393", "fix": "4019", "desc": "3395"}, {"messageId": "3390", "fix": "4020", "desc": "3392"}, {"messageId": "3393", "fix": "4021", "desc": "3395"}, {"messageId": "3390", "fix": "4022", "desc": "3392"}, {"messageId": "3393", "fix": "4023", "desc": "3395"}, {"messageId": "3390", "fix": "4024", "desc": "3392"}, {"messageId": "3393", "fix": "4025", "desc": "3395"}, {"messageId": "3390", "fix": "4026", "desc": "3392"}, {"messageId": "3393", "fix": "4027", "desc": "3395"}, {"messageId": "3390", "fix": "4028", "desc": "3392"}, {"messageId": "3393", "fix": "4029", "desc": "3395"}, {"messageId": "3390", "fix": "4030", "desc": "3392"}, {"messageId": "3393", "fix": "4031", "desc": "3395"}, {"messageId": "3390", "fix": "4032", "desc": "3392"}, {"messageId": "3393", "fix": "4033", "desc": "3395"}, {"desc": "4034", "fix": "4035"}, {"messageId": "3390", "fix": "4036", "desc": "3392"}, {"messageId": "3393", "fix": "4037", "desc": "3395"}, {"messageId": "3390", "fix": "4038", "desc": "3392"}, {"messageId": "3393", "fix": "4039", "desc": "3395"}, {"messageId": "3390", "fix": "4040", "desc": "3392"}, {"messageId": "3393", "fix": "4041", "desc": "3395"}, {"messageId": "3390", "fix": "4042", "desc": "3392"}, {"messageId": "3393", "fix": "4043", "desc": "3395"}, {"messageId": "3390", "fix": "4044", "desc": "3392"}, {"messageId": "3393", "fix": "4045", "desc": "3395"}, {"messageId": "3390", "fix": "4046", "desc": "3392"}, {"messageId": "3393", "fix": "4047", "desc": "3395"}, {"messageId": "3390", "fix": "4048", "desc": "3392"}, {"messageId": "3393", "fix": "4049", "desc": "3395"}, {"messageId": "3377", "data": "4050", "fix": "4051", "desc": "3380"}, {"messageId": "3377", "data": "4052", "fix": "4053", "desc": "3383"}, {"messageId": "3377", "data": "4054", "fix": "4055", "desc": "3386"}, {"messageId": "3377", "data": "4056", "fix": "4057", "desc": "3389"}, {"desc": "4058", "fix": "4059"}, {"messageId": "3390", "fix": "4060", "desc": "3392"}, {"messageId": "3393", "fix": "4061", "desc": "3395"}, {"messageId": "3390", "fix": "4062", "desc": "3392"}, {"messageId": "3393", "fix": "4063", "desc": "3395"}, {"messageId": "3390", "fix": "4064", "desc": "3392"}, {"messageId": "3393", "fix": "4065", "desc": "3395"}, {"messageId": "3377", "data": "4066", "fix": "4067", "desc": "3380"}, {"messageId": "3377", "data": "4068", "fix": "4069", "desc": "3383"}, {"messageId": "3377", "data": "4070", "fix": "4071", "desc": "3386"}, {"messageId": "3377", "data": "4072", "fix": "4073", "desc": "3389"}, {"messageId": "3377", "data": "4074", "fix": "4075", "desc": "3380"}, {"messageId": "3377", "data": "4076", "fix": "4077", "desc": "3383"}, {"messageId": "3377", "data": "4078", "fix": "4079", "desc": "3386"}, {"messageId": "3377", "data": "4080", "fix": "4081", "desc": "3389"}, {"messageId": "3390", "fix": "4082", "desc": "3392"}, {"messageId": "3393", "fix": "4083", "desc": "3395"}, {"messageId": "3390", "fix": "4084", "desc": "3392"}, {"messageId": "3393", "fix": "4085", "desc": "3395"}, {"messageId": "3390", "fix": "4086", "desc": "3392"}, {"messageId": "3393", "fix": "4087", "desc": "3395"}, {"desc": "4088", "fix": "4089"}, {"messageId": "3390", "fix": "4090", "desc": "3392"}, {"messageId": "3393", "fix": "4091", "desc": "3395"}, {"messageId": "3390", "fix": "4092", "desc": "3392"}, {"messageId": "3393", "fix": "4093", "desc": "3395"}, [4452, 4519], "const endPage = Math.min(totalPages, startPage + maxPageButtons - 1);", {"messageId": "3390", "fix": "4094", "desc": "3392"}, {"messageId": "3393", "fix": "4095", "desc": "3395"}, {"messageId": "3390", "fix": "4096", "desc": "3392"}, {"messageId": "3393", "fix": "4097", "desc": "3395"}, {"messageId": "3390", "fix": "4098", "desc": "3392"}, {"messageId": "3393", "fix": "4099", "desc": "3395"}, {"desc": "4100", "fix": "4101"}, {"desc": "4102", "fix": "4103"}, {"messageId": "3390", "fix": "4104", "desc": "3392"}, {"messageId": "3393", "fix": "4105", "desc": "3395"}, {"messageId": "3390", "fix": "4106", "desc": "3392"}, {"messageId": "3393", "fix": "4107", "desc": "3395"}, {"desc": "4108", "fix": "4109"}, {"messageId": "3390", "fix": "4110", "desc": "3392"}, {"messageId": "3393", "fix": "4111", "desc": "3395"}, {"messageId": "3390", "fix": "4112", "desc": "3392"}, {"messageId": "3393", "fix": "4113", "desc": "3395"}, {"messageId": "3390", "fix": "4114", "desc": "3392"}, {"messageId": "3393", "fix": "4115", "desc": "3395"}, {"messageId": "3390", "fix": "4116", "desc": "3392"}, {"messageId": "3393", "fix": "4117", "desc": "3395"}, {"messageId": "3390", "fix": "4118", "desc": "3392"}, {"messageId": "3393", "fix": "4119", "desc": "3395"}, {"messageId": "3390", "fix": "4120", "desc": "3392"}, {"messageId": "3393", "fix": "4121", "desc": "3395"}, {"messageId": "3390", "fix": "4122", "desc": "3392"}, {"messageId": "3393", "fix": "4123", "desc": "3395"}, {"messageId": "3390", "fix": "4124", "desc": "3392"}, {"messageId": "3393", "fix": "4125", "desc": "3395"}, {"desc": "4126", "fix": "4127"}, {"messageId": "3390", "fix": "4128", "desc": "3392"}, {"messageId": "3393", "fix": "4129", "desc": "3395"}, {"messageId": "3390", "fix": "4130", "desc": "3392"}, {"messageId": "3393", "fix": "4131", "desc": "3395"}, {"messageId": "3390", "fix": "4132", "desc": "3392"}, {"messageId": "3393", "fix": "4133", "desc": "3395"}, {"desc": "4134", "fix": "4135"}, {"messageId": "3390", "fix": "4136", "desc": "3392"}, {"messageId": "3393", "fix": "4137", "desc": "3395"}, {"desc": "4138", "fix": "4139"}, {"messageId": "3390", "fix": "4140", "desc": "3392"}, {"messageId": "3393", "fix": "4141", "desc": "3395"}, {"desc": "4142", "fix": "4143"}, {"messageId": "3390", "fix": "4144", "desc": "3392"}, {"messageId": "3393", "fix": "4145", "desc": "3395"}, {"desc": "4146", "fix": "4147"}, {"desc": "4148", "fix": "4149"}, {"desc": "4150", "fix": "4151"}, {"messageId": "3390", "fix": "4152", "desc": "3392"}, {"messageId": "3393", "fix": "4153", "desc": "3395"}, {"desc": "4154", "fix": "4155"}, {"messageId": "3390", "fix": "4156", "desc": "3392"}, {"messageId": "3393", "fix": "4157", "desc": "3395"}, {"desc": "4158", "fix": "4159"}, {"messageId": "3390", "fix": "4160", "desc": "3392"}, {"messageId": "3393", "fix": "4161", "desc": "3395"}, {"desc": "4162", "fix": "4163"}, {"messageId": "3390", "fix": "4164", "desc": "3392"}, {"messageId": "3393", "fix": "4165", "desc": "3395"}, {"messageId": "3390", "fix": "4166", "desc": "3392"}, {"messageId": "3393", "fix": "4167", "desc": "3395"}, {"messageId": "3390", "fix": "4168", "desc": "3392"}, {"messageId": "3393", "fix": "4169", "desc": "3395"}, {"messageId": "3390", "fix": "4170", "desc": "3392"}, {"messageId": "3393", "fix": "4171", "desc": "3395"}, {"messageId": "3390", "fix": "4172", "desc": "3392"}, {"messageId": "3393", "fix": "4173", "desc": "3395"}, {"messageId": "3390", "fix": "4174", "desc": "3392"}, {"messageId": "3393", "fix": "4175", "desc": "3395"}, {"messageId": "3390", "fix": "4176", "desc": "3392"}, {"messageId": "3393", "fix": "4177", "desc": "3395"}, {"messageId": "3390", "fix": "4178", "desc": "3392"}, {"messageId": "3393", "fix": "4179", "desc": "3395"}, {"messageId": "3390", "fix": "4180", "desc": "3392"}, {"messageId": "3393", "fix": "4181", "desc": "3395"}, {"messageId": "3390", "fix": "4182", "desc": "3392"}, {"messageId": "3393", "fix": "4183", "desc": "3395"}, {"messageId": "3390", "fix": "4184", "desc": "3392"}, {"messageId": "3393", "fix": "4185", "desc": "3395"}, {"messageId": "3390", "fix": "4186", "desc": "3392"}, {"messageId": "3393", "fix": "4187", "desc": "3395"}, {"messageId": "3390", "fix": "4188", "desc": "3392"}, {"messageId": "3393", "fix": "4189", "desc": "3395"}, {"messageId": "3390", "fix": "4190", "desc": "3392"}, {"messageId": "3393", "fix": "4191", "desc": "3395"}, {"messageId": "3390", "fix": "4192", "desc": "3392"}, {"messageId": "3393", "fix": "4193", "desc": "3395"}, {"messageId": "3390", "fix": "4194", "desc": "3392"}, {"messageId": "3393", "fix": "4195", "desc": "3395"}, {"messageId": "3390", "fix": "4196", "desc": "3392"}, {"messageId": "3393", "fix": "4197", "desc": "3395"}, {"messageId": "3390", "fix": "4198", "desc": "3392"}, {"messageId": "3393", "fix": "4199", "desc": "3395"}, {"messageId": "3390", "fix": "4200", "desc": "3392"}, {"messageId": "3393", "fix": "4201", "desc": "3395"}, {"messageId": "3390", "fix": "4202", "desc": "3392"}, {"messageId": "3393", "fix": "4203", "desc": "3395"}, {"messageId": "3390", "fix": "4204", "desc": "3392"}, {"messageId": "3393", "fix": "4205", "desc": "3395"}, {"desc": "4206", "fix": "4207"}, {"messageId": "3390", "fix": "4208", "desc": "3392"}, {"messageId": "3393", "fix": "4209", "desc": "3395"}, {"messageId": "3390", "fix": "4210", "desc": "3392"}, {"messageId": "3393", "fix": "4211", "desc": "3395"}, {"messageId": "3390", "fix": "4212", "desc": "3392"}, {"messageId": "3393", "fix": "4213", "desc": "3395"}, {"desc": "4214", "fix": "4215"}, {"messageId": "3390", "fix": "4216", "desc": "3392"}, {"messageId": "3393", "fix": "4217", "desc": "3395"}, {"desc": "4218", "fix": "4219"}, {"messageId": "3390", "fix": "4220", "desc": "3392"}, {"messageId": "3393", "fix": "4221", "desc": "3395"}, {"messageId": "3390", "fix": "4222", "desc": "3392"}, {"messageId": "3393", "fix": "4223", "desc": "3395"}, {"messageId": "3390", "fix": "4224", "desc": "3392"}, {"messageId": "3393", "fix": "4225", "desc": "3395"}, {"desc": "4226", "fix": "4227"}, {"messageId": "3390", "fix": "4228", "desc": "3392"}, {"messageId": "3393", "fix": "4229", "desc": "3395"}, {"messageId": "3377", "data": "4230", "fix": "4231", "desc": "3380"}, {"messageId": "3377", "data": "4232", "fix": "4233", "desc": "3383"}, {"messageId": "3377", "data": "4234", "fix": "4235", "desc": "3386"}, {"messageId": "3377", "data": "4236", "fix": "4237", "desc": "3389"}, {"messageId": "3390", "fix": "4238", "desc": "3392"}, {"messageId": "3393", "fix": "4239", "desc": "3395"}, {"messageId": "3390", "fix": "4240", "desc": "3392"}, {"messageId": "3393", "fix": "4241", "desc": "3395"}, {"desc": "4242", "fix": "4243"}, {"desc": "4244", "fix": "4245"}, {"messageId": "3390", "fix": "4246", "desc": "3392"}, {"messageId": "3393", "fix": "4247", "desc": "3395"}, {"messageId": "3390", "fix": "4248", "desc": "3392"}, {"messageId": "3393", "fix": "4249", "desc": "3395"}, {"messageId": "3390", "fix": "4250", "desc": "3392"}, {"messageId": "3393", "fix": "4251", "desc": "3395"}, {"messageId": "3390", "fix": "4252", "desc": "3392"}, {"messageId": "3393", "fix": "4253", "desc": "3395"}, {"messageId": "3390", "fix": "4254", "desc": "3392"}, {"messageId": "3393", "fix": "4255", "desc": "3395"}, {"messageId": "3390", "fix": "4256", "desc": "3392"}, {"messageId": "3393", "fix": "4257", "desc": "3395"}, {"messageId": "3390", "fix": "4258", "desc": "3392"}, {"messageId": "3393", "fix": "4259", "desc": "3395"}, {"messageId": "3390", "fix": "4260", "desc": "3392"}, {"messageId": "3393", "fix": "4261", "desc": "3395"}, {"messageId": "3390", "fix": "4262", "desc": "3392"}, {"messageId": "3393", "fix": "4263", "desc": "3395"}, {"messageId": "3390", "fix": "4264", "desc": "3392"}, {"messageId": "3393", "fix": "4265", "desc": "3395"}, {"messageId": "3390", "fix": "4266", "desc": "3392"}, {"messageId": "3393", "fix": "4267", "desc": "3395"}, {"messageId": "3390", "fix": "4268", "desc": "3392"}, {"messageId": "3393", "fix": "4269", "desc": "3395"}, [2273, 2305], "const currentUserId = startUserId;", [2308, 2373], "const queue: { userId: number; position: 'left' | 'right' }[] = [];", {"messageId": "3390", "fix": "4270", "desc": "3392"}, {"messageId": "3393", "fix": "4271", "desc": "3395"}, {"messageId": "3390", "fix": "4272", "desc": "3392"}, {"messageId": "3393", "fix": "4273", "desc": "3395"}, {"messageId": "3390", "fix": "4274", "desc": "3392"}, {"messageId": "3393", "fix": "4275", "desc": "3395"}, {"messageId": "3390", "fix": "4276", "desc": "3392"}, {"messageId": "3393", "fix": "4277", "desc": "3395"}, {"messageId": "3390", "fix": "4278", "desc": "3392"}, {"messageId": "3393", "fix": "4279", "desc": "3395"}, {"messageId": "3390", "fix": "4280", "desc": "3392"}, {"messageId": "3393", "fix": "4281", "desc": "3395"}, {"messageId": "3390", "fix": "4282", "desc": "3392"}, {"messageId": "3393", "fix": "4283", "desc": "3395"}, {"messageId": "3390", "fix": "4284", "desc": "3392"}, {"messageId": "3393", "fix": "4285", "desc": "3395"}, {"messageId": "3390", "fix": "4286", "desc": "3392"}, {"messageId": "3393", "fix": "4287", "desc": "3395"}, {"messageId": "3390", "fix": "4288", "desc": "3392"}, {"messageId": "3393", "fix": "4289", "desc": "3395"}, {"messageId": "3390", "fix": "4290", "desc": "3392"}, {"messageId": "3393", "fix": "4291", "desc": "3395"}, {"messageId": "3390", "fix": "4292", "desc": "3392"}, {"messageId": "3393", "fix": "4293", "desc": "3395"}, {"messageId": "3390", "fix": "4294", "desc": "3392"}, {"messageId": "3393", "fix": "4295", "desc": "3395"}, {"messageId": "3390", "fix": "4296", "desc": "3392"}, {"messageId": "3393", "fix": "4297", "desc": "3395"}, {"messageId": "3390", "fix": "4298", "desc": "3392"}, {"messageId": "3393", "fix": "4299", "desc": "3395"}, [2682, 2726], "const whereClause: any = { uplineId: userId };", {"messageId": "3390", "fix": "4300", "desc": "3392"}, {"messageId": "3393", "fix": "4301", "desc": "3395"}, {"messageId": "3390", "fix": "4302", "desc": "3392"}, {"messageId": "3393", "fix": "4303", "desc": "3395"}, {"messageId": "3390", "fix": "4304", "desc": "3392"}, {"messageId": "3393", "fix": "4305", "desc": "3395"}, {"messageId": "3390", "fix": "4306", "desc": "3392"}, {"messageId": "3393", "fix": "4307", "desc": "3395"}, {"messageId": "3390", "fix": "4308", "desc": "3392"}, {"messageId": "3393", "fix": "4309", "desc": "3395"}, {"messageId": "3390", "fix": "4310", "desc": "3392"}, {"messageId": "3393", "fix": "4311", "desc": "3395"}, {"messageId": "3390", "fix": "4312", "desc": "3392"}, {"messageId": "3393", "fix": "4313", "desc": "3395"}, [3329, 3373], {"messageId": "3390", "fix": "4314", "desc": "3392"}, {"messageId": "3393", "fix": "4315", "desc": "3395"}, {"messageId": "3390", "fix": "4316", "desc": "3392"}, {"messageId": "3393", "fix": "4317", "desc": "3395"}, {"messageId": "3390", "fix": "4318", "desc": "3392"}, {"messageId": "3393", "fix": "4319", "desc": "3395"}, {"messageId": "3390", "fix": "4320", "desc": "3392"}, {"messageId": "3393", "fix": "4321", "desc": "3395"}, {"messageId": "3390", "fix": "4322", "desc": "3392"}, {"messageId": "3393", "fix": "4323", "desc": "3395"}, {"messageId": "3390", "fix": "4324", "desc": "3392"}, {"messageId": "3393", "fix": "4325", "desc": "3395"}, {"messageId": "3390", "fix": "4326", "desc": "3392"}, {"messageId": "3393", "fix": "4327", "desc": "3395"}, {"messageId": "3390", "fix": "4328", "desc": "3392"}, {"messageId": "3393", "fix": "4329", "desc": "3395"}, {"messageId": "3390", "fix": "4330", "desc": "3392"}, {"messageId": "3393", "fix": "4331", "desc": "3395"}, {"messageId": "3390", "fix": "4332", "desc": "3392"}, {"messageId": "3393", "fix": "4333", "desc": "3395"}, {"messageId": "3390", "fix": "4334", "desc": "3392"}, {"messageId": "3393", "fix": "4335", "desc": "3395"}, {"messageId": "3390", "fix": "4336", "desc": "3392"}, {"messageId": "3393", "fix": "4337", "desc": "3395"}, {"messageId": "3390", "fix": "4338", "desc": "3392"}, {"messageId": "3393", "fix": "4339", "desc": "3395"}, {"messageId": "3390", "fix": "4340", "desc": "3392"}, {"messageId": "3393", "fix": "4341", "desc": "3395"}, {"messageId": "3390", "fix": "4342", "desc": "3392"}, {"messageId": "3393", "fix": "4343", "desc": "3395"}, {"messageId": "3390", "fix": "4344", "desc": "3392"}, {"messageId": "3393", "fix": "4345", "desc": "3395"}, {"messageId": "3390", "fix": "4346", "desc": "3392"}, {"messageId": "3393", "fix": "4347", "desc": "3395"}, {"messageId": "3390", "fix": "4348", "desc": "3392"}, {"messageId": "3393", "fix": "4349", "desc": "3395"}, {"messageId": "3390", "fix": "4350", "desc": "3392"}, {"messageId": "3393", "fix": "4351", "desc": "3395"}, {"messageId": "3390", "fix": "4352", "desc": "3392"}, {"messageId": "3393", "fix": "4353", "desc": "3395"}, {"messageId": "3390", "fix": "4354", "desc": "3392"}, {"messageId": "3393", "fix": "4355", "desc": "3395"}, {"messageId": "3390", "fix": "4356", "desc": "3392"}, {"messageId": "3393", "fix": "4357", "desc": "3395"}, {"messageId": "3390", "fix": "4358", "desc": "3392"}, {"messageId": "3393", "fix": "4359", "desc": "3395"}, {"messageId": "3390", "fix": "4360", "desc": "3392"}, {"messageId": "3393", "fix": "4361", "desc": "3395"}, {"messageId": "3390", "fix": "4362", "desc": "3392"}, {"messageId": "3393", "fix": "4363", "desc": "3395"}, {"messageId": "3390", "fix": "4364", "desc": "3392"}, {"messageId": "3393", "fix": "4365", "desc": "3395"}, {"messageId": "3390", "fix": "4366", "desc": "3392"}, {"messageId": "3393", "fix": "4367", "desc": "3395"}, {"messageId": "3390", "fix": "4368", "desc": "3392"}, {"messageId": "3393", "fix": "4369", "desc": "3395"}, {"messageId": "3390", "fix": "4370", "desc": "3392"}, {"messageId": "3393", "fix": "4371", "desc": "3395"}, {"messageId": "3390", "fix": "4372", "desc": "3392"}, {"messageId": "3393", "fix": "4373", "desc": "3395"}, {"messageId": "3390", "fix": "4374", "desc": "3392"}, {"messageId": "3393", "fix": "4375", "desc": "3395"}, {"messageId": "3390", "fix": "4376", "desc": "3392"}, {"messageId": "3393", "fix": "4377", "desc": "3395"}, {"messageId": "3390", "fix": "4378", "desc": "3392"}, {"messageId": "3393", "fix": "4379", "desc": "3395"}, {"messageId": "3390", "fix": "4380", "desc": "3392"}, {"messageId": "3393", "fix": "4381", "desc": "3395"}, {"messageId": "3390", "fix": "4382", "desc": "3392"}, {"messageId": "3393", "fix": "4383", "desc": "3395"}, {"messageId": "3390", "fix": "4384", "desc": "3392"}, {"messageId": "3393", "fix": "4385", "desc": "3395"}, {"messageId": "3390", "fix": "4386", "desc": "3392"}, {"messageId": "3393", "fix": "4387", "desc": "3395"}, {"messageId": "3390", "fix": "4388", "desc": "3392"}, {"messageId": "3393", "fix": "4389", "desc": "3395"}, {"messageId": "3390", "fix": "4390", "desc": "3392"}, {"messageId": "3393", "fix": "4391", "desc": "3395"}, {"messageId": "3390", "fix": "4392", "desc": "3392"}, {"messageId": "3393", "fix": "4393", "desc": "3395"}, {"messageId": "3390", "fix": "4394", "desc": "3392"}, {"messageId": "3393", "fix": "4395", "desc": "3395"}, {"messageId": "3390", "fix": "4396", "desc": "3392"}, {"messageId": "3393", "fix": "4397", "desc": "3395"}, {"messageId": "3390", "fix": "4398", "desc": "3392"}, {"messageId": "3393", "fix": "4399", "desc": "3395"}, {"messageId": "3390", "fix": "4400", "desc": "3392"}, {"messageId": "3393", "fix": "4401", "desc": "3395"}, {"messageId": "3390", "fix": "4402", "desc": "3392"}, {"messageId": "3393", "fix": "4403", "desc": "3395"}, {"messageId": "3390", "fix": "4404", "desc": "3392"}, {"messageId": "3393", "fix": "4405", "desc": "3395"}, {"messageId": "3390", "fix": "4406", "desc": "3392"}, {"messageId": "3393", "fix": "4407", "desc": "3395"}, {"messageId": "3390", "fix": "4408", "desc": "3392"}, {"messageId": "3393", "fix": "4409", "desc": "3395"}, {"messageId": "3390", "fix": "4410", "desc": "3392"}, {"messageId": "3393", "fix": "4411", "desc": "3395"}, {"messageId": "3390", "fix": "4412", "desc": "3392"}, {"messageId": "3393", "fix": "4413", "desc": "3395"}, {"messageId": "3390", "fix": "4414", "desc": "3392"}, {"messageId": "3393", "fix": "4415", "desc": "3395"}, {"messageId": "3390", "fix": "4416", "desc": "3392"}, {"messageId": "3393", "fix": "4417", "desc": "3395"}, {"messageId": "3390", "fix": "4418", "desc": "3392"}, {"messageId": "3393", "fix": "4419", "desc": "3395"}, {"messageId": "3390", "fix": "4420", "desc": "3392"}, {"messageId": "3393", "fix": "4421", "desc": "3395"}, {"messageId": "3390", "fix": "4422", "desc": "3392"}, {"messageId": "3393", "fix": "4423", "desc": "3395"}, {"messageId": "3390", "fix": "4424", "desc": "3392"}, {"messageId": "3393", "fix": "4425", "desc": "3395"}, {"messageId": "3390", "fix": "4426", "desc": "3392"}, {"messageId": "3393", "fix": "4427", "desc": "3395"}, {"messageId": "3390", "fix": "4428", "desc": "3392"}, {"messageId": "3393", "fix": "4429", "desc": "3395"}, [3479, 3566], "const memberId = rowData['member id'] || rowData['memberid'] || rowData['id'] || childId;", [4165, 4260], "const rowUplineId = rowData['upline id'] || rowData['uplineid'] || rowData['upline'] || uplineId;", {"messageId": "3390", "fix": "4430", "desc": "3392"}, {"messageId": "3393", "fix": "4431", "desc": "3395"}, {"messageId": "3390", "fix": "4432", "desc": "3392"}, {"messageId": "3393", "fix": "4433", "desc": "3395"}, {"messageId": "3390", "fix": "4434", "desc": "3392"}, {"messageId": "3393", "fix": "4435", "desc": "3395"}, {"messageId": "3390", "fix": "4436", "desc": "3392"}, {"messageId": "3393", "fix": "4437", "desc": "3395"}, {"messageId": "3390", "fix": "4438", "desc": "3392"}, {"messageId": "3393", "fix": "4439", "desc": "3395"}, {"messageId": "3390", "fix": "4440", "desc": "3392"}, {"messageId": "3393", "fix": "4441", "desc": "3395"}, {"messageId": "3390", "fix": "4442", "desc": "3392"}, {"messageId": "3393", "fix": "4443", "desc": "3395"}, {"messageId": "3390", "fix": "4444", "desc": "3392"}, {"messageId": "3393", "fix": "4445", "desc": "3395"}, {"messageId": "3390", "fix": "4446", "desc": "3392"}, {"messageId": "3393", "fix": "4447", "desc": "3395"}, {"messageId": "3390", "fix": "4448", "desc": "3392"}, {"messageId": "3393", "fix": "4449", "desc": "3395"}, {"messageId": "3390", "fix": "4450", "desc": "3392"}, {"messageId": "3393", "fix": "4451", "desc": "3395"}, {"messageId": "3390", "fix": "4452", "desc": "3392"}, {"messageId": "3393", "fix": "4453", "desc": "3395"}, {"messageId": "3390", "fix": "4454", "desc": "3392"}, {"messageId": "3393", "fix": "4455", "desc": "3395"}, {"messageId": "3390", "fix": "4456", "desc": "3392"}, {"messageId": "3393", "fix": "4457", "desc": "3395"}, {"messageId": "3390", "fix": "4458", "desc": "3392"}, {"messageId": "3393", "fix": "4459", "desc": "3395"}, {"messageId": "3390", "fix": "4460", "desc": "3392"}, {"messageId": "3393", "fix": "4461", "desc": "3395"}, {"messageId": "3390", "fix": "4462", "desc": "3392"}, {"messageId": "3393", "fix": "4463", "desc": "3395"}, {"messageId": "3390", "fix": "4464", "desc": "3392"}, {"messageId": "3393", "fix": "4465", "desc": "3395"}, {"messageId": "3390", "fix": "4466", "desc": "3392"}, {"messageId": "3393", "fix": "4467", "desc": "3395"}, {"messageId": "3390", "fix": "4468", "desc": "3392"}, {"messageId": "3393", "fix": "4469", "desc": "3395"}, {"messageId": "3390", "fix": "4470", "desc": "3392"}, {"messageId": "3393", "fix": "4471", "desc": "3395"}, {"messageId": "3390", "fix": "4472", "desc": "3392"}, {"messageId": "3393", "fix": "4473", "desc": "3395"}, {"messageId": "3390", "fix": "4474", "desc": "3392"}, {"messageId": "3393", "fix": "4475", "desc": "3395"}, "replaceWithAlt", {"alt": "4476"}, {"range": "4477", "text": "4478"}, "Replace with `&apos;`.", {"alt": "4479"}, {"range": "4480", "text": "4481"}, "Replace with `&lsquo;`.", {"alt": "4482"}, {"range": "4483", "text": "4484"}, "Replace with `&#39;`.", {"alt": "4485"}, {"range": "4486", "text": "4487"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "4488", "text": "4489"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "4490", "text": "4491"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "4492"}, {"range": "4493", "text": "4494"}, "Replace with `&quot;`.", {"alt": "4495"}, {"range": "4496", "text": "4497"}, "Replace with `&ldquo;`.", {"alt": "4498"}, {"range": "4499", "text": "4500"}, "Replace with `&#34;`.", {"alt": "4501"}, {"range": "4502", "text": "4503"}, "Replace with `&rdquo;`.", {"alt": "4492"}, {"range": "4504", "text": "4505"}, {"alt": "4495"}, {"range": "4506", "text": "4507"}, {"alt": "4498"}, {"range": "4508", "text": "4509"}, {"alt": "4501"}, {"range": "4510", "text": "4511"}, {"range": "4512", "text": "4489"}, {"range": "4513", "text": "4491"}, {"range": "4514", "text": "4489"}, {"range": "4515", "text": "4491"}, {"alt": "4492"}, {"range": "4516", "text": "4517"}, {"alt": "4495"}, {"range": "4518", "text": "4519"}, {"alt": "4498"}, {"range": "4520", "text": "4521"}, {"alt": "4501"}, {"range": "4522", "text": "4523"}, {"alt": "4492"}, {"range": "4524", "text": "4525"}, {"alt": "4495"}, {"range": "4526", "text": "4527"}, {"alt": "4498"}, {"range": "4528", "text": "4529"}, {"alt": "4501"}, {"range": "4530", "text": "4531"}, {"range": "4532", "text": "4489"}, {"range": "4533", "text": "4491"}, "Update the dependencies array to be: [status, search, activeFilter, sortBy, sortOrder, fetchProducts]", {"range": "4534", "text": "4535"}, {"range": "4536", "text": "4489"}, {"range": "4537", "text": "4491"}, {"range": "4538", "text": "4489"}, {"range": "4539", "text": "4491"}, {"alt": "4492"}, {"range": "4540", "text": "4541"}, {"alt": "4495"}, {"range": "4542", "text": "4543"}, {"alt": "4498"}, {"range": "4544", "text": "4545"}, {"alt": "4501"}, {"range": "4546", "text": "4547"}, {"alt": "4492"}, {"range": "4548", "text": "4549"}, {"alt": "4495"}, {"range": "4550", "text": "4551"}, {"alt": "4498"}, {"range": "4552", "text": "4553"}, {"alt": "4501"}, {"range": "4554", "text": "4555"}, "Update the dependencies array to be: [checkAdminStatus, status]", {"range": "4556", "text": "4557"}, "Update the dependencies array to be: [fetchRebates, status]", {"range": "4558", "text": "4559"}, "Update the dependencies array to be: [isAdmin, page, pageSize, statusFilter, search, dateRange, fetchRebates]", {"range": "4560", "text": "4561"}, {"range": "4562", "text": "4489"}, {"range": "4563", "text": "4491"}, "Update the dependencies array to be: [fetchReportData, status]", {"range": "4564", "text": "4565"}, "Update the dependencies array to be: [status, isAdmin, dateRange, fetchReportData]", {"range": "4566", "text": "4567"}, {"range": "4568", "text": "4489"}, {"range": "4569", "text": "4491"}, {"range": "4570", "text": "4489"}, {"range": "4571", "text": "4491"}, {"range": "4572", "text": "4489"}, {"range": "4573", "text": "4491"}, {"range": "4574", "text": "4489"}, {"range": "4575", "text": "4491"}, {"range": "4576", "text": "4489"}, {"range": "4577", "text": "4491"}, {"range": "4578", "text": "4489"}, {"range": "4579", "text": "4491"}, {"range": "4580", "text": "4489"}, {"range": "4581", "text": "4491"}, {"range": "4582", "text": "4489"}, {"range": "4583", "text": "4491"}, {"range": "4584", "text": "4489"}, {"range": "4585", "text": "4491"}, {"range": "4586", "text": "4489"}, {"range": "4587", "text": "4491"}, {"range": "4588", "text": "4489"}, {"range": "4589", "text": "4491"}, {"range": "4590", "text": "4489"}, {"range": "4591", "text": "4491"}, {"range": "4592", "text": "4489"}, {"range": "4593", "text": "4491"}, {"range": "4594", "text": "4489"}, {"range": "4595", "text": "4491"}, {"range": "4596", "text": "4489"}, {"range": "4597", "text": "4491"}, {"range": "4598", "text": "4489"}, {"range": "4599", "text": "4491"}, {"range": "4600", "text": "4489"}, {"range": "4601", "text": "4491"}, {"range": "4602", "text": "4489"}, {"range": "4603", "text": "4491"}, {"range": "4604", "text": "4489"}, {"range": "4605", "text": "4491"}, {"range": "4606", "text": "4489"}, {"range": "4607", "text": "4491"}, {"range": "4608", "text": "4489"}, {"range": "4609", "text": "4491"}, {"range": "4610", "text": "4489"}, {"range": "4611", "text": "4491"}, {"range": "4612", "text": "4489"}, {"range": "4613", "text": "4491"}, {"range": "4614", "text": "4489"}, {"range": "4615", "text": "4491"}, {"range": "4616", "text": "4489"}, {"range": "4617", "text": "4491"}, {"range": "4618", "text": "4489"}, {"range": "4619", "text": "4491"}, {"range": "4620", "text": "4489"}, {"range": "4621", "text": "4491"}, {"range": "4622", "text": "4489"}, {"range": "4623", "text": "4491"}, {"range": "4624", "text": "4489"}, {"range": "4625", "text": "4491"}, {"range": "4626", "text": "4489"}, {"range": "4627", "text": "4491"}, "Update the dependencies array to be: [selectedYear, selectedMonth, status, fetchPerformanceData]", {"range": "4628", "text": "4629"}, {"alt": "4492"}, {"range": "4630", "text": "4631"}, {"alt": "4495"}, {"range": "4632", "text": "4633"}, {"alt": "4498"}, {"range": "4634", "text": "4635"}, {"alt": "4501"}, {"range": "4636", "text": "4637"}, {"alt": "4492"}, {"range": "4638", "text": "4639"}, {"alt": "4495"}, {"range": "4640", "text": "4641"}, {"alt": "4498"}, {"range": "4642", "text": "4643"}, {"alt": "4501"}, {"range": "4644", "text": "4645"}, {"range": "4646", "text": "4489"}, {"range": "4647", "text": "4491"}, {"range": "4648", "text": "4489"}, {"range": "4649", "text": "4491"}, {"range": "4650", "text": "4489"}, {"range": "4651", "text": "4491"}, {"range": "4652", "text": "4489"}, {"range": "4653", "text": "4491"}, {"range": "4654", "text": "4489"}, {"range": "4655", "text": "4491"}, {"range": "4656", "text": "4489"}, {"range": "4657", "text": "4491"}, {"range": "4658", "text": "4489"}, {"range": "4659", "text": "4491"}, {"alt": "4476"}, {"range": "4660", "text": "4661"}, {"alt": "4479"}, {"range": "4662", "text": "4663"}, {"alt": "4482"}, {"range": "4664", "text": "4665"}, {"alt": "4485"}, {"range": "4666", "text": "4667"}, {"alt": "4476"}, {"range": "4668", "text": "4669"}, {"alt": "4479"}, {"range": "4670", "text": "4671"}, {"alt": "4482"}, {"range": "4672", "text": "4673"}, {"alt": "4485"}, {"range": "4674", "text": "4675"}, {"alt": "4476"}, {"range": "4676", "text": "4677"}, {"alt": "4479"}, {"range": "4678", "text": "4679"}, {"alt": "4482"}, {"range": "4680", "text": "4681"}, {"alt": "4485"}, {"range": "4682", "text": "4683"}, {"alt": "4476"}, {"range": "4684", "text": "4685"}, {"alt": "4479"}, {"range": "4686", "text": "4687"}, {"alt": "4482"}, {"range": "4688", "text": "4689"}, {"alt": "4485"}, {"range": "4690", "text": "4691"}, {"alt": "4492"}, {"range": "4692", "text": "4693"}, {"alt": "4495"}, {"range": "4694", "text": "4695"}, {"alt": "4498"}, {"range": "4696", "text": "4697"}, {"alt": "4501"}, {"range": "4698", "text": "4699"}, {"alt": "4476"}, {"range": "4700", "text": "4701"}, {"alt": "4479"}, {"range": "4702", "text": "4703"}, {"alt": "4482"}, {"range": "4704", "text": "4705"}, {"alt": "4485"}, {"range": "4706", "text": "4707"}, {"alt": "4492"}, {"range": "4708", "text": "4709"}, {"alt": "4495"}, {"range": "4710", "text": "4711"}, {"alt": "4498"}, {"range": "4712", "text": "4713"}, {"alt": "4501"}, {"range": "4714", "text": "4715"}, {"alt": "4476"}, {"range": "4716", "text": "4717"}, {"alt": "4479"}, {"range": "4718", "text": "4719"}, {"alt": "4482"}, {"range": "4720", "text": "4721"}, {"alt": "4485"}, {"range": "4722", "text": "4723"}, {"alt": "4476"}, {"range": "4724", "text": "4725"}, {"alt": "4479"}, {"range": "4726", "text": "4727"}, {"alt": "4482"}, {"range": "4728", "text": "4729"}, {"alt": "4485"}, {"range": "4730", "text": "4731"}, {"alt": "4476"}, {"range": "4732", "text": "4733"}, {"alt": "4479"}, {"range": "4734", "text": "4735"}, {"alt": "4482"}, {"range": "4736", "text": "4737"}, {"alt": "4485"}, {"range": "4738", "text": "4739"}, {"range": "4740", "text": "4489"}, {"range": "4741", "text": "4491"}, {"range": "4742", "text": "4489"}, {"range": "4743", "text": "4491"}, {"range": "4744", "text": "4489"}, {"range": "4745", "text": "4491"}, {"range": "4746", "text": "4489"}, {"range": "4747", "text": "4491"}, {"range": "4748", "text": "4489"}, {"range": "4749", "text": "4491"}, {"range": "4750", "text": "4489"}, {"range": "4751", "text": "4491"}, {"alt": "4476"}, {"range": "4752", "text": "4753"}, {"alt": "4479"}, {"range": "4754", "text": "4755"}, {"alt": "4482"}, {"range": "4756", "text": "4757"}, {"alt": "4485"}, {"range": "4758", "text": "4759"}, {"range": "4760", "text": "4489"}, {"range": "4761", "text": "4491"}, {"range": "4762", "text": "4489"}, {"range": "4763", "text": "4491"}, {"alt": "4476"}, {"range": "4764", "text": "4765"}, {"alt": "4479"}, {"range": "4766", "text": "4767"}, {"alt": "4482"}, {"range": "4768", "text": "4769"}, {"alt": "4485"}, {"range": "4770", "text": "4771"}, {"alt": "4476"}, {"range": "4772", "text": "4773"}, {"alt": "4479"}, {"range": "4774", "text": "4775"}, {"alt": "4482"}, {"range": "4776", "text": "4777"}, {"alt": "4485"}, {"range": "4778", "text": "4779"}, {"alt": "4476"}, {"range": "4780", "text": "4781"}, {"alt": "4479"}, {"range": "4782", "text": "4783"}, {"alt": "4482"}, {"range": "4784", "text": "4785"}, {"alt": "4485"}, {"range": "4786", "text": "4787"}, {"alt": "4476"}, {"range": "4788", "text": "4789"}, {"alt": "4479"}, {"range": "4790", "text": "4791"}, {"alt": "4482"}, {"range": "4792", "text": "4793"}, {"alt": "4485"}, {"range": "4794", "text": "4795"}, {"alt": "4476"}, {"range": "4796", "text": "4797"}, {"alt": "4479"}, {"range": "4798", "text": "4799"}, {"alt": "4482"}, {"range": "4800", "text": "4801"}, {"alt": "4485"}, {"range": "4802", "text": "4803"}, {"alt": "4476"}, {"range": "4804", "text": "4805"}, {"alt": "4479"}, {"range": "4806", "text": "4807"}, {"alt": "4482"}, {"range": "4808", "text": "4809"}, {"alt": "4485"}, {"range": "4810", "text": "4811"}, {"alt": "4476"}, {"range": "4812", "text": "4813"}, {"alt": "4479"}, {"range": "4814", "text": "4815"}, {"alt": "4482"}, {"range": "4816", "text": "4817"}, {"alt": "4485"}, {"range": "4818", "text": "4819"}, {"range": "4820", "text": "4489"}, {"range": "4821", "text": "4491"}, {"alt": "4476"}, {"range": "4822", "text": "4823"}, {"alt": "4479"}, {"range": "4824", "text": "4825"}, {"alt": "4482"}, {"range": "4826", "text": "4827"}, {"alt": "4485"}, {"range": "4828", "text": "4829"}, {"alt": "4476"}, {"range": "4830", "text": "4831"}, {"alt": "4479"}, {"range": "4832", "text": "4833"}, {"alt": "4482"}, {"range": "4834", "text": "4835"}, {"alt": "4485"}, {"range": "4836", "text": "4837"}, {"alt": "4476"}, {"range": "4838", "text": "4839"}, {"alt": "4479"}, {"range": "4840", "text": "4841"}, {"alt": "4482"}, {"range": "4842", "text": "4843"}, {"alt": "4485"}, {"range": "4844", "text": "4845"}, {"alt": "4476"}, {"range": "4846", "text": "4847"}, {"alt": "4479"}, {"range": "4848", "text": "4849"}, {"alt": "4482"}, {"range": "4850", "text": "4851"}, {"alt": "4485"}, {"range": "4852", "text": "4853"}, {"alt": "4492"}, {"range": "4854", "text": "4855"}, {"alt": "4495"}, {"range": "4856", "text": "4857"}, {"alt": "4498"}, {"range": "4858", "text": "4859"}, {"alt": "4501"}, {"range": "4860", "text": "4861"}, {"alt": "4492"}, {"range": "4862", "text": "4863"}, {"alt": "4495"}, {"range": "4864", "text": "4865"}, {"alt": "4498"}, {"range": "4866", "text": "4867"}, {"alt": "4501"}, {"range": "4868", "text": "4869"}, {"alt": "4492"}, {"range": "4870", "text": "4871"}, {"alt": "4495"}, {"range": "4872", "text": "4873"}, {"alt": "4498"}, {"range": "4874", "text": "4875"}, {"alt": "4501"}, {"range": "4876", "text": "4877"}, {"alt": "4492"}, {"range": "4878", "text": "4879"}, {"alt": "4495"}, {"range": "4880", "text": "4881"}, {"alt": "4498"}, {"range": "4882", "text": "4883"}, {"alt": "4501"}, {"range": "4884", "text": "4885"}, {"alt": "4476"}, {"range": "4886", "text": "4887"}, {"alt": "4479"}, {"range": "4888", "text": "4889"}, {"alt": "4482"}, {"range": "4890", "text": "4891"}, {"alt": "4485"}, {"range": "4892", "text": "4893"}, {"alt": "4476"}, {"range": "4894", "text": "4895"}, {"alt": "4479"}, {"range": "4896", "text": "4897"}, {"alt": "4482"}, {"range": "4898", "text": "4899"}, {"alt": "4485"}, {"range": "4900", "text": "4901"}, {"range": "4902", "text": "4489"}, {"range": "4903", "text": "4491"}, {"alt": "4476"}, {"range": "4904", "text": "4905"}, {"alt": "4479"}, {"range": "4906", "text": "4907"}, {"alt": "4482"}, {"range": "4908", "text": "4909"}, {"alt": "4485"}, {"range": "4910", "text": "4911"}, {"alt": "4476"}, {"range": "4912", "text": "4913"}, {"alt": "4479"}, {"range": "4914", "text": "4915"}, {"alt": "4482"}, {"range": "4916", "text": "4917"}, {"alt": "4485"}, {"range": "4918", "text": "4919"}, "Update the dependencies array to be: [status, maxLevel, targetUserId, fetchGenealogy]", {"range": "4920", "text": "4921"}, {"alt": "4492"}, {"range": "4922", "text": "4923"}, {"alt": "4495"}, {"range": "4924", "text": "4925"}, {"alt": "4498"}, {"range": "4926", "text": "4927"}, {"alt": "4501"}, {"range": "4928", "text": "4929"}, {"alt": "4492"}, {"range": "4930", "text": "4931"}, {"alt": "4495"}, {"range": "4932", "text": "4933"}, {"alt": "4498"}, {"range": "4934", "text": "4935"}, {"alt": "4501"}, {"range": "4936", "text": "4937"}, {"alt": "4476"}, {"range": "4938", "text": "4939"}, {"alt": "4479"}, {"range": "4940", "text": "4941"}, {"alt": "4482"}, {"range": "4942", "text": "4943"}, {"alt": "4485"}, {"range": "4944", "text": "4945"}, {"alt": "4476"}, {"range": "4946", "text": "4947"}, {"alt": "4479"}, {"range": "4948", "text": "4949"}, {"alt": "4482"}, {"range": "4950", "text": "4951"}, {"alt": "4485"}, {"range": "4952", "text": "4953"}, {"alt": "4492"}, {"range": "4954", "text": "4955"}, {"alt": "4495"}, {"range": "4956", "text": "4957"}, {"alt": "4498"}, {"range": "4958", "text": "4959"}, {"alt": "4501"}, {"range": "4960", "text": "4961"}, {"alt": "4492"}, {"range": "4962", "text": "4963"}, {"alt": "4495"}, {"range": "4964", "text": "4965"}, {"alt": "4498"}, {"range": "4966", "text": "4967"}, {"alt": "4501"}, {"range": "4968", "text": "4969"}, {"alt": "4476"}, {"range": "4970", "text": "4971"}, {"alt": "4479"}, {"range": "4972", "text": "4973"}, {"alt": "4482"}, {"range": "4974", "text": "4975"}, {"alt": "4485"}, {"range": "4976", "text": "4977"}, {"range": "4978", "text": "4489"}, {"range": "4979", "text": "4491"}, {"range": "4980", "text": "4489"}, {"range": "4981", "text": "4491"}, {"range": "4982", "text": "4489"}, {"range": "4983", "text": "4491"}, {"range": "4984", "text": "4489"}, {"range": "4985", "text": "4491"}, {"range": "4986", "text": "4489"}, {"range": "4987", "text": "4491"}, {"range": "4988", "text": "4489"}, {"range": "4989", "text": "4491"}, {"range": "4990", "text": "4489"}, {"range": "4991", "text": "4491"}, {"range": "4992", "text": "4489"}, {"range": "4993", "text": "4491"}, {"range": "4994", "text": "4489"}, {"range": "4995", "text": "4491"}, {"range": "4996", "text": "4489"}, {"range": "4997", "text": "4491"}, {"range": "4998", "text": "4489"}, {"range": "4999", "text": "4491"}, {"range": "5000", "text": "4489"}, {"range": "5001", "text": "4491"}, {"range": "5002", "text": "4489"}, {"range": "5003", "text": "4491"}, {"range": "5004", "text": "4489"}, {"range": "5005", "text": "4491"}, {"range": "5006", "text": "4489"}, {"range": "5007", "text": "4491"}, {"range": "5008", "text": "4489"}, {"range": "5009", "text": "4491"}, {"alt": "4476"}, {"range": "5010", "text": "5011"}, {"alt": "4479"}, {"range": "5012", "text": "5013"}, {"alt": "4482"}, {"range": "5014", "text": "5015"}, {"alt": "4485"}, {"range": "5016", "text": "5017"}, {"alt": "4476"}, {"range": "5018", "text": "5019"}, {"alt": "4479"}, {"range": "5020", "text": "5021"}, {"alt": "4482"}, {"range": "5022", "text": "5023"}, {"alt": "4485"}, {"range": "5024", "text": "5025"}, "Update the dependencies array to be: [fetchProfile, status]", {"range": "5026", "text": "5027"}, {"range": "5028", "text": "4489"}, {"range": "5029", "text": "4491"}, {"range": "5030", "text": "4489"}, {"range": "5031", "text": "4491"}, {"range": "5032", "text": "4489"}, {"range": "5033", "text": "4491"}, {"range": "5034", "text": "4489"}, {"range": "5035", "text": "4491"}, {"alt": "4476"}, {"range": "5036", "text": "5037"}, {"alt": "4479"}, {"range": "5038", "text": "5039"}, {"alt": "4482"}, {"range": "5040", "text": "5041"}, {"alt": "4485"}, {"range": "5042", "text": "5043"}, "Update the dependencies array to be: [status, pagination.offset, pagination.limit, fetchPurchases]", {"range": "5044", "text": "5045"}, {"alt": "4476"}, {"range": "5046", "text": "5047"}, {"alt": "4479"}, {"range": "5048", "text": "5049"}, {"alt": "4482"}, {"range": "5050", "text": "5051"}, {"alt": "4485"}, {"range": "5052", "text": "5053"}, {"range": "5054", "text": "4489"}, {"range": "5055", "text": "4491"}, "Update the dependencies array to be: [status, filter, fetchReferralCommissions]", {"range": "5056", "text": "5057"}, {"alt": "4476"}, {"range": "5058", "text": "5059"}, {"alt": "4479"}, {"range": "5060", "text": "5061"}, {"alt": "4482"}, {"range": "5062", "text": "5063"}, {"alt": "4485"}, {"range": "5064", "text": "5065"}, {"range": "5066", "text": "4489"}, {"range": "5067", "text": "4491"}, "Update the dependencies array to be: [formData, currentStep, validateStep]", {"range": "5068", "text": "5069"}, {"range": "5070", "text": "4489"}, {"range": "5071", "text": "4491"}, {"alt": "4492"}, {"range": "5072", "text": "5073"}, {"alt": "4495"}, {"range": "5074", "text": "5075"}, {"alt": "4498"}, {"range": "5076", "text": "5077"}, {"alt": "4501"}, {"range": "5078", "text": "5079"}, {"alt": "4476"}, {"range": "5080", "text": "5081"}, {"alt": "4479"}, {"range": "5082", "text": "5083"}, {"alt": "4482"}, {"range": "5084", "text": "5085"}, {"alt": "4485"}, {"range": "5086", "text": "5087"}, {"alt": "4492"}, {"range": "5088", "text": "5089"}, {"alt": "4495"}, {"range": "5090", "text": "5091"}, {"alt": "4498"}, {"range": "5092", "text": "5093"}, {"alt": "4501"}, {"range": "5094", "text": "5095"}, {"alt": "4476"}, {"range": "5096", "text": "5097"}, {"alt": "4479"}, {"range": "5098", "text": "5099"}, {"alt": "4482"}, {"range": "5100", "text": "5101"}, {"alt": "4485"}, {"range": "5102", "text": "5103"}, {"range": "5104", "text": "4489"}, {"range": "5105", "text": "4491"}, "Update the dependencies array to be: [fetchProduct, params.id]", {"range": "5106", "text": "5107"}, {"range": "5108", "text": "4489"}, {"range": "5109", "text": "4491"}, {"range": "5110", "text": "4489"}, {"range": "5111", "text": "4491"}, {"range": "5112", "text": "4489"}, {"range": "5113", "text": "4491"}, {"range": "5114", "text": "4489"}, {"range": "5115", "text": "4491"}, {"range": "5116", "text": "4489"}, {"range": "5117", "text": "4491"}, {"range": "5118", "text": "4489"}, {"range": "5119", "text": "4491"}, {"range": "5120", "text": "4489"}, {"range": "5121", "text": "4491"}, {"range": "5122", "text": "4489"}, {"range": "5123", "text": "4491"}, {"range": "5124", "text": "4489"}, {"range": "5125", "text": "4491"}, {"range": "5126", "text": "4489"}, {"range": "5127", "text": "4491"}, {"range": "5128", "text": "4489"}, {"range": "5129", "text": "4491"}, "Update the dependencies array to be: [fetchInventoryTransactions]", {"range": "5130", "text": "5131"}, {"range": "5132", "text": "4489"}, {"range": "5133", "text": "4491"}, {"range": "5134", "text": "4489"}, {"range": "5135", "text": "4491"}, {"range": "5136", "text": "4489"}, {"range": "5137", "text": "4491"}, {"range": "5138", "text": "4489"}, {"range": "5139", "text": "4491"}, {"range": "5140", "text": "4489"}, {"range": "5141", "text": "4491"}, {"range": "5142", "text": "4489"}, {"range": "5143", "text": "4491"}, {"range": "5144", "text": "4489"}, {"range": "5145", "text": "4491"}, {"alt": "4476"}, {"range": "5146", "text": "5147"}, {"alt": "4479"}, {"range": "5148", "text": "5149"}, {"alt": "4482"}, {"range": "5150", "text": "5151"}, {"alt": "4485"}, {"range": "5152", "text": "5153"}, "Update the dependencies array to be: [activeTab, fetchAuditLogs, fetchSalesHistory]", {"range": "5154", "text": "5155"}, {"range": "5156", "text": "4489"}, {"range": "5157", "text": "4491"}, {"range": "5158", "text": "4489"}, {"range": "5159", "text": "4491"}, {"range": "5160", "text": "4489"}, {"range": "5161", "text": "4491"}, {"alt": "4476"}, {"range": "5162", "text": "5163"}, {"alt": "4479"}, {"range": "5164", "text": "5165"}, {"alt": "4482"}, {"range": "5166", "text": "5167"}, {"alt": "4485"}, {"range": "5168", "text": "5169"}, {"alt": "4476"}, {"range": "5170", "text": "5147"}, {"alt": "4479"}, {"range": "5171", "text": "5149"}, {"alt": "4482"}, {"range": "5172", "text": "5151"}, {"alt": "4485"}, {"range": "5173", "text": "5153"}, {"range": "5174", "text": "4489"}, {"range": "5175", "text": "4491"}, {"range": "5176", "text": "4489"}, {"range": "5177", "text": "4491"}, {"range": "5178", "text": "4489"}, {"range": "5179", "text": "4491"}, "Update the dependencies array to be: [quantity, maxLevel, simulateRebates]", {"range": "5180", "text": "5181"}, {"range": "5182", "text": "4489"}, {"range": "5183", "text": "4491"}, {"range": "5184", "text": "4489"}, {"range": "5185", "text": "4491"}, {"range": "5186", "text": "4489"}, {"range": "5187", "text": "4491"}, {"range": "5188", "text": "4489"}, {"range": "5189", "text": "4491"}, {"range": "5190", "text": "4489"}, {"range": "5191", "text": "4491"}, "Update the dependencies array to be: [environment, fetchTestUsers]", {"range": "5192", "text": "5193"}, "Update the dependencies array to be: [pagination.page, pagination.pageSize, search, rankFilter, fetchUsers]", {"range": "5194", "text": "5195"}, {"range": "5196", "text": "4489"}, {"range": "5197", "text": "4491"}, {"range": "5198", "text": "4489"}, {"range": "5199", "text": "4491"}, "Update the dependencies array to be: [fetchAuditLogs]", {"range": "5200", "text": "5201"}, {"range": "5202", "text": "4489"}, {"range": "5203", "text": "4491"}, {"range": "5204", "text": "4489"}, {"range": "5205", "text": "4491"}, {"range": "5206", "text": "4489"}, {"range": "5207", "text": "4491"}, {"range": "5208", "text": "4489"}, {"range": "5209", "text": "4491"}, {"range": "5210", "text": "4489"}, {"range": "5211", "text": "4491"}, {"range": "5212", "text": "4489"}, {"range": "5213", "text": "4491"}, {"range": "5214", "text": "4489"}, {"range": "5215", "text": "4491"}, {"range": "5216", "text": "4489"}, {"range": "5217", "text": "4491"}, "Update the dependencies array to be: [maxLevel, userId, filterOptions.rankId, filterOptions.sortBy, filterOptions.sortDirection, searchQuery, transformDataToFlow, setNodes, setEdges]", {"range": "5218", "text": "5219"}, {"range": "5220", "text": "4489"}, {"range": "5221", "text": "4491"}, {"range": "5222", "text": "4489"}, {"range": "5223", "text": "4491"}, {"range": "5224", "text": "4489"}, {"range": "5225", "text": "4491"}, "Update the dependencies array to be: [maxLevel, userId, transformDataToFlow, setNodes, setEdges, reactFlowInstance]", {"range": "5226", "text": "5227"}, {"range": "5228", "text": "4489"}, {"range": "5229", "text": "4491"}, "Update the dependencies array to be: [allowEditing, handleEditUser, handleDeleteUser, handleAddUser, visualOptions, handleExpandNode, processChildren]", {"range": "5230", "text": "5231"}, {"range": "5232", "text": "4489"}, {"range": "5233", "text": "4491"}, "Update the dependencies array to be: [processChild, visualOptions.layout, visualOptions.levelSpacing, visualOptions.nodeSpacing, visualOptions.nodeWidth]", {"range": "5234", "text": "5235"}, {"range": "5236", "text": "4489"}, {"range": "5237", "text": "4491"}, "Update the dependencies array to be: [allowEditing, handleEditUser, handleDeleteUser, handleAddUser, expandedNodes, visualOptions, draggedNodes, getConnectionType, handleExpandNode, processChildren]", {"range": "5238", "text": "5239"}, "Update the dependencies array to be: [allowEditing, visualOptions.nodeWidth, visualOptions.nodeHeight, visualOptions.connectionWidth, visualOptions.connectionStyle, nodes, isDescendantOf, edges, setEdges, getConnectionType]", {"range": "5240", "text": "5241"}, "Update the dependencies array to be: [maxLevel, userId, filterOptions.rankId, filterOptions.sortBy, filterOptions.sortDirection, searchQuery, transformDataToFlow, setNodes, setEdges, reactFlowInstance]", {"range": "5242", "text": "5243"}, {"range": "5244", "text": "4489"}, {"range": "5245", "text": "4491"}, "Update the dependencies array to be: [handleExpandNode, processChildren]", {"range": "5246", "text": "5247"}, {"range": "5248", "text": "4489"}, {"range": "5249", "text": "4491"}, "Update the dependencies array to be: [layout, processChild]", {"range": "5250", "text": "5251"}, {"range": "5252", "text": "4489"}, {"range": "5253", "text": "4491"}, "Update the dependencies array to be: [expandedNodes, handleExpandNode, layout, processChildren]", {"range": "5254", "text": "5255"}, {"range": "5256", "text": "4489"}, {"range": "5257", "text": "4491"}, {"range": "5258", "text": "4489"}, {"range": "5259", "text": "4491"}, {"range": "5260", "text": "4489"}, {"range": "5261", "text": "4491"}, {"range": "5262", "text": "4489"}, {"range": "5263", "text": "4491"}, {"range": "5264", "text": "4489"}, {"range": "5265", "text": "4491"}, {"range": "5266", "text": "4489"}, {"range": "5267", "text": "4491"}, {"range": "5268", "text": "4489"}, {"range": "5269", "text": "4491"}, {"range": "5270", "text": "4489"}, {"range": "5271", "text": "4491"}, {"range": "5272", "text": "4489"}, {"range": "5273", "text": "4491"}, {"range": "5274", "text": "4489"}, {"range": "5275", "text": "4491"}, {"range": "5276", "text": "4489"}, {"range": "5277", "text": "4491"}, {"range": "5278", "text": "4489"}, {"range": "5279", "text": "4491"}, {"range": "5280", "text": "4489"}, {"range": "5281", "text": "4491"}, {"range": "5282", "text": "4489"}, {"range": "5283", "text": "4491"}, {"range": "5284", "text": "4489"}, {"range": "5285", "text": "4491"}, {"range": "5286", "text": "4489"}, {"range": "5287", "text": "4491"}, {"range": "5288", "text": "4489"}, {"range": "5289", "text": "4491"}, {"range": "5290", "text": "4489"}, {"range": "5291", "text": "4491"}, {"range": "5292", "text": "4489"}, {"range": "5293", "text": "4491"}, {"range": "5294", "text": "4489"}, {"range": "5295", "text": "4491"}, {"range": "5296", "text": "4489"}, {"range": "5297", "text": "4491"}, "Update the dependencies array to be: [userId, initialPageSize, transformDataToFlow, setNodes, setEdges, reactFlowInstance]", {"range": "5298", "text": "5299"}, {"range": "5300", "text": "4489"}, {"range": "5301", "text": "4491"}, {"range": "5302", "text": "4489"}, {"range": "5303", "text": "4491"}, {"range": "5304", "text": "4489"}, {"range": "5305", "text": "4491"}, "Update the dependencies array to be: [loadedNodes, initialPageSize, nodes, setNodes, setEdges, visualOptions, expandedNodes, getConnectionType, handleExpandNode]", {"range": "5306", "text": "5307"}, {"range": "5308", "text": "4489"}, {"range": "5309", "text": "4491"}, "Update the dependencies array to be: [expandedNodes, handleExpandNode, visualOptions]", {"range": "5310", "text": "5311"}, {"range": "5312", "text": "4489"}, {"range": "5313", "text": "4491"}, {"range": "5314", "text": "4489"}, {"range": "5315", "text": "4491"}, {"range": "5316", "text": "4489"}, {"range": "5317", "text": "4491"}, "Update the dependencies array to be: [fetchPaymentMethods]", {"range": "5318", "text": "5319"}, {"range": "5320", "text": "4489"}, {"range": "5321", "text": "4491"}, {"alt": "4476"}, {"range": "5322", "text": "5323"}, {"alt": "4479"}, {"range": "5324", "text": "5325"}, {"alt": "4482"}, {"range": "5326", "text": "5327"}, {"alt": "4485"}, {"range": "5328", "text": "5329"}, {"range": "5330", "text": "4489"}, {"range": "5331", "text": "4491"}, {"range": "5332", "text": "4489"}, {"range": "5333", "text": "4491"}, "Update the dependencies array to be: [fetchShippingMethods]", {"range": "5334", "text": "5335"}, "Update the dependencies array to be: [handleSelectMethod, selectedMethodId, shippingMethods]", {"range": "5336", "text": "5337"}, {"range": "5338", "text": "4489"}, {"range": "5339", "text": "4491"}, {"range": "5340", "text": "4489"}, {"range": "5341", "text": "4491"}, {"range": "5342", "text": "4489"}, {"range": "5343", "text": "4491"}, {"range": "5344", "text": "4489"}, {"range": "5345", "text": "4491"}, {"range": "5346", "text": "4489"}, {"range": "5347", "text": "4491"}, {"range": "5348", "text": "4489"}, {"range": "5349", "text": "4491"}, {"range": "5350", "text": "4489"}, {"range": "5351", "text": "4491"}, {"range": "5352", "text": "4489"}, {"range": "5353", "text": "4491"}, {"range": "5354", "text": "4489"}, {"range": "5355", "text": "4491"}, {"range": "5356", "text": "4489"}, {"range": "5357", "text": "4491"}, {"range": "5358", "text": "4489"}, {"range": "5359", "text": "4491"}, {"range": "5360", "text": "4489"}, {"range": "5361", "text": "4491"}, {"range": "5362", "text": "4489"}, {"range": "5363", "text": "4491"}, {"range": "5364", "text": "4489"}, {"range": "5365", "text": "4491"}, {"range": "5366", "text": "4489"}, {"range": "5367", "text": "4491"}, {"range": "5368", "text": "4489"}, {"range": "5369", "text": "4491"}, {"range": "5370", "text": "4489"}, {"range": "5371", "text": "4491"}, {"range": "5372", "text": "4489"}, {"range": "5373", "text": "4491"}, {"range": "5374", "text": "4489"}, {"range": "5375", "text": "4491"}, {"range": "5376", "text": "4489"}, {"range": "5377", "text": "4491"}, {"range": "5378", "text": "4489"}, {"range": "5379", "text": "4491"}, {"range": "5380", "text": "4489"}, {"range": "5381", "text": "4491"}, {"range": "5382", "text": "4489"}, {"range": "5383", "text": "4491"}, {"range": "5384", "text": "4489"}, {"range": "5385", "text": "4491"}, {"range": "5386", "text": "4489"}, {"range": "5387", "text": "4491"}, {"range": "5388", "text": "4489"}, {"range": "5389", "text": "4491"}, {"range": "5390", "text": "4489"}, {"range": "5391", "text": "4491"}, {"range": "5392", "text": "4489"}, {"range": "5393", "text": "4491"}, {"range": "5394", "text": "4489"}, {"range": "5395", "text": "4491"}, {"range": "5396", "text": "4489"}, {"range": "5397", "text": "4491"}, {"range": "5398", "text": "4489"}, {"range": "5399", "text": "4491"}, {"range": "5400", "text": "4489"}, {"range": "5401", "text": "4491"}, {"range": "5402", "text": "4489"}, {"range": "5403", "text": "4491"}, {"range": "5404", "text": "4489"}, {"range": "5405", "text": "4491"}, {"range": "5406", "text": "4489"}, {"range": "5407", "text": "4491"}, {"range": "5408", "text": "4489"}, {"range": "5409", "text": "4491"}, {"range": "5410", "text": "4489"}, {"range": "5411", "text": "4491"}, {"range": "5412", "text": "4489"}, {"range": "5413", "text": "4491"}, {"range": "5414", "text": "4489"}, {"range": "5415", "text": "4491"}, {"range": "5416", "text": "4489"}, {"range": "5417", "text": "4491"}, {"range": "5418", "text": "4489"}, {"range": "5419", "text": "4491"}, {"range": "5420", "text": "4489"}, {"range": "5421", "text": "4491"}, {"range": "5422", "text": "4489"}, {"range": "5423", "text": "4491"}, {"range": "5424", "text": "4489"}, {"range": "5425", "text": "4491"}, {"range": "5426", "text": "4489"}, {"range": "5427", "text": "4491"}, {"range": "5428", "text": "4489"}, {"range": "5429", "text": "4491"}, {"range": "5430", "text": "4489"}, {"range": "5431", "text": "4491"}, {"range": "5432", "text": "4489"}, {"range": "5433", "text": "4491"}, {"range": "5434", "text": "4489"}, {"range": "5435", "text": "4491"}, {"range": "5436", "text": "4489"}, {"range": "5437", "text": "4491"}, {"range": "5438", "text": "4489"}, {"range": "5439", "text": "4491"}, {"range": "5440", "text": "4489"}, {"range": "5441", "text": "4491"}, {"range": "5442", "text": "4489"}, {"range": "5443", "text": "4491"}, {"range": "5444", "text": "4489"}, {"range": "5445", "text": "4491"}, {"range": "5446", "text": "4489"}, {"range": "5447", "text": "4491"}, {"range": "5448", "text": "4489"}, {"range": "5449", "text": "4491"}, {"range": "5450", "text": "4489"}, {"range": "5451", "text": "4491"}, {"range": "5452", "text": "4489"}, {"range": "5453", "text": "4491"}, {"range": "5454", "text": "4489"}, {"range": "5455", "text": "4491"}, {"range": "5456", "text": "4489"}, {"range": "5457", "text": "4491"}, {"range": "5458", "text": "4489"}, {"range": "5459", "text": "4491"}, {"range": "5460", "text": "4489"}, {"range": "5461", "text": "4491"}, {"range": "5462", "text": "4489"}, {"range": "5463", "text": "4491"}, {"range": "5464", "text": "4489"}, {"range": "5465", "text": "4491"}, {"range": "5466", "text": "4489"}, {"range": "5467", "text": "4491"}, {"range": "5468", "text": "4489"}, {"range": "5469", "text": "4491"}, {"range": "5470", "text": "4489"}, {"range": "5471", "text": "4491"}, {"range": "5472", "text": "4489"}, {"range": "5473", "text": "4491"}, {"range": "5474", "text": "4489"}, {"range": "5475", "text": "4491"}, {"range": "5476", "text": "4489"}, {"range": "5477", "text": "4491"}, {"range": "5478", "text": "4489"}, {"range": "5479", "text": "4491"}, {"range": "5480", "text": "4489"}, {"range": "5481", "text": "4491"}, {"range": "5482", "text": "4489"}, {"range": "5483", "text": "4491"}, {"range": "5484", "text": "4489"}, {"range": "5485", "text": "4491"}, {"range": "5486", "text": "4489"}, {"range": "5487", "text": "4491"}, {"range": "5488", "text": "4489"}, {"range": "5489", "text": "4491"}, {"range": "5490", "text": "4489"}, {"range": "5491", "text": "4491"}, {"range": "5492", "text": "4489"}, {"range": "5493", "text": "4491"}, {"range": "5494", "text": "4489"}, {"range": "5495", "text": "4491"}, {"range": "5496", "text": "4489"}, {"range": "5497", "text": "4491"}, {"range": "5498", "text": "4489"}, {"range": "5499", "text": "4491"}, {"range": "5500", "text": "4489"}, {"range": "5501", "text": "4491"}, {"range": "5502", "text": "4489"}, {"range": "5503", "text": "4491"}, {"range": "5504", "text": "4489"}, {"range": "5505", "text": "4491"}, {"range": "5506", "text": "4489"}, {"range": "5507", "text": "4491"}, {"range": "5508", "text": "4489"}, {"range": "5509", "text": "4491"}, {"range": "5510", "text": "4489"}, {"range": "5511", "text": "4491"}, {"range": "5512", "text": "4489"}, {"range": "5513", "text": "4491"}, {"range": "5514", "text": "4489"}, {"range": "5515", "text": "4491"}, {"range": "5516", "text": "4489"}, {"range": "5517", "text": "4491"}, {"range": "5518", "text": "4489"}, {"range": "5519", "text": "4491"}, {"range": "5520", "text": "4489"}, {"range": "5521", "text": "4491"}, {"range": "5522", "text": "4489"}, {"range": "5523", "text": "4491"}, {"range": "5524", "text": "4489"}, {"range": "5525", "text": "4491"}, {"range": "5526", "text": "4489"}, {"range": "5527", "text": "4491"}, {"range": "5528", "text": "4489"}, {"range": "5529", "text": "4491"}, {"range": "5530", "text": "4489"}, {"range": "5531", "text": "4491"}, {"range": "5532", "text": "4489"}, {"range": "5533", "text": "4491"}, {"range": "5534", "text": "4489"}, {"range": "5535", "text": "4491"}, {"range": "5536", "text": "4489"}, {"range": "5537", "text": "4491"}, {"range": "5538", "text": "4489"}, {"range": "5539", "text": "4491"}, {"range": "5540", "text": "4489"}, {"range": "5541", "text": "4491"}, {"range": "5542", "text": "4489"}, {"range": "5543", "text": "4491"}, {"range": "5544", "text": "4489"}, {"range": "5545", "text": "4491"}, {"range": "5546", "text": "4489"}, {"range": "5547", "text": "4491"}, {"range": "5548", "text": "4489"}, {"range": "5549", "text": "4491"}, {"range": "5550", "text": "4489"}, {"range": "5551", "text": "4491"}, {"range": "5552", "text": "4489"}, {"range": "5553", "text": "4491"}, {"range": "5554", "text": "4489"}, {"range": "5555", "text": "4491"}, {"range": "5556", "text": "4489"}, {"range": "5557", "text": "4491"}, {"range": "5558", "text": "4489"}, {"range": "5559", "text": "4491"}, {"range": "5560", "text": "4489"}, {"range": "5561", "text": "4491"}, {"range": "5562", "text": "4489"}, {"range": "5563", "text": "4491"}, {"range": "5564", "text": "4489"}, {"range": "5565", "text": "4491"}, {"range": "5566", "text": "4489"}, {"range": "5567", "text": "4491"}, "&apos;", [3130, 3169], "Nature&apos;s Healing Power in Every Product", "&lsquo;", [3130, 3169], "Nature&lsquo;s Healing Power in Every Product", "&#39;", [3130, 3169], "Nature&#39;s <PERSON><PERSON> Power in Every Product", "&rsquo;", [3130, 3169], "Nature&rsquo;s Healing Power in Every Product", [5084, 5087], "unknown", [5084, 5087], "never", "&quot;", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click &quot;Add Cutoff\" to create one.\n              ", "&ldquo;", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click &ldquo;Add Cutoff\" to create one.\n              ", "&#34;", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click &#34;Add Cutoff\" to create one.\n              ", "&rdquo;", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click &rdquo;Add Cutoff\" to create one.\n              ", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click \"Add Cutoff&quot; to create one.\n              ", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click \"Add Cutoff&ldquo; to create one.\n              ", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click \"Add Cutoff&#34; to create one.\n              ", [9325, 9431], "\n                No current or upcoming cutoff scheduled. Click \"Add Cutoff&rdquo; to create one.\n              ", [4849, 4852], [4849, 4852], [5026, 5029], [5026, 5029], [17167, 17278], "\n                      No performance bonus tiers defined. Click &quot;Add Tier\" to create one.\n                    ", [17167, 17278], "\n                      No performance bonus tiers defined. Click &ldquo;Add Tier\" to create one.\n                    ", [17167, 17278], "\n                      No performance bonus tiers defined. Click &#34;Add Tier\" to create one.\n                    ", [17167, 17278], "\n                      No performance bonus tiers defined. Click &rdquo;Add Tier\" to create one.\n                    ", [17167, 17278], "\n                      No performance bonus tiers defined. Click \"Add Tier&quot; to create one.\n                    ", [17167, 17278], "\n                      No performance bonus tiers defined. Click \"Add Tier&ldquo; to create one.\n                    ", [17167, 17278], "\n                      No performance bonus tiers defined. Click \"Add Tier&#34; to create one.\n                    ", [17167, 17278], "\n                      No performance bonus tiers defined. Click \"Add Tier&rdquo; to create one.\n                    ", [3515, 3518], [3515, 3518], [2751, 2800], "[status, search, activeFilter, sortBy, sortOrder, fetchProducts]", [5369, 5372], [5369, 5372], [9846, 9849], [9846, 9849], [41025, 41087], "\n                Are you sure you want to delete the product &quot;", [41025, 41087], "\n                Are you sure you want to delete the product &ldquo;", [41025, 41087], "\n                Are you sure you want to delete the product &#34;", [41025, 41087], "\n                Are you sure you want to delete the product &rdquo;", [41109, 41156], "&quot;? This action cannot be undone.\n              ", [41109, 41156], "&ldquo;? This action cannot be undone.\n              ", [41109, 41156], "&#34;? This action cannot be undone.\n              ", [41109, 41156], "&rdquo;? This action cannot be undone.\n              ", [1529, 1537], "[checkAdminStatus, status]", [3059, 3067], "[fetchRebates, status]", [4613, 4671], "[isAdmin, page, pageSize, statusFilter, search, dateRange, fetchRebates]", [5318, 5321], [5318, 5321], [2856, 2864], "[fetchReportData, status]", [2973, 3001], "[status, isAdmin, dateRange, fetchReportData]", [1415, 1418], [1415, 1418], [2669, 2672], [2669, 2672], [477, 480], [477, 480], [3542, 3545], [3542, 3545], [1505, 1508], [1505, 1508], [1660, 1663], [1660, 1663], [1789, 1792], [1789, 1792], [1497, 1500], [1497, 1500], [6310, 6313], [6310, 6313], [5670, 5673], [5670, 5673], [5718, 5721], [5718, 5721], [5882, 5885], [5882, 5885], [5944, 5947], [5944, 5947], [3364, 3367], [3364, 3367], [2034, 2037], [2034, 2037], [2881, 2884], [2881, 2884], [4948, 4951], [4948, 4951], [5306, 5309], [5306, 5309], [3572, 3575], [3572, 3575], [1617, 1620], [1617, 1620], [1310, 1313], [1310, 1313], [3417, 3420], [3417, 3420], [2289, 2292], [2289, 2292], [3233, 3236], [3233, 3236], [3424, 3427], [3424, 3427], [3866, 3869], [3866, 3869], [3973, 3976], [3973, 3976], [1927, 1930], [1927, 1930], [4092, 4095], [4092, 4095], [5194, 5197], [5194, 5197], [2273, 2310], "[selectedYear, selectedMonth, status, fetchPerformanceData]", [12660, 12736], ".\n                Click &quot;Simulate Earnings\" to generate data.\n              ", [12660, 12736], ".\n                Click &ldquo;Simulate Earnings\" to generate data.\n              ", [12660, 12736], ".\n                Click &#34;Simulate Earnings\" to generate data.\n              ", [12660, 12736], ".\n                Click &rdquo;Simulate Earnings\" to generate data.\n              ", [12660, 12736], ".\n                Click \"Simulate Earnings&quot; to generate data.\n              ", [12660, 12736], ".\n                Click \"Simulate Earnings&ldquo; to generate data.\n              ", [12660, 12736], ".\n                Click \"Simulate Earnings&#34; to generate data.\n              ", [12660, 12736], ".\n                Click \"Simulate Earnings&rdquo; to generate data.\n              ", [1287, 1290], [1287, 1290], [1439, 1442], [1439, 1442], [2122, 2125], [2122, 2125], [3102, 3105], [3102, 3105], [4992, 4995], [4992, 4995], [6627, 6630], [6627, 6630], [8421, 8424], [8421, 8424], [9725, 9817], "\n                  We&apos;ve sent a confirmation email with all the details of your purchase to ", [9725, 9817], "\n                  We&lsquo;ve sent a confirmation email with all the details of your purchase to ", [9725, 9817], "\n                  We&#39;ve sent a confirmation email with all the details of your purchase to ", [9725, 9817], "\n                  We&rsquo;ve sent a confirmation email with all the details of your purchase to ", [10738, 10920], "\n                We&apos;ve sent a confirmation email with all the details of your purchase.\n                You can also track your order status in your account dashboard.\n              ", [10738, 10920], "\n                We&lsquo;ve sent a confirmation email with all the details of your purchase.\n                You can also track your order status in your account dashboard.\n              ", [10738, 10920], "\n                We&#39;ve sent a confirmation email with all the details of your purchase.\n                You can also track your order status in your account dashboard.\n              ", [10738, 10920], "\n                We&rsquo;ve sent a confirmation email with all the details of your purchase.\n                You can also track your order status in your account dashboard.\n              ", [15055, 15162], "\n                            Choose how you&apos;d like to proceed with your purchase\n                          ", [15055, 15162], "\n                            Choose how you&lsquo;d like to proceed with your purchase\n                          ", [15055, 15162], "\n                            Choose how you&#39;d like to proceed with your purchase\n                          ", [15055, 15162], "\n                            Choose how you&rsquo;d like to proceed with your purchase\n                          ", [3216, 3328], "\n              We&apos;ll send you instructions to reset your password and get you back to your account.\n            ", [3216, 3328], "\n              We&lsquo;ll send you instructions to reset your password and get you back to your account.\n            ", [3216, 3328], "\n              We&#39;ll send you instructions to reset your password and get you back to your account.\n            ", [3216, 3328], "\n              We&rsquo;ll send you instructions to reset your password and get you back to your account.\n            ", [3462, 3637], "\n                &quot;Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                &ldquo;Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                &#34;Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                &rdquo;Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We&apos;re committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We&lsquo;re committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We&#39;re committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We&rsquo;re committed to providing excellent service to all our distributors.\"\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.&quot;\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.&ldquo;\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.&#34;\n              ", [3462, 3637], "\n                \"Our support team is always ready to help you with any account issues. We're committed to providing excellent service to all our distributors.&rdquo;\n              ", [5621, 5733], "\n                Enter your email address and we&apos;ll send you instructions to reset your password.\n              ", [5621, 5733], "\n                Enter your email address and we&lsquo;ll send you instructions to reset your password.\n              ", [5621, 5733], "\n                Enter your email address and we&#39;ll send you instructions to reset your password.\n              ", [5621, 5733], "\n                Enter your email address and we&rsquo;ll send you instructions to reset your password.\n              ", [9247, 9323], "\n                We&apos;ve sent password reset instructions to:\n                ", [9247, 9323], "\n                We&lsquo;ve sent password reset instructions to:\n                ", [9247, 9323], "\n                We&#39;ve sent password reset instructions to:\n                ", [9247, 9323], "\n                We&rsquo;ve sent password reset instructions to:\n                ", [9466, 9600], "\n                If you don&apos;t see the email, check your spam folder or make sure you entered the correct email address.\n              ", [9466, 9600], "\n                If you don&lsquo;t see the email, check your spam folder or make sure you entered the correct email address.\n              ", [9466, 9600], "\n                If you don&#39;t see the email, check your spam folder or make sure you entered the correct email address.\n              ", [9466, 9600], "\n                If you don&rsquo;t see the email, check your spam folder or make sure you entered the correct email address.\n              ", [1305, 1308], [1305, 1308], [1527, 1530], [1527, 1530], [1594, 1597], [1594, 1597], [3892, 3895], [3892, 3895], [4048, 4051], [4048, 4051], [4252, 4255], [4252, 4255], [6155, 6331], "\n                  Exported data may contain sensitive information. Please handle it securely and in accordance with your organization&apos;s data privacy policies.\n                ", [6155, 6331], "\n                  Exported data may contain sensitive information. Please handle it securely and in accordance with your organization&lsquo;s data privacy policies.\n                ", [6155, 6331], "\n                  Exported data may contain sensitive information. Please handle it securely and in accordance with your organization&#39;s data privacy policies.\n                ", [6155, 6331], "\n                  Exported data may contain sensitive information. Please handle it securely and in accordance with your organization&rsquo;s data privacy policies.\n                ", [763, 766], [763, 766], [966, 969], [966, 969], [5614, 5639], "Name - Member&apos;s full name", [5614, 5639], "Name - Member&lsquo;s full name", [5614, 5639], "Name - Member&#39;s full name", [5614, 5639], "Name - Member&rsquo;s full name", [5671, 5701], "Email - Member&apos;s email address", [5671, 5701], "Email - Member&lsquo;s email address", [5671, 5701], "Email - Member&#39;s email address", [5671, 5701], "Email - Member&rsquo;s email address", [5733, 5770], "Upline ID - ID of the member&apos;s upline", [5733, 5770], "Upline ID - ID of the member&lsquo;s upline", [5733, 5770], "Upline ID - ID of the member&#39;s upline", [5733, 5770], "Upline ID - ID of the member&rsquo;s upline", [5802, 5847], "Rank Name - Member&apos;s rank in the organization", [5802, 5847], "Rank Name - Member&lsquo;s rank in the organization", [5802, 5847], "Rank Name - Member&#39;s rank in the organization", [5802, 5847], "Rank Name - Member&rsquo;s rank in the organization", [6185, 6233], "Wallet Balance - Member&apos;s current wallet balance", [6185, 6233], "Wallet Balance - Member&lsquo;s current wallet balance", [6185, 6233], "Wallet Balance - Member&#39;s current wallet balance", [6185, 6233], "Wallet Balance - Member&rsquo;s current wallet balance", [6336, 6365], "Phone - Member&apos;s phone number", [6336, 6365], "Phone - Member&lsquo;s phone number", [6336, 6365], "Phone - Member&#39;s phone number", [6336, 6365], "Phone - Member&rsquo;s phone number", [6397, 6423], "Address - Member&apos;s address", [6397, 6423], "Address - Member&lsquo;s address", [6397, 6423], "Address - Member&#39;s address", [6397, 6423], "Address - Member&rsquo;s address", [1741, 1744], [1741, 1744], [3642, 3890], "\n              The Genealogy Metrics Dashboard provides valuable insights into your network&apos;s performance and growth. Use these metrics to identify trends, recognize top performers, and make data-driven decisions to grow your business.\n            ", [3642, 3890], "\n              The Genealogy Metrics Dashboard provides valuable insights into your network&lsquo;s performance and growth. Use these metrics to identify trends, recognize top performers, and make data-driven decisions to grow your business.\n            ", [3642, 3890], "\n              The Genealogy Metrics Dashboard provides valuable insights into your network&#39;s performance and growth. Use these metrics to identify trends, recognize top performers, and make data-driven decisions to grow your business.\n            ", [3642, 3890], "\n              The Genealogy Metrics Dashboard provides valuable insights into your network&rsquo;s performance and growth. Use these metrics to identify trends, recognize top performers, and make data-driven decisions to grow your business.\n            ", [5066, 5114], "Network Depth - Analyze your network&apos;s structure", [5066, 5114], "Network Depth - Analyze your network&lsquo;s structure", [5066, 5114], "Network Depth - Analyze your network&#39;s structure", [5066, 5114], "Network Depth - Analyze your network&rsquo;s structure", [4204, 4432], "\n                  The mobile genealogy view is designed specifically for mobile devices and touch screens. It provides a simplified, list-based view of your genealogy that&apos;s easy to navigate on smaller screens.\n                ", [4204, 4432], "\n                  The mobile genealogy view is designed specifically for mobile devices and touch screens. It provides a simplified, list-based view of your genealogy that&lsquo;s easy to navigate on smaller screens.\n                ", [4204, 4432], "\n                  The mobile genealogy view is designed specifically for mobile devices and touch screens. It provides a simplified, list-based view of your genealogy that&#39;s easy to navigate on smaller screens.\n                ", [4204, 4432], "\n                  The mobile genealogy view is designed specifically for mobile devices and touch screens. It provides a simplified, list-based view of your genealogy that&rsquo;s easy to navigate on smaller screens.\n                ", [5402, 5445], "Tap on a member&apos;s name to see their details", [5402, 5445], "Tap on a member&lsquo;s name to see their details", [5402, 5445], "Tap on a member&#39;s name to see their details", [5402, 5445], "Tap on a member&rsquo;s name to see their details", [5475, 5526], "Use the &quot;Show Details\" button to expand information", [5475, 5526], "Use the &ldquo;Show Details\" button to expand information", [5475, 5526], "Use the &#34;Show Details\" button to expand information", [5475, 5526], "Use the &rdquo;Show Details\" button to expand information", [5475, 5526], "Use the \"Show Details&quot; button to expand information", [5475, 5526], "Use the \"Show Details&ldquo; button to expand information", [5475, 5526], "Use the \"Show Details&#34; button to expand information", [5475, 5526], "Use the \"Show Details&rdquo; button to expand information", [5556, 5610], "Tap &quot;View Downline\" to navigate to a member's downline", [5556, 5610], "Tap &ldquo;View Downline\" to navigate to a member's downline", [5556, 5610], "Tap &#34;View Downline\" to navigate to a member's downline", [5556, 5610], "Tap &rdquo;View Downline\" to navigate to a member's downline", [5556, 5610], "Tap \"View Downline&quot; to navigate to a member's downline", [5556, 5610], "Tap \"View Downline&ldquo; to navigate to a member's downline", [5556, 5610], "Tap \"View Downline&#34; to navigate to a member's downline", [5556, 5610], "Tap \"View Downline&rdquo; to navigate to a member's downline", [5556, 5610], "Tap \"View Downline\" to navigate to a member&apos;s downline", [5556, 5610], "Tap \"View Downline\" to navigate to a member&lsquo;s downline", [5556, 5610], "Tap \"View Downline\" to navigate to a member&#39;s downline", [5556, 5610], "Tap \"View Downline\" to navigate to a member&rsquo;s downline", [6578, 6735], "\n                  For a more comprehensive visualization with advanced features, switch to the desktop view when you&apos;re on a larger screen.\n                ", [6578, 6735], "\n                  For a more comprehensive visualization with advanced features, switch to the desktop view when you&lsquo;re on a larger screen.\n                ", [6578, 6735], "\n                  For a more comprehensive visualization with advanced features, switch to the desktop view when you&#39;re on a larger screen.\n                ", [6578, 6735], "\n                  For a more comprehensive visualization with advanced features, switch to the desktop view when you&rsquo;re on a larger screen.\n                ", [1275, 1278], [1275, 1278], [3419, 3501], "\n            Stay updated on your network&apos;s activities and achievements\n          ", [3419, 3501], "\n            Stay updated on your network&lsquo;s activities and achievements\n          ", [3419, 3501], "\n            Stay updated on your network&#39;s activities and achievements\n          ", [3419, 3501], "\n            Stay updated on your network&rsquo;s activities and achievements\n          ", [5570, 5625], " - When you earn rebates from your network&apos;s activities", [5570, 5625], " - When you earn rebates from your network&lsquo;s activities", [5570, 5625], " - When you earn rebates from your network&#39;s activities", [5570, 5625], " - When you earn rebates from your network&rsquo;s activities", [2471, 2503], "[status, maxLevel, targetUserId, fetchGenealogy]", [5409, 5595], "\n                &quot;Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.\"\n              ", [5409, 5595], "\n                &ldquo;Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.\"\n              ", [5409, 5595], "\n                &#34;Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.\"\n              ", [5409, 5595], "\n                &rdquo;Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.\"\n              ", [5409, 5595], "\n                \"Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.&quot;\n              ", [5409, 5595], "\n                \"Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.&ldquo;\n              ", [5409, 5595], "\n                \"Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.&#34;\n              ", [5409, 5595], "\n                \"Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness.&rdquo;\n              ", [15273, 15308], "\n            Don&apos;t have an account?", [15273, 15308], "\n            Don&lsquo;t have an account?", [15273, 15308], "\n            Don&#39;t have an account?", [15273, 15308], "\n            Don&rsquo;t have an account?", [7457, 7479], "Nature&apos;s Healing Power", [7457, 7479], "Nature&lsquo;s Healing Power", [7457, 7479], "Nature&#39;s <PERSON>aling Power", [7457, 7479], "Nature&rsquo;s Healing Power", [20133, 20157], "\n                      &quot;", [20133, 20157], "\n                      &ldquo;", [20133, 20157], "\n                      &#34;", [20133, 20157], "\n                      &rdquo;", [20196, 20218], "&quot;\n                    ", [20196, 20218], "&ldquo;\n                    ", [20196, 20218], "&#34;\n                    ", [20196, 20218], "&rdquo;\n                    ", [26565, 26636], "\n                Nature&apos;s Healing Power in Every Product\n              ", [26565, 26636], "\n                Nature&lsquo;s Healing Power in Every Product\n              ", [26565, 26636], "\n                Nature&#39;s <PERSON><PERSON> Power in Every Product\n              ", [26565, 26636], "\n                Nature&rsquo;s Healing Power in Every Product\n              ", [951, 954], [951, 954], [1543, 1546], [1543, 1546], [3465, 3468], [3465, 3468], [9386, 9389], [9386, 9389], [951, 954], [951, 954], [1543, 1546], [1543, 1546], [3465, 3468], [3465, 3468], [9386, 9389], [9386, 9389], [1370, 1373], [1370, 1373], [1952, 1955], [1952, 1955], [3834, 3837], [3834, 3837], [9899, 9902], [9899, 9902], [957, 960], [957, 960], [1547, 1550], [1547, 1550], [3469, 3472], [3469, 3472], [9515, 9518], [9515, 9518], [14020, 14103], "Take one sachet before meals to support your body&apos;s natural detoxification process.", [14020, 14103], "Take one sachet before meals to support your body&lsquo;s natural detoxification process.", [14020, 14103], "Take one sachet before meals to support your body&#39;s natural detoxification process.", [14020, 14103], "Take one sachet before meals to support your body&rsquo;s natural detoxification process.", [14490, 14582], "Take one sachet during meals to help maintain good health and support your body&apos;s functions.", [14490, 14582], "Take one sachet during meals to help maintain good health and support your body&lsquo;s functions.", [14490, 14582], "Take one sachet during meals to help maintain good health and support your body&#39;s functions.", [14490, 14582], "Take one sachet during meals to help maintain good health and support your body&rsquo;s functions.", [1891, 1899], "[fetchProfile, status]", [3020, 3023], [3020, 3023], [5474, 5477], [5474, 5477], [1324, 1327], [1324, 1327], [6329, 6332], [6329, 6332], [11123, 11162], "You don&apos;t have any payment methods yet.", [11123, 11162], "You don&lsquo;t have any payment methods yet.", [11123, 11162], "You don&#39;t have any payment methods yet.", [11123, 11162], "You don&rsquo;t have any payment methods yet.", [2208, 2253], "[status, pagination.offset, pagination.limit, fetchPurchases]", [6502, 6610], "\n              You haven&apos;t made any purchases yet. Start shopping to see your purchase history.\n            ", [6502, 6610], "\n              You haven&lsquo;t made any purchases yet. Start shopping to see your purchase history.\n            ", [6502, 6610], "\n              You haven&#39;t made any purchases yet. Start shopping to see your purchase history.\n            ", [6502, 6610], "\n              You haven&rsquo;t made any purchases yet. Start shopping to see your purchase history.\n            ", [1818, 1821], [1818, 1821], [1739, 1755], "[status, filter, fetchReferralCommissions]", [9103, 9147], "You haven&apos;t created any shareable links yet.", [9103, 9147], "You haven&lsquo;t created any shareable links yet.", [9103, 9147], "You haven&#39;t created any shareable links yet.", [9103, 9147], "You haven&rsquo;t created any shareable links yet.", [2206, 2209], [2206, 2209], [7411, 7434], "[formData, currentStep, validateStep]", [11858, 11861], [11858, 11861], [8017, 8189], "\n                &quot;A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.\"\n              ", [8017, 8189], "\n                &ldquo;A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.\"\n              ", [8017, 8189], "\n                &#34;A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.\"\n              ", [8017, 8189], "\n                &rdquo;A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.\"\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don&apos;t use for other services.\"\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don&lsquo;t use for other services.\"\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don&#39;t use for other services.\"\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don&rsquo;t use for other services.\"\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.&quot;\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.&ldquo;\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.&#34;\n              ", [8017, 8189], "\n                \"A strong password is the first line of defense for your account. Make sure to use a unique password that you don't use for other services.&rdquo;\n              ", [10169, 10274], "\n                Create a new password for your account. Make sure it&apos;s strong and secure.\n              ", [10169, 10274], "\n                Create a new password for your account. Make sure it&lsquo;s strong and secure.\n              ", [10169, 10274], "\n                Create a new password for your account. Make sure it&#39;s strong and secure.\n              ", [10169, 10274], "\n                Create a new password for your account. Make sure it&rsquo;s strong and secure.\n              ", [3298, 3301], [3298, 3301], [1527, 1538], "[fetchProduct, params.id]", [3051, 3054], [3051, 3054], [1617, 1620], [1617, 1620], [2802, 2805], [2802, 2805], [3389, 3392], [3389, 3392], [2430, 2433], [2430, 2433], [3238, 3241], [3238, 3241], [4358, 4361], [4358, 4361], [2031, 2034], [2031, 2034], [2839, 2842], [2839, 2842], [3172, 3175], [3172, 3175], [663, 666], [663, 666], [1701, 1703], "[fetchInventoryTransactions]", [3842, 3845], [3842, 3845], [4647, 4650], [4647, 4650], [268, 271], [268, 271], [753, 756], [753, 756], [947, 950], [947, 950], [283, 286], [283, 286], [3379, 3382], [3379, 3382], [19921, 20003], "\n                  Inactive products won&apos;t be visible in the shop\n                ", [19921, 20003], "\n                  Inactive products won&lsquo;t be visible in the shop\n                ", [19921, 20003], "\n                  Inactive products won&#39;t be visible in the shop\n                ", [19921, 20003], "\n                  Inactive products won&rsquo;t be visible in the shop\n                ", [1873, 1884], "[activeTab, fetchAuditLogs, fetchSalesHistory]", [4873, 4876], [4873, 4876], [775, 778], [775, 778], [3621, 3624], [3621, 3624], [11641, 11779], "\n                      You&apos;ll receive notifications when inventory falls below this level. Leave empty for no alerts.\n                    ", [11641, 11779], "\n                      You&lsquo;ll receive notifications when inventory falls below this level. Leave empty for no alerts.\n                    ", [11641, 11779], "\n                      You&#39;ll receive notifications when inventory falls below this level. Leave empty for no alerts.\n                    ", [11641, 11779], "\n                      You&rsquo;ll receive notifications when inventory falls below this level. Leave empty for no alerts.\n                    ", [20480, 20562], [20480, 20562], [20480, 20562], [20480, 20562], [457, 460], [457, 460], [640, 643], [640, 643], [7450, 7453], [7450, 7453], [1210, 1230], "[quantity, maxLevel, simulateRebates]", [860, 863], [860, 863], [1651, 1654], [1651, 1654], [2208, 2211], [2208, 2211], [3210, 3213], [3210, 3213], [4230, 4233], [4230, 4233], [4460, 4473], "[environment, fetchTestUsers]", [2331, 2389], "[pagination.page, pagination.pageSize, search, rankFilter, fetchUsers]", [6635, 6638], [6635, 6638], [7538, 7541], [7538, 7541], [777, 779], "[fetchAuditLogs]", [219, 222], [219, 222], [323, 326], [323, 326], [385, 388], [385, 388], [511, 514], [511, 514], [2650, 2653], [2650, 2653], [5116, 5119], [5116, 5119], [5796, 5799], [5796, 5799], [1177, 1180], [1177, 1180], [3601, 3667], "[maxLevel, userId, filterOptions.rankId, filterOptions.sortBy, filterOptions.sortDirection, searchQuery, transformDataToFlow, setNodes, setEdges]", [3917, 3920], [3917, 3920], [4988, 4991], [4988, 4991], [1818, 1821], [1818, 1821], [4619, 4676], "[maxLevel, userId, transformDataToFlow, setNodes, setEdges, reactFlowInstance]", [4942, 4945], [4942, 4945], [6208, 6237], "[allowEditing, handleEditUser, handleDeleteUser, handleAddUser, visualOptions, handleExpandNode, processChildren]", [6331, 6334], [6331, 6334], [7976, 7991], "[processChild, visualOptions.layout, visualOptions.levelSpacing, visualOptions.nodeSpacing, visualOptions.nodeWidth]", [8073, 8076], [8073, 8076], [9992, 10050], "[allowEditing, handleEditUser, handleDeleteUser, handleAddUser, expandedNodes, visualOptions, draggedNodes, getConnectionType, handleExpandNode, processChildren]", [14342, 14414], "[allowEditing, visualOptions.nodeWidth, visualOptions.nodeHeight, visualOptions.connectionWidth, visualOptions.connectionStyle, nodes, isDescendantOf, edges, setEdges, getConnectionType]", [4492, 4577], "[maxLevel, userId, filterOptions.rankId, filterOptions.sortBy, filterOptions.sortDirection, searchQuery, transformDataToFlow, setNodes, setEdges, reactFlowInstance]", [4843, 4846], [4843, 4846], [5889, 5891], "[handleExpandNode, processChildren]", [5985, 5988], [5985, 5988], [7045, 7053], "[layout, processChild]", [7135, 7138], [7135, 7138], [8660, 8700], "[expandedNodes, handleExpandNode, layout, processChildren]", [2210, 2213], [2210, 2213], [2270, 2273], [2270, 2273], [2364, 2367], [2364, 2367], [2431, 2434], [2431, 2434], [3622, 3625], [3622, 3625], [3865, 3868], [3865, 3868], [4573, 4576], [4573, 4576], [5131, 5134], [5131, 5134], [1267, 1270], [1267, 1270], [4516, 4519], [4516, 4519], [361, 364], [361, 364], [1006, 1009], [1006, 1009], [2432, 2435], [2432, 2435], [3441, 3444], [3441, 3444], [4372, 4375], [4372, 4375], [5673, 5676], [5673, 5676], [2997, 3000], [2997, 3000], [4400, 4403], [4400, 4403], [9606, 9609], [9606, 9609], [1465, 1468], [1465, 1468], [3694, 3697], [3694, 3697], [5414, 5478], "[userId, initialPageSize, transformDataToFlow, setNodes, setEdges, reactFlowInstance]", [7461, 7464], [7461, 7464], [9496, 9499], [9496, 9499], [11513, 11516], [11513, 11516], [14139, 14252], "[loadedNodes, initialPageSize, nodes, setNodes, setEdges, visualOptions, expandedNodes, getConnectionType, handleExpandNode]", [14380, 14383], [14380, 14383], [15248, 15278], "[expandedNodes, handleExpandNode, visualOptions]", [14640, 14643], [14640, 14643], [15552, 15555], [15552, 15555], [1370, 1373], [1370, 1373], [1508, 1510], "[fetchPaymentMethods]", [6802, 6805], [6802, 6805], [4705, 5061], "\n              In this video, we demonstrate the key benefits of Biogen Shield Herbal Care Soap.\n              You&apos;ll see how it effectively cleanses the skin while providing whitening and\n              anti-bacterial benefits. The natural herbal ingredients work together to nourish\n              your skin and provide long-lasting freshness.\n            ", [4705, 5061], "\n              In this video, we demonstrate the key benefits of Biogen Shield Herbal Care Soap.\n              You&lsquo;ll see how it effectively cleanses the skin while providing whitening and\n              anti-bacterial benefits. The natural herbal ingredients work together to nourish\n              your skin and provide long-lasting freshness.\n            ", [4705, 5061], "\n              In this video, we demonstrate the key benefits of Biogen Shield Herbal Care Soap.\n              You&#39;ll see how it effectively cleanses the skin while providing whitening and\n              anti-bacterial benefits. The natural herbal ingredients work together to nourish\n              your skin and provide long-lasting freshness.\n            ", [4705, 5061], "\n              In this video, we demonstrate the key benefits of Biogen Shield Herbal Care Soap.\n              You&rsquo;ll see how it effectively cleanses the skin while providing whitening and\n              anti-bacterial benefits. The natural herbal ingredients work together to nourish\n              your skin and provide long-lasting freshness.\n            ", [465, 468], [465, 468], [979, 982], [979, 982], [1224, 1226], "[fetchShippingMethods]", [1515, 1550], "[handleSelectMethod, selectedMethodId, shippingMethods]", [3644, 3647], [3644, 3647], [1594, 1597], [1594, 1597], [1840, 1843], [1840, 1843], [2255, 2258], [2255, 2258], [4600, 4603], [4600, 4603], [5284, 5287], [5284, 5287], [1211, 1214], [1211, 1214], [1560, 1563], [1560, 1563], [3746, 3749], [3746, 3749], [888, 891], [888, 891], [1002, 1005], [1002, 1005], [1072, 1075], [1072, 1075], [14498, 14501], [14498, 14501], [18050, 18053], [18050, 18053], [18606, 18609], [18606, 18609], [18636, 18639], [18636, 18639], [19242, 19245], [19242, 19245], [127, 130], [127, 130], [640, 643], [640, 643], [932, 935], [932, 935], [2168, 2171], [2168, 2171], [2197, 2200], [2197, 2200], [2896, 2899], [2896, 2899], [3249, 3252], [3249, 3252], [3766, 3769], [3766, 3769], [1314, 1317], [1314, 1317], [4230, 4233], [4230, 4233], [2699, 2702], [2699, 2702], [3189, 3192], [3189, 3192], [10992, 10995], [10992, 10995], [13691, 13694], [13691, 13694], [14063, 14066], [14063, 14066], [4788, 4791], [4788, 4791], [598, 601], [598, 601], [3346, 3349], [3346, 3349], [3836, 3839], [3836, 3839], [6488, 6491], [6488, 6491], [4116, 4119], [4116, 4119], [8345, 8348], [8345, 8348], [8354, 8357], [8354, 8357], [487, 490], [487, 490], [595, 598], [595, 598], [5995, 5998], [5995, 5998], [6029, 6032], [6029, 6032], [7017, 7020], [7017, 7020], [7049, 7052], [7049, 7052], [4991, 4994], [4991, 4994], [5025, 5028], [5025, 5028], [1441, 1444], [1441, 1444], [2050, 2053], [2050, 2053], [4051, 4054], [4051, 4054], [4080, 4083], [4080, 4083], [4481, 4484], [4481, 4484], [5331, 5334], [5331, 5334], [6880, 6883], [6880, 6883], [6982, 6985], [6982, 6985], [7130, 7133], [7130, 7133], [4001, 4004], [4001, 4004], [352, 355], [352, 355], [3428, 3431], [3428, 3431], [8966, 8969], [8966, 8969], [1606, 1609], [1606, 1609], [2741, 2744], [2741, 2744], [3940, 3943], [3940, 3943], [7645, 7648], [7645, 7648], [9899, 9902], [9899, 9902], [11001, 11004], [11001, 11004], [11072, 11075], [11072, 11075], [13115, 13118], [13115, 13118], [170, 173], [170, 173], [1296, 1299], [1296, 1299], [1367, 1370], [1367, 1370], [3454, 3457], [3454, 3457], [8213, 8216], [8213, 8216], [10620, 10623], [10620, 10623], [11652, 11655], [11652, 11655], [11723, 11726], [11723, 11726], [13584, 13587], [13584, 13587], [599, 602], [599, 602], [1237, 1240], [1237, 1240], [2070, 2073], [2070, 2073], [2896, 2899], [2896, 2899], [3433, 3436], [3433, 3436], [3912, 3915], [3912, 3915], [836, 839], [836, 839], [950, 953], [950, 953], [1025, 1028], [1025, 1028], [8108, 8111], [8108, 8111], [11291, 11294], [11291, 11294], [2425, 2428], [2425, 2428], [3050, 3053], [3050, 3053], [3255, 3258], [3255, 3258], [10627, 10630], [10627, 10630], [11412, 11415], [11412, 11415], [12346, 12349], [12346, 12349], [801, 804], [801, 804], [811, 814], [811, 814], [1271, 1274], [1271, 1274], [1281, 1284], [1281, 1284], [2880, 2883], [2880, 2883], [3805, 3808], [3805, 3808], [2710, 2713], [2710, 2713], [420, 423], [420, 423], [495, 498], [495, 498], [682, 685], [682, 685], [715, 718], [715, 718], [2193, 2196], [2193, 2196], [3008, 3011], [3008, 3011], [3022, 3025], [3022, 3025], [3310, 3313], [3310, 3313], [3324, 3327], [3324, 3327], [3625, 3628], [3625, 3628], [3639, 3642], [3639, 3642], [3936, 3939], [3936, 3939], [3950, 3953], [3950, 3953]]
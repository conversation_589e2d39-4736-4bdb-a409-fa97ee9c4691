(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{6673:(e,r,a)=>{Promise.resolve().then(a.bind(a,86616))},35695:(e,r,a)=>{"use strict";var s=a(18999);a.o(s,"usePathname")&&a.d(r,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},74436:(e,r,a)=>{"use strict";a.d(r,{k5:()=>m});var s=a(12115),t={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(t),o=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(this,arguments)}function c(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),a.push.apply(a,s)}return a}function d(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?c(Object(a),!0).forEach(function(r){var s,t,n;s=e,t=r,n=a[r],(t=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(t))in s?Object.defineProperty(s,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}function m(e){return r=>s.createElement(i,l({attr:d({},e.attr)},r),function e(r){return r&&r.map((r,a)=>s.createElement(r.tag,d({key:a},r.attr),e(r.child)))}(e.child))}function i(e){var r=r=>{var a,{attr:t,size:n,title:c}=e,m=function(e,r){if(null==e)return{};var a,s,t=function(e,r){if(null==e)return{};var a={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(r.indexOf(s)>=0)continue;a[s]=e[s]}return a}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)a=n[s],!(r.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(t[a]=e[a])}return t}(e,o),i=n||r.size||"1em";return r.className&&(a=r.className),e.className&&(a=(a?a+" ":"")+e.className),s.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,t,m,{className:a,style:d(d({color:e.color||r.color},r.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==n?s.createElement(n.Consumer,null,e=>r(e)):r(t)}},86616:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m});var s=a(95155),t=a(12115),n=a(35695),o=a(6874),l=a.n(o),c=a(66766),d=a(29911);function m(){let e=(0,n.useRouter)(),r={PERSONAL_INFO:0,CONTACT_INFO:1,SECURITY:2,PAYMENT_INFO:3,REFERRAL:4,REVIEW:5},[a,o]=(0,t.useState)(r.PERSONAL_INFO),[m,i]=(0,t.useState)({name:"",email:"",password:"",confirmPassword:"",phone:"",uplineId:"",address:"",city:"",region:"",postalCode:"",birthdate:"",preferredPaymentMethod:"",bankName:"",bankAccountNumber:"",bankAccountName:"",gcashNumber:"",payMayaNumber:"",agreeToTerms:!1,receiveUpdates:!1}),[u,h]=(0,t.useState)({}),[p,x]=(0,t.useState)({}),[b,g]=(0,t.useState)(!1),[y,f]=(0,t.useState)(!1),[N,j]=(0,t.useState)(!1),[v,w]=(0,t.useState)(!1),[k,P]=(0,t.useState)(""),C=e=>/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(String(e).toLowerCase()),M=e=>""===e||e.length>=10,A=e=>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(e),O=(e,r)=>{let a="";switch(e){case"name":r?r.length<2&&(a="Name must be at least 2 characters"):a="Name is required";break;case"email":r?C(r)||(a="Please enter a valid email address"):a="Email is required";break;case"phone":r&&!M(r)&&(a="Please enter a valid phone number");break;case"password":r?A(r)||(a="Password must be at least 8 characters with 1 uppercase letter, 1 lowercase letter, and 1 number"):a="Password is required";break;case"confirmPassword":r?r!==m.password&&(a="Passwords do not match"):a="Please confirm your password";break;case"preferredPaymentMethod":"bank"===r?m.bankName?m.bankAccountNumber?m.bankAccountName||(a="Bank account name is required"):a="Bank account number is required":a="Bank name is required":"gcash"===r?m.gcashNumber||(a="GCash number is required"):"paymaya"!==r||m.payMayaNumber||(a="PayMaya number is required");break;case"bankName":case"bankAccountNumber":case"bankAccountName":"bank"!==m.preferredPaymentMethod||r||(a="".concat(e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())," is required"));break;case"gcashNumber":"gcash"!==m.preferredPaymentMethod||r?r&&!/^\d{11}$/.test(r)&&(a="Please enter a valid 11-digit GCash number"):a="GCash number is required";break;case"payMayaNumber":"paymaya"!==m.preferredPaymentMethod||r?r&&!/^\d{11}$/.test(r)&&(a="Please enter a valid 11-digit PayMaya number"):a="PayMaya number is required";break;case"agreeToTerms":r||(a="You must agree to the terms and conditions")}return a},I=e=>{let{name:r,value:a,type:s,checked:t}=e.target,n="checkbox"===s?t:a;i(e=>({...e,[r]:n})),x(e=>({...e,[r]:!0}));let o=O(r,n);h(e=>({...e,[r]:o})),"uplineId"===r&&(j(!1),P(""))},T=e=>{let{name:r,value:a,type:s,checked:t}=e.target;x(e=>({...e,[r]:!0}));let n=O(r,"checkbox"===s?t:a);h(e=>({...e,[r]:n}))},E=()=>{let e=!0,s={},t={[r.PERSONAL_INFO]:["name"],[r.CONTACT_INFO]:["email","phone"],[r.SECURITY]:["password","confirmPassword"],[r.PAYMENT_INFO]:["preferredPaymentMethod"],[r.REFERRAL]:["agreeToTerms"],[r.REVIEW]:[]};return a===r.PAYMENT_INFO&&("bank"===m.preferredPaymentMethod?t[r.PAYMENT_INFO].push("bankName","bankAccountNumber","bankAccountName"):"gcash"===m.preferredPaymentMethod?t[r.PAYMENT_INFO].push("gcashNumber"):"paymaya"===m.preferredPaymentMethod&&t[r.PAYMENT_INFO].push("payMayaNumber")),t[a].forEach(r=>{let a=O(r,m[r]);a&&(e=!1,s[r]=a)}),h(s),g(e),e};(0,t.useEffect)(()=>{E()},[m,a]);let F=async()=>{if(m.uplineId){w(!0);try{await new Promise(e=>setTimeout(e,1e3));let e=["Maria Santos","Juan Dela Cruz","Angelica Reyes","Roberto Tan"],r=e[Math.floor(Math.random()*e.length)];P(r),j(!0)}catch(e){h(e=>({...e,uplineId:"Failed to verify upline ID"})),j(!1)}finally{w(!1)}}},R=async r=>{r.preventDefault();let a={},s=!0,t=["name","email","password","confirmPassword","agreeToTerms"];if(m.preferredPaymentMethod&&(t.push("preferredPaymentMethod"),"bank"===m.preferredPaymentMethod?t.push("bankName","bankAccountNumber","bankAccountName"):"gcash"===m.preferredPaymentMethod?t.push("gcashNumber"):"paymaya"===m.preferredPaymentMethod&&t.push("payMayaNumber")),t.forEach(e=>{let r=O(e,m[e]);r&&(s=!1,a[e]=r)}),!s)return void h(a);f(!0),console.log("Registration form submitted:",{...m,password:"***"});try{console.log("Sending registration request to API");let r={name:m.name,email:m.email,password:m.password,confirmPassword:m.confirmPassword,phone:m.phone,uplineId:m.uplineId||void 0,address:m.address,city:m.city,region:m.region,postalCode:m.postalCode,birthdate:m.birthdate,preferredPaymentMethod:m.preferredPaymentMethod||void 0,bankName:"bank"===m.preferredPaymentMethod?m.bankName:void 0,bankAccountNumber:"bank"===m.preferredPaymentMethod?m.bankAccountNumber:void 0,bankAccountName:"bank"===m.preferredPaymentMethod?m.bankAccountName:void 0,gcashNumber:"gcash"===m.preferredPaymentMethod?m.gcashNumber:void 0,payMayaNumber:"paymaya"===m.preferredPaymentMethod?m.payMayaNumber:void 0,agreeToTerms:m.agreeToTerms,receiveUpdates:m.receiveUpdates};console.log("Request body:",{...r,password:"***",confirmPassword:"***"});let a=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});console.log("Registration response status:",a.status);let s=await a.json();if(console.log("Registration response data:",s),!a.ok){if(s.errors){let e=Object.values(s.errors).join(", ");throw Error(e||"Validation failed")}throw Error(s.error||"Failed to register")}console.log("Registration successful, redirecting to login page"),e.push("/login?registered=true")}catch(e){console.error("Registration error:",e),h(r=>({...r,form:e.message||"An error occurred during registration"})),f(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-green-50 to-white py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"relative w-20 h-20",children:(0,s.jsx)(c.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})})}),(0,s.jsx)("h2",{className:"mt-2 text-center text-2xl font-bold text-gray-900",children:"Extreme Life Herbal"}),(0,s.jsx)("h3",{className:"text-center text-lg text-green-700 font-medium",children:"Create Your Account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/login",className:"font-medium text-green-600 hover:text-green-500 transition-colors",children:"Sign in"})]})]}),(0,s.jsx)("div",{className:"flex justify-between items-center w-full mb-8",children:["Personal Information","Contact Details","Security","Payment Details","Referral","Review & Submit"].map((e,r)=>(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ".concat(a===r?"bg-green-600 text-white":a>r?"bg-green-100 text-green-600 border-2 border-green-600":"bg-gray-100 text-gray-500"),children:a>r?(0,s.jsx)(d.CMH,{className:"h-4 w-4"}):r+1}),(0,s.jsx)("span",{className:"mt-2 text-xs ".concat(a===r?"text-green-600 font-medium":"text-gray-500"," hidden sm:block"),children:e})]},r))}),(0,s.jsxs)("form",{onSubmit:R,children:[(()=>{switch(a){case r.PERSONAL_INFO:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["Full Name ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.x$1,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(u.name&&p.name?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Juan Dela Cruz",value:m.name,onChange:I,onBlur:T}),u.name&&p.name&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.name})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"birthdate",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date of Birth"}),(0,s.jsx)("input",{id:"birthdate",name:"birthdate",type:"date",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",value:m.birthdate,onChange:I}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Your birth date is used to verify your identity and for birthday promotions"})]})]});case r.CONTACT_INFO:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["Email Address ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.maD,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(u.email&&p.email?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"<EMAIL>",value:m.email,onChange:I,onBlur:T}),u.email&&p.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.email})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.Cab,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",autoComplete:"tel",className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(u.phone&&p.phone?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"+63 XXX XXX XXXX",value:m.phone,onChange:I,onBlur:T}),u.phone&&p.phone&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.phone})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-1",children:"Address"}),(0,s.jsx)("input",{id:"address",name:"address",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Street Address",value:m.address,onChange:I})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700 mb-1",children:"City"}),(0,s.jsx)("input",{id:"city",name:"city",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"City",value:m.city,onChange:I})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"region",className:"block text-sm font-medium text-gray-700 mb-1",children:"Region/Province"}),(0,s.jsx)("input",{id:"region",name:"region",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Region/Province",value:m.region,onChange:I})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code"}),(0,s.jsx)("input",{id:"postalCode",name:"postalCode",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Postal Code",value:m.postalCode,onChange:I})]})]});case r.SECURITY:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:["Password ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(u.password&&p.password?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"••••••••",value:m.password,onChange:I,onBlur:T}),u.password&&p.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.password})]}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Password must be at least 8 characters with 1 uppercase letter, 1 lowercase letter, and 1 number"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["Confirm Password ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(u.confirmPassword&&p.confirmPassword?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"••••••••",value:m.confirmPassword,onChange:I,onBlur:T}),u.confirmPassword&&p.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.confirmPassword})]})]})]});case r.PAYMENT_INFO:return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:["Payment Details for Rebates ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-4",children:"Please provide your preferred payment method for receiving rebates and commissions."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Payment Method"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("label",{className:"relative block p-3 border rounded-lg ".concat("bank"===m.preferredPaymentMethod?"border-green-500 bg-green-50":"border-gray-300"," cursor-pointer"),children:[(0,s.jsx)("input",{type:"radio",name:"preferredPaymentMethod",value:"bank",className:"sr-only",checked:"bank"===m.preferredPaymentMethod,onChange:I}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Bank Transfer"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("label",{className:"relative block p-3 border rounded-lg ".concat("gcash"===m.preferredPaymentMethod?"border-green-500 bg-green-50":"border-gray-300"," cursor-pointer"),children:[(0,s.jsx)("input",{type:"radio",name:"preferredPaymentMethod",value:"gcash",className:"sr-only",checked:"gcash"===m.preferredPaymentMethod,onChange:I}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"GCash"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("label",{className:"relative block p-3 border rounded-lg ".concat("paymaya"===m.preferredPaymentMethod?"border-green-500 bg-green-50":"border-gray-300"," cursor-pointer"),children:[(0,s.jsx)("input",{type:"radio",name:"preferredPaymentMethod",value:"paymaya",className:"sr-only",checked:"paymaya"===m.preferredPaymentMethod,onChange:I}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"PayMaya"})]})})]}),u.preferredPaymentMethod&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.preferredPaymentMethod})]}),"bank"===m.preferredPaymentMethod&&(0,s.jsxs)("div",{className:"space-y-4 p-4 border border-gray-200 rounded-md bg-gray-50",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"bankName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Bank Name ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"bankName",name:"bankName",type:"text",className:"appearance-none block w-full px-3 py-2 border ".concat(u.bankName&&p.bankName?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"BDO, BPI, Metrobank, etc.",value:m.bankName,onChange:I,onBlur:T}),u.bankName&&p.bankName&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.bankName})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"bankAccountNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["Account Number ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"bankAccountNumber",name:"bankAccountNumber",type:"text",className:"appearance-none block w-full px-3 py-2 border ".concat(u.bankAccountNumber&&p.bankAccountNumber?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Your bank account number",value:m.bankAccountNumber,onChange:I,onBlur:T}),u.bankAccountNumber&&p.bankAccountNumber&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.bankAccountNumber})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"bankAccountName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Account Name ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"bankAccountName",name:"bankAccountName",type:"text",className:"appearance-none block w-full px-3 py-2 border ".concat(u.bankAccountName&&p.bankAccountName?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Name on your bank account",value:m.bankAccountName,onChange:I,onBlur:T}),u.bankAccountName&&p.bankAccountName&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.bankAccountName})]})]}),"gcash"===m.preferredPaymentMethod&&(0,s.jsx)("div",{className:"space-y-4 p-4 border border-gray-200 rounded-md bg-gray-50",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"gcashNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["GCash Number ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"gcashNumber",name:"gcashNumber",type:"text",className:"appearance-none block w-full px-3 py-2 border ".concat(u.gcashNumber&&p.gcashNumber?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"09XX XXX XXXX",value:m.gcashNumber,onChange:I,onBlur:T}),u.gcashNumber&&p.gcashNumber&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.gcashNumber}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Please ensure this is the same number registered with your GCash account"})]})}),"paymaya"===m.preferredPaymentMethod&&(0,s.jsx)("div",{className:"space-y-4 p-4 border border-gray-200 rounded-md bg-gray-50",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"payMayaNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["PayMaya Number ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"payMayaNumber",name:"payMayaNumber",type:"text",className:"appearance-none block w-full px-3 py-2 border ".concat(u.payMayaNumber&&p.payMayaNumber?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"09XX XXX XXXX",value:m.payMayaNumber,onChange:I,onBlur:T}),u.payMayaNumber&&p.payMayaNumber&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.payMayaNumber}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Please ensure this is the same number registered with your PayMaya account"})]})})]})]}),(0,s.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(d.__w,{className:"h-5 w-5 text-yellow-400"})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm text-yellow-700",children:"Your payment details are used to send your rebates and commissions. Make sure they are accurate."})})]})})]});case r.REFERRAL:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"uplineId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Upline ID (Referrer)"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.NPy,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"uplineId",name:"uplineId",type:"text",className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(u.uplineId?"border-red-300":N?"border-green-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Enter upline ID if you were referred",value:m.uplineId,onChange:I}),u.uplineId&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.uplineId}),N&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-green-600 flex items-center",children:[(0,s.jsx)(d.CMH,{className:"mr-1"})," Verified: ",k]})]}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("button",{type:"button",onClick:F,disabled:!m.uplineId||v,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md ".concat(!m.uplineId||v?"bg-gray-200 text-gray-500 cursor-not-allowed":"bg-green-100 text-green-700 hover:bg-green-200"),children:v?"Verifying...":"Verify ID"})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded ".concat(u.agreeToTerms&&p.agreeToTerms?"border-red-300":""),checked:m.agreeToTerms,onChange:I})}),(0,s.jsxs)("div",{className:"ml-3 text-sm",children:[(0,s.jsxs)("label",{htmlFor:"agreeToTerms",className:"font-medium text-gray-700",children:["I agree to the ",(0,s.jsx)(l(),{href:"/terms",className:"text-green-600 hover:text-green-500",children:"Terms and Conditions"})," and ",(0,s.jsx)(l(),{href:"/privacy",className:"text-green-600 hover:text-green-500",children:"Privacy Policy"})," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),u.agreeToTerms&&p.agreeToTerms&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.agreeToTerms})]})]})}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"receiveUpdates",name:"receiveUpdates",type:"checkbox",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:m.receiveUpdates,onChange:I})}),(0,s.jsx)("div",{className:"ml-3 text-sm",children:(0,s.jsx)("label",{htmlFor:"receiveUpdates",className:"font-medium text-gray-700",children:"I want to receive updates about products, promotions, and events"})})]})})]});case r.REVIEW:return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Review Your Information"}),(0,s.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Personal Information"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:m.name}),m.birthdate&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Born: ",m.birthdate]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Contact Information"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:m.email}),m.phone&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:m.phone}),m.address&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[m.address,", ",m.city,", ",m.region," ",m.postalCode]})]}),m.preferredPaymentMethod&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Payment Information"}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Payment Method: ","bank"===m.preferredPaymentMethod?"Bank Transfer":"gcash"===m.preferredPaymentMethod?"GCash":"PayMaya"]}),"bank"===m.preferredPaymentMethod&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Bank: ",m.bankName]}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Account: ",m.bankAccountNumber]}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Account Name: ",m.bankAccountName]})]}),"gcash"===m.preferredPaymentMethod&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["GCash Number: ",m.gcashNumber]}),"paymaya"===m.preferredPaymentMethod&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["PayMaya Number: ",m.payMayaNumber]})]}),m.uplineId&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Referral Information"}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Upline ID: ",m.uplineId,N&&" (".concat(k,")")]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Preferences"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:m.receiveUpdates?"Will receive updates":"Will not receive updates"})]})]})}),(0,s.jsx)("p",{className:"mt-4 text-sm text-gray-500",children:"Please review your information carefully. You can go back to previous steps to make changes if needed."})]}),u.form&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:u.form})]});default:return null}})(),(0,s.jsxs)("div",{className:"flex justify-between mt-8",children:[a>0?(0,s.jsxs)("button",{type:"button",onClick:()=>{o(e=>e-1)},className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,s.jsx)(d.QVr,{className:"mr-2 h-4 w-4"}),"Back"]}):(0,s.jsx)("div",{}),a<r.REVIEW?(0,s.jsxs)("button",{type:"button",onClick:()=>{E()&&o(e=>e+1)},disabled:!b,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ".concat(b?"bg-green-600 hover:bg-green-700":"bg-gray-300 cursor-not-allowed"," focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"),children:["Next",(0,s.jsx)(d.Z0P,{className:"ml-2 h-4 w-4"})]}):(0,s.jsx)("button",{type:"submit",disabled:y,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating account..."]}):(0,s.jsxs)(s.Fragment,{children:["Create Account",(0,s.jsx)(d.CMH,{className:"ml-2 h-4 w-4"})]})})]})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6711,6874,6766,8441,1684,7358],()=>r(6673)),_N_E=e.O()}]);
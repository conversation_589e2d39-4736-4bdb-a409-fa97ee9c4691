(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9987],{25369:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var n=s(95155),r=s(12115),a=s(12108),l=s(35695),o=s(66766),i=s(6874),c=s.n(i),d=s(70357),m=s(29911);function u(){let{data:e,status:t}=(0,a.useSession)(),s=(0,l.useRouter)(),i=(0,l.useSearchParams)(),[u,x]=(0,r.useState)(!0),[h,p]=(0,r.useState)([]),[f,g]=(0,r.useState)(null),[b,j]=(0,r.useState)(null),[y,w]=(0,r.useState)(!1),[N,v]=(0,r.useState)(null),[k,C]=(0,r.useState)(!1),[S,E]=(0,r.useState)(!1),[I,P]=(0,r.useState)(""),[F,_]=(0,r.useState)("");(0,r.useEffect)(()=>{"unauthenticated"===t&&s.push("/login?returnUrl=/referrals/generate")},[t,s]),(0,r.useEffect)(()=>{"authenticated"===t&&A()},[t]),(0,r.useEffect)(()=>{let e=i.get("productId");if(e&&h.length>0){let t=h.find(t=>t.id===parseInt(e));t&&(g(t),P(t.name),_(t.description||""))}},[i,h]);let A=async()=>{try{x(!0);let e=await fetch("/api/products");if(!e.ok)throw Error("Failed to fetch products");let t=(await e.json()).products||[];p(t),x(!1)}catch(e){console.error("Error fetching products:",e),v("Failed to load products. Please try again."),x(!1)}},T=async()=>{if(!f)return void v("Please select a product to share");try{C(!0),v(null);let e=await fetch("/api/shareable-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:f.id,title:I||f.name,description:F||f.description,customImage:f.image})});if(!e.ok)throw Error("Failed to generate link");let t=await e.json();j(t.link)}catch(e){console.error("Error generating link:",e),v("Failed to generate link. Please try again.")}finally{C(!1)}},L=async()=>{if(b)try{let e=window.location.origin,t="".concat(e,"/s/").concat(b.code);await navigator.clipboard.writeText(t),w(!0),setTimeout(()=>{w(!1)},2e3)}catch(e){console.error("Error copying to clipboard:",e),v("Failed to copy link to clipboard")}},R=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return"loading"===t||u?(0,n.jsx)(d.A,{children:(0,n.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,n.jsx)(m.hW,{className:"animate-spin text-green-500 mr-2"}),(0,n.jsx)("span",{children:"Loading..."})]})}):(0,n.jsx)(d.A,{children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)(c(),{href:"/referrals",className:"inline-flex items-center text-blue-600 hover:underline",children:[(0,n.jsx)(m.QVr,{className:"mr-2"}),"Back to Referrals"]})}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,n.jsxs)("div",{className:"p-4 bg-green-50 border-b border-green-100",children:[(0,n.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,n.jsx)(m.AnD,{className:"mr-2 text-green-600"}),"Generate Shareable Link"]}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"Create a custom link to share products and earn commissions"})]}),(0,n.jsxs)("div",{className:"p-6",children:[N&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-red-50 text-red-700 rounded-md",children:N}),b?(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Your Shareable Link"}),(0,n.jsxs)("div",{className:"mb-4 p-4 bg-gray-50 rounded-md flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"text-gray-700 mr-2 break-all",children:[window.location.origin,"/s/",b.code]}),(0,n.jsx)("button",{type:"button",className:"flex-shrink-0 text-blue-600 hover:text-blue-800",onClick:L,title:"Copy link",children:y?(0,n.jsx)(m.CMH,{className:"text-green-600"}):(0,n.jsx)(m.paH,{})})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Share Your Link"}),(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,n.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-green-50",onClick:()=>{if(!b)return;let e=window.location.origin,t="".concat(e,"/s/").concat(b.code),s="Check out ".concat(I||(null==f?void 0:f.name),"! ").concat(t);window.open("https://wa.me/?text=".concat(encodeURIComponent(s)),"_blank")},children:[(0,n.jsx)(m.EcP,{className:"text-green-600 text-2xl mb-2"}),(0,n.jsx)("span",{className:"text-sm",children:"WhatsApp"})]}),(0,n.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-blue-50",onClick:()=>{if(!b)return;let e=window.location.origin,t="".concat(e,"/s/").concat(b.code);window.open("https://www.facebook.com/sharer/sharer.php?u=".concat(encodeURIComponent(t)),"_blank")},children:[(0,n.jsx)(m.iYk,{className:"text-blue-600 text-2xl mb-2"}),(0,n.jsx)("span",{className:"text-sm",children:"Facebook"})]}),(0,n.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-blue-50",onClick:()=>{if(!b)return;let e=window.location.origin,t="".concat(e,"/s/").concat(b.code),s="Check out ".concat(I||(null==f?void 0:f.name),"! ").concat(t);window.open("https://twitter.com/intent/tweet?text=".concat(encodeURIComponent(s)),"_blank")},children:[(0,n.jsx)(m.feZ,{className:"text-blue-400 text-2xl mb-2"}),(0,n.jsx)("span",{className:"text-sm",children:"Twitter"})]}),(0,n.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-purple-50",onClick:()=>{if(!b)return;let e=window.location.origin,t="".concat(e,"/s/").concat(b.code),s="Check out ".concat(I||(null==f?void 0:f.name),"!"),n="I thought you might be interested in this product:\n\n".concat(I||(null==f?void 0:f.name),"\n\n").concat(F||(null==f?void 0:f.description),"\n\n").concat(t);window.open("mailto:?subject=".concat(encodeURIComponent(s),"&body=").concat(encodeURIComponent(n)))},children:[(0,n.jsx)(m.maD,{className:"text-purple-600 text-2xl mb-2"}),(0,n.jsx)("span",{className:"text-sm",children:"Email"})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("button",{type:"button",className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>j(null),children:"Generate Another Link"}),(0,n.jsx)(c(),{href:"/referrals",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"View All My Links"})]})]}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select a product to share"}),(0,n.jsxs)("select",{className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500",value:(null==f?void 0:f.id)||"",onChange:e=>{let t=parseInt(e.target.value),s=h.find(e=>e.id===t)||null;g(s),s&&(P(s.name),_(s.description||""))},children:[(0,n.jsx)("option",{value:"",children:"-- Select a product --"}),h.map(e=>(0,n.jsxs)("option",{value:e.id,children:[e.name," - ",R(e.price)," (",e.pv," PV)"]},e.id))]})]}),f&&(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:"flex-shrink-0 h-24 w-24 bg-gray-100 rounded-md overflow-hidden mr-4",children:f.image?(0,n.jsx)(o.default,{src:f.image,alt:f.name,width:96,height:96,className:"object-cover w-full h-full"}):(0,n.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,n.jsx)(m.AsH,{className:"text-gray-400 h-8 w-8"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium",children:f.name}),(0,n.jsx)("p",{className:"text-sm text-gray-500 line-clamp-2",children:f.description}),(0,n.jsxs)("div",{className:"mt-1 flex items-center",children:[(0,n.jsx)("span",{className:"text-green-600 font-medium mr-3",children:R(f.price)}),(0,n.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:[f.pv," PV"]})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Title (Optional)"}),(0,n.jsx)("input",{type:"text",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500",placeholder:"Enter a custom title for your link",value:I,onChange:e=>P(e.target.value)}),(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This will be displayed when your link is shared on social media"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Description (Optional)"}),(0,n.jsx)("textarea",{className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500",rows:3,placeholder:"Enter a custom description for your link",value:F,onChange:e=>_(e.target.value)}),(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This will be displayed when your link is shared on social media"})]}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{type:"button",className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:T,disabled:!f||k,children:k?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(m.hW,{className:"animate-spin mr-2"}),"Generating..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(m.AnD,{className:"mr-2"}),"Generate Shareable Link"]})})})]})]})]})]})})}},62585:(e,t,s)=>{Promise.resolve().then(s.bind(s,25369))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(62585)),_N_E=e.O()}]);
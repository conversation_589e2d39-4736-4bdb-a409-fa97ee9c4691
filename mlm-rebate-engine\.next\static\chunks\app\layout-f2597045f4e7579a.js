(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{5323:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>o,_:()=>n});var i=s(95155),r=s(12115);let a=(0,r.createContext)(void 0),n=()=>{let e=(0,r.useContext)(a);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},o=e=>{let{children:t}=e,[s,n]=(0,r.useState)([]);(0,r.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{n(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,r.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(s))},[s]);let o=e=>{n(t=>t.filter(t=>t.id!==e))},u=s.reduce((e,t)=>e+t.quantity,0),l=s.reduce((e,t)=>e+t.price*t.quantity,0),h=s.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,i.jsx)(a.Provider,{value:{items:s,addItem:e=>{n(t=>{let s=t.findIndex(t=>t.id===e.id);if(!(s>=0))return[...t,e];{let i=[...t];return i[s]={...i[s],quantity:i[s].quantity+e.quantity},i}})},removeItem:o,updateQuantity:(e,t)=>{if(t<=0)return void o(e);n(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{n([])},itemCount:u,subtotal:l,totalPV:h},children:t})}},7642:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var i=s(95155),r=s(12115);let a=()=>"serviceWorker"in navigator,n=async()=>{if(!a())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),o(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},o=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&u()})})},u=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},l=e=>{let{children:t}=e,[s,a]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{n();let e=()=>{a(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,i.jsxs)(i.Fragment,{children:[t,s&&(0,i.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,i.jsxs)("div",{className:"mr-4",children:[(0,i.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,i.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,i.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},30347:()=>{},34326:(e,t,s)=>{"use strict";s.d(t,{default:()=>w});var i=s(95155),r=s(52020),a=s(39853),n=s(7165),o=s(25910),u=class extends o.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,s){let i=t.queryKey,n=t.queryHash??(0,r.F$)(i,t),o=this.get(n);return o||(o=new a.X({client:e,queryKey:i,queryHash:n,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(i)}),this.add(o)),o}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){n.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,r.MK)(e,t)):t}notify(e){n.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){n.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){n.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},l=s(57948),h=s(6784),c=class extends l.k{#t;#s;#i;constructor(e){super(),this.mutationId=e.mutationId,this.#s=e.mutationCache,this.#t=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#t.includes(e)||(this.#t.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#t=this.#t.filter(t=>t!==e),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#r({type:"continue"})};this.#i=(0,h.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#r({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#r({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});let s="pending"===this.state.status,i=!this.#i.canStart();try{if(s)t();else{this.#r({type:"pending",variables:e,isPaused:i}),await this.#s.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#r({type:"pending",context:t,variables:e,isPaused:i})}let r=await this.#i.start();return await this.#s.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#s.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#r({type:"success",data:r}),r}catch(t){try{throw await this.#s.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#s.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#r({type:"error",error:t})}}finally{this.#s.runNext(this)}}#r(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),n.jG.batch(()=>{this.#t.forEach(t=>{t.onMutationUpdate(e)}),this.#s.notify({mutation:this,type:"updated",action:e})})}},d=class extends o.Q{constructor(e={}){super(),this.config=e,this.#a=new Set,this.#n=new Map,this.#o=0}#a;#n;#o;build(e,t,s){let i=new c({mutationCache:this,mutationId:++this.#o,options:e.defaultMutationOptions(t),state:s});return this.add(i),i}add(e){this.#a.add(e);let t=f(e);if("string"==typeof t){let s=this.#n.get(t);s?s.push(e):this.#n.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#a.delete(e)){let t=f(e);if("string"==typeof t){let s=this.#n.get(t);if(s)if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#n.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=f(e);if("string"!=typeof t)return!0;{let s=this.#n.get(t),i=s?.find(e=>"pending"===e.state.status);return!i||i===e}}runNext(e){let t=f(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#n.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){n.jG.batch(()=>{this.#a.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#a.clear(),this.#n.clear()})}getAll(){return Array.from(this.#a)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,r.nJ)(e,t))}notify(e){n.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return n.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(r.lQ))))}};function f(e){return e.options.scope?.id}var p=s(50920),y=s(21239);function m(e){return{onFetch:(t,s)=>{let i=t.options,a=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],o=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,h=async()=>{let s=!1,h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},c=(0,r.ZM)(t.options,t.fetchOptions),d=async(e,i,a)=>{if(s)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let n={client:t.client,queryKey:t.queryKey,pageParam:i,direction:a?"backward":"forward",meta:t.options.meta};h(n);let o=await c(n),{maxPages:u}=t.options,l=a?r.ZZ:r.y9;return{pages:l(e.pages,o,u),pageParams:l(e.pageParams,i,u)}};if(a&&n.length){let e="backward"===a,t={pages:n,pageParams:o},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:v)(i,t);u=await d(t,s,e)}else{let t=e??n.length;do{let e=0===l?o[0]??i.initialPageParam:v(i,u);if(l>0&&null==e)break;u=await d(u,e),l++}while(l<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=h}}}function v(e,{pages:t,pageParams:s}){let i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}var g=class{#u;#s;#l;#h;#c;#d;#f;#p;constructor(e={}){this.#u=e.queryCache||new u,this.#s=e.mutationCache||new d,this.#l=e.defaultOptions||{},this.#h=new Map,this.#c=new Map,this.#d=0}mount(){this.#d++,1===this.#d&&(this.#f=p.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onFocus())}),this.#p=y.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#u.onOnline())}))}unmount(){this.#d--,0===this.#d&&(this.#f?.(),this.#f=void 0,this.#p?.(),this.#p=void 0)}isFetching(e){return this.#u.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#u.build(this,t),i=s.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime((0,r.d2)(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#u.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let i=this.defaultQueryOptions({queryKey:e}),a=this.#u.get(i.queryHash),n=a?.state.data,o=(0,r.Zw)(t,n);if(void 0!==o)return this.#u.build(this,i).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return n.jG.batch(()=>this.#u.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#u.get(t.queryHash)?.state}removeQueries(e){let t=this.#u;n.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#u;return n.jG.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(n.jG.batch(()=>this.#u.findAll(e).map(e=>e.cancel(s)))).then(r.lQ).catch(r.lQ)}invalidateQueries(e,t={}){return n.jG.batch(()=>(this.#u.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(n.jG.batch(()=>this.#u.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(r.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(r.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#u.build(this,t);return s.isStaleByTime((0,r.d2)(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r.lQ).catch(r.lQ)}fetchInfiniteQuery(e){return e.behavior=m(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r.lQ).catch(r.lQ)}ensureInfiniteQueryData(e){return e.behavior=m(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return y.t.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#u}getMutationCache(){return this.#s}getDefaultOptions(){return this.#l}setDefaultOptions(e){this.#l=e}setQueryDefaults(e,t){this.#h.set((0,r.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#h.values()],s={};return t.forEach(t=>{(0,r.Cp)(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#c.set((0,r.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#c.values()],s={};return t.forEach(t=>{(0,r.Cp)(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#l.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,r.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===r.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#l.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#u.clear(),this.#s.clear()}},b=s(26715),C=s(12115);function w(e){let{children:t}=e,[s]=(0,C.useState)(()=>new g({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,i.jsx)(b.Ht,{client:s,children:t})}},42112:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,62093,23)),Promise.resolve().then(s.t.bind(s,27735,23)),Promise.resolve().then(s.t.bind(s,30347,23)),Promise.resolve().then(s.bind(s,5323)),Promise.resolve().then(s.bind(s,65881)),Promise.resolve().then(s.bind(s,34326)),Promise.resolve().then(s.bind(s,7642))},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},65881:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>a});var i=s(95155),r=s(12108);function a(e){let{children:t}=e;return(0,i.jsx)(r.SessionProvider,{children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6360,2108,6967,8441,1684,7358],()=>t(42112)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2854],{14104:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(95155),n=s(12115),r=s(12108),l=s(35695),i=s(70357),c=s(29911);function o(){let{data:e,status:t}=(0,r.useSession)(),s=(0,l.useRouter)(),[o,d]=(0,n.useState)(!0),[m,u]=(0,n.useState)(!1),[x,p]=(0,n.useState)(null),[g,h]=(0,n.useState)([]),[f,b]=(0,n.useState)({type:"",text:""}),[y,j]=(0,n.useState)(null),[v,N]=(0,n.useState)(!1);(0,n.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,n.useEffect)(()=>{"authenticated"===t&&w()},[t]);let w=async()=>{d(!0);try{let e=await fetch("/api/mlm-config?includePerformanceTiers=true");if(!e.ok)throw Error("Failed to fetch MLM configuration: ".concat(e.statusText));let t=await e.json();p(t.config),h(t.performanceTiers||[])}catch(e){console.error("Error fetching MLM configuration:",e),b({type:"error",text:"Failed to load MLM configuration. Please try again."})}finally{d(!1)}},k=async()=>{if(x){u(!0),b({type:"",text:""});try{let e=await fetch("/api/mlm-config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"updateConfig",config:x})});if(!e.ok)throw Error("Failed to update MLM configuration: ".concat(e.statusText));let t=await e.json();p(t.config),b({type:"success",text:"MLM configuration updated successfully."})}catch(e){console.error("Error updating MLM configuration:",e),b({type:"error",text:"Failed to update MLM configuration. Please try again."})}finally{u(!1)}}},C=async()=>{if(y){u(!0),b({type:"",text:""});try{let e=!y.id,t=await fetch("/api/mlm-config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e?"createPerformanceTier":"updatePerformanceTier",tier:y,tierId:e?void 0:y.id})});if(!t.ok)throw Error("Failed to ".concat(e?"create":"update"," performance tier: ").concat(t.statusText));await t.json(),w(),b({type:"success",text:"Performance tier ".concat(e?"created":"updated"," successfully.")}),j(null),N(!1)}catch(e){console.error("Error saving performance tier:",e),b({type:"error",text:"Failed to save performance tier. Please try again."})}finally{u(!1)}}},S=(e,t)=>{x&&p({...x,[e]:t})},M=(e,t)=>{y&&j({...y,[e]:t})},T=e=>{j({...e}),N(!0)};return"loading"===t||o?(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-green-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})}):(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"MLM Configuration"}),f.text&&(0,a.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("error"===f.type?"bg-red-100 text-red-700":"success"===f.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:f.text}),x&&(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"System Configuration"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"MLM Structure"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>S("mlmStructure","binary"),className:"flex items-center px-4 py-2 rounded-md ".concat("binary"===x.mlmStructure?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"),children:[(0,a.jsx)(c.yk7,{className:"mr-2"}),"Binary"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>S("mlmStructure","unilevel"),className:"flex items-center px-4 py-2 rounded-md ".concat("unilevel"===x.mlmStructure?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"),children:[(0,a.jsx)(c.yk7,{className:"mr-2"}),"Unilevel"]})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"binary"===x.mlmStructure?"Binary structure: Each member can have at most 2 direct downlines (left and right legs)":"Unilevel structure: Each member can have unlimited direct downlines"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"PV Calculation Method"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>S("pvCalculation","percentage"),className:"flex items-center px-4 py-2 rounded-md ".concat("percentage"===x.pvCalculation?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"),children:[(0,a.jsx)(c.gdQ,{className:"mr-2"}),"Percentage"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>S("pvCalculation","fixed"),className:"flex items-center px-4 py-2 rounded-md ".concat("fixed"===x.pvCalculation?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"),children:[(0,a.jsx)(c.Tsk,{className:"mr-2"}),"Fixed"]})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"percentage"===x.pvCalculation?"Percentage: PV is calculated as a percentage of the product price":"Fixed: PV is a fixed value set for each product"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Performance Bonus"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>S("performanceBonusEnabled",!0),className:"flex items-center px-4 py-2 rounded-md ".concat(x.performanceBonusEnabled?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"),children:[(0,a.jsx)(c.SBv,{className:"mr-2"}),"Enabled"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>S("performanceBonusEnabled",!1),className:"flex items-center px-4 py-2 rounded-md ".concat(x.performanceBonusEnabled?"bg-gray-200 text-gray-700":"bg-blue-600 text-white"),children:[(0,a.jsx)(c.SBv,{className:"mr-2"}),"Disabled"]})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:x.performanceBonusEnabled?"Performance bonus is enabled based on personal sales volume":"Performance bonus is disabled"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Monthly Cutoff Day"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.bfZ,{className:"text-gray-400 mr-2"}),(0,a.jsx)("input",{type:"number",min:"1",max:"31",value:x.monthlyCutoffDay,onChange:e=>S("monthlyCutoffDay",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-20"})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Day of the month when commissions are calculated and paid"})]}),(0,a.jsxs)("div",{className:"md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Binary Max Depth"}),(0,a.jsx)("input",{type:"number",min:"1",max:"10",value:x.binaryMaxDepth,onChange:e=>S("binaryMaxDepth",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Maximum depth for binary structure (1-10)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Unilevel Max Depth"}),(0,a.jsx)("input",{type:"number",min:"1",max:"10",value:x.unilevelMaxDepth,onChange:e=>S("unilevelMaxDepth",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Maximum depth for unilevel structure (1-10)"})]})]})]}),(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)("button",{type:"button",onClick:k,disabled:m,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.dIn,{className:"inline mr-2"}),"Save Configuration"]})})})]})]}),x.performanceBonusEnabled&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Performance Bonus Tiers"}),(0,a.jsxs)("button",{type:"button",onClick:()=>{j({id:0,name:"",minSales:0,maxSales:null,bonusType:"percentage",percentage:0,fixedAmount:0,active:!0}),N(!0)},className:"bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:[(0,a.jsx)(c.OiG,{className:"inline mr-1"}),"Add Tier"]})]}),(0,a.jsxs)("div",{className:"p-6",children:[0===g.length?(0,a.jsx)("div",{className:"text-center py-4 text-gray-500",children:'No performance bonus tiers defined. Click "Add Tier" to create one.'}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sales Range"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bonus Type"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bonus Value"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["₱",e.minSales.toFixed(2)," - ",e.maxSales?"₱".concat(e.maxSales.toFixed(2)):"Unlimited"]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:"percentage"===e.bonusType?"Percentage":"Fixed Amount"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:"percentage"===e.bonusType?"".concat(e.percentage.toFixed(2),"%"):"₱".concat(e.fixedAmount.toFixed(2))})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(e.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.active?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsx)("button",{type:"button",onClick:()=>T(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:(0,a.jsx)(c.uO9,{})})})]},e.id))})]})}),v&&y&&(0,a.jsxs)("div",{className:"mt-6 border rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:y.id?"Edit Tier":"Add New Tier"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tier Name"}),(0,a.jsx)("input",{type:"text",value:y.name,onChange:e=>M("name",e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:y.active?"active":"inactive",onChange:e=>M("active","active"===e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:[(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Minimum Sales"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:y.minSales,onChange:e=>M("minSales",parseFloat(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Maximum Sales (leave empty for unlimited)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:y.maxSales||"",onChange:e=>M("maxSales",e.target.value?parseFloat(e.target.value):null),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bonus Type"}),(0,a.jsxs)("select",{value:y.bonusType,onChange:e=>M("bonusType",e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:[(0,a.jsx)("option",{value:"percentage",children:"Percentage"}),(0,a.jsx)("option",{value:"fixed",children:"Fixed Amount"})]})]}),"percentage"===y.bonusType?(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Percentage (%)"}),(0,a.jsx)("input",{type:"number",min:"0",max:"100",step:"0.01",value:y.percentage,onChange:e=>M("percentage",parseFloat(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fixed Amount"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:y.fixedAmount,onChange:e=>M("fixedAmount",parseFloat(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{j(null),N(!1)},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:C,disabled:m,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.dIn,{className:"inline mr-2"}),"Save Tier"]})})]})]})]})]})]})]})})}},47706:(e,t,s)=>{Promise.resolve().then(s.bind(s,14104))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(47706)),_N_E=e.O()}]);
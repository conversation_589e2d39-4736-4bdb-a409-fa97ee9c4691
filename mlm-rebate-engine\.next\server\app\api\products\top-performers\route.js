(()=>{var e={};e.id=6290,e.ids=[6290],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>c,Nh:()=>u,aP:()=>l});var s=t(96330),o=t(13581),i=t(85663),n=t(55511),a=t.n(n);async function c(e){return await i.Ay.hash(e,10)}function l(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let u={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await i.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:n,...a}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94452:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),c=t(35426),l=t(12909),u=t(31183);async function p(e){try{let r=await (0,c.getServerSession)(l.Nh);if(!r||!r.user)return a.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=new URL(e.url),s=parseInt(t.searchParams.get("limit")||"5"),o=[...await u.z.$transaction(async e=>{let r=await e.shareableLink.findMany({where:{productId:{not:null},clickCount:{gt:0}},select:{productId:!0,clickCount:!0,conversionCount:!0,totalRevenue:!0,totalCommission:!0}}),t={};r.forEach(e=>{e.productId&&(t[e.productId]||(t[e.productId]={productId:e.productId,totalClicks:0,totalConversions:0,totalRevenue:0,totalCommissions:0,conversionRate:0}),t[e.productId].totalClicks+=e.clickCount,t[e.productId].totalConversions+=e.conversionCount,t[e.productId].totalRevenue+=e.totalRevenue,t[e.productId].totalCommissions+=e.totalCommission)}),Object.values(t).forEach(e=>{e.totalClicks>0&&(e.conversionRate=e.totalConversions/e.totalClicks*100)});let o=Object.values(t).sort((e,r)=>r.conversionRate-e.conversionRate).slice(0,s).map(e=>e.productId);return(await e.product.findMany({where:{id:{in:o},isActive:!0},select:{id:!0,name:!0,description:!0,price:!0,srp:!0,pv:!0,image:!0}})).map(e=>{let r=t[e.id];return{...e,conversionRate:r?Math.round(10*r.conversionRate)/10:0,totalSales:r?r.totalRevenue:0,totalCommissions:r?r.totalCommissions:0}}).sort((e,r)=>r.conversionRate-e.conversionRate)})];if(o.length<s){let e=s-o.length,r=o.map(e=>e.id),t=await u.z.product.findMany({where:{id:{notIn:r},isActive:!0},select:{id:!0,name:!0,description:!0,price:!0,srp:!0,pv:!0,image:!0},take:e,orderBy:{pv:"desc"}});o=[...o,...t]}return a.NextResponse.json({products:o})}catch(e){return console.error("Error fetching top performing products:",e),a.NextResponse.json({error:"Failed to fetch top performing products"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/products/top-performers/route",pathname:"/api/products/top-performers",filename:"route",bundlePath:"app/api/products/top-performers/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\top-performers\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:w}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(94452));module.exports=s})();
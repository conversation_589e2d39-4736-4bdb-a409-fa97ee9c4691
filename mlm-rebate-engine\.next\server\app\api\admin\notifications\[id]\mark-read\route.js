(()=>{var e={};e.id=5753,e.ids=[5753],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{Er:()=>u,Nh:()=>d,aP:()=>l});var t=s(96330),o=s(13581),i=s(85663),a=s(55511),n=s.n(a);async function u(e){return await i.Ay.hash(e,10)}function l(){let e=n().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new t.PrismaClient;let d={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new t.PrismaClient,s=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!s)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",s.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await i.Ay.compare(e.password,s.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",s.id);let{password:a,...n}=s;return{id:s.id.toString(),email:s.email,name:s.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>o});var t=s(96330);let o=global.prisma||new t.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},44931:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{POST:()=>c});var o=s(96559),i=s(48088),a=s(37719),n=s(32190),u=s(35426),l=s(12909),d=s(31183);async function c(e,{params:r}){try{let e=await (0,u.getServerSession)(l.Nh);if(!e||!e.user)return n.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});if("admin"!==e.user.role)return n.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let s=parseInt(r.id);if(isNaN(s))return n.NextResponse.json({error:"Invalid notification ID"},{status:400});let t=await d.z.user.findUnique({where:{email:e.user.email}});if(!t)return n.NextResponse.json({error:"User not found"},{status:404});if(!await d.z.notification.findFirst({where:{id:s,OR:[{userId:t.id},{userId:null}]}}))return n.NextResponse.json({error:"Notification not found"},{status:404});let o=await d.z.notification.update({where:{id:s},data:{isRead:!0,readAt:new Date}});return n.NextResponse.json({message:"Notification marked as read",notification:{id:o.id,isRead:o.isRead,readAt:o.readAt}})}catch(e){return console.error("Error marking notification as read:",e),n.NextResponse.json({error:"Failed to mark notification as read"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/notifications/[id]/mark-read/route",pathname:"/api/admin/notifications/[id]/mark-read",filename:"route",bundlePath:"app/api/admin/notifications/[id]/mark-read/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\notifications\\[id]\\mark-read\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:w}=p;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,8044,3112],()=>s(44931));module.exports=t})();
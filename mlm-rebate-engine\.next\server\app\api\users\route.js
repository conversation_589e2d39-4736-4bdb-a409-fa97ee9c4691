(()=>{var e={};e.id=318,e.ids=[318],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69812:(e,t,r)=>{"use strict";r.d(t,{HU:()=>i,Ld:()=>o,q8:()=>n,tf:()=>p,zK:()=>s});var a=r(70762);a.z.object({email:a.z.string().email("Invalid email address"),password:a.z.string().min(8,"Password must be at least 8 characters"),csrfToken:a.z.string().optional()});let s=a.z.object({name:a.z.string().min(2,"Name must be at least 2 characters"),email:a.z.string().email("Invalid email address"),password:a.z.string().min(8,"Password must be at least 8 characters"),confirmPassword:a.z.string().min(8,"Confirm password must be at least 8 characters"),phone:a.z.string().optional(),birthdate:a.z.string().optional(),address:a.z.string().optional(),city:a.z.string().optional(),region:a.z.string().optional(),postalCode:a.z.string().optional(),uplineId:a.z.string().optional(),profileImage:a.z.string().optional(),preferredPaymentMethod:a.z.string().optional(),bankName:a.z.string().optional(),bankAccountNumber:a.z.string().optional(),bankAccountName:a.z.string().optional(),gcashNumber:a.z.string().optional(),payMayaNumber:a.z.string().optional(),receiveUpdates:a.z.boolean().optional().default(!1),agreeToTerms:a.z.boolean()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>!0===e.agreeToTerms,{message:"You must agree to the terms and conditions",path:["agreeToTerms"]}).refine(e=>"bank"===e.preferredPaymentMethod?!!e.bankName&&!!e.bankAccountNumber&&!!e.bankAccountName:"gcash"===e.preferredPaymentMethod?!!e.gcashNumber:"paymaya"!==e.preferredPaymentMethod||!!e.payMayaNumber,{message:"Payment details are required for the selected payment method",path:["preferredPaymentMethod"]}),n=a.z.object({name:a.z.string().min(2,"Name must be at least 2 characters").optional(),phone:a.z.string().optional(),currentPassword:a.z.string().optional(),newPassword:a.z.string().min(8,"New password must be at least 8 characters").optional(),confirmNewPassword:a.z.string().optional(),profileImage:a.z.string().optional()}).refine(e=>!e.newPassword||e.newPassword===e.confirmNewPassword,{message:"New passwords don't match",path:["confirmNewPassword"]}).refine(e=>!e.newPassword||!!e.currentPassword,{message:"Current password is required to set a new password",path:["currentPassword"]}),i=a.z.object({name:a.z.string().min(2,"Product name must be at least 2 characters"),description:a.z.string().optional(),price:a.z.number().positive("Price must be positive"),image:a.z.string().optional(),rebateConfigs:a.z.array(a.z.object({level:a.z.number().int().positive("Level must be a positive integer"),percentage:a.z.number().positive("Percentage must be positive").max(100,"Percentage cannot exceed 100%")})).min(1,"At least one rebate configuration is required")}),o=a.z.object({productId:a.z.number().int().positive("Product ID must be a positive integer"),quantity:a.z.number().int().positive("Quantity must be a positive integer"),paymentMethodId:a.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:a.z.record(a.z.any()).optional(),referenceNumber:a.z.string().optional(),shippingMethodId:a.z.number().int().positive("Shipping method ID must be a positive integer").optional(),shippingDetails:a.z.record(a.z.any()).optional(),shippingAddress:a.z.string().optional(),shippingFee:a.z.number().nonnegative("Shipping fee must be a non-negative number").optional(),referralCode:a.z.string().optional()});function p(e,t){try{let r=e.parse(t);return{success:!0,data:r}}catch(e){if(e instanceof a.z.ZodError){let t={};return e.errors.forEach(e=>{t[e.path.join(".")]=e.message}),{success:!1,errors:t}}return{success:!1,errors:{_error:"An unexpected error occurred during validation"}}}}a.z.object({amount:a.z.number().positive("Amount must be positive"),type:a.z.enum(["withdrawal","deposit"],{errorMap:()=>({message:"Invalid transaction type"})}),description:a.z.string().optional(),paymentMethodId:a.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:a.z.record(a.z.any()).optional(),referenceNumber:a.z.string().optional()}),a.z.object({checkAll:a.z.boolean().optional()})},78335:()=>{},80867:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>g,serverHooks:()=>b,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>z});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>l});var s=r(96559),n=r(48088),i=r(37719),o=r(31183),p=r(32190),c=r(85663),u=r(89605),d=r(69812);async function l(e){try{let t=await e.json(),r=(0,d.tf)(d.zK,t);if(!r.success)return p.NextResponse.json({errors:r.errors},{status:400});let{name:a,email:s,password:n,uplineId:i,phone:l,profileImage:m,birthdate:g,address:h,city:z,region:b,postalCode:w,preferredPaymentMethod:f,bankName:y,bankAccountNumber:v,bankAccountName:P,gcashNumber:N,payMayaNumber:x,receiveUpdates:I}=r.data;if(await o.z.user.findUnique({where:{email:s}}))return p.NextResponse.json({error:"User with this email already exists"},{status:400});let k=null;if(i){if(k=parseInt(i),isNaN(k))return p.NextResponse.json({error:"Invalid upline ID"},{status:400});if(!await o.z.user.findUnique({where:{id:k}}))return p.NextResponse.json({error:"Upline user not found"},{status:400})}let j=await c.Ay.hash(n,10),M={};"bank"===f&&y&&v&&P?(M.bankName=y,M.accountNumber=v,M.accountName=P):"gcash"===f&&N?M.gcashNumber=N:"paymaya"===f&&x&&(M.payMayaNumber=x);let q=await o.z.user.create({data:{name:a,email:s,password:j,phone:l,profileImage:m,birthdate:g||void 0,address:h||void 0,city:z||void 0,region:b||void 0,postalCode:w||void 0,preferredPaymentMethod:f||void 0,paymentDetails:Object.keys(M).length>0?M:void 0,receiveUpdates:I||!1,uplineId:k,rankId:1}});u.g3.clearNamespace();let{password:A,...$}=q;return p.NextResponse.json($,{status:201})}catch(e){return console.error("Error creating user:",e),p.NextResponse.json({error:"Failed to create user"},{status:500})}}async function m(e){try{let t=new URL(e.url),r=parseInt(t.searchParams.get("page")||"1"),a=parseInt(t.searchParams.get("pageSize")||"10"),s=t.searchParams.get("search")||"",n=t.searchParams.get("rankId"),i=t.searchParams.get("uplineId"),c=(r-1)*a,d={};s&&(d.OR=[{name:{contains:s}},{email:{contains:s}}]),n&&(d.rankId=parseInt(n)),i&&(d.uplineId=parseInt(i));let l=`users:${r}:${a}:${s}:${n}:${i}`,m=await u.g3.getOrSet(l,async()=>{let e=await o.z.user.findMany({where:d,select:{id:!0,name:!0,email:!0,phone:!0,rankId:!0,uplineId:!0,walletBalance:!0,createdAt:!0,rank:{select:{name:!0}},upline:{select:{id:!0,name:!0,email:!0}},_count:{select:{downline:!0}}},skip:c,take:a,orderBy:{createdAt:"desc"}}),t=await o.z.user.count({where:d}),s=Math.ceil(t/a);return{users:e,pagination:{page:r,pageSize:a,totalItems:t,totalPages:s}}},3e5);return p.NextResponse.json(m)}catch(e){return console.error("Error fetching users:",e),p.NextResponse.json({error:"Failed to fetch users"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:z,serverHooks:b}=g;function w(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:z})}},89605:(e,t,r)=>{"use strict";r.d(t,{_L:()=>o,g3:()=>i,ic:()=>p});class a{constructor(e=3e5){this.cache=new Map,this.defaultTTL=e}set(e,t,r){let a=Date.now()+(r||this.defaultTTL);this.cache.set(e,{value:t,expiry:a})}get(e){let t=this.cache.get(e);if(t)return Date.now()>t.expiry?void this.cache.delete(e):t.value}has(e){let t=this.cache.get(e);return!!t&&(!(Date.now()>t.expiry)||(this.cache.delete(e),!1))}delete(e){this.cache.delete(e)}clear(){this.cache.clear()}async getOrSet(e,t,r){let a=this.get(e);if(void 0!==a)return a;let s=await t();return this.set(e,s,r),s}cleanup(){let e=Date.now();for(let[t,r]of this.cache.entries())e>r.expiry&&this.cache.delete(t)}}let s=new a,n=e=>({set:(t,r,a)=>s.set(`${e}:${t}`,r,a),get:t=>s.get(`${e}:${t}`),has:t=>s.has(`${e}:${t}`),delete:t=>s.delete(`${e}:${t}`),getOrSet:(t,r,a)=>s.getOrSet(`${e}:${t}`,r,a),clearNamespace:()=>{for(let[t]of s.cache.entries())t.startsWith(`${e}:`)&&s.delete(t)}}),i=n("user"),o=n("product"),p=n("genealogy");n("rebate"),setInterval(()=>{s.cleanup()},36e5)},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8044,8381],()=>r(80867));module.exports=a})();
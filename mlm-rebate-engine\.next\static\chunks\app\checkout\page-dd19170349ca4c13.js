(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{18959:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t={};r.r(t);var a=r(95155),l=r(12115),n=r(12108),d=r(35695),i=r(6874),o=r.n(i),c=r(5323),m=r(70357),x=r(29911);let u=e=>{let{addresses:s,selectedAddressId:r,onAddressSelect:t,onSubmit:n}=e,[d,i]=(0,l.useState)(!1),[o,c]=(0,l.useState)({name:"",phone:"",addressLine1:"",addressLine2:"",city:"",region:"",postalCode:"",isDefault:!1});(0,l.useEffect)(()=>{0===s.length&&i(!0)},[s]);let m=e=>{let{name:s,value:r,type:t,checked:a}=e.target;c(e=>({...e,[s]:"checkbox"===t?a:r}))};return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Shipping Address"}),s.length>0&&!d&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4",children:s.map(e=>(0,a.jsx)("div",{className:"border rounded-md p-4 cursor-pointer ".concat(r===e.id?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"),onClick:()=>t(e.id),children:(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600",children:e.phone}),(0,a.jsx)("p",{className:"text-gray-600",children:e.addressLine1}),e.addressLine2&&(0,a.jsx)("p",{className:"text-gray-600",children:e.addressLine2}),(0,a.jsxs)("p",{className:"text-gray-600",children:[e.city,", ",e.region," ",e.postalCode]}),e.isDefault&&(0,a.jsx)("span",{className:"inline-flex items-center mt-1 px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Default"})]}),r===e.id&&(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)(x.CMH,{className:"text-white h-3 w-3"})})})]})},e.id))}),(0,a.jsxs)("button",{type:"button",className:"mt-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>i(!0),children:[(0,a.jsx)(x.OiG,{className:"mr-2 h-4 w-4"}),"Add New Address"]}),(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)("button",{type:"button",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:()=>n({id:r}),disabled:!r,children:"Continue to Shipping Method"})})]}),d&&(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n(o)},children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6",children:[(0,a.jsxs)("div",{className:"sm:col-span-3",children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:o.name,onChange:m,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-3",children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:o.phone,onChange:m,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-6",children:[(0,a.jsx)("label",{htmlFor:"addressLine1",className:"block text-sm font-medium text-gray-700",children:"Address Line 1"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"addressLine1",name:"addressLine1",value:o.addressLine1,onChange:m,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-6",children:[(0,a.jsx)("label",{htmlFor:"addressLine2",className:"block text-sm font-medium text-gray-700",children:"Address Line 2 (Optional)"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"addressLine2",name:"addressLine2",value:o.addressLine2,onChange:m,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700",children:"City"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"city",name:"city",value:o.city,onChange:m,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"region",className:"block text-sm font-medium text-gray-700",children:"Region/Province"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"region",name:"region",value:o.region,onChange:m,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-gray-700",children:"Postal Code"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"postalCode",name:"postalCode",value:o.postalCode,onChange:m,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsx)("div",{className:"sm:col-span-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"isDefault",name:"isDefault",type:"checkbox",checked:o.isDefault,onChange:m,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,a.jsx)("div",{className:"ml-3 text-sm",children:(0,a.jsx)("label",{htmlFor:"isDefault",className:"font-medium text-gray-700",children:"Set as default shipping address"})})]})})]}),(0,a.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[s.length>0&&(0,a.jsx)("button",{type:"button",className:"py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>i(!1),children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:s.length>0?"Save & Continue":"Continue to Shipping Method"})]})]})]})},h=e=>{let{methods:s,selectedMethodId:r,onMethodSelect:t,onSubmit:l,onBack:n}=e,d=e=>"₱".concat(e.toFixed(2));return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Shipping Method"}),0===s.length?(0,a.jsxs)("div",{className:"text-center py-6 bg-gray-50 rounded-md",children:[(0,a.jsx)(x.dv1,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No shipping methods available"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Please try again later or contact support."})]}):(0,a.jsx)("div",{className:"space-y-4 mb-6",children:s.map(e=>(0,a.jsx)("div",{className:"border rounded-md p-4 cursor-pointer ".concat(r===e.id?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"),onClick:()=>t(e.id),children:(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.dv1,{className:"h-5 w-5 text-gray-400 mr-2"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name})]}),e.description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:e.description}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:["Estimated delivery: ",e.estimatedDeliveryDays," day",1!==e.estimatedDeliveryDays?"s":""]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 mr-3",children:d(e.price)}),r===e.id&&(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)(x.CMH,{className:"text-white h-3 w-3"})})]})]})},e.id))}),(0,a.jsxs)("div",{className:"mt-6 flex justify-between",children:[(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:n,children:[(0,a.jsx)(x.QVr,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,a.jsx)("button",{type:"button",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:l,disabled:!r||0===s.length,children:"Continue to Payment"})]})]})},p=e=>{let{selectedMethod:s,onMethodSelect:r,onSubmit:n,onBack:d,isGuestCheckout:i=!1}=e,[o,c]=(0,l.useState)({cardNumber:"",cardholderName:"",expiryDate:"",cvv:""}),m=e=>{let{name:s,value:r}=e.target;c(e=>({...e,[s]:r}))},u=[{id:"credit_card",name:"Credit/Debit Card",icon:(0,a.jsx)(x.x1c,{className:"h-6 w-6"}),description:"Pay securely with your credit or debit card"},{id:"gcash",name:"GCash",icon:(0,a.jsx)(t.SiGcash,{className:"h-6 w-6 text-blue-600"}),description:"Pay using your GCash wallet"},{id:"maya",name:"Maya",icon:(0,a.jsx)(t.SiPaymaya,{className:"h-6 w-6 text-green-600"}),description:"Pay using your Maya wallet"},{id:"bank_transfer",name:"Bank Transfer",icon:(0,a.jsx)(x.MxO,{className:"h-6 w-6 text-green-700"}),description:"Pay via bank transfer"},{id:"wallet",name:"Wallet Balance",icon:(0,a.jsx)(x.lcY,{className:"h-6 w-6 text-purple-600"}),description:"Pay using your wallet balance"},{id:"cod",name:"Cash on Delivery",icon:(0,a.jsx)(x.MxO,{className:"h-6 w-6 text-yellow-600"}),description:"Pay when you receive your order"}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Payment Method"}),i&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,a.jsx)("strong",{children:"Guest Checkout:"})," You are checking out as a guest. Some payment methods like Wallet Balance are only available to registered members."]})}),(0,a.jsx)("div",{className:"space-y-4 mb-6",children:u.filter(e=>!i||"wallet"!==e.id).map(e=>(0,a.jsxs)("div",{className:"border rounded-md p-4 cursor-pointer ".concat(s===e.id?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"),onClick:()=>r(e.id),children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mr-3",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.description})]})]}),s===e.id&&(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)(x.CMH,{className:"text-white h-3 w-3"})})]}),"credit_card"===s&&"credit_card"===e.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cardNumber",className:"block text-sm font-medium text-gray-700",children:"Card Number"}),(0,a.jsx)("input",{type:"text",id:"cardNumber",name:"cardNumber",placeholder:"1234 5678 9012 3456",value:o.cardNumber,onChange:m,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cardholderName",className:"block text-sm font-medium text-gray-700",children:"Cardholder Name"}),(0,a.jsx)("input",{type:"text",id:"cardholderName",name:"cardholderName",placeholder:"John Doe",value:o.cardholderName,onChange:m,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"expiryDate",className:"block text-sm font-medium text-gray-700",children:"Expiry Date"}),(0,a.jsx)("input",{type:"text",id:"expiryDate",name:"expiryDate",placeholder:"MM/YY",value:o.expiryDate,onChange:m,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cvv",className:"block text-sm font-medium text-gray-700",children:"CVV"}),(0,a.jsx)("input",{type:"text",id:"cvv",name:"cvv",placeholder:"123",value:o.cvv,onChange:m,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]})]})]})}),"gcash"===s&&"gcash"===e.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"You will be redirected to GCash to complete your payment after placing your order."})}),"maya"===s&&"maya"===e.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"You will be redirected to Maya to complete your payment after placing your order."})}),"bank_transfer"===s&&"bank_transfer"===e.id&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Please transfer the total amount to one of the following bank accounts:"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-md text-sm",children:[(0,a.jsx)("p",{className:"font-medium",children:"BDO"}),(0,a.jsx)("p",{children:"Account Name: Extreme Life Herbal Products"}),(0,a.jsx)("p",{children:"Account Number: 1234 5678 9012"})]}),(0,a.jsxs)("div",{className:"mt-2 bg-gray-50 p-3 rounded-md text-sm",children:[(0,a.jsx)("p",{className:"font-medium",children:"BPI"}),(0,a.jsx)("p",{children:"Account Name: Extreme Life Herbal Products"}),(0,a.jsx)("p",{children:"Account Number: 9876 5432 1098"})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"After making the transfer, please send a copy of the deposit slip or transfer confirmation to our email: <EMAIL>"})]}),"wallet"===s&&"wallet"===e.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Your current wallet balance: ",(0,a.jsx)("span",{className:"font-medium",children:"₱1,250.00"})]})}),"cod"===s&&"cod"===e.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Pay with cash when your order is delivered. Please prepare the exact amount."})})]},e.id))}),(0,a.jsxs)("div",{className:"mt-6 flex justify-between",children:[(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:d,children:[(0,a.jsx)(x.QVr,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,a.jsx)("button",{type:"button",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:n,disabled:!s,children:"Review Order"})]})]})};var g=r(66766);let b=e=>{let{items:s,shippingFee:r,discount:t=0,isGuestCheckout:l=!1}=e,n=e=>"₱".concat(e.toFixed(2)),d=s.reduce((e,s)=>e+(l&&s.srp?s.srp:s.price)*s.quantity,0),i=s.reduce((e,s)=>e+s.pv*s.quantity,0);return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden sticky top-6",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Order Summary"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:["Items (",s.length,")"]}),(0,a.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border border-gray-200 relative",children:e.image?(0,a.jsx)(g.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover object-center"}):(0,a.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(x.AsH,{className:"text-gray-400 h-6 w-6"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Qty: ",e.quantity]}),(0,a.jsxs)("div",{className:"flex justify-between mt-1",children:[(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.pv," PV \xd7 ",e.quantity]}),l&&e.srp?(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[n(e.srp*e.quantity),e.price<e.srp&&(0,a.jsx)("span",{className:"text-xs text-gray-500 line-through ml-1",children:n(e.price*e.quantity)})]}):(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:n(e.price*e.quantity)})]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Subtotal"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:n(d)})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Shipping"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:n(r)})]}),t>0&&(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Discount"}),(0,a.jsxs)("p",{className:"text-sm font-medium text-green-600",children:["-",n(t)]})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total PV"}),(0,a.jsxs)("p",{className:"text-sm font-medium text-blue-600",children:[i," PV"]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-2 border-t border-gray-200 mt-2",children:[(0,a.jsx)("p",{className:"text-base font-medium text-gray-900",children:"Total"}),(0,a.jsx)("p",{className:"text-base font-medium text-gray-900",children:n(d+r-t)})]})]}),!l&&(0,a.jsxs)("div",{className:"mt-6 bg-blue-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800 mb-1",children:"Potential Earnings"}),(0,a.jsxs)("p",{className:"text-xs text-blue-700",children:["This purchase will earn you approximately ",Math.round(.05*i)," PV in rebates based on your current rank."]})]}),l&&(0,a.jsxs)("div",{className:"mt-6 bg-yellow-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800 mb-1",children:"Retail Price"}),(0,a.jsx)("p",{className:"text-xs text-yellow-700 mb-2",children:"You are purchasing at retail price as a guest. Sign up as a member to get discounted prices and earn rebates on your purchases."}),(()=>{let e=s.reduce((e,s)=>e+s.srp*s.quantity,0),r=e-s.reduce((e,s)=>e+s.price*s.quantity,0),t=Math.round(r/e*100);return r>0?(0,a.jsxs)("div",{className:"text-xs bg-white p-2 rounded border border-yellow-200",children:[(0,a.jsxs)("p",{className:"font-medium text-green-700",children:["Members save ₱",r.toFixed(2)," (",t,"%) on this order!"]}),(0,a.jsx)("a",{href:"/register",className:"text-blue-600 underline block mt-1",children:"Sign up now to save"})]}):null})()]})]})]})},f=e=>{let{onSubmit:s,onCancel:r}=e,[t,n]=(0,l.useState)({name:"",email:"",phone:"",addressLine1:"",addressLine2:"",city:"",region:"",postalCode:""}),[d,i]=(0,l.useState)({}),[o,c]=(0,l.useState)({}),m=e=>{let{name:s,value:r}=e.target;n(e=>({...e,[s]:r})),d[s]&&i(e=>({...e,[s]:""}))},u=e=>{let{name:s}=e.target;c(e=>({...e,[s]:!0})),h(s,t[s])},h=(e,s)=>{let r="";switch(e){case"name":s.trim()||(r="Name is required");break;case"email":s.trim()?/\S+@\S+\.\S+/.test(s)||(r="Email is invalid"):r="Email is required";break;case"phone":s.trim()||(r="Phone number is required");break;case"addressLine1":s.trim()||(r="Address is required");break;case"city":s.trim()||(r="City is required");break;case"region":s.trim()||(r="Region is required");break;case"postalCode":s.trim()||(r="Postal code is required")}return i(s=>({...s,[e]:r})),r},p=()=>{let e=!0,s={};return["name","email","phone","addressLine1","city","region","postalCode"].forEach(r=>{let a=h(r,t[r]);a&&(e=!1,s[r]=a)}),i(s),e};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Guest Checkout"}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-1",children:"Member Benefits"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 list-disc pl-5 space-y-1",children:[(0,a.jsx)("li",{children:"Discounted member prices (save up to 20%)"}),(0,a.jsx)("li",{children:"Earn rebates on every purchase"}),(0,a.jsx)("li",{children:"Access to exclusive products and promotions"}),(0,a.jsx)("li",{children:"Build your own network and earn commissions"})]}),(0,a.jsxs)("p",{className:"text-sm text-blue-700 mt-2",children:[(0,a.jsx)("a",{href:"/register",className:"font-medium underline",children:"Sign up now"})," to enjoy these benefits!"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Please provide your information to complete your purchase as a guest."}),(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),p()&&s(t)},children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:"Personal Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["Full Name ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.x$1,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"name",name:"name",type:"text",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(d.name&&o.name?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Juan Dela Cruz",value:t.name,onChange:m,onBlur:u}),d.name&&o.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.name})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["Email Address ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.maD,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(d.email&&o.email?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"<EMAIL>",value:t.email,onChange:m,onBlur:u}),d.email&&o.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.email})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:["Phone Number ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.Cab,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"phone",name:"phone",type:"tel",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(d.phone&&o.phone?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"+63 XXX XXX XXXX",value:t.phone,onChange:m,onBlur:u}),d.phone&&o.phone&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.phone})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:"Shipping Address"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"addressLine1",className:"block text-sm font-medium text-gray-700 mb-1",children:["Address Line 1 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.vq8,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"addressLine1",name:"addressLine1",type:"text",required:!0,className:"appearance-none block w-full pl-10 pr-3 py-2 border ".concat(d.addressLine1&&o.addressLine1?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Street address",value:t.addressLine1,onChange:m,onBlur:u}),d.addressLine1&&o.addressLine1&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.addressLine1})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"addressLine2",className:"block text-sm font-medium text-gray-700 mb-1",children:"Address Line 2 (Optional)"}),(0,a.jsx)("input",{id:"addressLine2",name:"addressLine2",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Apartment, suite, unit, building, floor, etc.",value:t.addressLine2,onChange:m})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700 mb-1",children:["City ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{id:"city",name:"city",type:"text",required:!0,className:"appearance-none block w-full px-3 py-2 border ".concat(d.city&&o.city?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"City",value:t.city,onChange:m,onBlur:u}),d.city&&o.city&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.city})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"region",className:"block text-sm font-medium text-gray-700 mb-1",children:["Region/Province ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{id:"region",name:"region",type:"text",required:!0,className:"appearance-none block w-full px-3 py-2 border ".concat(d.region&&o.region?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Region/Province",value:t.region,onChange:m,onBlur:u}),d.region&&o.region&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.region})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-gray-700 mb-1",children:["Postal Code ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{id:"postalCode",name:"postalCode",type:"text",required:!0,className:"appearance-none block w-full px-3 py-2 border ".concat(d.postalCode&&o.postalCode?"border-red-300":"border-gray-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"),placeholder:"Postal Code",value:t.postalCode,onChange:m,onBlur:u}),d.postalCode&&o.postalCode&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d.postalCode})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Continue to Shipping"})]})]})})]})};var y=function(e){return e[e.SHIPPING_ADDRESS=0]="SHIPPING_ADDRESS",e[e.SHIPPING_METHOD=1]="SHIPPING_METHOD",e[e.PAYMENT=2]="PAYMENT",e[e.REVIEW=3]="REVIEW",e}(y||{});function j(){var e,s,r,t,i,g,y,j,N;let{data:v,status:w}=(0,n.useSession)(),C=(0,d.useRouter)(),{items:k,subtotal:S,clearCart:P}=(0,c._)(),[L,F]=(0,l.useState)(0),[A,E]=(0,l.useState)([]),[M,q]=(0,l.useState)(null),[D,B]=(0,l.useState)([]),[O,G]=(0,l.useState)(null),[I,_]=(0,l.useState)(null),[H,T]=(0,l.useState)(!1),[R,Y]=(0,l.useState)(null),[V,X]=(0,l.useState)(!1),[W,Q]=(0,l.useState)(null),[J,$]=(0,l.useState)(!1),[z,U]=(0,l.useState)(!1),[K,Z]=(0,l.useState)(null),[ee,es]=(0,l.useState)(null);(0,l.useEffect)(()=>{0!==k.length||V||C.push("/shop")},[k,C,V]),(0,l.useEffect)(()=>{et()},[]),(0,l.useEffect)(()=>{"authenticated"===w?er():"unauthenticated"===w&&$(!0)},[w]);let er=async()=>{try{let e=await fetch("/api/shipping/addresses");if(!e.ok)throw Error("Failed to fetch shipping addresses");let s=await e.json();E(s);let r=s.find(e=>e.isDefault);r?q(r.id):s.length>0&&q(s[0].id)}catch(e){console.error("Error fetching shipping addresses:",e),Y("Failed to load shipping addresses. Please try again.")}},et=async()=>{try{let e=await fetch("/api/shipping/methods");if(!e.ok)throw Error("Failed to fetch shipping methods");let s=await e.json();B(s),s.length>0&&G(s[0].id)}catch(e){console.error("Error fetching shipping methods:",e),Y("Failed to load shipping methods. Please try again.")}},ea=async()=>{if(!J&&!M)return void Y("Please select a shipping address");if(J&&!K)return void Y("Please provide your shipping information");if(!O||!I)return void Y("Please complete all required information");T(!0),Y(null);try{let e=D.find(e=>e.id===O),s=e?e.price:0,r={items:k.map(e=>({productId:e.id,quantity:e.quantity,price:J?e.srp:e.price,priceType:J?"srp":"member"})),shippingMethodId:O,paymentMethod:I,subtotal:S,shippingFee:s,total:S+s};J&&K?(r.isGuestOrder=!0,r.customerName=K.name,r.customerEmail=K.email,r.customerPhone=K.phone,r.guestShippingAddress={name:K.name,phone:K.phone,email:K.email,addressLine1:K.addressLine1,addressLine2:K.addressLine2||"",city:K.city,region:K.region,postalCode:K.postalCode,isGuestAddress:!0}):r.shippingAddressId=M;let t=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to place order")}let a=await t.json();Q(a.orderNumber),X(!0),P()}catch(e){console.error("Error placing order:",e),Y(e.message||"Failed to place order. Please try again.")}finally{T(!1)}};return"loading"!==w&&(0!==k.length||V)?V?(0,a.jsx)(m.A,{children:(0,a.jsx)("div",{className:"max-w-3xl mx-auto py-8 px-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6",children:(0,a.jsx)(x.CMH,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Order Placed Successfully!"}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 mb-6",children:["Thank you for your order. Your order number is ",(0,a.jsx)("span",{className:"font-semibold",children:W}),"."]}),J?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["We've sent a confirmation email with all the details of your purchase to ",null==K?void 0:K.email,"."]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8 max-w-lg mx-auto",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 text-lg mb-2",children:"Create an Account"}),(0,a.jsx)("p",{className:"text-blue-700 mb-4",children:"Create an account to track your order, get member discounts, and earn rebates on future purchases!"}),(0,a.jsxs)(o(),{href:"/register",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(x.NPy,{className:"mr-2"}),"Create Account"]})]})]}):(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"We've sent a confirmation email with all the details of your purchase. You can also track your order status in your account dashboard."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[!J&&(0,a.jsx)(o(),{href:"/orders",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:"View My Orders"}),(0,a.jsx)(o(),{href:"/shop",className:"inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Continue Shopping"})]})]})})}):(0,a.jsx)(m.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-8 px-4",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(o(),{href:"/shop",className:"flex items-center text-blue-600 hover:underline",children:[(0,a.jsx)(x.QVr,{className:"mr-2"}),"Back to Shop"]})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Checkout"}),R&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-red-600",children:R}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsxs)("div",{className:"lg:w-2/3",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex items-center",children:[{label:"Shipping Address",icon:(0,a.jsx)(x.vq8,{})},{label:"Shipping Method",icon:(0,a.jsx)(x.dv1,{})},{label:"Payment",icon:(0,a.jsx)(x.x1c,{})},{label:"Review",icon:(0,a.jsx)(x.CMH,{})}].map((e,s)=>(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-center ".concat(s>0?"ml-4":""),children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ".concat(s<=L?"bg-green-600 text-white":"bg-gray-200 text-gray-500"),children:e.icon}),(0,a.jsx)("div",{className:"ml-2 text-sm font-medium ".concat(s<=L?"text-gray-900":"text-gray-500"," hidden sm:block"),children:e.label}),s<3&&(0,a.jsx)("div",{className:"flex-1 ml-2 h-0.5 ".concat(s<L?"bg-green-600":"bg-gray-200")})]})},s))})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[0===L&&(0,a.jsx)(a.Fragment,{children:J?z?(0,a.jsx)(f,{onSubmit:e=>{Z(e),es({id:"guest-address",name:e.name,phone:e.phone,email:e.email,addressLine1:e.addressLine1,addressLine2:e.addressLine2||"",city:e.city,region:e.region,postalCode:e.postalCode,isGuestAddress:!0}),F(1)},onCancel:()=>{U(!1)}}):(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 text-gray-400 mb-4",children:(0,a.jsx)(x.x$1,{className:"h-full w-full"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Checkout Options"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Choose how you'd like to proceed with your purchase"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-8",children:[(0,a.jsxs)("div",{className:"border border-blue-200 rounded-lg p-6 bg-blue-50 flex flex-col",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 text-lg mb-2",children:"Sign In as Member"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 list-disc pl-5 space-y-1 mb-4 flex-grow",children:[(0,a.jsxs)("li",{children:["Get ",(0,a.jsx)("strong",{children:"discounted member prices"})]}),(0,a.jsx)("li",{children:"Earn rebates on your purchase"}),(0,a.jsx)("li",{children:"Track your order history"}),(0,a.jsx)("li",{children:"Faster checkout experience"})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>{C.push("/login?returnUrl=/checkout")},className:"w-full inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(x.x$1,{className:"mr-2"}),"Sign In for Member Prices"]})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6 bg-gray-50 flex flex-col",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 text-lg mb-2",children:"Continue as Guest"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-700 list-disc pl-5 space-y-1 mb-4 flex-grow",children:[(0,a.jsx)("li",{children:"Quick checkout without registration"}),(0,a.jsx)("li",{children:"Purchase at retail price"}),(0,a.jsx)("li",{children:"No account required"}),(0,a.jsx)("li",{children:"Option to create account later"})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>{U(!0)},className:"w-full inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100",children:[(0,a.jsx)(x.NPy,{className:"mr-2"}),"Continue as Guest"]})]})]})]}):(0,a.jsx)(u,{addresses:A,selectedAddressId:M,onAddressSelect:e=>{q(e)},onSubmit:e=>{console.log("Address submitted:",e),F(1)}})}),1===L&&(0,a.jsx)(h,{methods:D,selectedMethodId:O,onMethodSelect:e=>{G(e)},onSubmit:()=>{if(!O)return void Y("Please select a shipping method");F(2)},onBack:()=>F(0)}),2===L&&(0,a.jsx)(p,{selectedMethod:I,onMethodSelect:e=>{_(e)},onSubmit:()=>{if(!I)return void Y("Please select a payment method");F(3)},onBack:()=>F(1),isGuestCheckout:J}),3===L&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Review Your Order"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Address"}),(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-md",children:J&&ee?(0,a.jsxs)(a.Fragment,{children:[ee.name,(0,a.jsx)("br",{}),ee.addressLine1,ee.addressLine2&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("br",{}),ee.addressLine2]}),(0,a.jsx)("br",{}),ee.city,", ",ee.region," ",ee.postalCode,(0,a.jsx)("br",{}),"Phone: ",ee.phone,(0,a.jsx)("br",{}),"Email: ",ee.email]}):(0,a.jsxs)(a.Fragment,{children:[null==(e=A.find(e=>e.id===M))?void 0:e.name,(0,a.jsx)("br",{}),null==(s=A.find(e=>e.id===M))?void 0:s.addressLine1,(0,a.jsx)("br",{}),null==(r=A.find(e=>e.id===M))?void 0:r.city,","," ",null==(t=A.find(e=>e.id===M))?void 0:t.region," ",null==(i=A.find(e=>e.id===M))?void 0:i.postalCode,(0,a.jsx)("br",{}),"Phone: ",null==(g=A.find(e=>e.id===M))?void 0:g.phone]})}),(0,a.jsx)("button",{type:"button",className:"mt-2 text-sm text-blue-600 hover:text-blue-500",onClick:()=>F(0),children:"Change"})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-md",children:[null==(y=D.find(e=>e.id===O))?void 0:y.name," - ₱",null==(j=D.find(e=>e.id===O))?void 0:j.price.toFixed(2)]}),(0,a.jsx)("button",{type:"button",className:"mt-2 text-sm text-blue-600 hover:text-blue-500",onClick:()=>F(1),children:"Change"})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-md",children:["credit_card"===I&&"Credit Card","gcash"===I&&"GCash","maya"===I&&"Maya","bank_transfer"===I&&"Bank Transfer","wallet"===I&&"Wallet Balance","cod"===I&&"Cash on Delivery"]}),(0,a.jsx)("button",{type:"button",className:"mt-2 text-sm text-blue-600 hover:text-blue-500",onClick:()=>F(2),children:"Change"})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)("button",{type:"button",className:"w-full flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:ea,disabled:H,children:H?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.hW,{className:"animate-spin mr-2"}),"Processing..."]}):"Place Order"})})]})]})]}),(0,a.jsx)("div",{className:"lg:w-1/3",children:(0,a.jsx)(b,{items:k,shippingFee:O&&(null==(N=D.find(e=>e.id===O))?void 0:N.price)||0,isGuestCheckout:J})})]})]})}):(0,a.jsx)(m.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(x.hW,{className:"animate-spin h-8 w-8 text-green-500 mr-2"}),(0,a.jsx)("span",{className:"text-xl",children:"Loading..."})]})})}},90028:(e,s,r)=>{Promise.resolve().then(r.bind(r,18959))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(90028)),_N_E=e.O()}]);
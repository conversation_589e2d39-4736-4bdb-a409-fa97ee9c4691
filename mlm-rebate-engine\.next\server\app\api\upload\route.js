(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={633:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>x,serverHooks:()=>j,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{POST:()=>y});var o=t(96559),n=t(48088),i=t(37719),a=t(32190),u=t(19854),l=t(12909);let c=require("fs/promises");var p=t(33873),d=t(55511);let f={randomUUID:d.randomUUID},g=new Uint8Array(256),m=g.length,w=[];for(let e=0;e<256;++e)w.push((e+256).toString(16).slice(1));let h=function(e,r,t){if(f.randomUUID&&!r&&!e)return f.randomUUID();let s=(e=e||{}).random??e.rng?.()??(m>g.length-16&&((0,d.randomFillSync)(g),m=0),g.slice(m,m+=16));if(s.length<16)throw Error("Random bytes length must be >= 16");if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,r){if((t=t||0)<0||t+16>r.length)throw RangeError(`UUID byte range ${t}:${t+15} is out of buffer bounds`);for(let e=0;e<16;++e)r[t+e]=s[e];return r}return function(e,r=0){return(w[e[r+0]]+w[e[r+1]]+w[e[r+2]]+w[e[r+3]]+"-"+w[e[r+4]]+w[e[r+5]]+"-"+w[e[r+6]]+w[e[r+7]]+"-"+w[e[r+8]]+w[e[r+9]]+"-"+w[e[r+10]]+w[e[r+11]]+w[e[r+12]]+w[e[r+13]]+w[e[r+14]]+w[e[r+15]]).toLowerCase()}(s)};async function y(e){try{let r=await (0,u.getServerSession)(l.Nh);if(!r||!r.user)return a.NextResponse.json({error:"You must be logged in to upload files"},{status:401});if(!r.user.email)return a.NextResponse.json({error:"User email not found in session"},{status:400});let t=(await e.formData()).get("file");if(!t)return a.NextResponse.json({error:"No file uploaded"},{status:400});if(!["image/jpeg","image/png","image/webp","image/gif"].includes(t.type))return a.NextResponse.json({error:"File type not allowed. Please upload a JPEG, PNG, WebP, or GIF image."},{status:400});if(t.size>5242880)return a.NextResponse.json({error:"File size exceeds the 5MB limit"},{status:400});let s=t.name.split(".").pop(),o=`${h()}.${s}`,n=(0,p.join)(process.cwd(),"public","uploads");try{let e=await t.arrayBuffer(),r=Buffer.from(e),s=(0,p.join)(n,o);await (0,c.writeFile)(s,r);let i=`/uploads/${o}`;return a.NextResponse.json({success:!0,url:i,message:"File uploaded successfully"})}catch(e){return console.error("Error saving file:",e),a.NextResponse.json({error:"Failed to save the uploaded file"},{status:500})}}catch(e){return console.error("Error uploading file:",e),a.NextResponse.json({error:"Failed to process file upload"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:v,serverHooks:j}=x;function P(){return(0,i.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:v})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>c,aP:()=>l});var s=t(96330),o=t(13581),n=t(85663),i=t(55511),a=t.n(i);async function u(e){return await n.Ay.hash(e,10)}function l(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:i,...a}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=o?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(35426));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(633));module.exports=s})();
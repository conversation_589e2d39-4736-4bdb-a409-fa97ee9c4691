(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8938],{19825:()=>{},36680:(e,s,t)=>{Promise.resolve().then(t.bind(t,74271))},74271:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(95155),r=t(12115),l=t(12108),n=t(35695),i=t(99526),d=t(29911),c=t(46671);t.g.prisma||new c.PrismaClient({log:["query"]});var o=t(25943),m=t(57599);t(8777);var x=t(87358);new c.PrismaClient,(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,s){if(console.log("Authorize function called with credentials:",null==e?void 0:e.email),!(null==e?void 0:e.email)||!(null==e?void 0:e.password))throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let s=new c.PrismaClient,t=await s.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await s.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let a=await m.Ay.compare(e.password,t.password);if(console.log("Password valid:",a),!a)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:r,...l}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}}),x.env.NEXTAUTH_SECRET;var u=function(e){return e.NEW_MEMBER="new_member",e.ESTABLISHED_MEMBER="established_member",e.HIGH_PERFORMER="high_performer",e.EDGE_CASES="edge_cases",e}({});function h(){var e;let{data:s,status:t}=(0,l.useSession)(),c=(0,n.useRouter)(),[o,m]=(0,r.useState)(u.NEW_MEMBER),[x,h]=(0,r.useState)(1),[g,p]=(0,r.useState)("test"),[b,f]=(0,r.useState)("cleanup_".concat(Date.now())),[j,N]=(0,r.useState)(!1),[w,y]=(0,r.useState)(null),[v,E]=(0,r.useState)(null),[C,S]=(0,r.useState)([]),[k,_]=(0,r.useState)(null),[M,R]=(0,r.useState)(!1),[T,A]=(0,r.useState)(null),[P,D]=(0,r.useState)(null),[H,B]=(0,r.useState)(0),[G,I]=(0,r.useState)(""),[F,U]=(0,r.useState)(!1),[O,L]=(0,r.useState)(null);if("unauthenticated"===t)return c.push("/login?returnUrl=/admin/test-data"),null;if("loading"===t)return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})});if((null==s||null==(e=s.user)?void 0:e.role)!=="admin")return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,a.jsxs)("h1",{className:"text-red-700 text-lg font-semibold flex items-center",children:[(0,a.jsx)(d.BS8,{className:"mr-2"}),"Access Denied"]}),(0,a.jsx)("p",{className:"mt-2",children:"You do not have permission to access this page. This page is restricted to administrators only."})]})});let W=async()=>{try{N(!0),y(null),E(null),S([]),_(null);let e=await fetch("/api/admin/test-data/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({scenario:o,count:x,prefix:g,cleanupToken:b})}),s=await e.json();if(!e.ok)throw Error(s.error||"Failed to generate test data");E(s.message),S(s.users||[]),_(s.stats||null)}catch(e){console.error("Error generating test data:",e),y(e instanceof Error?e.message:String(e))}finally{N(!1)}},Y=async()=>{try{R(!0),A(null),D(null),B(0);let e=G||b,s=await fetch("/api/admin/test-data/cleanup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cleanupToken:e})}),t=await s.json();if(!s.ok)throw Error(t.error||"Failed to clean up test data");D(t.message),B(t.count||0),U(!1),e===b&&(S([]),_(null))}catch(e){console.error("Error cleaning up test data:",e),A(e instanceof Error?e.message:String(e))}finally{R(!1)}},X=(e,s)=>{navigator.clipboard.writeText(e),L(s),setTimeout(()=>L(null),2e3)},z=e=>{switch(e){case u.NEW_MEMBER:return(0,a.jsx)(d.NPy,{className:"text-green-500"});case u.ESTABLISHED_MEMBER:return(0,a.jsx)(d.YXz,{className:"text-blue-500"});case u.HIGH_PERFORMER:return(0,a.jsx)(d.p7b,{className:"text-purple-500"});case u.EDGE_CASES:return(0,a.jsx)(d.XiY,{className:"text-red-500"});default:return(0,a.jsx)(d.YXz,{className:"text-gray-500"})}};return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Data Generator"}),(0,a.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(d.BS8,{className:"h-5 w-5 text-yellow-400"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,a.jsx)("strong",{children:"Warning:"})," This tool is for testing purposes only. Generated data will be marked as test data and can be cleaned up using the cleanup token."]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h2",{className:"font-medium text-gray-700",children:"Generate Test Data"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Test Scenario"}),(0,a.jsxs)("select",{className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:o,onChange:e=>m(e.target.value),children:[(0,a.jsx)("option",{value:u.NEW_MEMBER,children:"New Member"}),(0,a.jsx)("option",{value:u.ESTABLISHED_MEMBER,children:"Established Member"}),(0,a.jsx)("option",{value:u.HIGH_PERFORMER,children:"High Performer"}),(0,a.jsx)("option",{value:u.EDGE_CASES,children:"Edge Cases"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:(e=>{switch(e){case u.NEW_MEMBER:return"New member with no downline, no purchases, and no rebates";case u.ESTABLISHED_MEMBER:return"Established member with moderate downline (3-5), purchases (5-10), and rebates";case u.HIGH_PERFORMER:return"High-performing member with large downline (20-30), many purchases (20-30), and high earnings";case u.EDGE_CASES:return"Edge cases with unusual data patterns (very long names, extreme values, etc.)";default:return""}})(o)})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Number of Users"}),(0,a.jsx)("input",{type:"number",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:x,onChange:e=>h(Math.max(1,parseInt(e.target.value)||1)),min:"1",max:"10"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Maximum 10 users per generation"})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Prefix"}),(0,a.jsx)("input",{type:"text",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:g,onChange:e=>p(e.target.value)}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:["Emails will be generated as ",g,"_",o,"<EMAIL>, etc."]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cleanup Token"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("input",{type:"text",className:"flex-1 border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:b,onChange:e=>f(e.target.value)}),(0,a.jsx)("button",{type:"button",className:"inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100",onClick:()=>f("cleanup_".concat(Date.now())),title:"Generate new token",children:(0,a.jsx)(d.pXu,{})})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Save this token to clean up the generated test data later"})]}),(0,a.jsx)("button",{type:"button",className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:W,disabled:j,children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.hW,{className:"animate-spin mr-2"}),"Generating..."]}):(0,a.jsx)(a.Fragment,{children:"Generate Test Data"})}),w&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm",children:w}),v&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-green-50 text-green-700 rounded-md text-sm",children:v})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h2",{className:"font-medium text-gray-700",children:"Clean Up Test Data"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cleanup Token"}),(0,a.jsx)("input",{type:"text",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:G,onChange:e=>I(e.target.value),placeholder:"Enter cleanup token"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Enter the cleanup token used when generating the test data"})]}),F?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"mb-2 text-sm text-red-600 font-medium",children:"Are you sure you want to clean up all test data with this token?"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{type:"button",className:"flex-1 flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",onClick:Y,disabled:M,children:M?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.hW,{className:"animate-spin mr-2"}),"Cleaning..."]}):(0,a.jsx)(a.Fragment,{children:"Yes, Clean Up"})}),(0,a.jsx)("button",{type:"button",className:"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>U(!1),disabled:M,children:"Cancel"})]})]}):(0,a.jsxs)("button",{type:"button",className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",onClick:()=>U(!0),disabled:M||!G&&!b,children:[(0,a.jsx)(d.qbC,{className:"mr-2"}),"Clean Up Test Data"]}),T&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm",children:T}),P&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-green-50 text-green-700 rounded-md text-sm",children:[P,H>0&&(0,a.jsxs)("p",{className:"mt-1 font-medium",children:[H," items deleted"]})]})]})]})]}),C.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h2",{className:"font-medium text-gray-700",children:"Generated Test Users"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Password"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Scenario"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:C.map((e,s)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center",children:z(e.scenario)}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.email}),(0,a.jsx)("button",{className:"text-xs text-blue-600 hover:text-blue-800 flex items-center mt-1",onClick:()=>X(e.email,3*s),children:O===3*s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.aZA,{className:"mr-1"}),"Copied!"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Y4c,{className:"mr-1"}),"Copy"]})})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.password}),(0,a.jsx)("button",{className:"text-xs text-blue-600 hover:text-blue-800 flex items-center mt-1",onClick:()=>X(e.password,3*s+1),children:O===3*s+1?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.aZA,{className:"mr-1"}),"Copied!"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Y4c,{className:"mr-1"}),"Copy"]})})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \n                          ".concat(e.scenario===u.NEW_MEMBER?"bg-green-100 text-green-800":e.scenario===u.ESTABLISHED_MEMBER?"bg-blue-100 text-blue-800":e.scenario===u.HIGH_PERFORMER?"bg-purple-100 text-purple-800":"bg-red-100 text-red-800"),children:e.scenario.replace("_"," ")})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("button",{className:"text-blue-600 hover:text-blue-800 flex items-center",onClick:()=>{let s="/login?email=".concat(encodeURIComponent(e.email),"&password=").concat(encodeURIComponent(e.password));window.open(s,"_blank")},children:["Login as User",(0,a.jsx)(d.Z0P,{className:"ml-1"})]})})]},e.id))})]})})]}),k&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h2",{className:"font-medium text-gray-700",children:"Test Data Statistics"})}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-blue-500 text-xl font-semibold",children:k.usersCreated}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Users Created"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-500 text-xl font-semibold",children:k.downlinesCreated}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Downlines Created"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-purple-500 text-xl font-semibold",children:k.purchasesCreated}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Purchases Created"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-yellow-600 text-xl font-semibold",children:k.rebatesGenerated}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Rebates Generated"})]}),(0,a.jsxs)("div",{className:"bg-indigo-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-indigo-500 text-xl font-semibold",children:k.referralLinksCreated}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Referral Links Created"})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsxs)("h2",{className:"font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(d.__w,{className:"mr-2 text-blue-500"}),"Test Scenarios"]})}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,a.jsx)(d.NPy,{className:"mr-2 text-green-500"}),"New Member"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates a new member with no downline, no purchases, and no rebates. This is useful for testing the onboarding experience and empty states in the dashboard."})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,a.jsx)(d.YXz,{className:"mr-2 text-blue-500"}),"Established Member"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates a member with a moderate downline (3-5 members), purchases (5-10), and rebates. This is useful for testing the typical user experience with some activity."})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,a.jsx)(d.p7b,{className:"mr-2 text-purple-500"}),"High Performer"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates a high-performing member with a large downline (20-30 members), many purchases (20-30), and high earnings. This is useful for testing performance with large data sets and formatting of large numbers."})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,a.jsx)(d.XiY,{className:"mr-2 text-red-500"}),"Edge Cases"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates users with unusual data patterns, such as very long names, extreme values, etc. This is useful for testing edge cases and error handling."})]})]})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,9268,6874,2108,6766,9890,9526,8441,1684,7358],()=>s(36680)),_N_E=e.O()}]);
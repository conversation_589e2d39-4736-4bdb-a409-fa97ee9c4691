(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},64622:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95155),a=s(12115),n=s(35695),l=s(6874),o=s.n(l),c=s(66766),i=s(29911);function d(){(0,n.useRouter)();let e=(0,n.useSearchParams)(),[t,s]=(0,a.useState)(null),[l,d]=(0,a.useState)(""),[m,u]=(0,a.useState)(""),[x,h]=(0,a.useState)(!1),[f,g]=(0,a.useState)(!1),[p,b]=(0,a.useState)(!0),[w,j]=(0,a.useState)(!0),[y,v]=(0,a.useState)(!1),[N,k]=(0,a.useState)(!1),[P,O]=(0,a.useState)(!1),[S,C]=(0,a.useState)(""),[E,L]=(0,a.useState)(!1),[z,A]=(0,a.useState)(!0),[F,H]=(0,a.useState)(!1),R=/[A-Z]/.test(l),B=/[a-z]/.test(l),_=/[0-9]/.test(l),D=/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(l),M=[l.length>=8,R,B,_,D].filter(Boolean).length;(0,a.useEffect)(()=>{let t=null==e?void 0:e.get("token");s(t),(async()=>{if(!t){A(!1),H(!0);return}try{await new Promise(e=>setTimeout(e,1e3)),A(!0)}catch(e){console.error("Error validating token:",e),A(!1)}finally{H(!0)}})()},[e]);let V=async e=>{e.preventDefault();let t=l.length>=8,s=m===l;if(b(t),j(s),!t||!s)return void C("Please correct the errors in the form");O(!0),C("");try{await new Promise(e=>setTimeout(e,1500)),L(!0)}catch(e){console.error("Error resetting password:",e),C("Failed to reset password: ".concat(e instanceof Error?e.message:String(e)))}finally{O(!1)}};return F?z?(0,r.jsxs)("div",{className:"min-h-screen flex flex-col md:flex-row",children:[(0,r.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-gradient-to-br from-green-500 to-green-700 text-white p-12 flex-col justify-between relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsx)("div",{className:"relative w-12 h-12 mr-3",children:(0,r.jsx)(c.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain invert"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Extreme Life Herbal"})]}),(0,r.jsxs)("div",{className:"mt-16 mb-8",children:[(0,r.jsx)("h2",{className:"text-4xl font-bold mb-6",children:"Create New Password"}),(0,r.jsx)("p",{className:"text-xl opacity-90 mb-8",children:"Choose a strong password to protect your account and keep your business secure."}),(0,r.jsxs)("div",{className:"bg-white/10 p-6 rounded-lg backdrop-blur-sm",children:[(0,r.jsx)("p",{className:"italic text-white/90 mb-4",children:'"A strong password is the first line of defense for your account. Make sure to use a unique password that you don\'t use for other services."'}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-white/30 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Security Team"}),(0,r.jsx)("p",{className:"text-sm opacity-75",children:"Extreme Life Herbal"})]})]})]})]})]}),(0,r.jsx)("div",{className:"absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -mr-48 -mt-48"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 w-96 h-96 bg-white/5 rounded-full -ml-48 -mb-48"}),(0,r.jsx)("div",{className:"relative z-10",children:(0,r.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",new Date().getFullYear()," Extreme Life Herbal Products. All rights reserved."]})})]}),(0,r.jsxs)("div",{className:"flex flex-col justify-center md:w-1/2 p-6 sm:p-12 bg-white",children:[(0,r.jsx)("div",{className:"md:hidden flex justify-center mb-8",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,r.jsx)(c.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})}),(0,r.jsx)("h1",{className:"text-xl font-bold text-green-700",children:"Extreme Life Herbal"})]})}),(0,r.jsxs)("div",{className:"max-w-md mx-auto w-full",children:[(0,r.jsxs)(o(),{href:"/login",className:"inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 mb-6 transition-colors",children:[(0,r.jsx)(i.QVr,{className:"mr-2"})," Back to login"]}),E?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,r.jsx)(i.CMH,{className:"h-6 w-6 text-green-600"})}),(0,r.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Password Reset Successful"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Your password has been successfully reset. You can now log in with your new password."}),(0,r.jsx)(o(),{href:"/login",className:"inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors",children:"Go to Login"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Reset Password"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Create a new password for your account. Make sure it's strong and secure."}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:V,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(i.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"password",name:"password",type:y?"text":"password",autoComplete:"new-password",required:!0,className:"appearance-none block w-full pl-10 pr-10 py-2 border ".concat(p?x?"border-green-500":"border-gray-300":"border-red-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 transition-colors sm:text-sm"),placeholder:"••••••••",value:l,onChange:e=>{let t=e.target.value;d(t),b(t.length>=8),m&&j(m===t)},onFocus:()=>h(!0),onBlur:()=>h(!1)}),(0,r.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>v(!y),children:y?(0,r.jsx)(i.mx3,{className:"h-5 w-5 text-gray-400"}):(0,r.jsx)(i.Ny1,{className:"h-5 w-5 text-gray-400"})})]}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Password strength:"}),(0,r.jsxs)("span",{className:"text-xs font-medium",children:[0===M&&"Very Weak",1===M&&"Weak",2===M&&"Fair",3===M&&"Good",4===M&&"Strong",5===M&&"Very Strong"]})]}),(0,r.jsx)("div",{className:"h-1.5 w-full bg-gray-200 rounded-full overflow-hidden",children:(0,r.jsx)("div",{className:"h-full ".concat(0===M?"w-0":1===M?"w-1/5 bg-red-500":2===M?"w-2/5 bg-orange-500":3===M?"w-3/5 bg-yellow-500":4===M?"w-4/5 bg-blue-500":"w-full bg-green-500"," transition-all duration-300")})}),(0,r.jsxs)("ul",{className:"mt-2 space-y-1 text-xs text-gray-500",children:[(0,r.jsxs)("li",{className:"flex items-center ".concat(l.length>=8?"text-green-600":""),children:[(0,r.jsx)("span",{className:"mr-1 ".concat(l.length>=8?"text-green-600":""),children:"•"}),"At least ",8," characters"]}),(0,r.jsxs)("li",{className:"flex items-center ".concat(R?"text-green-600":""),children:[(0,r.jsx)("span",{className:"mr-1 ".concat(R?"text-green-600":""),children:"•"}),"At least one uppercase letter"]}),(0,r.jsxs)("li",{className:"flex items-center ".concat(B?"text-green-600":""),children:[(0,r.jsx)("span",{className:"mr-1 ".concat(B?"text-green-600":""),children:"•"}),"At least one lowercase letter"]}),(0,r.jsxs)("li",{className:"flex items-center ".concat(_?"text-green-600":""),children:[(0,r.jsx)("span",{className:"mr-1 ".concat(_?"text-green-600":""),children:"•"}),"At least one number"]}),(0,r.jsxs)("li",{className:"flex items-center ".concat(D?"text-green-600":""),children:[(0,r.jsx)("span",{className:"mr-1 ".concat(D?"text-green-600":""),children:"•"}),"At least one special character"]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(i.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"confirm-password",name:"confirm-password",type:N?"text":"password",autoComplete:"new-password",required:!0,className:"appearance-none block w-full pl-10 pr-10 py-2 border ".concat(w?f?"border-green-500":"border-gray-300":"border-red-300"," rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 transition-colors sm:text-sm"),placeholder:"••••••••",value:m,onChange:e=>{let t=e.target.value;u(t),j(t===l)},onFocus:()=>g(!0),onBlur:()=>g(!1)}),(0,r.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>k(!N),children:N?(0,r.jsx)(i.mx3,{className:"h-5 w-5 text-gray-400"}):(0,r.jsx)(i.Ny1,{className:"h-5 w-5 text-gray-400"})})]}),!w&&m&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:"Passwords do not match"})]}),S&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:S}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:P,className:"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors ".concat(P?"opacity-70 cursor-not-allowed":""),children:P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Resetting..."]}):"Reset Password"})})]})]})]})]})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-4",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-red-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),(0,r.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Invalid or Expired Link"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"The password reset link you clicked is invalid or has expired. Please request a new password reset link."}),(0,r.jsx)(o(),{href:"/forgot-password",className:"inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors",children:"Request New Link"})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("svg",{className:"animate-spin h-10 w-10 text-green-600 mx-auto mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Verifying your reset link..."})]})})}},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var r=s(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=r.createContext&&r.createContext(a),l=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e}).apply(this,arguments)}function c(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,r)}return s}function i(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?c(Object(s),!0).forEach(function(t){var r,a,n;r=e,a=t,n=s[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in r?Object.defineProperty(r,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):c(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>r.createElement(m,o({attr:i({},e.attr)},t),function e(t){return t&&t.map((t,s)=>r.createElement(t.tag,i({key:s},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var s,{attr:a,size:n,title:c}=e,d=function(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;s[r]=e[r]}return s}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)s=n[r],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}(e,l),m=n||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),r.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:s,style:i(i({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),e.children)};return void 0!==n?r.createElement(n.Consumer,null,e=>t(e)):t(a)}},87759:(e,t,s)=>{Promise.resolve().then(s.bind(s,64622))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,6766,8441,1684,7358],()=>t(87759)),_N_E=e.O()}]);
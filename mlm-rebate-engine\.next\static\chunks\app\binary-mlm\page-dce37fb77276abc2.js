(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6011],{39736:(e,s,r)=>{Promise.resolve().then(r.bind(r,59650))},59650:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h});var t=r(95155),a=r(12115),l=r(12108),n=r(35695),d=r(70357),i=r(29911);let c=e=>{switch(e.toLowerCase()){case"diamond":return"text-purple-600 bg-purple-100";case"platinum":return"text-blue-600 bg-blue-100";case"gold":return"text-yellow-600 bg-yellow-100";case"silver":return"text-gray-600 bg-gray-200";case"bronze":return"text-orange-600 bg-orange-100";default:return"text-green-600 bg-green-100"}},o=e=>{let{node:s,isRoot:r=!1,depth:l,maxDepth:n,initialExpandedLevels:d,onUserSelect:x}=e,[m,h]=(0,a.useState)(l<d),u=null!==s.left||null!==s.right,g=u&&l<n,p=c(s.user.rank.name);return(0,t.jsxs)("div",{className:"mb-2 ".concat(r?"":"ml-6"),children:[(0,t.jsxs)("div",{className:"flex items-center p-2 rounded-lg border ".concat(r?"bg-gray-100 border-gray-300":"bg-white border-gray-200"," hover:bg-gray-50 transition-colors"),children:[g&&(0,t.jsx)("button",{onClick:()=>h(!m),className:"mr-2 text-gray-500 hover:text-gray-700",children:m?(0,t.jsx)(i.Vr3,{}):(0,t.jsx)(i.X6T,{})}),(0,t.jsx)("div",{className:"flex-shrink-0 mr-3",children:(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center ".concat(p),children:(0,t.jsx)(i.x$1,{className:"text-lg"})})}),(0,t.jsxs)("div",{className:"flex-grow",onClick:()=>x&&x(s.user),children:[(0,t.jsx)("div",{className:"font-medium",children:s.user.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:s.user.email}),(0,t.jsxs)("div",{className:"flex items-center mt-1",children:[(0,t.jsx)("span",{className:"text-xs px-2 py-0.5 rounded-full ".concat(p),children:s.user.rank.name}),s.position&&(0,t.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-700",children:"left"===s.position?"Left Leg":"Right Leg"})]})]}),u&&(0,t.jsx)("div",{className:"flex-shrink-0 ml-2 text-gray-500",children:(0,t.jsx)(i.YXz,{})})]}),m&&u&&(0,t.jsxs)("div",{className:"mt-2 grid grid-cols-2 gap-4",children:[(0,t.jsx)("div",{className:"border-t-2 border-l-2 border-gray-200 pt-2 pl-2",children:s.left?(0,t.jsx)(o,{node:s.left,depth:l+1,maxDepth:n,initialExpandedLevels:d,onUserSelect:x}):(0,t.jsxs)("div",{className:"p-4 border border-dashed border-gray-300 rounded-lg text-center text-gray-500",children:[(0,t.jsx)(i.QVr,{className:"mx-auto mb-2"}),"Empty Left Position"]})}),(0,t.jsx)("div",{className:"border-t-2 border-r-2 border-gray-200 pt-2 pr-2",children:s.right?(0,t.jsx)(o,{node:s.right,depth:l+1,maxDepth:n,initialExpandedLevels:d,onUserSelect:x}):(0,t.jsxs)("div",{className:"p-4 border border-dashed border-gray-300 rounded-lg text-center text-gray-500",children:[(0,t.jsx)(i.Z0P,{className:"mx-auto mb-2"}),"Empty Right Position"]})})]})]})},x=e=>{let{data:s,maxDepth:r=6,initialExpandedLevels:l=2,onUserSelect:n}=e,[d,i]=(0,a.useState)(!1);return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,t.jsxs)("div",{className:"p-4 border-b",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Binary MLM Structure"}),(0,t.jsx)("button",{onClick:()=>i(!d),className:"px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700",children:d?"Collapse All":"Expand All"})]}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:["Showing binary placement structure up to ",r," levels deep"]})]}),(0,t.jsx)("div",{className:"p-4 overflow-x-auto",children:(0,t.jsx)(o,{node:s,isRoot:!0,depth:0,maxDepth:r,initialExpandedLevels:d?r:l,onUserSelect:n})})]})},m=e=>{let{performance:s,commissions:r,onExport:a}=e,l=["January","February","March","April","May","June","July","August","September","October","November","December"][s.month-1];return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,t.jsx)("div",{className:"p-4 border-b",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold",children:["Earnings Report: ",l," ",s.year]}),a&&(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("button",{className:"px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 flex items-center",children:[(0,t.jsx)(i.WCW,{className:"mr-1"})," Export"]}),(0,t.jsx)("div",{className:"absolute right-0 mt-2 w-32 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden group-hover:block z-10",children:(0,t.jsxs)("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[(0,t.jsx)("button",{onClick:()=>a("csv"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as CSV"}),(0,t.jsx)("button",{onClick:()=>a("json"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as JSON"})]})})]})]})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-2 rounded-full mr-3",children:(0,t.jsx)(i.v$b,{className:"text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Total Earnings"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",s.totalEarnings.toFixed(2)]})]})]})}),(0,t.jsx)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-green-100 p-2 rounded-full mr-3",children:(0,t.jsx)(i.YXz,{className:"text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Group Volume"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[s.totalGroupPV.toFixed(0)," PV"]})]})]})}),(0,t.jsx)("div",{className:"bg-purple-50 p-4 rounded-lg border border-purple-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-purple-100 p-2 rounded-full mr-3",children:(0,t.jsx)(i.MxO,{className:"text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"Personal Volume"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[s.personalPV.toFixed(0)," PV"]})]})]})}),(0,t.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg border border-yellow-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-yellow-100 p-2 rounded-full mr-3",children:(0,t.jsx)(i.Wp,{className:"text-yellow-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-yellow-600 font-medium",children:"Referral Bonus"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",s.directReferralBonus.toFixed(2)]})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gray-50 p-3 border-b",children:(0,t.jsx)("h4",{className:"font-medium",children:"PV Breakdown"})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Personal PV"}),(0,t.jsx)("span",{className:"font-medium",children:s.personalPV.toFixed(0)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(Math.min(100,s.personalPV/(s.totalGroupPV||1)*100),"%")}})})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Left Leg PV"}),(0,t.jsx)("span",{className:"font-medium",children:s.leftLegPV.toFixed(0)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(Math.min(100,s.leftLegPV/(s.totalGroupPV||1)*100),"%")}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Right Leg PV"}),(0,t.jsx)("span",{className:"font-medium",children:s.rightLegPV.toFixed(0)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-purple-600 h-2 rounded-full",style:{width:"".concat(Math.min(100,s.rightLegPV/(s.totalGroupPV||1)*100),"%")}})})]})]})]}),(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gray-50 p-3 border-b",children:(0,t.jsx)("h4",{className:"font-medium",children:"Commission Breakdown"})}),(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("table",{className:"w-full",children:(0,t.jsxs)("tbody",{children:[(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("td",{className:"py-2 text-sm text-gray-600",children:"Direct Referral Bonus"}),(0,t.jsxs)("td",{className:"py-2 text-right font-medium",children:["₱",s.directReferralBonus.toFixed(2)]})]}),(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("td",{className:"py-2 text-sm text-gray-600",children:"Level Commissions"}),(0,t.jsxs)("td",{className:"py-2 text-right font-medium",children:["₱",s.levelCommissions.toFixed(2)]})]}),(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("td",{className:"py-2 text-sm text-gray-600",children:"Group Volume Bonus"}),(0,t.jsxs)("td",{className:"py-2 text-right font-medium",children:["₱",s.groupVolumeBonus.toFixed(2)]})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"py-2 font-medium",children:"Total Earnings"}),(0,t.jsxs)("td",{className:"py-2 text-right font-bold",children:["₱",s.totalEarnings.toFixed(2)]})]})]})})})]})]}),r&&r.breakdown.levelCommissions.byLevel&&(0,t.jsxs)("div",{className:"mt-6 border rounded-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gray-50 p-3 border-b",children:(0,t.jsx)("h4",{className:"font-medium",children:"Level Commission Details"})}),(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-6 gap-4",children:Object.entries(r.breakdown.levelCommissions.byLevel).map(e=>{let[s,r]=e;return(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Level ",s]}),(0,t.jsxs)("div",{className:"font-medium",children:["₱",parseFloat(r.toString()).toFixed(2)]})]},s)})})})]})]})]})};function h(){let{data:e,status:s}=(0,l.useSession)(),r=(0,n.useRouter)(),[c,o]=(0,a.useState)(!0),[h,u]=(0,a.useState)(null),[g,p]=(0,a.useState)(null),[b,j]=(0,a.useState)(new Date().getFullYear()),[f,N]=(0,a.useState)(new Date().getMonth()+1),[v,y]=(0,a.useState)({type:"",text:""});(0,a.useEffect)(()=>{"unauthenticated"===s&&r.push("/login")},[s,r]),(0,a.useEffect)(()=>{"authenticated"===s&&w()},[s]),(0,a.useEffect)(()=>{"authenticated"===s&&E()},[b,f,s]);let w=async()=>{o(!0);try{let e=await fetch("/api/binary-mlm?action=tree&maxDepth=6");if(!e.ok)throw Error("Failed to fetch binary MLM data: ".concat(e.statusText));let s=await e.json();u(s.tree)}catch(e){console.error("Error fetching binary MLM data:",e),y({type:"error",text:"Failed to load binary MLM structure. Please try again."})}finally{o(!1)}},E=async()=>{try{let e=await fetch("/api/binary-mlm?action=performance&year=".concat(b,"&month=").concat(f));if(!e.ok)throw Error("Failed to fetch performance data: ".concat(e.statusText));let s=await e.json();s.performance&&s.performance.length>0?p(s.performance[0]):(p(null),y({type:"info",text:"No performance data available for ".concat(P(f)," ").concat(b,".")}))}catch(e){console.error("Error fetching performance data:",e),y({type:"error",text:"Failed to load performance data. Please try again."})}},k=async()=>{o(!0),y({type:"",text:""});try{let e=await fetch("/api/binary-mlm",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"simulate",year:b,month:f})});if(!e.ok)throw Error("Failed to simulate earnings: ".concat(e.statusText));await e.json(),y({type:"success",text:"Successfully simulated earnings for ".concat(P(f)," ").concat(b,".")}),E()}catch(e){console.error("Error simulating earnings:",e),y({type:"error",text:"Failed to simulate earnings. Please try again."})}finally{o(!1)}},L=async e=>{try{if("csv"===e)return void window.open("/api/binary-mlm/export?type=personal&year=".concat(b,"&month=").concat(f,"&format=").concat(e),"_blank");let s=await fetch("/api/binary-mlm/export?type=personal&year=".concat(b,"&month=").concat(f,"&format=").concat(e));if(!s.ok)throw Error("Failed to export report: ".concat(s.statusText));let r=await s.json(),t=new Blob([JSON.stringify(r,null,2)],{type:"application/json"}),a=URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download="earnings_report_".concat(b,"_").concat(f,".json"),document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(a)}catch(e){console.error("Error exporting report:",e),y({type:"error",text:"Failed to export report. Please try again."})}},P=e=>["January","February","March","April","May","June","July","August","September","October","November","December"][e-1];return"loading"===s?(0,t.jsx)(d.A,{children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,t.jsx)(i.hW,{className:"animate-spin text-green-500 mr-2"}),(0,t.jsx)("span",{children:"Loading..."})]})}):(0,t.jsx)(d.A,{children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Binary MLM Dashboard"}),v.text&&(0,t.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("error"===v.type?"bg-red-100 text-red-700":"success"===v.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:v.text}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,t.jsx)(i.YYR,{className:"text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Monthly Earnings"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",g?g.totalEarnings.toFixed(2):"0.00"]})]})]})}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-green-100 p-3 rounded-full mr-4",children:(0,t.jsx)(i.YXz,{className:"text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Group Volume"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[g?g.totalGroupPV.toFixed(0):"0"," PV"]})]})]})}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-purple-100 p-3 rounded-full mr-4",children:(0,t.jsx)(i.NPy,{className:"text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Referral Bonus"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",g?g.directReferralBonus.toFixed(2):"0.00"]})]})]})}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full mr-4",children:(0,t.jsx)(i.bfZ,{className:"text-yellow-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Period"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[P(f)," ",b]})]})]})})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex flex-wrap items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 mb-4 md:mb-0",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"year",className:"block text-sm text-gray-600 mb-1",children:"Year"}),(0,t.jsx)("select",{id:"year",value:b,onChange:e=>j(parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:Array.from({length:5},(e,s)=>new Date().getFullYear()-2+s).map(e=>(0,t.jsx)("option",{value:e,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"month",className:"block text-sm text-gray-600 mb-1",children:"Month"}),(0,t.jsx)("select",{id:"month",value:f,onChange:e=>N(parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:Array.from({length:12},(e,s)=>s+1).map(e=>(0,t.jsx)("option",{value:e,children:P(e)},e))})]})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{onClick:k,disabled:c,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.hW,{className:"animate-spin inline mr-2"}),"Simulating..."]}):"Simulate Earnings"})})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[g?(0,t.jsx)(m,{performance:g,onExport:L}):(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6 text-center",children:(0,t.jsxs)("p",{className:"text-gray-500",children:["No performance data available for ",P(f)," ",b,'. Click "Simulate Earnings" to generate data.']})}),c?(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 flex items-center justify-center h-64",children:[(0,t.jsx)(i.hW,{className:"animate-spin text-green-500 mr-2"}),(0,t.jsx)("span",{children:"Loading binary structure..."})]}):h?(0,t.jsx)(x,{data:h,maxDepth:6,initialExpandedLevels:2,onUserSelect:e=>console.log("Selected user:",e)}):(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6 text-center",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No binary structure data available."})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(39736)),_N_E=e.O()}]);
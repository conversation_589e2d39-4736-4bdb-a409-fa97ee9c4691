(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[387],{18673:(e,t,s)=>{Promise.resolve().then(s.bind(s,86269))},74436:(e,t,s)=>{"use strict";s.d(t,{k5:()=>d});var a=s(12115),r={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(r),i=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a])}return e}).apply(this,arguments)}function l(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,a)}return s}function o(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?l(Object(s),!0).forEach(function(t){var a,r,n;a=e,r=t,n=s[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in a?Object.defineProperty(a,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):l(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}function d(e){return t=>a.createElement(m,c({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,s)=>a.createElement(t.tag,o({key:s},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var s,{attr:r,size:n,title:l}=e,d=function(e,t){if(null==e)return{};var s,a,r=function(e,t){if(null==e)return{};var s={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)s=n[a],!(t.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(e,s)&&(r[s]=e[s])}return r}(e,i),m=n||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),a.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,r,d,{className:s,style:o(o({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),l&&a.createElement("title",null,l),e.children)};return void 0!==n?a.createElement(n.Consumer,null,e=>t(e)):t(r)}},86269:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(95155),r=s(12115),n=s(12108),i=s(87747),c=s(29911),l=s(6874),o=s.n(l);function d(e){let{userId:t,limit:s=5,showControls:n=!0,onNotificationClick:i}=e,[l,d]=(0,r.useState)([]),[m,h]=(0,r.useState)(!0),[u,x]=(0,r.useState)(null),[b,f]=(0,r.useState)(!1),[y,g]=(0,r.useState)(null),[p,j]=(0,r.useState)(!1),[N,v]=(0,r.useState)({newMembers:!0,purchases:!0,rankAdvancements:!0,rebates:!0,system:!0,emailNotifications:!1,pushNotifications:!1});(0,r.useEffect)(()=>{(async()=>{h(!0),x(null);try{let e=new URLSearchParams({userId:t.toString(),limit:b?"50":s.toString()});y&&e.append("type",y);let a=await fetch("/api/genealogy/notifications?".concat(e.toString()));if(!a.ok)throw Error("Failed to fetch notifications");let r=await a.json();d(r)}catch(e){x(e instanceof Error?e.message:"An unknown error occurred")}finally{h(!1)}})()},[t,s,b,y]);let w=async e=>{try{if(!(await fetch("/api/genealogy/notifications/".concat(e,"/read"),{method:"POST"})).ok)throw Error("Failed to mark notification as read");d(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))}catch(e){console.error("Error marking notification as read:",e)}},k=async()=>{try{if(!(await fetch("/api/genealogy/notifications/read-all",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t})})).ok)throw Error("Failed to mark all notifications as read");d(e=>e.map(e=>({...e,isRead:!0})))}catch(e){console.error("Error marking all notifications as read:",e)}},S=e=>{e.isRead||w(e.id),i&&i(e)},O=async()=>{try{if(!(await fetch("/api/genealogy/notifications/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,settings:N})})).ok)throw Error("Failed to save notification settings");j(!1)}catch(e){console.error("Error saving notification settings:",e)}},C=e=>{switch(e){case"new_member":return(0,a.jsx)(c.NPy,{className:"text-green-500"});case"purchase":return(0,a.jsx)(c.AsH,{className:"text-blue-500"});case"rank_advancement":return(0,a.jsx)(c.SBv,{className:"text-yellow-500"});case"rebate":return(0,a.jsx)(FaWallet,{className:"text-purple-500"});case"system":return(0,a.jsx)(c.Pcn,{className:"text-gray-500"});default:return(0,a.jsx)(c.jNV,{className:"text-gray-500"})}},P=e=>{let t=new Date(e),s=Math.floor(Math.floor((new Date().getTime()-t.getTime())/1e3)/60),a=Math.floor(s/60),r=Math.floor(a/24);return r>0?"".concat(r," day").concat(r>1?"s":""," ago"):a>0?"".concat(a," hour").concat(a>1?"s":""," ago"):s>0?"".concat(s," minute").concat(s>1?"s":""," ago"):"Just now"},E=l.filter(e=>!e.isRead).length;return m&&0===l.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4 flex items-center justify-center h-32",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading notifications..."})]}):u?(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-4",children:(0,a.jsxs)("div",{className:"bg-red-50 p-3 rounded-md",children:[(0,a.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,a.jsx)(c.BS8,{className:"mr-2"}),"Error loading notifications"]}),(0,a.jsx)("p",{className:"text-red-600",children:u})]})}):p?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,a.jsx)(c.Pcn,{className:"mr-2 text-blue-500"}),"Notification Settings"]}),(0,a.jsx)("button",{onClick:()=>j(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border-b pb-3",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Notification Types"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"newMembers",checked:N.newMembers,onChange:e=>v(t=>({...t,newMembers:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"newMembers",className:"ml-2 block text-sm text-gray-700",children:"New Members"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"purchases",checked:N.purchases,onChange:e=>v(t=>({...t,purchases:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"purchases",className:"ml-2 block text-sm text-gray-700",children:"Purchases"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"rankAdvancements",checked:N.rankAdvancements,onChange:e=>v(t=>({...t,rankAdvancements:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"rankAdvancements",className:"ml-2 block text-sm text-gray-700",children:"Rank Advancements"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"rebates",checked:N.rebates,onChange:e=>v(t=>({...t,rebates:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"rebates",className:"ml-2 block text-sm text-gray-700",children:"Rebates"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"system",checked:N.system,onChange:e=>v(t=>({...t,system:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"system",className:"ml-2 block text-sm text-gray-700",children:"System Notifications"})]})]})]}),(0,a.jsxs)("div",{className:"border-b pb-3",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Delivery Methods"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"emailNotifications",checked:N.emailNotifications,onChange:e=>v(t=>({...t,emailNotifications:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"emailNotifications",className:"ml-2 block text-sm text-gray-700",children:"Email Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"pushNotifications",checked:N.pushNotifications,onChange:e=>v(t=>({...t,pushNotifications:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"pushNotifications",className:"ml-2 block text-sm text-gray-700",children:"Push Notifications"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>j(!1),className:"px-3 py-1.5 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{onClick:O,className:"px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700",children:"Save Settings"})]})]})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,a.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,a.jsx)(c.jNV,{className:"mr-2 text-blue-500"}),"Notifications",E>0&&(0,a.jsxs)("span",{className:"ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full",children:[E," new"]})]}),n&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>g(y?null:"new_member"),className:"p-1 rounded-md ".concat("new_member"===y?"bg-green-100 text-green-800":"text-gray-500 hover:text-gray-700"),title:"Filter by New Members",children:(0,a.jsx)(c.NPy,{})}),(0,a.jsx)("button",{onClick:()=>g(y?null:"purchase"),className:"p-1 rounded-md ".concat("purchase"===y?"bg-blue-100 text-blue-800":"text-gray-500 hover:text-gray-700"),title:"Filter by Purchases",children:(0,a.jsx)(c.AsH,{})}),(0,a.jsx)("button",{onClick:()=>g(y?null:"rank_advancement"),className:"p-1 rounded-md ".concat("rank_advancement"===y?"bg-yellow-100 text-yellow-800":"text-gray-500 hover:text-gray-700"),title:"Filter by Rank Advancements",children:(0,a.jsx)(c.SBv,{})}),(0,a.jsx)("button",{onClick:()=>j(!0),className:"p-1 text-gray-500 hover:text-gray-700",title:"Notification Settings",children:(0,a.jsx)(c.Pcn,{})})]})]}),0===l.length?(0,a.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,a.jsx)("p",{children:"No notifications found."}),y&&(0,a.jsx)("button",{onClick:()=>g(null),className:"mt-2 text-blue-600 text-sm",children:"Clear filter"})]}):(0,a.jsx)("div",{className:"divide-y",children:l.map(e=>{var t,s;return(0,a.jsx)("div",{className:"p-4 ".concat(e.isRead?"bg-white":"bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"mr-3 mt-1",children:C(e.type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"cursor-pointer",onClick:()=>S(e),children:[(0,a.jsx)("div",{className:"font-medium",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.message}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:P(e.createdAt)})]}),(0,a.jsxs)("div",{className:"mt-2 flex justify-end",children:[!e.isRead&&(0,a.jsx)("button",{onClick:()=>w(e.id),className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark as read"}),"new_member"===e.type&&(null==(t=e.data)?void 0:t.userId)&&(0,a.jsx)(o(),{href:"/users/".concat(e.data.userId),className:"ml-3 text-xs text-blue-600 hover:text-blue-800",children:"View Profile"}),"purchase"===e.type&&(null==(s=e.data)?void 0:s.purchaseId)&&(0,a.jsx)(o(),{href:"/purchases/".concat(e.data.purchaseId),className:"ml-3 text-xs text-blue-600 hover:text-blue-800",children:"View Purchase"})]})]})]})},e.id)})}),n&&(0,a.jsxs)("div",{className:"px-4 py-3 border-t bg-gray-50 flex justify-between items-center",children:[(0,a.jsx)("button",{onClick:k,className:"text-sm text-blue-600 hover:text-blue-800",disabled:0===E,children:"Mark all as read"}),(0,a.jsx)("button",{onClick:()=>f(!b),className:"text-sm text-blue-600 hover:text-blue-800",children:b?"Show less":"View all"})]})]})}function m(){let{data:e,status:t}=(0,n.useSession)(),[s,l]=(0,r.useState)(void 0),{data:m,isLoading:h}=(0,i.I)({queryKey:["user"],queryFn:async()=>{var t;if(!(null==e||null==(t=e.user)?void 0:t.email))return null;let s=await fetch("/api/users/me");if(!s.ok)throw Error("Failed to fetch user data");return await s.json()},enabled:"authenticated"===t});return(m&&!s&&l(m.id),"loading"===t||h)?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===t?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,a.jsx)(c.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view genealogy notifications."}),(0,a.jsx)(o(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Notifications"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Stay updated on your network's activities and achievements"})]}),(0,a.jsxs)(o(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(c.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:s?(0,a.jsx)(d,{userId:s,limit:20,showControls:!0,onNotificationClick:e=>{var t,s,a,r;switch(console.log("Notification clicked:",e),e.type){case"new_member":(null==(t=e.data)?void 0:t.userId)&&(window.location.href="/users/".concat(e.data.userId));break;case"purchase":(null==(s=e.data)?void 0:s.purchaseId)&&(window.location.href="/purchases/".concat(e.data.purchaseId));break;case"rank_advancement":(null==(a=e.data)?void 0:a.userId)&&(window.location.href="/users/".concat(e.data.userId));break;case"rebate":(null==(r=e.data)?void 0:r.rebateId)&&(window.location.href="/rebates/".concat(e.data.rebateId))}}}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading notifications..."})]})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold flex items-center mb-4",children:[(0,a.jsx)(c.__w,{className:"mr-2 text-blue-500"}),"About Notifications"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{children:"The Genealogy Notifications system keeps you informed about important events and activities in your network. Stay updated on new members, purchases, rank advancements, and more."}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Notification Types"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"New Members"})," - When someone joins your network"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Purchases"})," - When members in your network make purchases"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Rank Advancements"})," - When members achieve new ranks"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Rebates"})," - When you earn rebates from your network's activities"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"System"})," - Important system announcements and updates"]})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,a.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"Notification Settings"}),(0,a.jsx)("p",{className:"text-green-700 mb-2",children:"You can customize your notification preferences to receive only the types of notifications that matter to you."}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,a.jsx)("li",{children:"Choose which notification types to receive"}),(0,a.jsx)("li",{children:"Enable or disable email notifications"}),(0,a.jsx)("li",{children:"Enable or disable push notifications"})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,a.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"Tips for Managing Notifications"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,a.jsx)("li",{children:"Use filters to focus on specific notification types"}),(0,a.jsx)("li",{children:"Mark notifications as read to keep your inbox organized"}),(0,a.jsx)("li",{children:"Click on notifications to view more details or take action"}),(0,a.jsx)("li",{children:"Check notifications regularly to stay informed about your network"})]})]}),(0,a.jsx)("p",{children:"Notifications are an essential tool for managing your network effectively. They help you identify opportunities, recognize achievements, and stay connected with your team."})]})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6967,7747,8441,1684,7358],()=>t(18673)),_N_E=e.O()}]);
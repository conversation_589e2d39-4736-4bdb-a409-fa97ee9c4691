(()=>{var e={};e.id=8279,e.ids=[8279],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54787:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\checkout\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\checkout\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77603:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t={};r.r(t);var a=r(60687),n=r(43210),l=r(82136),i=r(16189),d=r(85814),o=r.n(d),c=r(28253),m=r(68367),x=r(23877);let u=({addresses:e,selectedAddressId:s,onAddressSelect:r,onSubmit:t})=>{let[l,i]=(0,n.useState)(!1),[d,o]=(0,n.useState)({name:"",phone:"",addressLine1:"",addressLine2:"",city:"",region:"",postalCode:"",isDefault:!1});(0,n.useEffect)(()=>{0===e.length&&i(!0)},[e]);let c=e=>{let{name:s,value:r,type:t,checked:a}=e.target;o(e=>({...e,[s]:"checkbox"===t?a:r}))};return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Shipping Address"}),e.length>0&&!l&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4",children:e.map(e=>(0,a.jsx)("div",{className:`border rounded-md p-4 cursor-pointer ${s===e.id?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>r(e.id),children:(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600",children:e.phone}),(0,a.jsx)("p",{className:"text-gray-600",children:e.addressLine1}),e.addressLine2&&(0,a.jsx)("p",{className:"text-gray-600",children:e.addressLine2}),(0,a.jsxs)("p",{className:"text-gray-600",children:[e.city,", ",e.region," ",e.postalCode]}),e.isDefault&&(0,a.jsx)("span",{className:"inline-flex items-center mt-1 px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Default"})]}),s===e.id&&(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)(x.CMH,{className:"text-white h-3 w-3"})})})]})},e.id))}),(0,a.jsxs)("button",{type:"button",className:"mt-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>i(!0),children:[(0,a.jsx)(x.OiG,{className:"mr-2 h-4 w-4"}),"Add New Address"]}),(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)("button",{type:"button",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:()=>t({id:s}),disabled:!s,children:"Continue to Shipping Method"})})]}),l&&(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(d)},children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6",children:[(0,a.jsxs)("div",{className:"sm:col-span-3",children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:d.name,onChange:c,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-3",children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:d.phone,onChange:c,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-6",children:[(0,a.jsx)("label",{htmlFor:"addressLine1",className:"block text-sm font-medium text-gray-700",children:"Address Line 1"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"addressLine1",name:"addressLine1",value:d.addressLine1,onChange:c,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-6",children:[(0,a.jsx)("label",{htmlFor:"addressLine2",className:"block text-sm font-medium text-gray-700",children:"Address Line 2 (Optional)"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"addressLine2",name:"addressLine2",value:d.addressLine2,onChange:c,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700",children:"City"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"city",name:"city",value:d.city,onChange:c,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"region",className:"block text-sm font-medium text-gray-700",children:"Region/Province"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"region",name:"region",value:d.region,onChange:c,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsxs)("div",{className:"sm:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-gray-700",children:"Postal Code"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{type:"text",id:"postalCode",name:"postalCode",value:d.postalCode,onChange:c,required:!0,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"})})]}),(0,a.jsx)("div",{className:"sm:col-span-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"isDefault",name:"isDefault",type:"checkbox",checked:d.isDefault,onChange:c,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,a.jsx)("div",{className:"ml-3 text-sm",children:(0,a.jsx)("label",{htmlFor:"isDefault",className:"font-medium text-gray-700",children:"Set as default shipping address"})})]})})]}),(0,a.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[e.length>0&&(0,a.jsx)("button",{type:"button",className:"py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>i(!1),children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:e.length>0?"Save & Continue":"Continue to Shipping Method"})]})]})]})},h=({methods:e,selectedMethodId:s,onMethodSelect:r,onSubmit:t,onBack:n})=>{let l=e=>`₱${e.toFixed(2)}`;return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Shipping Method"}),0===e.length?(0,a.jsxs)("div",{className:"text-center py-6 bg-gray-50 rounded-md",children:[(0,a.jsx)(x.dv1,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No shipping methods available"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Please try again later or contact support."})]}):(0,a.jsx)("div",{className:"space-y-4 mb-6",children:e.map(e=>(0,a.jsx)("div",{className:`border rounded-md p-4 cursor-pointer ${s===e.id?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>r(e.id),children:(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.dv1,{className:"h-5 w-5 text-gray-400 mr-2"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name})]}),e.description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:e.description}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:["Estimated delivery: ",e.estimatedDeliveryDays," day",1!==e.estimatedDeliveryDays?"s":""]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 mr-3",children:l(e.price)}),s===e.id&&(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)(x.CMH,{className:"text-white h-3 w-3"})})]})]})},e.id))}),(0,a.jsxs)("div",{className:"mt-6 flex justify-between",children:[(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:n,children:[(0,a.jsx)(x.QVr,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,a.jsx)("button",{type:"button",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:t,disabled:!s||0===e.length,children:"Continue to Payment"})]})]})},p=({selectedMethod:e,onMethodSelect:s,onSubmit:r,onBack:l,isGuestCheckout:i=!1})=>{let[d,o]=(0,n.useState)({cardNumber:"",cardholderName:"",expiryDate:"",cvv:""}),c=e=>{let{name:s,value:r}=e.target;o(e=>({...e,[s]:r}))},m=[{id:"credit_card",name:"Credit/Debit Card",icon:(0,a.jsx)(x.x1c,{className:"h-6 w-6"}),description:"Pay securely with your credit or debit card"},{id:"gcash",name:"GCash",icon:(0,a.jsx)(t.SiGcash,{className:"h-6 w-6 text-blue-600"}),description:"Pay using your GCash wallet"},{id:"maya",name:"Maya",icon:(0,a.jsx)(t.SiPaymaya,{className:"h-6 w-6 text-green-600"}),description:"Pay using your Maya wallet"},{id:"bank_transfer",name:"Bank Transfer",icon:(0,a.jsx)(x.MxO,{className:"h-6 w-6 text-green-700"}),description:"Pay via bank transfer"},{id:"wallet",name:"Wallet Balance",icon:(0,a.jsx)(x.lcY,{className:"h-6 w-6 text-purple-600"}),description:"Pay using your wallet balance"},{id:"cod",name:"Cash on Delivery",icon:(0,a.jsx)(x.MxO,{className:"h-6 w-6 text-yellow-600"}),description:"Pay when you receive your order"}];return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Payment Method"}),i&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,a.jsx)("strong",{children:"Guest Checkout:"})," You are checking out as a guest. Some payment methods like Wallet Balance are only available to registered members."]})}),(0,a.jsx)("div",{className:"space-y-4 mb-6",children:m.filter(e=>!i||"wallet"!==e.id).map(r=>(0,a.jsxs)("div",{className:`border rounded-md p-4 cursor-pointer ${e===r.id?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>s(r.id),children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mr-3",children:r.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:r.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:r.description})]})]}),e===r.id&&(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-green-500 flex items-center justify-center",children:(0,a.jsx)(x.CMH,{className:"text-white h-3 w-3"})})]}),"credit_card"===e&&"credit_card"===r.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cardNumber",className:"block text-sm font-medium text-gray-700",children:"Card Number"}),(0,a.jsx)("input",{type:"text",id:"cardNumber",name:"cardNumber",placeholder:"1234 5678 9012 3456",value:d.cardNumber,onChange:c,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cardholderName",className:"block text-sm font-medium text-gray-700",children:"Cardholder Name"}),(0,a.jsx)("input",{type:"text",id:"cardholderName",name:"cardholderName",placeholder:"John Doe",value:d.cardholderName,onChange:c,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"expiryDate",className:"block text-sm font-medium text-gray-700",children:"Expiry Date"}),(0,a.jsx)("input",{type:"text",id:"expiryDate",name:"expiryDate",placeholder:"MM/YY",value:d.expiryDate,onChange:c,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cvv",className:"block text-sm font-medium text-gray-700",children:"CVV"}),(0,a.jsx)("input",{type:"text",id:"cvv",name:"cvv",placeholder:"123",value:d.cvv,onChange:c,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]})]})]})}),"gcash"===e&&"gcash"===r.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"You will be redirected to GCash to complete your payment after placing your order."})}),"maya"===e&&"maya"===r.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"You will be redirected to Maya to complete your payment after placing your order."})}),"bank_transfer"===e&&"bank_transfer"===r.id&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Please transfer the total amount to one of the following bank accounts:"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-md text-sm",children:[(0,a.jsx)("p",{className:"font-medium",children:"BDO"}),(0,a.jsx)("p",{children:"Account Name: Extreme Life Herbal Products"}),(0,a.jsx)("p",{children:"Account Number: 1234 5678 9012"})]}),(0,a.jsxs)("div",{className:"mt-2 bg-gray-50 p-3 rounded-md text-sm",children:[(0,a.jsx)("p",{className:"font-medium",children:"BPI"}),(0,a.jsx)("p",{children:"Account Name: Extreme Life Herbal Products"}),(0,a.jsx)("p",{children:"Account Number: 9876 5432 1098"})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"After making the transfer, please send a copy of the deposit slip or transfer confirmation to our email: <EMAIL>"})]}),"wallet"===e&&"wallet"===r.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Your current wallet balance: ",(0,a.jsx)("span",{className:"font-medium",children:"₱1,250.00"})]})}),"cod"===e&&"cod"===r.id&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Pay with cash when your order is delivered. Please prepare the exact amount."})})]},r.id))}),(0,a.jsxs)("div",{className:"mt-6 flex justify-between",children:[(0,a.jsxs)("button",{type:"button",className:"inline-flex items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:l,children:[(0,a.jsx)(x.QVr,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,a.jsx)("button",{type:"button",className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:r,disabled:!e,children:"Review Order"})]})]})};var b=r(30474);let g=({items:e,shippingFee:s,discount:r=0,isGuestCheckout:t=!1})=>{let n=e=>`₱${e.toFixed(2)}`,l=e.reduce((e,s)=>e+(t&&s.srp?s.srp:s.price)*s.quantity,0),i=e.reduce((e,s)=>e+s.pv*s.quantity,0);return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden sticky top-6",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Order Summary"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:["Items (",e.length,")"]}),(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border border-gray-200 relative",children:e.image?(0,a.jsx)(b.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover object-center"}):(0,a.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,a.jsx)(x.AsH,{className:"text-gray-400 h-6 w-6"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Qty: ",e.quantity]}),(0,a.jsxs)("div",{className:"flex justify-between mt-1",children:[(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.pv," PV \xd7 ",e.quantity]}),t&&e.srp?(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[n(e.srp*e.quantity),e.price<e.srp&&(0,a.jsx)("span",{className:"text-xs text-gray-500 line-through ml-1",children:n(e.price*e.quantity)})]}):(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:n(e.price*e.quantity)})]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Subtotal"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:n(l)})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Shipping"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:n(s)})]}),r>0&&(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Discount"}),(0,a.jsxs)("p",{className:"text-sm font-medium text-green-600",children:["-",n(r)]})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total PV"}),(0,a.jsxs)("p",{className:"text-sm font-medium text-blue-600",children:[i," PV"]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-2 border-t border-gray-200 mt-2",children:[(0,a.jsx)("p",{className:"text-base font-medium text-gray-900",children:"Total"}),(0,a.jsx)("p",{className:"text-base font-medium text-gray-900",children:n(l+s-r)})]})]}),!t&&(0,a.jsxs)("div",{className:"mt-6 bg-blue-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800 mb-1",children:"Potential Earnings"}),(0,a.jsxs)("p",{className:"text-xs text-blue-700",children:["This purchase will earn you approximately ",Math.round(.05*i)," PV in rebates based on your current rank."]})]}),t&&(0,a.jsxs)("div",{className:"mt-6 bg-yellow-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800 mb-1",children:"Retail Price"}),(0,a.jsx)("p",{className:"text-xs text-yellow-700 mb-2",children:"You are purchasing at retail price as a guest. Sign up as a member to get discounted prices and earn rebates on your purchases."}),(()=>{let s=e.reduce((e,s)=>e+s.srp*s.quantity,0),r=s-e.reduce((e,s)=>e+s.price*s.quantity,0),t=Math.round(r/s*100);return r>0?(0,a.jsxs)("div",{className:"text-xs bg-white p-2 rounded border border-yellow-200",children:[(0,a.jsxs)("p",{className:"font-medium text-green-700",children:["Members save ₱",r.toFixed(2)," (",t,"%) on this order!"]}),(0,a.jsx)("a",{href:"/register",className:"text-blue-600 underline block mt-1",children:"Sign up now to save"})]}):null})()]})]})]})},f=({onSubmit:e,onCancel:s})=>{let[r,t]=(0,n.useState)({name:"",email:"",phone:"",addressLine1:"",addressLine2:"",city:"",region:"",postalCode:""}),[l,i]=(0,n.useState)({}),[d,o]=(0,n.useState)({}),c=e=>{let{name:s,value:r}=e.target;t(e=>({...e,[s]:r})),l[s]&&i(e=>({...e,[s]:""}))},m=e=>{let{name:s}=e.target;o(e=>({...e,[s]:!0})),u(s,r[s])},u=(e,s)=>{let r="";switch(e){case"name":s.trim()||(r="Name is required");break;case"email":s.trim()?/\S+@\S+\.\S+/.test(s)||(r="Email is invalid"):r="Email is required";break;case"phone":s.trim()||(r="Phone number is required");break;case"addressLine1":s.trim()||(r="Address is required");break;case"city":s.trim()||(r="City is required");break;case"region":s.trim()||(r="Region is required");break;case"postalCode":s.trim()||(r="Postal code is required")}return i(s=>({...s,[e]:r})),r},h=()=>{let e=!0,s={};return["name","email","phone","addressLine1","city","region","postalCode"].forEach(t=>{let a=u(t,r[t]);a&&(e=!1,s[t]=a)}),i(s),e};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Guest Checkout"}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-1",children:"Member Benefits"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 list-disc pl-5 space-y-1",children:[(0,a.jsx)("li",{children:"Discounted member prices (save up to 20%)"}),(0,a.jsx)("li",{children:"Earn rebates on every purchase"}),(0,a.jsx)("li",{children:"Access to exclusive products and promotions"}),(0,a.jsx)("li",{children:"Build your own network and earn commissions"})]}),(0,a.jsxs)("p",{className:"text-sm text-blue-700 mt-2",children:[(0,a.jsx)("a",{href:"/register",className:"font-medium underline",children:"Sign up now"})," to enjoy these benefits!"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Please provide your information to complete your purchase as a guest."}),(0,a.jsx)("form",{onSubmit:s=>{s.preventDefault(),h()&&e(r)},children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:"Personal Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["Full Name ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.x$1,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"name",name:"name",type:"text",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${l.name&&d.name?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Juan Dela Cruz",value:r.name,onChange:c,onBlur:m}),l.name&&d.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.name})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["Email Address ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.maD,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${l.email&&d.email?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"<EMAIL>",value:r.email,onChange:c,onBlur:m}),l.email&&d.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.email})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:["Phone Number ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.Cab,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"phone",name:"phone",type:"tel",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${l.phone&&d.phone?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"+63 XXX XXX XXXX",value:r.phone,onChange:c,onBlur:m}),l.phone&&d.phone&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.phone})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:"Shipping Address"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"addressLine1",className:"block text-sm font-medium text-gray-700 mb-1",children:["Address Line 1 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.vq8,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"addressLine1",name:"addressLine1",type:"text",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${l.addressLine1&&d.addressLine1?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Street address",value:r.addressLine1,onChange:c,onBlur:m}),l.addressLine1&&d.addressLine1&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.addressLine1})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"addressLine2",className:"block text-sm font-medium text-gray-700 mb-1",children:"Address Line 2 (Optional)"}),(0,a.jsx)("input",{id:"addressLine2",name:"addressLine2",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Apartment, suite, unit, building, floor, etc.",value:r.addressLine2,onChange:c})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700 mb-1",children:["City ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{id:"city",name:"city",type:"text",required:!0,className:`appearance-none block w-full px-3 py-2 border ${l.city&&d.city?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"City",value:r.city,onChange:c,onBlur:m}),l.city&&d.city&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.city})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"region",className:"block text-sm font-medium text-gray-700 mb-1",children:["Region/Province ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{id:"region",name:"region",type:"text",required:!0,className:`appearance-none block w-full px-3 py-2 border ${l.region&&d.region?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Region/Province",value:r.region,onChange:c,onBlur:m}),l.region&&d.region&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.region})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-gray-700 mb-1",children:["Postal Code ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{id:"postalCode",name:"postalCode",type:"text",required:!0,className:`appearance-none block w-full px-3 py-2 border ${l.postalCode&&d.postalCode?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Postal Code",value:r.postalCode,onChange:c,onBlur:m}),l.postalCode&&d.postalCode&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l.postalCode})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Continue to Shipping"})]})]})})]})};var y=function(e){return e[e.SHIPPING_ADDRESS=0]="SHIPPING_ADDRESS",e[e.SHIPPING_METHOD=1]="SHIPPING_METHOD",e[e.PAYMENT=2]="PAYMENT",e[e.REVIEW=3]="REVIEW",e}(y||{});function j(){let{data:e,status:s}=(0,l.useSession)(),r=(0,i.useRouter)(),{items:t,subtotal:d,clearCart:b}=(0,c._)(),[y,j]=(0,n.useState)(0),[N,v]=(0,n.useState)([]),[w,C]=(0,n.useState)(null),[k,P]=(0,n.useState)([]),[S,L]=(0,n.useState)(null),[M,q]=(0,n.useState)(null),[A,F]=(0,n.useState)(!1),[D,E]=(0,n.useState)(null),[_,G]=(0,n.useState)(!1),[B,I]=(0,n.useState)(null),[O,$]=(0,n.useState)(!1),[R,T]=(0,n.useState)(!1),[H,Y]=(0,n.useState)(null),[V,X]=(0,n.useState)(null),W=async()=>{if(!O&&!w)return void E("Please select a shipping address");if(O&&!H)return void E("Please provide your shipping information");if(!S||!M)return void E("Please complete all required information");F(!0),E(null);try{let e=k.find(e=>e.id===S),s=e?e.price:0,r={items:t.map(e=>({productId:e.id,quantity:e.quantity,price:O?e.srp:e.price,priceType:O?"srp":"member"})),shippingMethodId:S,paymentMethod:M,subtotal:d,shippingFee:s,total:d+s};O&&H?(r.isGuestOrder=!0,r.customerName=H.name,r.customerEmail=H.email,r.customerPhone=H.phone,r.guestShippingAddress={name:H.name,phone:H.phone,email:H.email,addressLine1:H.addressLine1,addressLine2:H.addressLine2||"",city:H.city,region:H.region,postalCode:H.postalCode,isGuestAddress:!0}):r.shippingAddressId=w;let a=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to place order")}let n=await a.json();I(n.orderNumber),G(!0),b()}catch(e){console.error("Error placing order:",e),E(e.message||"Failed to place order. Please try again.")}finally{F(!1)}};return"loading"!==s&&(0!==t.length||_)?_?(0,a.jsx)(m.A,{children:(0,a.jsx)("div",{className:"max-w-3xl mx-auto py-8 px-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6",children:(0,a.jsx)(x.CMH,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Order Placed Successfully!"}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 mb-6",children:["Thank you for your order. Your order number is ",(0,a.jsx)("span",{className:"font-semibold",children:B}),"."]}),O?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["We've sent a confirmation email with all the details of your purchase to ",H?.email,"."]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8 max-w-lg mx-auto",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 text-lg mb-2",children:"Create an Account"}),(0,a.jsx)("p",{className:"text-blue-700 mb-4",children:"Create an account to track your order, get member discounts, and earn rebates on future purchases!"}),(0,a.jsxs)(o(),{href:"/register",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(x.NPy,{className:"mr-2"}),"Create Account"]})]})]}):(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"We've sent a confirmation email with all the details of your purchase. You can also track your order status in your account dashboard."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[!O&&(0,a.jsx)(o(),{href:"/orders",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:"View My Orders"}),(0,a.jsx)(o(),{href:"/shop",className:"inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Continue Shopping"})]})]})})}):(0,a.jsx)(m.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto py-8 px-4",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(o(),{href:"/shop",className:"flex items-center text-blue-600 hover:underline",children:[(0,a.jsx)(x.QVr,{className:"mr-2"}),"Back to Shop"]})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Checkout"}),D&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-red-600",children:D}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsxs)("div",{className:"lg:w-2/3",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex items-center",children:[{label:"Shipping Address",icon:(0,a.jsx)(x.vq8,{})},{label:"Shipping Method",icon:(0,a.jsx)(x.dv1,{})},{label:"Payment",icon:(0,a.jsx)(x.x1c,{})},{label:"Review",icon:(0,a.jsx)(x.CMH,{})}].map((e,s)=>(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:`flex items-center ${s>0?"ml-4":""}`,children:[(0,a.jsx)("div",{className:`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${s<=y?"bg-green-600 text-white":"bg-gray-200 text-gray-500"}`,children:e.icon}),(0,a.jsx)("div",{className:`ml-2 text-sm font-medium ${s<=y?"text-gray-900":"text-gray-500"} hidden sm:block`,children:e.label}),s<3&&(0,a.jsx)("div",{className:`flex-1 ml-2 h-0.5 ${s<y?"bg-green-600":"bg-gray-200"}`})]})},s))})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[0===y&&(0,a.jsx)(a.Fragment,{children:O?R?(0,a.jsx)(f,{onSubmit:e=>{Y(e),X({id:"guest-address",name:e.name,phone:e.phone,email:e.email,addressLine1:e.addressLine1,addressLine2:e.addressLine2||"",city:e.city,region:e.region,postalCode:e.postalCode,isGuestAddress:!0}),j(1)},onCancel:()=>{T(!1)}}):(0,a.jsxs)("div",{className:"py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 text-gray-400 mb-4",children:(0,a.jsx)(x.x$1,{className:"h-full w-full"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Checkout Options"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Choose how you'd like to proceed with your purchase"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-8",children:[(0,a.jsxs)("div",{className:"border border-blue-200 rounded-lg p-6 bg-blue-50 flex flex-col",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 text-lg mb-2",children:"Sign In as Member"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 list-disc pl-5 space-y-1 mb-4 flex-grow",children:[(0,a.jsxs)("li",{children:["Get ",(0,a.jsx)("strong",{children:"discounted member prices"})]}),(0,a.jsx)("li",{children:"Earn rebates on your purchase"}),(0,a.jsx)("li",{children:"Track your order history"}),(0,a.jsx)("li",{children:"Faster checkout experience"})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>{r.push("/login?returnUrl=/checkout")},className:"w-full inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(x.x$1,{className:"mr-2"}),"Sign In for Member Prices"]})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6 bg-gray-50 flex flex-col",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 text-lg mb-2",children:"Continue as Guest"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-700 list-disc pl-5 space-y-1 mb-4 flex-grow",children:[(0,a.jsx)("li",{children:"Quick checkout without registration"}),(0,a.jsx)("li",{children:"Purchase at retail price"}),(0,a.jsx)("li",{children:"No account required"}),(0,a.jsx)("li",{children:"Option to create account later"})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>{T(!0)},className:"w-full inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100",children:[(0,a.jsx)(x.NPy,{className:"mr-2"}),"Continue as Guest"]})]})]})]}):(0,a.jsx)(u,{addresses:N,selectedAddressId:w,onAddressSelect:e=>{C(e)},onSubmit:e=>{console.log("Address submitted:",e),j(1)}})}),1===y&&(0,a.jsx)(h,{methods:k,selectedMethodId:S,onMethodSelect:e=>{L(e)},onSubmit:()=>{if(!S)return void E("Please select a shipping method");j(2)},onBack:()=>j(0)}),2===y&&(0,a.jsx)(p,{selectedMethod:M,onMethodSelect:e=>{q(e)},onSubmit:()=>{if(!M)return void E("Please select a payment method");j(3)},onBack:()=>j(1),isGuestCheckout:O}),3===y&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Review Your Order"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Address"}),(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-md",children:O&&V?(0,a.jsxs)(a.Fragment,{children:[V.name,(0,a.jsx)("br",{}),V.addressLine1,V.addressLine2&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("br",{}),V.addressLine2]}),(0,a.jsx)("br",{}),V.city,", ",V.region," ",V.postalCode,(0,a.jsx)("br",{}),"Phone: ",V.phone,(0,a.jsx)("br",{}),"Email: ",V.email]}):(0,a.jsxs)(a.Fragment,{children:[N.find(e=>e.id===w)?.name,(0,a.jsx)("br",{}),N.find(e=>e.id===w)?.addressLine1,(0,a.jsx)("br",{}),N.find(e=>e.id===w)?.city,","," ",N.find(e=>e.id===w)?.region," ",N.find(e=>e.id===w)?.postalCode,(0,a.jsx)("br",{}),"Phone: ",N.find(e=>e.id===w)?.phone]})}),(0,a.jsx)("button",{type:"button",className:"mt-2 text-sm text-blue-600 hover:text-blue-500",onClick:()=>j(0),children:"Change"})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-md",children:[k.find(e=>e.id===S)?.name," - ₱",k.find(e=>e.id===S)?.price.toFixed(2)]}),(0,a.jsx)("button",{type:"button",className:"mt-2 text-sm text-blue-600 hover:text-blue-500",onClick:()=>j(1),children:"Change"})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-md",children:["credit_card"===M&&"Credit Card","gcash"===M&&"GCash","maya"===M&&"Maya","bank_transfer"===M&&"Bank Transfer","wallet"===M&&"Wallet Balance","cod"===M&&"Cash on Delivery"]}),(0,a.jsx)("button",{type:"button",className:"mt-2 text-sm text-blue-600 hover:text-blue-500",onClick:()=>j(2),children:"Change"})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)("button",{type:"button",className:"w-full flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:W,disabled:A,children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.hW,{className:"animate-spin mr-2"}),"Processing..."]}):"Place Order"})})]})]})]}),(0,a.jsx)("div",{className:"lg:w-1/3",children:(0,a.jsx)(g,{items:t,shippingFee:S&&k.find(e=>e.id===S)?.price||0,isGuestCheckout:O})})]})]})}):(0,a.jsx)(m.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(x.hW,{className:"animate-spin h-8 w-8 text-green-500 mr-2"}),(0,a.jsx)("span",{className:"text-xl",children:"Loading..."})]})})}},79551:e=>{"use strict";e.exports=require("url")},85742:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(65239),a=r(48088),n=r(88170),l=r.n(n),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let o={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54787)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\checkout\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\checkout\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},92292:(e,s,r)=>{Promise.resolve().then(r.bind(r,54787))},99908:(e,s,r)=>{Promise.resolve().then(r.bind(r,77603))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>r(85742));module.exports=t})();
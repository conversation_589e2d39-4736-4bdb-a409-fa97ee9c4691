(()=>{var e={};e.id=7294,e.ids=[7294],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>l,Nh:()=>u,aP:()=>c});var a=r(96330),o=r(13581),n=r(85663),s=r(55511),i=r.n(s);async function l(e){return await n.Ay.hash(e,10)}function c(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new a.PrismaClient;let u={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new a.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,r.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:s,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=r(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=o?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(a,n,i):a[n]=e[n]}return a.default=e,r&&r.set(e,a),a}(r(35426));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var a=r(96330);let o=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78146:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{GET:()=>d,POST:()=>p});var o=r(96559),n=r(48088),s=r(37719),i=r(32190),l=r(19854),c=r(12909),u=r(31183);async function d(e){try{let e=await u.z.product.findFirst({where:{sku:"BIOGEN-EXTREME-30ML"},include:{category:!0,productImages:!0,productVariants:!0}});if(!e)return i.NextResponse.json({error:"Biogen Extreme product not found"},{status:404});let t=await u.z.productReview.findMany({where:{productId:e.id},include:{user:{select:{id:!0,name:!0,image:!0}}},orderBy:{createdAt:"desc"},take:10}),r=await u.z.product.findMany({where:{categoryId:e.categoryId,id:{not:e.id}},take:4});return i.NextResponse.json({product:e,reviews:t,relatedProducts:r})}catch(e){return console.error("Error fetching Biogen Extreme product:",e),i.NextResponse.json({error:"Failed to fetch Biogen Extreme product"},{status:500})}}async function p(e){try{let e,t=await (0,l.getServerSession)(c.Nh);if(!t||!t.user||"ADMIN"!==t.user.role)return i.NextResponse.json({error:"You must be an admin to create or update products"},{status:401});let r=await u.z.product.findFirst({where:{sku:"BIOGEN-EXTREME-30ML"}}),a=await u.z.productCategory.findFirst({where:{name:"Health Supplements"}});a||(a=await u.z.productCategory.create({data:{name:"Health Supplements",description:"Natural health supplements and wellness products",slug:"health-supplements"}}));let o={name:"Biogen Extreme Concentrate",description:`Biogen Extreme is a concentrated organic enzyme formula that helps maintain pH balance in the body. This revolutionary product helps oxygenate cells, supporting optimal health at the cellular level.

Key Benefits:
- Helps maintain acid-alkaline balance
- Oxygenates the cells
- Tasteless and odorless in water
- Gluten-free and vegan
- Contains essential minerals and trace minerals
- Supports overall wellness for both body and mind

Biogen Extreme contains bioavailable ionic trace minerals that help maintain healthy pH in the body for optimal health and wellness. The body is constantly working to maintain acid-base balance, commonly known as pH. The most important nutrients in our bodies for maintaining acid-base balance are minerals and trace minerals.

How to Use:
Add 10-15 drops to 8oz of water, 3 times daily or as directed by your healthcare professional.

Size: 30ml (approximately 30-day supply)`,shortDescription:"Concentrated organic enzyme formula that helps maintain pH balance and oxygenate cells.",sku:"BIOGEN-EXTREME-30ML",price:1250,salePrice:1100,cost:550,pointValue:50,stock:100,weight:50,dimensions:"3x3x10cm",featured:!0,categoryId:a.id,status:"ACTIVE",tags:["pH balance","organic","enzyme","health","wellness","minerals"]};r?(e=await u.z.product.update({where:{id:r.id},data:o}),await u.z.productVariant.deleteMany({where:{productId:e.id}})):e=await u.z.product.create({data:o}),await u.z.productVariant.create({data:{productId:e.id,name:"30ml Bottle",sku:"BIOGEN-EXTREME-30ML",price:1250,salePrice:1100,stock:100,isDefault:!0}});let n=["/images/products/biogen-extreme/biogen-extreme-main.jpg","/images/products/biogen-extreme/biogen-extreme-lifestyle.jpg","/images/products/biogen-extreme/biogen-extreme-benefits.jpg"];await u.z.productImage.deleteMany({where:{productId:e.id}});for(let t=0;t<n.length;t++)await u.z.productImage.create({data:{productId:e.id,url:n[t],sortOrder:t,isDefault:0===t}});let s=await u.z.productReview.count({where:{productId:e.id}});if(0===s){let t=await u.z.user.findMany({take:5,orderBy:{id:"asc"}});if(t.length>0)for(let r of[{rating:5,title:"Amazing product!",content:"I've been using Biogen Extreme for a month now and I feel so much more energetic. Highly recommend!",userId:t[0].id},{rating:5,title:"Life changing",content:"This product has completely changed my life. My pH levels are now balanced and I feel great every day.",userId:t[1].id},{rating:4,title:"Good product",content:"I like how it's tasteless and easy to add to my daily water intake. I've noticed some improvements in my energy levels.",userId:t[2].id},{rating:5,title:"Best supplement ever",content:"I've tried many supplements but this one is by far the best. It's now a permanent part of my daily routine.",userId:t[3].id},{rating:4,title:"Great for overall health",content:"I've been using this for 3 weeks and I can definitely feel the difference in my overall health and wellbeing.",userId:t[4].id}])await u.z.productReview.create({data:{productId:e.id,userId:r.userId,rating:r.rating,title:r.title,content:r.content}})}return i.NextResponse.json({success:!0,product:e})}catch(e){return console.error("Error creating/updating Biogen Extreme product:",e),i.NextResponse.json({error:"Failed to create/update Biogen Extreme product"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/products/biogen-extreme/route",pathname:"/api/products/biogen-extreme",filename:"route",bundlePath:"app/api/products/biogen-extreme/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\biogen-extreme\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:f}=m;function w(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8044,3112],()=>r(78146));module.exports=a})();
(()=>{var e={};e.id=3445,e.ids=[3445],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>u,Nh:()=>l,aP:()=>d});var s=r(96330),o=r(13581),n=r(85663),a=r(55511),i=r.n(a);async function u(e){return await n.Ay.hash(e,10)}function d(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new s.PrismaClient;let l={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new s.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,r.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:a,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=r(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=o?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(s,n,i):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(35426));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var s=r(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50127:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>P,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{DELETE:()=>g,GET:()=>h,POST:()=>y,PUT:()=>w});var o=r(96559),n=r(48088),a=r(37719),i=r(31183),u=r(32190),d=r(19854),l=r(12909),c=r(92509),p=r(70762);let m=p.z.object({paymentMethodId:p.z.number().int().positive(),details:p.z.record(p.z.any()).optional(),isDefault:p.z.boolean().optional()}),f=p.z.object({id:p.z.number().int().positive(),details:p.z.record(p.z.any()).optional(),isDefault:p.z.boolean().optional()});async function h(e){try{let e=await (0,d.getServerSession)(l.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to view your payment methods"},{status:401});let t=e.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let r=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!r)return u.NextResponse.json({error:"User not found"},{status:404});let s=await (0,c._)(r.id);return u.NextResponse.json({userPaymentMethods:s})}catch(e){return console.error("Error fetching user payment methods:",e),u.NextResponse.json({error:"Failed to fetch user payment methods"},{status:500})}}async function y(e){try{let t=await (0,d.getServerSession)(l.Nh);if(!t||!t.user)return u.NextResponse.json({error:"You must be logged in to add a payment method"},{status:401});let r=t.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:r},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let o=await e.json(),n=m.safeParse(o);if(!n.success)return u.NextResponse.json({error:n.error.errors},{status:400});let{paymentMethodId:a,details:p,isDefault:f}=n.data,h=await (0,c.gY)(a);if(!h)return u.NextResponse.json({error:"Payment method not found"},{status:404});if(!h.isActive)return u.NextResponse.json({error:"Payment method is not active"},{status:400});if(h.requiresDetails){let e=await (0,c.XE)(a,p||{});if(!e.isValid)return u.NextResponse.json({error:`Invalid payment details: ${e.errors?.join(", ")}`},{status:400})}let y=await (0,c.Gb)(s.id,a,p?JSON.stringify(p):"{}",f||!1);return u.NextResponse.json({userPaymentMethod:y,message:"Payment method added successfully"})}catch(e){return console.error("Error adding payment method:",e),u.NextResponse.json({error:"Failed to add payment method"},{status:500})}}async function w(e){try{let t=await (0,d.getServerSession)(l.Nh);if(!t||!t.user)return u.NextResponse.json({error:"You must be logged in to update a payment method"},{status:401});let r=t.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:r},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let o=await e.json(),n=f.safeParse(o);if(!n.success)return u.NextResponse.json({error:n.error.errors},{status:400});let{id:a,details:p,isDefault:m}=n.data,h=await i.z.userPaymentMethod.findUnique({where:{id:a},include:{paymentMethod:!0}});if(!h)return u.NextResponse.json({error:"Payment method not found"},{status:404});if(h.userId!==s.id)return u.NextResponse.json({error:"You do not have permission to update this payment method"},{status:403});if(p&&h.paymentMethod.requiresDetails){let e=await (0,c.XE)(h.paymentMethodId,p);if(!e.isValid)return u.NextResponse.json({error:`Invalid payment details: ${e.errors?.join(", ")}`},{status:400})}let y=await (0,c.cE)(a,p?JSON.stringify(p):void 0,m);return u.NextResponse.json({userPaymentMethod:y,message:"Payment method updated successfully"})}catch(e){return console.error("Error updating payment method:",e),u.NextResponse.json({error:"Failed to update payment method"},{status:500})}}async function g(e){try{let t=await (0,d.getServerSession)(l.Nh);if(!t||!t.user)return u.NextResponse.json({error:"You must be logged in to delete a payment method"},{status:401});let r=t.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:r},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let o=new URL(e.url).searchParams.get("id");if(!o)return u.NextResponse.json({error:"Payment method ID is required"},{status:400});let n=parseInt(o),a=await i.z.userPaymentMethod.findUnique({where:{id:n}});if(!a)return u.NextResponse.json({error:"Payment method not found"},{status:404});if(a.userId!==s.id)return u.NextResponse.json({error:"You do not have permission to delete this payment method"},{status:403});return await (0,c.w9)(n),u.NextResponse.json({message:"Payment method deleted successfully"})}catch(e){return console.error("Error deleting payment method:",e),u.NextResponse.json({error:"Failed to delete payment method"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/payment-methods/user/route",pathname:"/api/payment-methods/user",filename:"route",bundlePath:"app/api/payment-methods/user/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\payment-methods\\user\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:v,serverHooks:P}=x;function b(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:v})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},92509:(e,t,r)=>{"use strict";r.d(t,{Gb:()=>i,Gx:()=>o,XE:()=>l,_:()=>a,cE:()=>u,gY:()=>n,w9:()=>d});var s=r(31183);async function o(e=!0){return await s.z.paymentMethod.findMany({where:e?{isActive:!0}:void 0,orderBy:{name:"asc"}})}async function n(e){return await s.z.paymentMethod.findUnique({where:{id:e}})}async function a(e){return await s.z.userPaymentMethod.findMany({where:{userId:e},include:{paymentMethod:!0},orderBy:[{isDefault:"desc"},{createdAt:"desc"}]})}async function i(e,t,r,o=!1){return o&&await s.z.userPaymentMethod.updateMany({where:{userId:e,isDefault:!0},data:{isDefault:!1}}),await s.z.userPaymentMethod.create({data:{userId:e,paymentMethodId:t,details:r,isDefault:o},include:{paymentMethod:!0}})}async function u(e,t,r){let o=await s.z.userPaymentMethod.findUnique({where:{id:e},select:{userId:!0}});if(!o)throw Error(`User payment method with ID ${e} not found`);return r&&await s.z.userPaymentMethod.updateMany({where:{userId:o.userId,isDefault:!0,id:{not:e}},data:{isDefault:!1}}),await s.z.userPaymentMethod.update({where:{id:e},data:{details:void 0!==t?t:void 0,isDefault:void 0!==r?r:void 0,updatedAt:new Date},include:{paymentMethod:!0}})}async function d(e){return await s.z.userPaymentMethod.delete({where:{id:e},include:{paymentMethod:!0}})}async function l(e,t){let r=await n(e);if(!r)return{isValid:!1,errors:["Payment method not found"]};if(!r.requiresDetails||!r.detailsSchema)return{isValid:!0};try{let e=JSON.parse(r.detailsSchema);if(e.required&&Array.isArray(e.required)){let r=[];for(let s of e.required)t[s]||r.push(`Field "${s}" is required`);if(r.length>0)return{isValid:!1,errors:r}}return{isValid:!0}}catch(e){return console.error("Error validating payment details:",e),{isValid:!1,errors:["Invalid schema format"]}}}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,8044,3112,8381],()=>r(50127));module.exports=s})();
(()=>{var e={};e.id=8181,e.ids=[8181],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>d,aP:()=>l});var s=t(96330),o=t(13581),n=t(85663),i=t(55511),a=t.n(i);async function u(e){return await n.Ay.hash(e,10)}function l(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let d={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:i,...a}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89004:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>c});var o=t(96559),n=t(48088),i=t(37719),a=t(32190),u=t(35426),l=t(12909),d=t(31183);async function c(e,{params:r}){try{let t=await (0,u.getServerSession)(l.Nh);if(!t||!t.user)return a.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});if("admin"!==t.user.role)return a.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let s=parseInt(r.id);if(isNaN(s))return a.NextResponse.json({error:"Invalid product ID"},{status:400});let o=await e.json();if(void 0===o.quantity)return a.NextResponse.json({error:"Quantity is required"},{status:400});let n=parseInt(o.quantity);if(isNaN(n))return a.NextResponse.json({error:"Invalid quantity"},{status:400});let i=await d.z.product.findUnique({where:{id:s}});if(!i)return a.NextResponse.json({error:"Product not found"},{status:404});let c=Math.max(0,i.inventory+n),p=await d.z.$transaction(async e=>{let r=await e.product.update({where:{id:s},data:{inventory:c}}),a=await e.inventoryTransaction.create({data:{productId:s,quantity:n,type:"adjustment",notes:o.notes||null,createdBy:t.user.id,createdByName:t.user.name||null}});return null!==r.lowStockThreshold&&c<=r.lowStockThreshold&&i.inventory>r.lowStockThreshold&&await e.notification.create({data:{type:"low_stock",title:"Low Stock Alert",message:`${r.name} is running low on stock (${c} remaining)`,productId:r.id,isRead:!1}}),{product:r,transaction:a}});return a.NextResponse.json({message:"Inventory adjusted successfully",newInventory:p.product.inventory,transaction:p.transaction})}catch(e){return console.error("Error adjusting inventory:",e),a.NextResponse.json({error:"Failed to adjust inventory"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/products/[id]/adjust-inventory/route",pathname:"/api/admin/products/[id]/adjust-inventory",filename:"route",bundlePath:"app/api/admin/products/[id]/adjust-inventory/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\adjust-inventory\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:g}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(89004));module.exports=s})();
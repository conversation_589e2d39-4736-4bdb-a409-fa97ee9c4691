import React from 'react';

const ProductReviews = ({ productId }) => {
  // Mock reviews data
  const reviews = [
    {
      id: 1,
      author: '<PERSON>',
      rating: 5,
      date: '2023-04-15',
      content: 'This product has significantly improved my energy levels and overall health. Highly recommended!'
    },
    {
      id: 2,
      author: '<PERSON>',
      rating: 4,
      date: '2023-03-22',
      content: 'Great product, noticed improvements within a week of use. Would buy again.'
    },
    {
      id: 3,
      author: '<PERSON>',
      rating: 5,
      date: '2023-02-10',
      content: 'Amazing results! My family and friends have noticed the difference in my energy and appearance.'
    }
  ];

  // Function to render stars based on rating
  const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span key={i} className={i <= rating ? "text-yellow-500" : "text-gray-300"}>
          ★
        </span>
      );
    }
    return stars;
  };

  return (
    <div className="mt-10">
      <h2 className="text-2xl font-bold mb-4">Customer Reviews</h2>
      
      {reviews.length === 0 ? (
        <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
      ) : (
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border-b pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-semibold">{review.author}</p>
                  <div className="flex text-xl">
                    {renderStars(review.rating)}
                  </div>
                </div>
                <span className="text-sm text-gray-500">{review.date}</span>
              </div>
              <p className="mt-2 text-gray-700">{review.content}</p>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-8">
        <h3 className="text-xl font-semibold mb-2">Write a Review</h3>
        <form className="space-y-4">
          <div>
            <label htmlFor="rating" className="block text-sm font-medium text-gray-700">Rating</label>
            <select 
              id="rating" 
              name="rating" 
              className="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="5">5 Stars - Excellent</option>
              <option value="4">4 Stars - Very Good</option>
              <option value="3">3 Stars - Good</option>
              <option value="2">2 Stars - Fair</option>
              <option value="1">1 Star - Poor</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="review" className="block text-sm font-medium text-gray-700">Your Review</label>
            <textarea 
              id="review" 
              name="review" 
              rows="4" 
              className="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Share your experience with this product..."
            ></textarea>
          </div>
          
          <button 
            type="submit" 
            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Submit Review
          </button>
        </form>
      </div>
    </div>
  );
};

export default ProductReviews;

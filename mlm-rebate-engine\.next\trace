[{"name": "generate-buildid", "duration": 219, "timestamp": 2707532124, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748104651489, "traceId": "4a4186de449b5ef8"}, {"name": "load-custom-routes", "duration": 414, "timestamp": 2707532435, "id": 5, "parentId": 1, "tags": {}, "startTime": 1748104651489, "traceId": "4a4186de449b5ef8"}, {"name": "create-dist-dir", "duration": 716, "timestamp": 2707631040, "id": 6, "parentId": 1, "tags": {}, "startTime": 1748104651588, "traceId": "4a4186de449b5ef8"}, {"name": "create-pages-mapping", "duration": 213, "timestamp": 2707729534, "id": 7, "parentId": 1, "tags": {}, "startTime": 1748104651686, "traceId": "4a4186de449b5ef8"}, {"name": "collect-app-paths", "duration": 8933, "timestamp": 2707729799, "id": 8, "parentId": 1, "tags": {}, "startTime": 1748104651687, "traceId": "4a4186de449b5ef8"}, {"name": "create-app-mapping", "duration": 8914, "timestamp": 2707738800, "id": 9, "parentId": 1, "tags": {}, "startTime": 1748104651696, "traceId": "4a4186de449b5ef8"}, {"name": "public-dir-conflict-check", "duration": 945, "timestamp": 2707749304, "id": 10, "parentId": 1, "tags": {}, "startTime": 1748104651706, "traceId": "4a4186de449b5ef8"}, {"name": "generate-routes-manifest", "duration": 5612, "timestamp": 2707750821, "id": 11, "parentId": 1, "tags": {}, "startTime": 1748104651708, "traceId": "4a4186de449b5ef8"}, {"name": "next-build", "duration": 17657428, "timestamp": 2707179600, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.1", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1748104651136, "traceId": "4a4186de449b5ef8"}]
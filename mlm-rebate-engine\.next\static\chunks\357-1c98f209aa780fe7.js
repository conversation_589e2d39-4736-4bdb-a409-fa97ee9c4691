"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[357],{5323:(e,s,t)=>{t.d(s,{CartProvider:()=>n,_:()=>i});var l=t(95155),r=t(12115);let a=(0,r.createContext)(void 0),i=()=>{let e=(0,r.useContext)(a);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},n=e=>{let{children:s}=e,[t,i]=(0,r.useState)([]);(0,r.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{i(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,r.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let n=e=>{i(s=>s.filter(s=>s.id!==e))},c=t.reduce((e,s)=>e+s.quantity,0),x=t.reduce((e,s)=>e+s.price*s.quantity,0),d=t.reduce((e,s)=>e+s.pv*s.quantity,0);return(0,l.jsx)(a.Provider,{value:{items:t,addItem:e=>{i(s=>{let t=s.findIndex(s=>s.id===e.id);if(!(t>=0))return[...s,e];{let l=[...s];return l[t]={...l[t],quantity:l[t].quantity+e.quantity},l}})},removeItem:n,updateQuantity:(e,s)=>{if(s<=0)return void n(e);i(t=>t.map(t=>t.id===e?{...t,quantity:s}:t))},clearCart:()=>{i([])},itemCount:c,subtotal:x,totalPV:d},children:s})}},70357:(e,s,t)=>{t.d(s,{A:()=>p});var l=t(95155),r=t(12115),a=t(12108),i=t(6874),n=t.n(i),c=t(66766),x=t(29911),d=t(5323),m=t(15939),o=t(98212),h=t(35695);let u=e=>{let{isOpen:s,onClose:t}=e,a=(0,h.useRouter)(),{items:i,removeItem:n,updateQuantity:u,clearCart:j,subtotal:f,totalPV:p}=(0,d._)(),b=e=>"₱".concat(e.toFixed(2));return(0,l.jsx)(m.e.Root,{show:s,as:r.Fragment,children:(0,l.jsxs)(o.lG,{as:"div",className:"relative z-50",onClose:t,children:[(0,l.jsx)(m.e.Child,{as:r.Fragment,enter:"ease-in-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,l.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"})}),(0,l.jsx)("div",{className:"fixed inset-0 overflow-hidden",children:(0,l.jsx)("div",{className:"absolute inset-0 overflow-hidden",children:(0,l.jsx)("div",{className:"pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10",children:(0,l.jsx)(m.e.Child,{as:r.Fragment,enter:"transform transition ease-in-out duration-300",enterFrom:"translate-x-full",enterTo:"translate-x-0",leave:"transform transition ease-in-out duration-300",leaveFrom:"translate-x-0",leaveTo:"translate-x-full",children:(0,l.jsx)(o.lG.Panel,{className:"pointer-events-auto w-screen max-w-md",children:(0,l.jsxs)("div",{className:"flex h-full flex-col overflow-y-scroll bg-white shadow-xl",children:[(0,l.jsxs)("div",{className:"flex-1 overflow-y-auto px-4 py-6 sm:px-6",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)(o.lG.Title,{className:"text-lg font-medium text-gray-900 flex items-center",children:[(0,l.jsx)(x.AsH,{className:"mr-2"})," Shopping Cart"]}),(0,l.jsx)("div",{className:"ml-3 flex h-7 items-center",children:(0,l.jsxs)("button",{type:"button",className:"relative -m-2 p-2 text-gray-400 hover:text-gray-500",onClick:t,children:[(0,l.jsx)("span",{className:"absolute -inset-0.5"}),(0,l.jsx)("span",{className:"sr-only",children:"Close panel"}),(0,l.jsx)(x.QCr,{className:"h-6 w-6","aria-hidden":"true"})]})})]}),(0,l.jsx)("div",{className:"mt-8",children:i.length>0?(0,l.jsxs)("div",{className:"flow-root",children:[(0,l.jsx)("ul",{role:"list",className:"-my-6 divide-y divide-gray-200",children:i.map(e=>(0,l.jsxs)("li",{className:"flex py-6",children:[(0,l.jsx)("div",{className:"h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200 relative",children:e.image?(0,l.jsx)(c.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover object-center"}):(0,l.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,l.jsx)(x.AsH,{className:"text-gray-400 h-8 w-8"})})}),(0,l.jsxs)("div",{className:"ml-4 flex flex-1 flex-col",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,l.jsx)("h3",{children:e.name}),(0,l.jsx)("p",{className:"ml-4",children:b(e.price*e.quantity)})]}),(0,l.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:["PV: ",e.pv*e.quantity]})]}),(0,l.jsxs)("div",{className:"flex flex-1 items-end justify-between text-sm",children:[(0,l.jsxs)("div",{className:"flex items-center border rounded-md",children:[(0,l.jsx)("button",{type:"button",className:"p-2 text-gray-600 hover:text-gray-800",onClick:()=>u(e.id,e.quantity-1),children:(0,l.jsx)(x.iu5,{size:12})}),(0,l.jsx)("span",{className:"px-2 py-1 text-gray-900 min-w-[40px] text-center",children:e.quantity}),(0,l.jsx)("button",{type:"button",className:"p-2 text-gray-600 hover:text-gray-800",onClick:()=>u(e.id,e.quantity+1),children:(0,l.jsx)(x.OiG,{size:12})})]}),(0,l.jsxs)("button",{type:"button",className:"font-medium text-red-600 hover:text-red-500 flex items-center",onClick:()=>n(e.id),children:[(0,l.jsx)(x.qbC,{className:"mr-1",size:14}),"Remove"]})]})]})]},e.id))}),(0,l.jsx)("div",{className:"mt-4 text-right",children:(0,l.jsx)("button",{type:"button",className:"text-sm font-medium text-red-600 hover:text-red-500",onClick:j,children:"Clear Cart"})})]}):(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("div",{className:"mx-auto h-24 w-24 text-gray-400",children:(0,l.jsx)(x.AsH,{className:"h-full w-full"})}),(0,l.jsx)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"Your cart is empty"}),(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Start adding products to your cart to see them here."}),(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)("button",{type:"button",className:"inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500",onClick:t,children:"Continue Shopping"})})]})})]}),i.length>0&&(0,l.jsxs)("div",{className:"border-t border-gray-200 px-4 py-6 sm:px-6",children:[(0,l.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900 mb-1",children:[(0,l.jsx)("p",{children:"Subtotal"}),(0,l.jsx)("p",{children:b(f)})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mb-4",children:[(0,l.jsx)("p",{children:"Total PV"}),(0,l.jsxs)("p",{children:[p," PV"]})]}),(0,l.jsx)("p",{className:"mt-0.5 text-sm text-gray-500",children:"Shipping and taxes calculated at checkout."}),(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsxs)("button",{type:"button",className:"flex w-full items-center justify-center rounded-md border border-transparent bg-green-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-green-700",onClick:()=>{t(),a.push("/checkout")},children:["Checkout ",(0,l.jsx)(x.Z0P,{className:"ml-2"})]})}),(0,l.jsx)("div",{className:"mt-6 flex justify-center text-center text-sm text-gray-500",children:(0,l.jsxs)("p",{children:["or"," ",(0,l.jsxs)("button",{type:"button",className:"font-medium text-green-600 hover:text-green-500",onClick:t,children:["Continue Shopping",(0,l.jsx)("span",{"aria-hidden":"true",children:" →"})]})]})})]})]})})})})})})]})})},j=()=>{let{itemCount:e}=(0,d._)(),[s,t]=(0,r.useState)(!1);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("button",{type:"button",className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none",onClick:()=>t(!0),"aria-label":"Open shopping cart",children:[(0,l.jsx)(x.AsH,{className:"h-6 w-6"}),e>0&&(0,l.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 rounded-full bg-green-600 flex items-center justify-center text-xs text-white",children:e})]}),(0,l.jsx)(u,{isOpen:s,onClose:()=>t(!1)})]})},f=(0,t(55028).default)(()=>t.e(2113).then(t.bind(t,62113)),{loadableGenerated:{webpack:()=>[62113]},ssr:!1}),p=e=>{var s,t;let{children:r}=e,{data:i}=(0,a.useSession)();return(0,l.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[i&&(0,l.jsxs)("div",{className:"w-64 bg-white shadow-md",children:[(0,l.jsx)("div",{className:"p-4 border-b",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,l.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Extreme Life Rewards"})]})}),(0,l.jsx)("nav",{className:"mt-4",children:(0,l.jsxs)("ul",{children:[(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/dashboard",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.rQ8,{className:"mr-3"}),(0,l.jsx)("span",{children:"Dashboard"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/genealogy",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.YXz,{className:"mr-3"}),(0,l.jsx)("span",{children:"Genealogy"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/shop",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.AsH,{className:"mr-3"}),(0,l.jsx)("span",{children:"Shop"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/wallet",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.lcY,{className:"mr-3"}),(0,l.jsx)("span",{children:"Wallet"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/rebates",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.YYR,{className:"mr-3"}),(0,l.jsx)("span",{children:"Rebates"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/binary-mlm",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.ph9,{className:"mr-3"}),(0,l.jsx)("span",{children:"Binary MLM"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/referrals",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.AnD,{className:"mr-3"}),(0,l.jsx)("span",{children:"My Referrals"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/rank-advancement",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.SBv,{className:"mr-3"}),(0,l.jsx)("span",{children:"Rank Advancement"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/profile",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.x$1,{className:"mr-3"}),(0,l.jsx)("span",{children:"My Profile"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/about",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.__w,{className:"mr-3"}),(0,l.jsx)("span",{children:"About Us"})]})}),(0,l.jsx)("li",{className:"mt-8 border-t pt-2",children:(0,l.jsxs)(n(),{href:"/admin",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,l.jsx)(x.Pcn,{className:"mr-3"}),(0,l.jsx)("span",{children:"Admin Panel"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/admin/mlm-config",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 pl-10",children:[(0,l.jsx)(x.yk7,{className:"mr-3"}),(0,l.jsx)("span",{children:"MLM Configuration"})]})}),(0,l.jsx)("li",{children:(0,l.jsxs)(n(),{href:"/admin/mlm-config/cutoff",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 pl-10",children:[(0,l.jsx)(x.bfZ,{className:"mr-3"}),(0,l.jsx)("span",{children:"Monthly Cutoff"})]})})]})})]}),(0,l.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,l.jsx)("header",{className:"bg-white shadow-sm",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"flex justify-between h-16",children:[(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsxs)(n(),{href:"/",className:"flex items-center",children:[(0,l.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,l.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})}),(0,l.jsx)("h1",{className:"text-xl font-semibold text-green-700",children:"Extreme Life Herbal Product Rewards"})]})}),(0,l.jsx)("div",{className:"flex items-center",children:i?(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(j,{}),(0,l.jsx)("div",{className:"mx-4 h-6 border-l border-gray-300"}),(0,l.jsxs)(n(),{href:"/profile",className:"flex items-center mr-4 hover:text-blue-600",children:[(0,l.jsx)(x.x$1,{className:"mr-2"}),(0,l.jsx)("span",{children:(null==(s=i.user)?void 0:s.name)||(null==(t=i.user)?void 0:t.email)})]}),(0,l.jsx)(n(),{href:"/api/auth/signout",className:"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600",children:"Sign Out"})]}):(0,l.jsx)(n(),{href:"/login",className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Sign In"})})]})})}),(0,l.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:r}),(0,l.jsx)(f,{})]})]})}}}]);
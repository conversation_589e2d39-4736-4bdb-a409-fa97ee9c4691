"use strict";exports.id=4859,exports.ids=[4859],exports.modules={252:(e,t,n)=>{n.d(t,{L:()=>o});var r=n(43210),l=n(48143);function o(){let[e]=(0,r.useState)(l.e);return e}},10327:(e,t,n)=>{n.d(t,{Y:()=>o});var r=n(43210),l=n(52315);function o(e){let t=(0,r.useRef)(e);return(0,l.s)(()=>{t.current=e},[e]),t}},13337:(e,t,n)=>{n.d(t,{x:()=>r});function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},13516:(e,t,n)=>{n.d(t,{e:()=>_,_:()=>A});var r,l,o=n(43210),i=n(252),a=n(52263),u=n(84818),s=n(52315),c=n(10327),d=n(15319),f=n(44967),m=n(48143);"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(r=null==process?void 0:process.env)?void 0:r.NODE_ENV)==="test"&&void 0===(null==(l=null==Element?void 0:Element.prototype)?void 0:l.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var p=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(p||{}),v=n(39857),h=n(13337),g=n(44685),b=n(69334);function E(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:S)!==o.Fragment||1===o.Children.count(e.children)}let y=(0,o.createContext)(null);y.displayName="TransitionContext";var w=(e=>(e.Visible="visible",e.Hidden="hidden",e))(w||{});let P=(0,o.createContext)(null);function F(e){return"children"in e?F(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function C(e,t){let n=(0,c.Y)(e),r=(0,o.useRef)([]),l=(0,u.a)(),s=(0,i.L)(),d=(0,a._)((e,t=b.mK.Hidden)=>{let o=r.current.findIndex(({el:t})=>t===e);-1!==o&&((0,g.Y)(t,{[b.mK.Unmount](){r.current.splice(o,1)},[b.mK.Hidden](){r.current[o].state="hidden"}}),s.microTask(()=>{var e;!F(r)&&l.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,a._)(e=>{let t=r.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,b.mK.Unmount)}),m=(0,o.useRef)([]),p=(0,o.useRef)(Promise.resolve()),v=(0,o.useRef)({enter:[],leave:[]}),h=(0,a._)((e,n,r)=>{m.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{m.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(v.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?p.current=p.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),E=(0,a._)((e,t,n)=>{Promise.all(v.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=m.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:f,unregister:d,onStart:h,onStop:E,wait:p,chains:v}),[f,d,r,h,E,v,p])}P.displayName="NestingContext";let S=o.Fragment,T=b.Ac.RenderStrategy,x=(0,b.FX)(function(e,t){let{show:n,appear:r=!1,unmount:l=!0,...i}=e,u=(0,o.useRef)(null),c=E(e),m=(0,f.P)(...c?[u,t]:null===t?[]:[t]);(0,d.g)();let p=(0,v.O_)();if(void 0===n&&null!==p&&(n=(p&v.Uw.Open)===v.Uw.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[h,g]=(0,o.useState)(n?"visible":"hidden"),w=C(()=>{n||g("hidden")}),[S,x]=(0,o.useState)(!0),A=(0,o.useRef)([n]);(0,s.s)(()=>{!1!==S&&A.current[A.current.length-1]!==n&&(A.current.push(n),x(!1))},[A,n]);let _=(0,o.useMemo)(()=>({show:n,appear:r,initial:S}),[n,r,S]);(0,s.s)(()=>{n?g("visible"):F(w)||null===u.current||g("hidden")},[n,w]);let R={unmount:l},M=(0,a._)(()=>{var t;S&&x(!1),null==(t=e.beforeEnter)||t.call(e)}),N=(0,a._)(()=>{var t;S&&x(!1),null==(t=e.beforeLeave)||t.call(e)}),L=(0,b.Ci)();return o.createElement(P.Provider,{value:w},o.createElement(y.Provider,{value:_},L({ourProps:{...R,as:o.Fragment,children:o.createElement(O,{ref:m,...R,...i,beforeEnter:M,beforeLeave:N})},theirProps:{},defaultTag:o.Fragment,features:T,visible:"visible"===h,name:"Transition"})))}),O=(0,b.FX)(function(e,t){var n,r;let{transition:l=!0,beforeEnter:u,afterEnter:c,beforeLeave:p,afterLeave:w,enter:x,enterFrom:O,enterTo:A,entered:_,leave:R,leaveFrom:M,leaveTo:N,...L}=e,[k,D]=(0,o.useState)(null),j=(0,o.useRef)(null),I=E(e),H=(0,f.P)(...I?[j,t,D]:null===t?[]:[t]),Y=null==(n=L.unmount)||n?b.mK.Unmount:b.mK.Hidden,{show:U,appear:$,initial:X}=function(){let e=(0,o.useContext)(y);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[B,W]=(0,o.useState)(U?"visible":"hidden"),q=function(){let e=(0,o.useContext)(P);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:K,unregister:V}=q;(0,s.s)(()=>K(j),[K,j]),(0,s.s)(()=>{if(Y===b.mK.Hidden&&j.current)return U&&"visible"!==B?void W("visible"):(0,g.Y)(B,{hidden:()=>V(j),visible:()=>K(j)})},[B,j,K,V,U,Y]);let G=(0,d.g)();(0,s.s)(()=>{if(I&&G&&"visible"===B&&null===j.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[j,B,G,I]);let z=$&&U&&X,Z=(0,o.useRef)(!1),J=C(()=>{Z.current||(W("hidden"),V(j))},q),[,Q]=function(e,t,n,r){let[l,a]=(0,o.useState)(n),{hasFlag:u,addFlag:c,removeFlag:d}=function(e=0){let[t,n]=(0,o.useState)(e),r=(0,o.useCallback)(e=>n(e),[t]),l=(0,o.useCallback)(e=>n(t=>t|e),[t]),i=(0,o.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:l,hasFlag:i,removeFlag:(0,o.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,o.useCallback)(e=>n(t=>t^e),[n])}}(e&&l?3:0),f=(0,o.useRef)(!1),p=(0,o.useRef)(!1),v=(0,i.L)();return(0,s.s)(()=>{var l;if(e){if(n&&a(!0),!t){n&&c(3);return}return null==(l=null==r?void 0:r.start)||l.call(r,n),function(e,{prepare:t,run:n,done:r,inFlight:l}){let o=(0,m.e)();return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current)return n();let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}(e,{prepare:t,inFlight:l}),o.nextFrame(()=>{n(),o.requestAnimationFrame(()=>{o.add(function(e,t){var n,r;let l=(0,m.e)();if(!e)return l.dispose;let o=!1;l.add(()=>{o=!0});let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===i.length?t():Promise.allSettled(i.map(e=>e.finished)).then(()=>{o||t()}),l.dispose}(e,r))})}),o.dispose}(t,{inFlight:f,prepare(){p.current?p.current=!1:p.current=f.current,f.current=!0,p.current||(n?(c(3),d(4)):(c(4),d(2)))},run(){p.current?n?(d(3),c(4)):(d(4),c(3)):n?d(1):c(1)},done(){var e;p.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(f.current=!1,d(7),n||a(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,v]),e?[l,{closed:u(1),enter:u(2),leave:u(4),transition:u(2)||u(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!l||!I||!G||X&&!$),k,U,{start:(0,a._)(e=>{Z.current=!0,J.onStart(j,e?"enter":"leave",e=>{"enter"===e?null==u||u():"leave"===e&&(null==p||p())})}),end:(0,a._)(e=>{let t=e?"enter":"leave";Z.current=!1,J.onStop(j,t,e=>{"enter"===e?null==c||c():"leave"===e&&(null==w||w())}),"leave"!==t||F(J)||(W("hidden"),V(j))})}),ee=(0,b.oE)({ref:H,className:(null==(r=(0,h.x)(L.className,z&&x,z&&O,Q.enter&&x,Q.enter&&Q.closed&&O,Q.enter&&!Q.closed&&A,Q.leave&&R,Q.leave&&!Q.closed&&M,Q.leave&&Q.closed&&N,!Q.transition&&U&&_))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}(Q)}),et=0;"visible"===B&&(et|=v.Uw.Open),"hidden"===B&&(et|=v.Uw.Closed),U&&"hidden"===B&&(et|=v.Uw.Opening),U||"visible"!==B||(et|=v.Uw.Closing);let en=(0,b.Ci)();return o.createElement(P.Provider,{value:J},o.createElement(v.El,{value:et},en({ourProps:ee,theirProps:L,defaultTag:S,features:T,visible:"visible"===B,name:"Transition.Child"})))}),A=(0,b.FX)(function(e,t){let n=null!==(0,o.useContext)(y),r=null!==(0,v.O_)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(x,{ref:t,...e}):o.createElement(O,{ref:t,...e}))}),_=Object.assign(x,{Child:A,Root:x})},15319:(e,t,n)=>{n.d(t,{g:()=>i});var r,l=n(43210),o=n(99923);function i(){let e,t=(e="undefined"==typeof document,"useSyncExternalStore"in(r||(r=n.t(l,2)))&&(0,(r||(r=n.t(l,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,a]=l.useState(o._.isHandoffComplete);return i&&!1===o._.isHandoffComplete&&a(!1),l.useEffect(()=>{!0!==i&&a(!0)},[i]),l.useEffect(()=>o._.handoff(),[]),!t&&i}},16189:(e,t,n)=>{var r=n(65773);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},19587:(e,t)=>{function n(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return n}})},30036:(e,t,n)=>{n.d(t,{default:()=>l.a});var r=n(49587),l=n.n(r)},39704:(e,t,n)=>{n.d(t,{_:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},39857:(e,t,n)=>{n.d(t,{$x:()=>u,El:()=>a,O_:()=>i,Uw:()=>o});var r=n(43210);let l=(0,r.createContext)(null);l.displayName="OpenClosedContext";var o=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(o||{});function i(){return(0,r.useContext)(l)}function a({value:e,children:t}){return r.createElement(l.Provider,{value:e},t)}function u({children:e}){return r.createElement(l.Provider,{value:null},e)}},44685:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let l=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,r),l}},44967:(e,t,n)=>{n.d(t,{P:()=>a,a:()=>i});var r=n(43210),l=n(52263);let o=Symbol();function i(e,t=!0){return Object.assign(e,{[o]:t})}function a(...e){let t=(0,r.useRef)(e),n=(0,l._)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[o]))?void 0:n}},48143:(e,t,n)=>{n.d(t,{e:()=>function e(){let t=[],n={addEventListener:(e,t,r,l)=>(e.addEventListener(t,r,l),n.add(()=>e.removeEventListener(t,r,l))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r._)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(39704)},49587:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let r=n(14985)._(n(64963));function l(e,t){var n;let l={};"function"==typeof e&&(l.loader=e);let o={...l,...t};return(0,r.default)({...o,modules:null==(n=o.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52263:(e,t,n)=>{n.d(t,{_:()=>o});var r=n(43210),l=n(10327);let o=function(e){let t=(0,l.Y)(e);return r.useCallback((...e)=>t.current(...e),[t])}},52315:(e,t,n)=>{n.d(t,{s:()=>o});var r=n(43210),l=n(99923);let o=(e,t)=>{l._.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},56780:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return l}});let r=n(81208);function l(e){let{reason:t,children:n}=e;throw Object.defineProperty(new r.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},64777:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}});let r=n(60687),l=n(51215),o=n(29294),i=n(19587);function a(e){let{moduleIds:t}=e,n=o.workAsyncStorage.getStore();if(void 0===n)return null;let a=[];if(n.reactLoadableManifest&&t){let e=n.reactLoadableManifest;for(let n of t){if(!e[n])continue;let t=e[n].files;a.push(...t)}}return 0===a.length?null:(0,r.jsx)(r.Fragment,{children:a.map(e=>{let t=n.assetPrefix+"/_next/"+(0,i.encodeURIPath)(e);return e.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,l.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let r=n(60687),l=n(43210),o=n(56780),i=n(64777);function a(e){return{default:e&&"default"in e?e.default:e}}let u={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},s=function(e){let t={...u,...e},n=(0,l.lazy)(()=>t.loader().then(a)),s=t.loading;function c(e){let a=s?(0,r.jsx)(s,{isLoading:!0,pastDelay:!0,error:null}):null,u=!t.ssr||!!t.loading,c=u?l.Suspense:l.Fragment,d=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.PreloadChunks,{moduleIds:t.modules}),(0,r.jsx)(n,{...e})]}):(0,r.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(c,{...u?{fallback:a}:{},children:d})}return c.displayName="LoadableComponent",c}},69334:(e,t,n)=>{n.d(t,{Ac:()=>i,Ci:()=>u,FX:()=>f,mK:()=>a,oE:()=>m});var r=n(43210),l=n(13337),o=n(44685),i=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(i||{}),a=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(a||{});function u(){let e,t,n=(e=(0,r.useRef)([]),t=(0,r.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),(...n)=>{if(!n.every(e=>null==e))return e.current=n,t});return(0,r.useCallback)(e=>(function({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:l,visible:i=!0,name:a,mergeRefs:u}){u=null!=u?u:c;let f=d(t,e);if(i)return s(f,n,r,a,u);let m=null!=l?l:0;if(2&m){let{static:e=!1,...t}=f;if(e)return s(t,n,r,a,u)}if(1&m){let{unmount:e=!0,...t}=f;return(0,o.Y)(+!e,{0:()=>null,1:()=>s({...t,hidden:!0,style:{display:"none"}},n,r,a,u)})}return s(f,n,r,a,u)})({mergeRefs:n,...e}),[n])}function s(e,t={},n,o,i){let{as:a=n,children:u,refName:c="ref",...f}=p(e,["unmount","static"]),v=void 0!==e.ref?{[c]:e.ref}:{},h="function"==typeof u?u(t):u;"className"in f&&f.className&&"function"==typeof f.className&&(f.className=f.className(t)),f["aria-labelledby"]&&f["aria-labelledby"]===f.id&&(f["aria-labelledby"]=void 0);let g={};if(t){let e=!1,n=[];for(let[r,l]of Object.entries(t))"boolean"==typeof l&&(e=!0),!0===l&&n.push(r.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e)for(let e of(g["data-headlessui-state"]=n.join(" "),n))g[`data-${e}`]=""}if(a===r.Fragment&&(Object.keys(m(f)).length>0||Object.keys(m(g)).length>0))if(!(0,r.isValidElement)(h)||Array.isArray(h)&&h.length>1){if(Object.keys(m(f)).length>0)throw Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(m(f)).concat(Object.keys(m(g))).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`))}else{var b;let e=h.props,t=null==e?void 0:e.className,n="function"==typeof t?(...e)=>(0,l.x)(t(...e),f.className):(0,l.x)(t,f.className),o=d(h.props,m(p(f,["ref"])));for(let e in g)e in o&&delete g[e];return(0,r.cloneElement)(h,Object.assign({},o,g,v,{ref:i((b=h,r.version.split(".")[0]>="19"?b.props.ref:b.ref),v.ref)},n?{className:n}:{}))}return(0,r.createElement)(a,Object.assign({},p(f,["ref"]),a!==r.Fragment&&v,a!==r.Fragment&&g),h)}function c(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function d(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let e in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(n[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in n)Object.assign(t,{[e](t,...r){for(let l of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;l(t,...r)}}});return t}function f(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function m(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function p(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},84818:(e,t,n)=>{n.d(t,{a:()=>o});var r=n(43210),l=n(52315);function o(){let e=(0,r.useRef)(!1);return(0,l.s)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},94408:(e,t,n)=>{n.d(t,{lG:()=>eC});var r=n(43210),l=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(l||{}),o=n(10327);class i extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}function a(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...l){let o=t[e].call(n,...l);o&&(n=o,r.forEach(e=>e()))}}}var u=n(52315);function s(e){return(0,r.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}let c=new i(()=>a(()=>[],{ADD(e){return this.includes(e)?this:[...this,e]},REMOVE(e){let t=this.indexOf(e);if(-1===t)return this;let n=this.slice();return n.splice(t,1),n}}));function d(e,t){let n=c.get(t),l=(0,r.useId)(),o=s(n);if((0,u.s)(()=>{if(e)return n.dispatch("ADD",l),()=>n.dispatch("REMOVE",l)},[n,e]),!e)return!1;let i=o.indexOf(l),a=o.length;return -1===i&&(i=a,a+=1),i===a-1}var f=n(52263),m=n(48143),p=n(99923);function v(e){var t,n;return p._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let h=new Map,g=new Map;function b(e){var t;let n=null!=(t=g.get(e))?t:0;return g.set(e,n+1),0!==n||(h.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=g.get(e))?t:1;if(1===n?g.delete(e):g.set(e,n-1),1!==n)return;let r=h.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,h.delete(e))})(e)}var E=n(44685);let y=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),w=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var P=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(P||{}),F=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(F||{}),C=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(C||{}),S=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(S||{}),T=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(T||{});function x(e){null==e||e.focus({preventScroll:!0})}function O(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:l=[]}={}){var o,i,a;let u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),l=t(n);if(null===r||null===l)return 0;let o=r.compareDocumentPosition(l);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(w)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(y)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);l.length>0&&s.length>1&&(s=s.filter(e=>!l.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),r=null!=r?r:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},m=0,p=s.length,v;do{if(m>=p||m+p<=0)return 0;let e=d+m;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}null==(v=s[e])||v.focus(f),m+=c}while(v!==u.activeElement);return 6&t&&null!=(a=null==(i=null==(o=v)?void 0:o.matches)?void 0:i.call(o,"textarea,input"))&&a&&v.select(),2}function A(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function _(...e){return(0,r.useMemo)(()=>v(...e),[...e])}var R=n(69334),M=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(M||{});let N=(0,R.FX)(function(e,t){var n;let{features:r=1,...l}=e,o={ref:t,"aria-hidden":(2&r)==2||(null!=(n=l["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,R.Ci)()({ourProps:o,theirProps:l,slot:{},defaultTag:"span",name:"Hidden"})}),L=(0,r.createContext)(null);function k({children:e,node:t}){let[n,l]=(0,r.useState)(null),o=D(null!=t?t:n);return r.createElement(L.Provider,{value:o},e,null===o&&r.createElement(N,{features:M.Hidden,ref:e=>{var t,n;if(e){for(let r of null!=(n=null==(t=v(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&r instanceof HTMLElement&&null!=r&&r.contains(e)){l(r);break}}}}))}function D(e=null){var t;return null!=(t=(0,r.useContext)(L))?t:e}let j=a(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,m.e)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r,l={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},o=[A()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,m.e)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let l=null!=(n=window.scrollY)?n:window.pageYOffset,o=null;t.addEventListener(e,"click",t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:l}=new URL(n.href),i=e.querySelector(l);i&&!r(i)&&(o=i)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(e.target instanceof HTMLElement)if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}),t.addEventListener(e,"touchmove",e=>{if(e.target instanceof HTMLElement&&"INPUT"!==e.target.tagName)if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()},{passive:!1}),t.add(()=>{var e;l!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,l),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=Math.max(0,(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth)},after({doc:e,d:t}){let n=e.documentElement,l=Math.max(0,n.clientWidth-n.offsetWidth),o=Math.max(0,r-l);t.style(n,"paddingRight",`${o}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach(({before:e})=>null==e?void 0:e(l)),o.forEach(({after:e})=>null==e?void 0:e(l))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});j.subscribe(()=>{let e=j.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&j.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&j.dispatch("TEARDOWN",n)}});var I=n(15319),H=n(44967);let Y=(0,r.createContext)(()=>{});function U({value:e,children:t}){return r.createElement(Y.Provider,{value:e},t)}var $=n(39857);let X=(0,r.createContext)(!1);function B(e){return r.createElement(X.Provider,{value:e.force},e.children)}let W=(0,r.createContext)(void 0),q=(0,r.createContext)(null);q.displayName="DescriptionContext";let K=Object.assign((0,R.FX)(function(e,t){let n=(0,r.useId)(),l=(0,r.useContext)(W),{id:o=`headlessui-description-${n}`,...i}=e,a=function e(){let t=(0,r.useContext)(q);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),s=(0,H.P)(t);(0,u.s)(()=>a.register(o),[o,a.register]);let c=l||!1,d=(0,r.useMemo)(()=>({...a.slot,disabled:c}),[a.slot,c]),f={ref:s,...a.props,id:o};return(0,R.Ci)()({ourProps:f,theirProps:i,slot:d,defaultTag:"p",name:a.name||"Description"})}),{});var V=n(252),G=n(84818),z=n(39704);function Z(e){(0,f._)(e),(0,r.useRef)(!1)}var J=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(J||{});function Q(e,t){(0,r.useRef)([]),(0,f._)(e)}let ee=[];function et(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var en=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(en||{});let er=Object.assign((0,R.FX)(function(e,t){var n;let l,i=(0,r.useRef)(null),a=(0,H.P)(i,t),{initialFocus:u,initialFocusFallback:s,containers:c,features:m=15,...p}=e;(0,I.g)()||(m=0);let v=_(i);!function(e,{ownerDocument:t}){let n=!!(8&e),l=function(e=!0){let t=(0,r.useRef)(ee.slice());return Q(([e],[n])=>{!0===n&&!1===e&&(0,z._)(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=ee.slice())},[e,ee,t]),(0,f._)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(n);Q(()=>{n||(null==t?void 0:t.activeElement)===(null==t?void 0:t.body)&&x(l())},[n]),Z(()=>{n&&x(l())})}(m,{ownerDocument:v});let h=function(e,{ownerDocument:t,container:n,initialFocus:l,initialFocusFallback:o}){let i=(0,r.useRef)(null),a=d(!!(1&e),"focus-trap#initial-focus"),u=(0,G.a)();return Q(()=>{if(0===e)return;if(!a){null!=o&&o.current&&x(o.current);return}let r=n.current;r&&(0,z._)(()=>{if(!u.current)return;let n=null==t?void 0:t.activeElement;if(null!=l&&l.current){if((null==l?void 0:l.current)===n){i.current=n;return}}else if(r.contains(n)){i.current=n;return}if(null!=l&&l.current)x(l.current);else{if(16&e){if(O(r,P.First|P.AutoFocus)!==F.Error)return}else if(O(r,P.First)!==F.Error)return;if(null!=o&&o.current&&(x(o.current),(null==t?void 0:t.activeElement)===o.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}i.current=null==t?void 0:t.activeElement})},[o,a,e]),i}(m,{ownerDocument:v,container:i,initialFocus:u,initialFocusFallback:s});!function(e,{ownerDocument:t,container:n,containers:r,previousActiveElement:l}){var i;let a=(0,G.a)(),u=!!(4&e);null==t||t.defaultView,(0,o.Y)(e=>{if(!u||!a.current)return;let t=et(r);n.current instanceof HTMLElement&&t.add(n.current);let o=l.current;if(!o)return;let i=e.target;i&&i instanceof HTMLElement?el(t,i)?(l.current=i,x(i)):(e.preventDefault(),e.stopPropagation(),x(o)):x(l.current)})}(m,{ownerDocument:v,container:i,containers:c,previousActiveElement:h});let g=(l=(0,r.useRef)(0),n=e=>{"Tab"===e.key&&(l.current=+!!e.shiftKey)},(0,o.Y)(n),l),b=(0,f._)(e=>{let t=i.current;t&&(0,E.Y)(g.current,{[J.Forwards]:()=>{O(t,P.First,{skipElements:[e.relatedTarget,s]})},[J.Backwards]:()=>{O(t,P.Last,{skipElements:[e.relatedTarget,s]})}})}),y=d(!!(2&m),"focus-trap#tab-lock"),w=(0,V.L)(),C=(0,r.useRef)(!1),S=(0,R.Ci)();return r.createElement(r.Fragment,null,y&&r.createElement(N,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:b,features:M.Focusable}),S({ourProps:{ref:a,onKeyDown(e){"Tab"==e.key&&(C.current=!0,w.requestAnimationFrame(()=>{C.current=!1}))},onBlur(e){if(!(4&m))return;let t=et(c);i.current instanceof HTMLElement&&t.add(i.current);let n=e.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(el(t,n)||(C.current?O(i.current,(0,E.Y)(g.current,{[J.Forwards]:()=>P.Next,[J.Backwards]:()=>P.Previous})|P.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&x(e.target)))}},theirProps:p,defaultTag:"div",name:"FocusTrap"}),y&&r.createElement(N,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:b,features:M.Focusable}))}),{features:en});function el(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var eo=n(51215);let ei=r.Fragment,ea=(0,R.FX)(function(e,t){let{ownerDocument:n=null,...l}=e,o=(0,r.useRef)(null),i=(0,H.P)((0,H.a)(e=>{o.current=e}),t),a=_(o),s=null!=n?n:a,c=function(e){let t=(0,r.useContext)(X),n=(0,r.useContext)(es),[l,o]=(0,r.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(p._.isServer)return null;let l=null==e?void 0:e.getElementById("headlessui-portal-root");if(l)return l;if(null===e)return null;let o=e.createElement("div");return o.setAttribute("id","headlessui-portal-root"),e.body.appendChild(o)});return l}(s),[d]=(0,r.useState)(()=>{var e;return p._.isServer?null:null!=(e=null==s?void 0:s.createElement("div"))?e:null}),f=(0,r.useContext)(ec),m=(0,I.g)();(0,u.s)(()=>{!c||!d||c.contains(d)||(d.setAttribute("data-headlessui-portal",""),c.appendChild(d))},[c,d]),(0,u.s)(()=>{if(d&&f)return f.register(d)},[f,d]),Z(()=>{var e;c&&d&&(d instanceof Node&&c.contains(d)&&c.removeChild(d),c.childNodes.length<=0&&(null==(e=c.parentElement)||e.removeChild(c)))});let v=(0,R.Ci)();return m&&c&&d?(0,eo.createPortal)(v({ourProps:{ref:i},theirProps:l,slot:{},defaultTag:ei,name:"Portal"}),d):null}),eu=r.Fragment,es=(0,r.createContext)(null),ec=(0,r.createContext)(null),ed=(0,R.FX)(function(e,t){let n=(0,H.P)(t),{enabled:l=!0,ownerDocument:o,...i}=e,a=(0,R.Ci)();return l?r.createElement(ea,{...i,ownerDocument:o,ref:n}):a({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:ei,name:"Portal"})}),ef=(0,R.FX)(function(e,t){let{target:n,...l}=e,o={ref:(0,H.P)(t)},i=(0,R.Ci)();return r.createElement(es.Provider,{value:n},i({ourProps:o,theirProps:l,defaultTag:eu,name:"Popover.Group"}))}),em=Object.assign(ed,{Group:ef});var ep=n(13516),ev=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ev||{}),eh=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(eh||{});let eg={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eb=(0,r.createContext)(null);function eE(e){let t=(0,r.useContext)(eb);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eE),t}return t}function ey(e,t){return(0,E.Y)(t.type,eg,e,t)}eb.displayName="DialogContext";let ew=(0,R.FX)(function(e,t){let n,i,a,c,p,h,g,w,P,F,C=(0,r.useId)(),{id:T=`headlessui-dialog-${C}`,open:x,onClose:O,initialFocus:M,role:N="dialog",autoFocus:L=!0,__demoMode:k=!1,unmount:Y=!1,...X}=e,W=(0,r.useRef)(!1);N="dialog"===N||"alertdialog"===N?N:(W.current||(W.current=!0,console.warn(`Invalid role [${N}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let K=(0,$.O_)();void 0===x&&null!==K&&(x=(K&$.Uw.Open)===$.Uw.Open);let V=(0,r.useRef)(null),G=(0,H.P)(V,t),z=_(V),Z=+!x,[J,Q]=(0,r.useReducer)(ey,{titleId:null,descriptionId:null,panelRef:(0,r.createRef)()}),ee=(0,f._)(()=>O(!1)),et=(0,f._)(e=>Q({type:0,id:e})),el=!!(0,I.g)()&&0===Z,[eo,ei]=(n=(0,r.useContext)(ec),i=(0,r.useRef)([]),a=(0,f._)(e=>(i.current.push(e),n&&n.register(e),()=>c(e))),c=(0,f._)(e=>{let t=i.current.indexOf(e);-1!==t&&i.current.splice(t,1),n&&n.unregister(e)}),p=(0,r.useMemo)(()=>({register:a,unregister:c,portals:i}),[a,c,i]),[i,(0,r.useMemo)(()=>function({children:e}){return r.createElement(ec.Provider,{value:p},e)},[p])]),ea=D(),{resolveContainers:eu}=function({defaultContainers:e=[],portals:t,mainTreeNode:n}={}){let r=_(n),l=(0,f._)(()=>{var l,o;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"current"in t&&t.current instanceof HTMLElement&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(l=null==r?void 0:r.querySelectorAll("html > *, body > *"))?l:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(o=null==n?void 0:n.getRootNode())?void 0:o.host))||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:l,contains:(0,f._)(e=>l().some(t=>t.contains(e)))}}({mainTreeNode:ea,portals:eo,defaultContainers:[{get current(){var es;return null!=(es=J.panelRef.current)?es:V.current}}]}),ed=null!==K&&(K&$.Uw.Closing)===$.Uw.Closing;(function(e,{allowed:t,disallowed:n}={}){let r=d(e,"inert-others");(0,u.s)(()=>{var e,l;if(!r)return;let o=(0,m.e)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&o.add(b(t));let i=null!=(l=null==t?void 0:t())?l:[];for(let e of i){if(!e)continue;let t=v(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)i.some(t=>e.contains(t))||o.add(b(e));n=n.parentElement}}return o.dispose},[r,t,n])})(!k&&!ed&&el,{allowed:(0,f._)(()=>{var e,t;return[null!=(t=null==(e=V.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,f._)(()=>{var e;return[null!=(e=null==ea?void 0:ea.closest("body > *:not(#headlessui-portal-root)"))?e:null]})}),d(el,"outside-click"),g=(0,o.Y)(e=>{e.preventDefault(),ee()}),w=(0,r.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(eu))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e,t=0){var n;return e!==(null==(n=v(e))?void 0:n.body)&&(0,E.Y)(t,{0:()=>e.matches(y),1(){let t=e;for(;null!==t;){if(t.matches(y))return!0;t=t.parentElement}return!1}})}(n,S.Loose)||-1===n.tabIndex||e.preventDefault(),g.current(e,n)}},[g,eu]),P=(0,r.useRef)(null),(0,o.Y)(e=>{var t,n;P.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target}),(0,o.Y)(e=>{var t,n;P.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target}),(0,o.Y)(e=>{A()||/Android/gi.test(window.navigator.userAgent)||P.current&&(w(e,()=>P.current),P.current=null)}),F=(0,r.useRef)({x:0,y:0}),(0,o.Y)(e=>{F.current.x=e.touches[0].clientX,F.current.y=e.touches[0].clientY}),(0,o.Y)(e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-F.current.x)>=30||Math.abs(t.y-F.current.y)>=30))return w(e,()=>e.target instanceof HTMLElement?e.target:null)}),(0,o.Y)(e=>w(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null)),function(e,t="undefined"!=typeof document?document.defaultView:null,n){let r=d(e,"escape");(0,o.Y)(e=>{r&&(e.defaultPrevented||e.key===l.Escape&&n(e))})}(el,null==z?void 0:z.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),ee()}),function(e,t,n=()=>[document.body]){!function(e,t,n=()=>({containers:[]})){let r=s(j),l=t?r.get(t):void 0;l&&l.count,(0,u.s)(()=>{if(!(!t||!e))return j.dispatch("PUSH",t,n),()=>j.dispatch("POP",t,n)},[e,t])}(d(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!k&&!ed&&el,z,eu),(0,o.Y)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&ee()});let[ep,ev]=function(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)(()=>function(e){let n=(0,f._)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),l=(0,r.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return r.createElement(q.Provider,{value:l},e.children)},[t])]}(),eh=(0,r.useMemo)(()=>[{dialogState:Z,close:ee,setTitleId:et,unmount:Y},J],[Z,J,ee,et,Y]),eg=(0,r.useMemo)(()=>({open:0===Z}),[Z]),eE={ref:G,id:T,role:N,tabIndex:-1,"aria-modal":k?void 0:0===Z||void 0,"aria-labelledby":J.titleId,"aria-describedby":ep,unmount:Y},ew=!function(){var e;let[t]=(0,r.useState)(()=>null),[n,l]=(0,r.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,u.s)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){l(e.matches)}},[t]),n}(),eC=en.None;el&&!k&&(eC|=en.RestoreFocus,eC|=en.TabLock,L&&(eC|=en.AutoFocus),ew&&(eC|=en.InitialFocus));let eS=(0,R.Ci)();return r.createElement($.$x,null,r.createElement(B,{force:!0},r.createElement(em,null,r.createElement(eb.Provider,{value:eh},r.createElement(ef,{target:V},r.createElement(B,{force:!1},r.createElement(ev,{slot:eg},r.createElement(ei,null,r.createElement(er,{initialFocus:M,initialFocusFallback:V,containers:eu,features:eC},r.createElement(U,{value:ee},eS({ourProps:eE,theirProps:X,slot:eg,defaultTag:eP,features:eF,visible:0===Z,name:"Dialog"})))))))))))}),eP="div",eF=R.Ac.RenderStrategy|R.Ac.Static,eC=Object.assign((0,R.FX)(function(e,t){let{transition:n=!1,open:l,...o}=e,i=(0,$.O_)(),a=e.hasOwnProperty("open")||null!==i,u=e.hasOwnProperty("onClose");if(!a&&!u)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&"boolean"!=typeof e.open)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(void 0!==l||n)&&!o.static?r.createElement(k,null,r.createElement(ep.e,{show:l,transition:n,unmount:o.unmount},r.createElement(ew,{ref:t,...o}))):r.createElement(k,null,r.createElement(ew,{ref:t,open:l,...o}))}),{Panel:(0,R.FX)(function(e,t){let n=(0,r.useId)(),{id:l=`headlessui-dialog-panel-${n}`,transition:o=!1,...i}=e,[{dialogState:a,unmount:u},s]=eE("Dialog.Panel"),c=(0,H.P)(t,s.panelRef),d=(0,r.useMemo)(()=>({open:0===a}),[a]),m=(0,f._)(e=>{e.stopPropagation()}),p=o?ep._:r.Fragment,v=(0,R.Ci)();return r.createElement(p,{...o?{unmount:u}:{}},v({ourProps:{ref:c,id:l,onClick:m},theirProps:i,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,R.FX)(function(e,t){let{transition:n=!1,...l}=e,[{dialogState:o,unmount:i}]=eE("Dialog.Backdrop"),a=(0,r.useMemo)(()=>({open:0===o}),[o]),u=n?ep._:r.Fragment,s=(0,R.Ci)();return r.createElement(u,{...n?{unmount:i}:{}},s({ourProps:{ref:t,"aria-hidden":!0},theirProps:l,slot:a,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,R.FX)(function(e,t){let n=(0,r.useId)(),{id:l=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:i,setTitleId:a}]=eE("Dialog.Title"),u=(0,H.P)(t),s=(0,r.useMemo)(()=>({open:0===i}),[i]);return(0,R.Ci)()({ourProps:{ref:u,id:l},theirProps:o,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),Description:K})},99923:(e,t,n)=>{n.d(t,{_:()=>a});var r=Object.defineProperty,l=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,o=(e,t,n)=>(l(e,"symbol"!=typeof t?t+"":t,n),n);class i{constructor(){o(this,"current",this.detect()),o(this,"handoffState","pending"),o(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"server"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let a=new i}};
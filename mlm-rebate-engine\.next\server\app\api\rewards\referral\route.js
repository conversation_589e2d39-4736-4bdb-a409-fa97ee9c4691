"use strict";(()=>{var e={};e.id=3338,e.ids=[3338],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},74167:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>q});var t={};s.r(t),s.d(t,{GET:()=>f,POST:()=>x});var o=s(96559),n=s(48088),a=s(37719),i=s(31183),u=s(32190),p=s(19854),d=s(12909),l=s(73967);async function x(e){try{let r=await (0,p.getServerSession)(d.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to process referral rewards"},{status:401});let s=r.user.email;if(!s)return u.NextResponse.json({error:"User email not found in session"},{status:400});let t=await i.z.user.findUnique({where:{email:s},select:{id:!0,rankId:!0}});if(!t)return u.NextResponse.json({error:"User not found"},{status:404});let o=6===t.rankId,{referrerId:n,newUserId:a}=await e.json();if(!n||!a)return u.NextResponse.json({error:"Missing required fields: referrerId and newUserId"},{status:400});if(!o)return u.NextResponse.json({error:"You do not have permission to process referral rewards"},{status:403});let x=await (0,l.G)(n,a);return u.NextResponse.json(x)}catch(e){return console.error("Error processing referral reward:",e),u.NextResponse.json({error:"Failed to process referral reward"},{status:500})}}async function f(e){try{let e=await (0,p.getServerSession)(d.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to view referral rewards"},{status:401});let r=e.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:r},select:{id:!0,rankId:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});if(6!==s.rankId)return u.NextResponse.json({error:"You do not have permission to view referral rewards"},{status:403});let t=await i.z.referralReward.findMany({orderBy:{createdAt:"desc"}});return u.NextResponse.json({referralRewards:t})}catch(e){return console.error("Error fetching referral rewards:",e),u.NextResponse.json({error:"Failed to fetch referral rewards"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/rewards/referral/route",pathname:"/api/rewards/referral",filename:"route",bundlePath:"app/api/rewards/referral/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rewards\\referral\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:w,workUnitAsyncStorage:q,serverHooks:m}=c;function h(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:q})}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,8044,3112,4079,4935],()=>s(74167));module.exports=t})();
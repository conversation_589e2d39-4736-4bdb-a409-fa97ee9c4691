(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>l,aP:()=>c});var s=t(96330),o=t(13581),n=t(85663),a=t(55511),i=t.n(a);async function u(e){return await n.Ay.hash(e,10)}function c(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let l={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:a,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=o?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(s,n,i):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70805:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),u=t(19854),c=t(12909),l=t(31183);async function d(e){try{let e=await (0,u.getServerSession)(c.Nh);if(!e||!e.user)return i.NextResponse.json({error:"You must be logged in to access this resource"},{status:401});let r=e.user.email;if(!r)return i.NextResponse.json({error:"User email not found in session"},{status:400});let t=await l.default.user.findUnique({where:{email:r},include:{rank:!0}});if(!t||t.rank.level<6)return i.NextResponse.json({error:"You do not have permission to access this resource"},{status:403});let[s,o,n,a,d,p,f,m,g,w,h]=await Promise.all([l.default.user.count(),l.default.product.count(),l.default.purchase.count(),l.default.rebate.count(),l.default.rebate.count({where:{status:"pending"}}),l.default.rebate.count({where:{status:"processed"}}),l.default.rebate.aggregate({_sum:{amount:!0}}),l.default.user.findMany({take:5,orderBy:{createdAt:"desc"},include:{rank:!0}}),l.default.purchase.findMany({take:5,orderBy:{createdAt:"desc"},include:{user:!0,product:!0}}),l.default.product.findMany({take:5,include:{_count:{select:{purchases:!0}}},orderBy:{purchases:{_count:"desc"}}}),l.default.user.findMany({take:5,include:{rank:!0,_count:{select:{downline:!0}}},orderBy:{downline:{_count:"desc"}}})]),y=m.map(e=>({id:e.id,name:e.name,email:e.email,rank:e.rank.name,createdAt:e.createdAt})),b=g.map(e=>({id:e.id,productName:e.product.name,userName:e.user.name,amount:e.totalAmount,date:e.createdAt})),x=w.map(e=>({id:e.id,name:e.name,price:e.price,purchaseCount:e._count.purchases})),v=h.map(e=>({id:e.id,name:e.name,rank:e.rank.name,downlineCount:e._count.downline}));return i.NextResponse.json({stats:{totalUsers:s,totalProducts:o,totalPurchases:n,totalRebates:a,pendingRebates:d,processedRebates:p,totalRebateAmount:f._sum.amount||0},recentActivity:{users:y,purchases:b},topPerformers:{products:x,distributors:v}})}catch(e){return console.error("Error fetching admin stats:",e),i.NextResponse.json({error:"Failed to fetch admin statistics"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:g}=p;function w(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(70805));module.exports=s})();
"use strict";(()=>{var e={};e.id=4944,e.ids=[4944],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},82468:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{POST:()=>x});var o=s(96559),i=s(48088),n=s(37719),a=s(31183),p=s(92168),u=s(32190),d=s(19854),c=s(12909);async function x(e){try{let e=await (0,d.getServerSession)(c.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let r=e.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await a.z.user.findUnique({where:{email:r},select:{id:!0,rankId:!0}});if(!s)return u.NextResponse.json({error:"Authenticated user not found"},{status:404});if(6!==s.rankId)return u.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let t=await a.z.rebate.count({where:{status:"pending"}}),o=await (0,p.sQ)();return u.NextResponse.json({message:"Rebates processed successfully",processed:o.processed,failed:o.failed,totalProcessed:t})}catch(e){return console.error("Error processing rebates:",e),u.NextResponse.json({error:"Failed to process rebates"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/rebates/process/route",pathname:"/api/admin/rebates/process",filename:"route",bundlePath:"app/api/admin/rebates/process/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\process\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:q,workUnitAsyncStorage:m,serverHooks:f}=l;function h(){return(0,n.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:m})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,8044,3112,4079,2896],()=>s(82468));module.exports=t})();
"use strict";(()=>{var e={};e.id=5914,e.ids=[5914],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},24704:(e,r,n)=>{n.d(r,{k0:()=>i,oX:()=>u,oc:()=>s});var a=n(31183),t=n(61904),o=n(6375);async function s(e){try{let r=await a.z.user.findUnique({where:{id:e},include:{rank:!0}});if(!r)throw Error(`User with ID ${e} not found`);let n=await a.z.rank.findMany({where:{level:{gt:r.rank.level}},orderBy:{level:"asc"}});if(0===n.length)return{eligible:!1,currentRank:r.rank,nextRank:null,message:"You are already at the highest rank."};let t=n[0],o=await a.z.rankRequirement.findUnique({where:{rankId:t.id}});if(!o)throw Error(`Requirements for rank ${t.name} not found`);let s=await l(e),i=s>=o.requiredPersonalSales,u=await d(e),m=u>=o.requiredGroupSales,f=await c(e),g=f>=o.requiredDirectDownline,k=0,w=!0;o.requiredQualifiedDownline>0&&o.qualifiedRankId&&(w=(k=await p(e,o.qualifiedRankId))>=o.requiredQualifiedDownline);let v=i&&m&&g&&w;return{eligible:v,currentRank:r.rank,nextRank:t,requirements:{personalSales:{required:o.requiredPersonalSales,actual:s,qualified:i},groupSales:{required:o.requiredGroupSales,actual:u,qualified:m},directDownline:{required:o.requiredDirectDownline,actual:f,qualified:g},qualifiedDownline:{required:o.requiredQualifiedDownline,actual:k,qualified:w,qualifiedRankId:o.qualifiedRankId}},message:v?`Congratulations! You qualify for advancement to ${t.name} rank.`:`You do not yet qualify for advancement to ${t.name} rank.`}}catch(e){throw console.error("Error checking rank advancement eligibility:",e),e}}async function i(e){try{let r=await s(e);if(!r.eligible||!r.nextRank)return{success:!1,message:r.message,previousRank:r.currentRank,newRank:null};let n=await a.z.user.findUnique({where:{id:e},select:{id:!0,name:!0,email:!0,rankId:!0}});if(!n)throw Error(`User with ID ${e} not found`);let o=await a.z.user.update({where:{id:e},data:{rankId:r.nextRank.id},include:{rank:!0}});if(await a.z.rankAdvancement.create({data:{userId:e,previousRankId:r.currentRank.id,newRankId:r.nextRank.id,personalSales:r.requirements.personalSales.actual,groupSales:r.requirements.groupSales.actual,directDownlineCount:r.requirements.directDownline.actual,qualifiedDownlineCount:r.requirements.qualifiedDownline.actual}}),n.email)try{await (0,t.Z)(n.email,"rankAdvancement",{userName:n.name,previousRank:r.currentRank.name,newRank:r.nextRank.name})}catch(r){console.error(`Failed to send rank advancement email to user ${e}:`,r)}return{success:!0,message:`Congratulations! You have been advanced to ${r.nextRank.name} rank.`,previousRank:r.currentRank,newRank:o.rank}}catch(e){throw console.error("Error processing rank advancement:",e),e}}async function u(){try{let e=await a.z.user.findMany({select:{id:!0}}),r={processed:0,advanced:0,failed:0,advancedUsers:[],failedUsers:[]};for(let n of e)try{r.processed++;let e=await s(n.id);if(e.eligible&&e.nextRank){let e=await i(n.id);e.success?(r.advanced++,r.advancedUsers.push({userId:n.id,previousRank:e.previousRank.name,newRank:e.newRank.name})):(r.failed++,r.failedUsers.push({userId:n.id,reason:e.message}))}}catch(e){console.error(`Error processing rank advancement for user ${n.id}:`,e),r.failed++,r.failedUsers.push({userId:n.id,reason:e instanceof Error?e.message:"Unknown error"})}return r}catch(e){throw console.error("Error processing all rank advancements:",e),e}}async function l(e){return(await a.z.purchase.aggregate({where:{userId:e},_sum:{totalAmount:!0}}))._sum.totalAmount||0}async function d(e){let r=await (0,o.Op)(e),n=[e,...r];return(await a.z.purchase.aggregate({where:{userId:{in:n}},_sum:{totalAmount:!0}}))._sum.totalAmount||0}async function c(e){return await a.z.user.count({where:{uplineId:e}})}async function p(e,r){let n=await a.z.rank.findUnique({where:{id:r},select:{level:!0}});if(!n)throw Error(`Qualified rank with ID ${r} not found`);let t=await (0,o.Op)(e);return await a.z.user.count({where:{id:{in:t},rank:{level:{gte:n.level}}}})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},61904:(e,r,n)=>{n.d(r,{Z:()=>o});let a=n(49526).createTransport({host:process.env.EMAIL_HOST||"smtp.example.com",port:parseInt(process.env.EMAIL_PORT||"587"),secure:"true"===process.env.EMAIL_SECURE,auth:{user:process.env.EMAIL_USER||"<EMAIL>",pass:process.env.EMAIL_PASSWORD||"password"}}),t={rebateReceived:e=>({subject:`You've Received a Rebate of $${e.amount.toFixed(2)}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">New Rebate Received!</h2>
          <p>Hello ${e.userName},</p>
          <p>Great news! You've received a rebate of <strong style="color: #48bb78;">$${e.amount.toFixed(2)}</strong>.</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Rebate Details:</h3>
            <ul style="padding-left: 20px;">
              <li>Amount: <strong>$${e.amount.toFixed(2)}</strong></li>
              <li>From: <strong>${e.generatorName}</strong></li>
              <li>Level: <strong>${e.level}</strong></li>
              <li>Product: <strong>${e.productName}</strong></li>
            </ul>
          </div>
          <p>This rebate has been added to your wallet balance. You can view your rebate details and wallet balance by logging into your account.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/wallet" style="background-color: #4299e1; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Wallet</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for being part of our MLM network!</p>
        </div>
      `}),rankAdvancement:e=>({subject:`Congratulations on Your Rank Advancement to ${e.newRank}!`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">Rank Advancement Achievement!</h2>
          <p>Hello ${e.userName},</p>
          <p>Congratulations! You've advanced from <strong>${e.oldRank}</strong> to <strong style="color: #805ad5;">${e.newRank}</strong>!</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Your New Benefits:</h3>
            <ul style="padding-left: 20px;">
              ${e.benefits.map(e=>`<li>${e}</li>`).join("")}
            </ul>
          </div>
          <p>Keep up the great work! As you continue to grow your network and increase your sales, you'll unlock even more benefits and higher rebate percentages.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" style="background-color: #805ad5; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Dashboard</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for your dedication and commitment to our MLM network!</p>
        </div>
      `})};async function o(e,r,n){try{let{subject:o,html:s}=t[r](n),i={from:process.env.EMAIL_FROM||"MLM Rebate Engine <<EMAIL>>",to:e,subject:o,html:s},u=await a.sendMail(i);return console.log("Email sent:",u.messageId),{success:!0,messageId:u.messageId}}catch(e){return console.error("Error sending email:",e),{success:!1,error:e}}}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},75895:(e,r,n)=>{n.r(r),n.d(r,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>k});var a={};n.r(a),n.d(a,{GET:()=>p,POST:()=>m});var t=n(96559),o=n(48088),s=n(37719),i=n(31183),u=n(32190),l=n(19854),d=n(12909),c=n(24704);async function p(e){try{let e=await (0,l.getServerSession)(d.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to check rank advancement eligibility"},{status:401});let r=e.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let n=await i.z.user.findUnique({where:{email:r},select:{id:!0}});if(!n)return u.NextResponse.json({error:"User not found"},{status:404});let a=await (0,c.oc)(n.id);return u.NextResponse.json(a)}catch(e){return console.error("Error checking rank advancement eligibility:",e),u.NextResponse.json({error:"Failed to check rank advancement eligibility"},{status:500})}}async function m(e){try{let r=await (0,l.getServerSession)(d.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to process rank advancement"},{status:401});let n=r.user.email;if(!n)return u.NextResponse.json({error:"User email not found in session"},{status:400});let a=await i.z.user.findUnique({where:{email:n},select:{id:!0,rankId:!0}});if(!a)return u.NextResponse.json({error:"User not found"},{status:404});let t=6===a.rankId,o=(await e.json()).userId||a.id;if(o!==a.id&&!t)return u.NextResponse.json({error:"You do not have permission to process rank advancement for other users"},{status:403});let s=await (0,c.k0)(o);return u.NextResponse.json(s)}catch(e){return console.error("Error processing rank advancement:",e),u.NextResponse.json({error:"Failed to process rank advancement"},{status:500})}}let f=new t.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/users/rank-advancement/route",pathname:"/api/users/rank-advancement",filename:"route",bundlePath:"app/api/users/rank-advancement/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\rank-advancement\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:k,serverHooks:w}=f;function v(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:k})}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var n=e=>r(r.s=e),a=r.X(0,[4243,580,8044,3112,4079,6719],()=>n(75895));module.exports=a})();
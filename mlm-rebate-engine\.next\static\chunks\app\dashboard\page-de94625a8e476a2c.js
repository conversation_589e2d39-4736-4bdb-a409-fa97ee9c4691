(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{8190:(e,s,t)=>{Promise.resolve().then(t.bind(t,44879))},44879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(95155),l=t(12115),r=t(12108),i=t(35695),n=t(66766),d=t(87747),c=t(70357),m=t(29911);let x=(0,l.lazy)(()=>Promise.all([t.e(5647),t.e(8579),t.e(1382)]).then(t.bind(t,41382))),o=(0,l.lazy)(()=>t.e(9794).then(t.bind(t,99794))),h=(0,l.lazy)(()=>t.e(8453).then(t.bind(t,98453))),u=(0,l.lazy)(()=>t.e(5134).then(t.bind(t,45134))),j=(0,l.lazy)(()=>t.e(9537).then(t.bind(t,39537))),g=(0,l.lazy)(()=>t.e(4884).then(t.bind(t,64884))),b=(0,l.lazy)(()=>t.e(3380).then(t.bind(t,3380))),f=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month",s=await fetch("/api/dashboard?timeframe=".concat(e));if(!s.ok)throw Error("Failed to fetch dashboard data");return s.json()};function N(){var e,s,t,N,v,p,y;let{data:w,status:k}=(0,r.useSession)(),C=(0,i.useRouter)(),[D,z]=(0,l.useState)("month");(0,l.useEffect)(()=>{"unauthenticated"===k&&C.push("/login")},[k,C]);let{data:L,isLoading:S,error:A}=(0,d.I)({queryKey:["dashboardData",D],queryFn:()=>f(D),enabled:"authenticated"===k,staleTime:3e5,refetchOnWindowFocus:!1});if("loading"===k||S)return(0,a.jsx)(c.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})});if(A)return(0,a.jsx)(c.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl text-red-500",children:"Error loading dashboard data"})})});let H={id:(null==L||null==(e=L.user)?void 0:e.id.toString())||"current-user",name:(null==L||null==(s=L.user)?void 0:s.name)||(null==w||null==(t=w.user)?void 0:t.name)||"Current User",rank:(null==L||null==(N=L.user)?void 0:N.rank)||"Distributor",image:(null==L||null==(v=L.user)?void 0:v.profileImage)||(null==w||null==(p=w.user)?void 0:p.image)||void 0},P=(null==L||null==(y=L.recentData)?void 0:y.rebates.slice(0,3).map((e,s)=>({id:"user".concat(s+1),name:e.generator.name,rank:"Distributor",position:s%2==0?"left":"right"})))||[],E=e=>{z(e)};return(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold",children:"Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"flex items-center bg-gray-100 rounded-md",children:(0,a.jsxs)("select",{value:D,onChange:e=>E(e.target.value),className:"bg-transparent border-none py-2 px-3 focus:ring-0 text-sm",children:[(0,a.jsx)("option",{value:"week",children:"Last 7 Days"}),(0,a.jsx)("option",{value:"month",children:"This Month"}),(0,a.jsx)("option",{value:"year",children:"This Year"})]})}),(0,a.jsxs)("button",{className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none",children:[(0,a.jsx)(m.jNV,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center text-xs text-white",children:"3"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,a.jsx)(m.lcY,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Wallet Balance"}),(0,a.jsxs)("p",{className:"text-2xl font-semibold",children:["₱",(null==L?void 0:L.stats.walletBalance.toFixed(2))||"0.00"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,a.jsx)(m.YYR,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,a.jsxs)("p",{className:"text-2xl font-semibold",children:["₱",(null==L?void 0:L.stats.totalRebates.toFixed(2))||"0.00"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-purple-100 text-purple-500 mr-4",children:(0,a.jsx)(m.YXz,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Downline Members"}),(0,a.jsx)("p",{className:"text-2xl font-semibold",children:(null==L?void 0:L.stats.downlineCount)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-orange-100 text-orange-500 mr-4",children:(0,a.jsx)(m.AsH,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Purchases"}),(0,a.jsx)("p",{className:"text-2xl font-semibold",children:(null==L?void 0:L.stats.purchaseCount)||0})]})]})})]}),(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading performance summary..."}),children:(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(h,{metrics:[]})})}),(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading charts..."}),children:(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(x,{rebateData:(null==L?void 0:L.charts.rebates)?Object.values(L.charts.rebates):[],salesData:(null==L?void 0:L.charts.sales)?Object.values(L.charts.sales):[],rankDistribution:(null==L?void 0:L.charts.rankDistribution)||{},timeframe:D})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading widget..."}),children:(0,a.jsx)("div",{children:(0,a.jsx)(j,{})})}),(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading widget..."}),children:(0,a.jsx)("div",{children:(0,a.jsx)(g,{})})}),(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading widget..."}),children:(0,a.jsx)("div",{children:(0,a.jsx)(b,{})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading genealogy..."}),children:(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)(o,{currentUser:H,downlineMembers:P})})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Recent Rebates"})}),(0,a.jsxs)("div",{className:"p-4",children:[(null==L?void 0:L.recentData.rebates)&&L.recentData.rebates.length>0?(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:L.recentData.rebates.map(e=>(0,a.jsxs)("div",{className:"py-3 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.generator.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"text-sm font-medium text-green-600",children:["+₱",e.amount.toFixed(2)]})]},e.id))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No rebates received yet."}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("button",{onClick:()=>C.push("/rebates"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"View All Rebates"})})]})]})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(u,{})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md mb-8",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Quick Actions"})}),(0,a.jsxs)("div",{className:"p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>C.push("/shop"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(m.AsH,{className:"mr-2"})," Shop Products"]}),(0,a.jsxs)("button",{onClick:()=>C.push("/genealogy"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors",children:[(0,a.jsx)(m.YXz,{className:"mr-2"})," View Genealogy"]}),(0,a.jsxs)("button",{onClick:()=>C.push("/wallet"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 transition-colors",children:[(0,a.jsx)(m.lcY,{className:"mr-2"})," Manage Wallet"]}),(0,a.jsxs)("button",{onClick:()=>C.push("/referrals"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:[(0,a.jsx)(m.YXz,{className:"mr-2"})," Invite Members"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(m.sHz,{className:"text-green-500 mr-2"})," About Extreme Life Herbal"]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"relative w-24 h-24",children:(0,a.jsx)(n.default,{src:"/images/20250503.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})})}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Extreme Life Herbal Products Trading is a leading provider of high-quality herbal supplements and wellness products in the Philippines."}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Our mission is to promote health and wellness through natural products while providing business opportunities for our distributors."}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("button",{onClick:()=>C.push("/about"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"Learn More"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(m.AsH,{className:"text-green-500 mr-2"})," Featured Products"]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,a.jsx)(m.sHz,{})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Biogen Extreme Concentrate"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Concentrated organic enzyme formula"})]})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,a.jsx)(m.sHz,{})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Veggie Coffee 124 in 1"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Caffeine-free coffee alternative"})]})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,a.jsx)(m.sHz,{})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Shield Herbal Care Soap"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Premium herbal soap for skin care"})]})]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("button",{onClick:()=>C.push("/shop"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"View All Products"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,a.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(m.dRU,{className:"text-green-500 mr-2"})," Contact Us"]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("ul",{className:"space-y-4",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,a.jsx)(m.dRU,{})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Phone"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"+63 (2) 8123 4567"})]})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,a.jsx)(m.maD,{})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"<EMAIL>"})]})]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,a.jsx)(m.vq8,{})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Address"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"123 Herbal Street, Makati City, Metro Manila, Philippines"})]})]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("button",{onClick:()=>C.push("/contact"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"Contact Us"})})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,6967,7747,357,8441,1684,7358],()=>s(8190)),_N_E=e.O()}]);
"use strict";exports.id=2537,exports.ids=[2537],exports.modules={12537:(e,s,r)=>{r.r(s),r.d(s,{default:()=>i});var a=r(60687),t=r(43210),l=r(23877);let i=({metrics:e=[],period:s="month"})=>{let[r,i]=(0,t.useState)(s),n=(e,s)=>{switch(s){case"currency":return`₱${e.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})}`;case"percentage":return`${e}%`;default:return e.toLocaleString()}},o=(e,s)=>0===s?100*(e>0):(e-s)/s*100,c=(e,s)=>{let r=o(e,s),t=Math.abs(r).toFixed(1);return r>0?(0,a.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,a.jsx)(l.uCC,{className:"mr-1"}),(0,a.jsxs)("span",{children:[t,"%"]})]}):r<0?(0,a.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,a.jsx)(l.$TP,{className:"mr-1"}),(0,a.jsxs)("span",{children:[t,"%"]})]}):(0,a.jsxs)("div",{className:"flex items-center text-gray-500",children:[(0,a.jsx)(l.VsL,{className:"mr-1"}),(0,a.jsx)("span",{children:"0%"})]})},d=e.length>0?e:[{label:"Total Earnings",value:12500,previousValue:10800,format:"currency",info:"Sum of all rebates and bonuses earned"},{label:"Group Volume",value:45e3,previousValue:38e3,format:"number",info:"Total Point Value (PV) from your entire downline"},{label:"Personal Volume",value:5200,previousValue:4800,format:"number",info:"Point Value (PV) from your personal purchases"},{label:"Conversion Rate",value:68,previousValue:62,format:"percentage",info:"Percentage of referrals who became distributors"}];return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-b flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,a.jsx)(l.YYR,{className:"mr-2 text-green-500"})," Performance Summary"]}),(0,a.jsxs)("select",{value:r,onChange:e=>i(e.target.value),className:"text-sm border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",children:[(0,a.jsx)("option",{value:"week",children:"This Week"}),(0,a.jsx)("option",{value:"month",children:"This Month"}),(0,a.jsx)("option",{value:"year",children:"This Year"})]})]}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:d.map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:e.label}),e.info&&(0,a.jsxs)("div",{className:"group relative",children:[(0,a.jsx)(l.__w,{className:"text-gray-400 hover:text-gray-600 cursor-help"}),(0,a.jsx)("div",{className:"absolute right-0 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-10",children:e.info})]})]}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mb-1",children:n(e.value,e.format)}),(0,a.jsxs)("div",{className:"flex items-center text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-500 mr-2",children:["vs previous ",r,":"]}),c(e.value,e.previousValue)]})]},s))})})]})}}};
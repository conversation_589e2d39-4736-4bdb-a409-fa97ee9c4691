(()=>{var e={};e.id=8597,e.ids=[8597],e.modules={1935:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\products\\\\oxygen-extreme\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\oxygen-extreme\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25261:(e,s,r)=>{Promise.resolve().then(r.bind(r,1935))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40332:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(65239),a=r(48088),l=r(88170),n=r.n(l),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["products",{children:["oxygen-extreme",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1935)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\oxygen-extreme\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\products\\oxygen-extreme\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/products/oxygen-extreme/page",pathname:"/products/oxygen-extreme",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},88877:(e,s,r)=>{Promise.resolve().then(r.bind(r,96033))},96033:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h});var t=r(60687),a=r(43210),l=r(82136),n=r(59391),i=r(85814),d=r.n(i),c=r(23877),o=r(37590),x=r(83515),m=r(231),p=r(38458),u=r(49929);function h(){let{data:e}=(0,l.useSession)(),{addToCart:s}=(0,x._)(),[r,i]=(0,a.useState)(1),[h,g]=(0,a.useState)(null),[b,j]=(0,a.useState)(!1),{data:y,isLoading:v,error:N}=(0,n.I)({queryKey:["oxygen-extreme"],queryFn:async()=>{let e=await fetch("/api/products/oxygen-extreme");if(!e.ok)throw Error("Failed to fetch product");return e.json()}});if(v)return(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsx)("div",{className:"h-96 bg-gray-200 rounded"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-2/3 mb-4"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-6"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-6"}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded mb-6"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-full mb-4"})]})]})]})});if(N||!y)return(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,t.jsx)("p",{children:"Failed to load product. Please try again later."})})});let{product:f,reviews:w,relatedProducts:P}=y,k=y?.reviews&&0!==y.reviews.length?y.reviews.reduce((e,s)=>e+s.rating,0)/y.reviews.length:0;return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("nav",{className:"text-sm text-gray-500",children:(0,t.jsxs)("ol",{className:"list-none p-0 inline-flex",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(d(),{href:"/products",className:"hover:text-blue-600",children:"Products"}),(0,t.jsx)("span",{className:"mx-2",children:"/"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(d(),{href:"/products/category/health-supplements",className:"hover:text-blue-600",children:"Health Supplements"}),(0,t.jsx)("span",{className:"mx-2",children:"/"})]}),(0,t.jsx)("li",{className:"flex items-center text-gray-700",children:"Oxygen Extreme"})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12",children:[(0,t.jsx)("div",{children:f.productImages&&f.productImages.length>0?(0,t.jsx)(u.A,{images:f.productImages}):(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg flex items-center justify-center h-96",children:(0,t.jsx)(c.__w,{className:"text-gray-400 text-4xl"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:f.name}),(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"flex mr-2",children:(e=>{let s=[],r=Math.floor(e),a=e%1>=.5;for(let e=1;e<=5;e++)e<=r?s.push((0,t.jsx)(c.gt3,{className:"text-yellow-400"},e)):e===r+1&&a?s.push((0,t.jsx)(c.gVl,{className:"text-yellow-400"},e)):s.push((0,t.jsx)(c.wei,{className:"text-yellow-400"},e));return s})(k)}),(0,t.jsxs)("span",{className:"text-gray-600",children:[w.length," ",1===w.length?"review":"reviews"]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[f.salePrice&&f.salePrice<f.price?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("span",{className:"text-2xl font-bold text-blue-600 mr-2",children:["₱",(f.salePrice/100).toFixed(2)]}),(0,t.jsxs)("span",{className:"text-lg text-gray-500 line-through",children:["₱",(f.price/100).toFixed(2)]}),(0,t.jsxs)("span",{className:"ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded",children:["Save ",Math.round((f.price-f.salePrice)/f.price*100),"%"]})]}):(0,t.jsxs)("span",{className:"text-2xl font-bold text-blue-600",children:["₱",(f.price/100).toFixed(2)]}),(0,t.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:["Point Value: ",(0,t.jsxs)("span",{className:"font-medium text-green-600",children:[f.pointValue," PV"]})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("p",{className:"text-gray-700",children:f.shortDescription})}),(0,t.jsxs)("div",{className:"mb-6 bg-blue-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Key Benefits"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)(c.DFS,{className:"text-blue-500 mt-1 mr-2"}),(0,t.jsx)("span",{children:"Helps maintain acid-alkaline balance"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)(c.XcJ,{className:"text-blue-500 mt-1 mr-2"}),(0,t.jsx)("span",{children:"Oxygenates the cells"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)(c.A7C,{className:"text-blue-500 mt-1 mr-2"}),(0,t.jsx)("span",{children:"Tasteless and odorless in water"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)(c.sHz,{className:"text-blue-500 mt-1 mr-2"}),(0,t.jsx)("span",{children:"Gluten-free and vegan"})]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)(c.C8M,{className:"text-blue-500 mt-1 mr-2"}),(0,t.jsx)("span",{children:"Contains essential minerals and trace minerals"})]})]})]}),f.productVariants&&f.productVariants.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Size"}),(0,t.jsx)("div",{className:"flex space-x-2",children:f.productVariants.map(e=>(0,t.jsx)("button",{onClick:()=>g(e),className:`px-4 py-2 border rounded-md ${h?.id===e.id?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:e.name},e.id))})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quantity"}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("button",{onClick:()=>r>1&&i(r-1),className:"px-3 py-1 border border-gray-300 rounded-l-md bg-gray-50 text-gray-600 hover:bg-gray-100",children:"-"}),(0,t.jsx)("input",{type:"number",min:"1",max:"10",value:r,onChange:e=>{let s=parseInt(e.target.value);s>0&&s<=10&&i(s)},className:"w-16 text-center border-t border-b border-gray-300 py-1"}),(0,t.jsx)("button",{onClick:()=>r<10&&i(r+1),className:"px-3 py-1 border border-gray-300 rounded-r-md bg-gray-50 text-gray-600 hover:bg-gray-100",children:"+"}),(0,t.jsxs)("span",{className:"ml-3 text-sm text-gray-500",children:[h?.stock||f.stock," available"]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-4 mb-6",children:[(0,t.jsxs)("button",{onClick:()=>{y?.product&&h&&(s({id:y.product.id,name:y.product.name,price:h.salePrice||h.price,image:y.product.productImages[0]?.url||"/images/placeholder.png",quantity:r,variant:h.name,variantId:h.id,pointValue:y.product.pointValue}),o.oR.success("Added to cart!"))},className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-md font-medium flex items-center justify-center",children:[(0,t.jsx)(c.AsH,{className:"mr-2"}),"Add to Cart"]}),(0,t.jsx)("button",{onClick:()=>{j(!b),b?o.oR.success("Removed from favorites!"):o.oR.success("Added to favorites!")},className:"p-3 border border-gray-300 rounded-md hover:bg-gray-50","aria-label":"Add to favorites",children:b?(0,t.jsx)(c.Mbv,{className:"text-red-500"}):(0,t.jsx)(c.sOK,{className:"text-gray-600"})}),(0,t.jsx)("button",{className:"p-3 border border-gray-300 rounded-md hover:bg-gray-50","aria-label":"Share product",children:(0,t.jsx)(c.Zzu,{className:"text-gray-600"})})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,t.jsx)(c.A7C,{className:"text-green-500 mr-2"}),(0,t.jsx)("span",{children:h?.stock||f.stock>0?"In Stock":"Out of Stock"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Free shipping on orders over ₱1,500"})]})]})]}),(0,t.jsxs)("div",{className:"mb-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Product Description"}),(0,t.jsx)("div",{className:"prose max-w-none",children:f.description.split("\n\n").map((e,s)=>(0,t.jsx)("p",{className:"mb-4",children:e},s))})]}),(0,t.jsx)(m.A,{productId:f.id,reviews:w,averageRating:k}),P&&P.length>0&&(0,t.jsx)(p.A,{products:P})]})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,8414,9567,3877,474,9391,9180],()=>r(40332));module.exports=t})();
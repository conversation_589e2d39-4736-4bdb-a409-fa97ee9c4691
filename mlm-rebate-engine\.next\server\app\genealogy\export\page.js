(()=>{var e={};e.id=2431,e.ids=[2431],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9135:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\export\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\export\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>i,_:()=>o});var s=r(60687),n=r(43210);let a=(0,n.createContext)(void 0),o=()=>{let e=(0,n.useContext)(a);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},i=({children:e})=>{let[t,r]=(0,n.useState)([]);(0,n.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{r(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,n.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let o=e=>{r(t=>t.filter(t=>t.id!==e))},i=t.reduce((e,t)=>e+t.quantity,0),l=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,s.jsx)(a.Provider,{value:{items:t,addItem:e=>{r(t=>{let r=t.findIndex(t=>t.id===e.id);if(!(r>=0))return[...t,e];{let s=[...t];return s[r]={...s[r],quantity:s[r].quantity+e.quantity},s}})},removeItem:o,updateQuantity:(e,t)=>{if(t<=0)return void o(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},itemCount:i,subtotal:l,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>n});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},45021:(e,t,r)=>{Promise.resolve().then(r.bind(r,68525))},45851:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),n=r(25217),a=r(8693),o=r(43210);function i({children:e}){let[t]=(0,o.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,s.jsx)(a.Ht,{client:t,children:e})}},49757:(e,t,r)=>{Promise.resolve().then(r.bind(r,9135))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(60687),n=r(43210);let a=()=>"serviceWorker"in navigator,o=async()=>{if(!a())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),i(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},i=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,r]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{o();let e=()=>{r(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,s.jsxs)(s.Fragment,{children:[e,t&&(0,s.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,s.jsxs)("div",{className:"mr-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,s.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,s.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,r)=>{Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,23229)),Promise.resolve().then(r.bind(r,82113)),Promise.resolve().then(r.bind(r,41750))},68525:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),n=r(43210),a=r(82136),o=r(16189),i=r(23877),l=r(85814),d=r.n(l),c=r(33103);function m({userId:e,userName:t}){let[r,a]=(0,n.useState)(!1),[o,l]=(0,n.useState)("excel"),[d,m]=(0,n.useState)(!0),[u,p]=(0,n.useState)(3),[h,x]=(0,n.useState)(null),[b,v]=(0,n.useState)(null),g=async()=>{a(!0),x(null),v(null);try{let r=await fetch(`/api/genealogy?userId=${e}&maxLevel=${u}&includePerformanceMetrics=${d}`);if(!r.ok)throw Error("Failed to fetch genealogy data");let s=await r.json(),n=f(s,d);switch(o){case"excel":y(n,`${t}_genealogy`);break;case"csv":j(n,`${t}_genealogy`);break;case"pdf":await w(n,`${t}_genealogy`)}x(!0)}catch(e){console.error("Export error:",e),v(e instanceof Error?e.message:"An unknown error occurred"),x(!1)}finally{a(!1)}},f=(e,t)=>{let r=[],s=(e,n,a="")=>{let o={ID:e.id,Name:e.name,Email:e.email,Rank:e.rank.name,Level:n,"Downline Count":e._count.downline,"Parent Name":a,"Joined Date":e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"};void 0!==e.walletBalance&&(o["Wallet Balance"]=e.walletBalance),t&&e.performanceMetrics&&(o["Personal Sales"]=e.performanceMetrics.personalSales,o["Team Sales"]=e.performanceMetrics.teamSales,o["Total Sales"]=e.performanceMetrics.totalSales,o["Rebates Earned"]=e.performanceMetrics.rebatesEarned,o["Team Size"]=e.performanceMetrics.teamSize,o["New Team Members (30d)"]=e.performanceMetrics.newTeamMembers),r.push(o),e.children&&e.children.length>0&&e.children.forEach(t=>{s(t,n+1,e.name)})};return s(e,0),r},y=(e,t)=>{let r=c.Wp.json_to_sheet(e),s=c.Wp.book_new();c.Wp.book_append_sheet(s,r,"Genealogy");let n=e.reduce((e,t)=>(Object.keys(t).forEach(r=>{let s=String(t[r]);e[r]=Math.max(e[r]||0,s.length)}),e),{});r["!cols"]=Object.keys(n).map(e=>({wch:n[e]+2})),c._h(s,`${t}.xlsx`)},j=(e,t)=>{let r=c.Wp.json_to_sheet(e),s=new Blob([c.Wp.sheet_to_csv(r)],{type:"text/csv;charset=utf-8;"}),n=URL.createObjectURL(s),a=document.createElement("a");a.href=n,a.setAttribute("download",`${t}.csv`),document.body.appendChild(a),a.click(),document.body.removeChild(a)},w=async(e,r)=>{let s='<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';e.length>0&&(s+='<thead><tr style="background-color: #f2f2f2;">',Object.keys(e[0]).forEach(e=>{s+=`<th>${e}</th>`}),s+="</tr></thead>"),s+="<tbody>",e.forEach(e=>{s+="<tr>",Object.values(e).forEach(e=>{s+=`<td>${e}</td>`}),s+="</tr>"}),s+="</tbody></table>";let n=window.open("","_blank");if(!n)throw Error("Failed to open print window. Please check your popup blocker settings.");n.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${r}</title>
          <style>
            body { font-family: Arial, sans-serif; }
            h1 { text-align: center; }
            table { width: 100%; border-collapse: collapse; }
            th { background-color: #f2f2f2; }
            th, td { padding: 8px; border: 1px solid #ddd; }
            tr:nth-child(even) { background-color: #f9f9f9; }
          </style>
        </head>
        <body>
          <h1>Genealogy Export: ${t}</h1>
          <p>Generated on: ${new Date().toLocaleString()}</p>
          ${s}
        </body>
      </html>
    `),n.document.close(),setTimeout(()=>{n.print()},500)};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,s.jsx)(i.Mbn,{className:"mr-2 text-blue-500"}),"Export Genealogy Data"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Export Format"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>l("excel"),className:`flex-1 flex items-center justify-center px-4 py-2 border rounded-md ${"excel"===o?"bg-green-50 border-green-500 text-green-700":"border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)(i.Ru,{className:"mr-2"}),"Excel"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>l("csv"),className:`flex-1 flex items-center justify-center px-4 py-2 border rounded-md ${"csv"===o?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)(i.QaY,{className:"mr-2"}),"CSV"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>l("pdf"),className:`flex-1 flex items-center justify-center px-4 py-2 border rounded-md ${"pdf"===o?"bg-red-50 border-red-500 text-red-700":"border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:[(0,s.jsx)(i.kl1,{className:"mr-2"}),"PDF"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Maximum Levels to Export"}),(0,s.jsxs)("select",{value:u,onChange:e=>p(parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:1,children:"1 Level (Direct Downline Only)"}),(0,s.jsx)("option",{value:2,children:"2 Levels"}),(0,s.jsx)("option",{value:3,children:"3 Levels"}),(0,s.jsx)("option",{value:4,children:"4 Levels"}),(0,s.jsx)("option",{value:5,children:"5 Levels"}),(0,s.jsx)("option",{value:6,children:"6 Levels (Maximum)"})]})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"includePerformanceMetrics",checked:d,onChange:e=>m(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"includePerformanceMetrics",className:"ml-2 block text-sm text-gray-700",children:"Include Performance Metrics (sales, rebates, etc.)"})]})}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{type:"button",onClick:g,disabled:r,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed flex items-center",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.hW,{className:"animate-spin mr-2"}),"Exporting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.Mbn,{className:"mr-2"}),"Export Genealogy"]})})}),null!==h&&(0,s.jsx)("div",{className:`mt-4 p-3 rounded-md ${h?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,children:(0,s.jsx)("div",{className:"flex items-center",children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.CMH,{className:"mr-2"}),(0,s.jsx)("span",{children:"Export completed successfully!"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.QCr,{className:"mr-2"}),(0,s.jsxs)("span",{children:["Export failed: ",b]})]})})})]})}function u(){let{data:e,status:t}=(0,a.useSession)();(0,o.useSearchParams)().get("userId");let[r,l]=(0,n.useState)(void 0),[c,u]=(0,n.useState)(""),[p,h]=(0,n.useState)(!0),[x,b]=(0,n.useState)(null);return"loading"===t||p?(0,s.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,s.jsx)(i.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("span",{children:"Loading..."})]})}):"unauthenticated"===t?(0,s.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,s.jsx)(i.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to export genealogy data."}),(0,s.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):x?(0,s.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:[(0,s.jsx)("p",{className:"font-medium",children:"Error"}),(0,s.jsx)("p",{children:x})]})}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,s.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Export Genealogy Data"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Export your genealogy data in various formats"})]}),(0,s.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,s.jsx)(i.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:r&&(0,s.jsx)(m,{userId:r,userName:c})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,s.jsx)(i.__w,{className:"mr-2 text-blue-500"}),"Export Information"]}),(0,s.jsxs)("div",{className:"space-y-4 text-sm",children:[(0,s.jsx)("p",{children:"Export your genealogy data to analyze it in external tools or share it with your team."}),(0,s.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-700 mb-2",children:"Available Export Formats"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Excel (.xlsx)"})," - Best for detailed analysis and formatting"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"CSV"})," - Compatible with most spreadsheet and database applications"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"PDF"})," - Best for printing and sharing"]})]})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,s.jsx)("h4",{className:"font-medium text-yellow-700 mb-2",children:"Export Tips"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,s.jsx)("li",{children:"Exporting more levels will result in larger files"}),(0,s.jsx)("li",{children:"Including performance metrics provides more detailed data"}),(0,s.jsx)("li",{children:"PDF export opens a print dialog for saving or printing"})]})]}),(0,s.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,s.jsx)("h4",{className:"font-medium text-green-700 mb-2",children:"Data Privacy"}),(0,s.jsx)("p",{className:"text-green-700",children:"Exported data may contain sensitive information. Please handle it securely and in accordance with your organization's data privacy policies."})]})]})]})})]})]})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,r)=>{Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,97695)),Promise.resolve().then(r.bind(r,45851)),Promise.resolve().then(r.bind(r,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},85786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),n=r(48088),a=r(88170),o=r.n(a),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["genealogy",{children:["export",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9135)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\export\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\export\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/genealogy/export/page",pathname:"/genealogy/export",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>u});var s=r(37413),n=r(22376),a=r.n(n),o=r(68726),i=r.n(o);r(61135);var l=r(23229),d=r(37043),c=r(82113),m=r(41750);let u={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function p({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${i().variable} antialiased`,children:(0,s.jsx)(l.AuthProvider,{children:(0,s.jsx)(c.default,{children:(0,s.jsx)(d.CartProvider,{children:(0,s.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},97695:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(60687),n=r(82136);function a({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,8414,9567,3877,3103],()=>r(85786));module.exports=s})();
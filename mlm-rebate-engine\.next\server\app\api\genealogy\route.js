"use strict";(()=>{var e={};e.id=337,e.ids=[337],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12269:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{e.exports=require("assert")},19854:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(r);if(t&&t.has(e))return t.get(e);var a={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var i=n?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(a,s,i):a[s]=e[s]}return a.default=e,t&&t.set(e,a),a}(t(35426));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43129:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var a={};t.r(a),t.d(a,{GET:()=>d});var n=t(96559),s=t(48088),o=t(37719),i=t(31183),u=t(32190),l=t(19854),p=t(12909),c=t(6375);async function d(e){try{let r=await (0,l.getServerSession)(p.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view genealogy"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let a=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!a)return u.NextResponse.json({error:"User not found"},{status:404});let n=a.id,s=new URL(e.url),o=s.searchParams.get("maxLevel"),d=o?parseInt(o):10,f=s.searchParams.get("page"),g=f?parseInt(f):1,y=s.searchParams.get("pageSize"),m=y?parseInt(y):10,h="true"===s.searchParams.get("includeStats"),x="true"===s.searchParams.get("includePerformanceMetrics"),P=s.searchParams.get("userId"),w=s.searchParams.get("loadAdditionalLevels"),v=s.searchParams.get("currentLevel"),j=s.searchParams.get("filterRank"),b=s.searchParams.get("filterJoinedAfter"),k=s.searchParams.get("filterJoinedBefore"),O=s.searchParams.get("sortBy"),q=s.searchParams.get("sortDirection"),M="true"===s.searchParams.get("lazyLoad"),_=n;if(P&&(_=parseInt(P)),"true"===w&&v){let e=parseInt(v),r=await (0,c.Z3)(_,e,d);return u.NextResponse.json(r)}let R={includePerformanceMetrics:x,lazyLoadLevels:M};j&&(R.filterRank=parseInt(j)),b&&(R.filterJoinedAfter=new Date(b)),k&&(R.filterJoinedBefore=new Date(k)),O&&(R.sortBy=O),q&&(R.sortDirection=q);let I=await (0,c.yM)(_,d,g,m,R),U={...I.user,children:I.downline,pagination:I.pagination,metadata:I.metadata},E=null;if(h){let[e,r,t,a]=await Promise.all([(0,c.dO)(_,d),i.z.$queryRaw`
          SELECT SUM(walletBalance) as totalBalance
          FROM User
          WHERE uplineId = ${_}
        `,i.z.user.groupBy({by:["rankId"],where:{uplineId:_},_count:{id:!0}}),i.z.rank.findMany({select:{id:!0,name:!0},cacheStrategy:{ttl:36e5}})]),n=Object.values(e).reduce((e,r)=>e+r,0)+1,s=e[1]||0,o=r[0]?.totalBalance||0,u=new Map(a.map(e=>[e.id,e.name])),l=t.map(e=>({rankId:e.rankId,rankName:u.get(e.rankId)||"Unknown",count:e._count.id}));E={totalUsers:n,levelCounts:e,totalDownlineBalance:o,directDownlineCount:s,rankDistribution:l,lastUpdated:new Date}}return u.NextResponse.json({...U,statistics:E})}catch(e){return console.error("Error fetching genealogy:",e),u.NextResponse.json({error:"Failed to fetch genealogy"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/genealogy/route",pathname:"/api/genealogy",filename:"route",bundlePath:"app/api/genealogy/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:m}=f;function h(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4243,580,8044,3112,6719],()=>t(43129));module.exports=a})();
"use strict";(()=>{var e={};e.id=4890,e.ids=[4890],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12269:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{e.exports=require("assert")},19854:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o.default}});var a=r(12269);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(35426));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))})},28354:e=>{e.exports=require("util")},28586:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var n={};r.r(n),r.d(n,{GET:()=>c});var a=r(96559),o=r(48088),s=r(37719),i=r(31183),l=r(32190),u=r(19854),p=r(12909),d=r(6375);async function c(e){try{let t=await (0,u.getServerSession)(p.Nh);if(!t||!t.user)return l.NextResponse.json({error:"You must be logged in to export genealogy data"},{status:401});let r=t.user.email;if(!r)return l.NextResponse.json({error:"User email not found in session"},{status:400});let n=await i.z.user.findUnique({where:{email:r},select:{id:!0}});if(!n)return l.NextResponse.json({error:"User not found"},{status:404});let a=new URL(e.url),o=a.searchParams.get("userId"),s=a.searchParams.get("format")||"csv",c=a.searchParams.get("maxLevel"),f=c?parseInt(c):10,g=n.id;o&&(g=parseInt(o));let x=await (0,d.Op)(g),m=await i.z.user.findMany({where:{id:{in:[...x,g]}},select:{id:!0,name:!0,email:!0,rankId:!0,uplineId:!0,walletBalance:!0,createdAt:!0,rank:{select:{name:!0}},_count:{select:{downline:!0}}},orderBy:{createdAt:"asc"}}),y=new Map;m.forEach(e=>{y.set(e.id,e)});let w=new Map;w.set(g,0);let h=0,j=[g];for(;j.length>0;){let e=[];for(let t of j)for(let r of m.filter(e=>e.uplineId===t))w.set(r.id,h+1),e.push(r.id);if(h++,j=e,h>f)break}if("csv"===s){let e=[];e.push("ID,Name,Email,Rank,Level,Upline ID,Upline Name,Wallet Balance,Downline Count,Joined Date"),m.forEach(t=>{let r=w.get(t.id)||0,n=t.uplineId?y.get(t.uplineId):null;e.push([t.id,`"${t.name.replace(/"/g,'""')}"`,`"${t.email.replace(/"/g,'""')}"`,`"${t.rank.name}"`,r,t.uplineId||"",n?`"${n.name.replace(/"/g,'""')}"`:"",t.walletBalance||0,t._count.downline,t.createdAt?t.createdAt.toISOString():""].join(","))});let t=e.join("\n");return new l.NextResponse(t,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="genealogy_export_${new Date().toISOString().split("T")[0]}.csv"`}})}if("json"!==s)return l.NextResponse.json({error:"Unsupported export format. Supported formats: csv, json"},{status:400});{let e=m.map(e=>{let t=w.get(e.id)||0,r=e.uplineId?y.get(e.uplineId):null;return{id:e.id,name:e.name,email:e.email,rank:e.rank.name,level:t,uplineId:e.uplineId,uplineName:r?r.name:null,walletBalance:e.walletBalance||0,downlineCount:e._count.downline,joinedDate:e.createdAt}});return l.NextResponse.json({exportDate:new Date,rootUserId:g,totalUsers:m.length,maxLevel:f,data:e})}}catch(e){return console.error("Error exporting genealogy data:",e),l.NextResponse.json({error:"Failed to export genealogy data"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/genealogy/export/route",pathname:"/api/genealogy/export",filename:"route",bundlePath:"app/api/genealogy/export/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\export\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:m}=f;function y(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,580,8044,3112,6719],()=>r(28586));module.exports=n})();
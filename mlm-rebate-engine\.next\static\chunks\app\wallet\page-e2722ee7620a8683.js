(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1730],{34732:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(95155),r=s(12115),l=s(12108),i=s(35695),n=s(70357),d=s(29911);function c(){let{data:e,status:t}=(0,l.useSession)(),s=(0,i.useRouter)(),[c,x]=(0,r.useState)({balance:0,transactions:[]}),[o,m]=(0,r.useState)(!0),[p,u]=(0,r.useState)(""),[h,b]=(0,r.useState)(""),[g,y]=(0,r.useState)(!1),[f,w]=(0,r.useState)({type:"",text:""});(0,r.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,r.useEffect)(()=>{"authenticated"===t&&(async()=>{try{let e=await fetch("/api/wallet"),t=await e.json();x(t),m(!1)}catch(e){console.error("Error fetching wallet data:",e),m(!1)}})()},[t]);let j=async t=>{if(t.preventDefault(),!e)return;let s=parseFloat(p);if(isNaN(s)||s<=0)return void w({type:"error",text:"Please enter a valid amount"});if(s>c.balance)return void w({type:"error",text:"Insufficient balance"});y(!0),w({type:"",text:""});try{let e=await fetch("/api/wallet",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:s,type:"withdrawal",description:h||"Withdrawal request"})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to process withdrawal");let a=await fetch("/api/wallet"),r=await a.json();x(r),w({type:"success",text:"Withdrawal request processed successfully"}),u(""),b("")}catch(e){w({type:"error",text:e.message||"An error occurred during withdrawal"})}finally{y(!1)}};return"loading"===t||o?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold mb-6",children:"Wallet"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,a.jsx)(d.lcY,{className:"h-6 w-6"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Wallet Balance"})]}),(0,a.jsxs)("div",{className:"text-3xl font-bold mb-4",children:["₱",c.balance.toFixed(2)]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Withdraw Funds"}),f.text&&(0,a.jsx)("div",{className:"mb-4 p-3 rounded-md text-sm ".concat("success"===f.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:f.text}),(0,a.jsxs)("form",{onSubmit:j,children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 mb-1",children:"Amount"}),(0,a.jsx)("input",{type:"number",id:"amount",value:p,onChange:e=>u(e.target.value),min:"0.01",step:"0.01",max:c.balance,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (Optional)"}),(0,a.jsx)("input",{type:"text",id:"description",value:h,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsx)("button",{type:"submit",disabled:g,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300",children:g?"Processing...":"Withdraw"})]})]})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Transaction History"})}),(0,a.jsx)("div",{className:"p-6",children:c.transactions.length>0?(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.transactions.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("rebate"===e.type?"bg-green-100 text-green-800":"withdrawal"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:["rebate"===e.type?(0,a.jsx)(d.$TP,{className:"mr-1"}):(0,a.jsx)(d.uCC,{className:"mr-1"}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("span",{className:"withdrawal"===e.type?"text-red-600":"text-green-600",children:["withdrawal"===e.type?"-":"+","₱",e.amount.toFixed(2)]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.description}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}):(0,a.jsx)("p",{className:"text-gray-500",children:"No transactions yet."})})]})})]})]})})}},59813:(e,t,s)=>{Promise.resolve().then(s.bind(s,34732))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(59813)),_N_E=e.O()}]);
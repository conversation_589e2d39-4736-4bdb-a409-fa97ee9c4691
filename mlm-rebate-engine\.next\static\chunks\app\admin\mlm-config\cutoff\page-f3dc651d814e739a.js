(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3686],{1856:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(95155),r=s(12115),n=s(12108),c=s(35695),l=s(70357),o=s(29911);function i(){let{data:e,status:t}=(0,n.useSession)(),s=(0,c.useRouter)(),[i,d]=(0,r.useState)(!0),[u,m]=(0,r.useState)(!1),[x,f]=(0,r.useState)([]),[h,p]=(0,r.useState)(null),[g,y]=(0,r.useState)({type:"",text:""}),[b,j]=(0,r.useState)(!1),[N,w]=(0,r.useState)({year:new Date().getFullYear(),month:new Date().getMonth()+1,cutoffDay:25,notes:""});(0,r.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,r.useEffect)(()=>{"authenticated"===t&&(v(),C())},[t]);let v=async()=>{d(!0);try{let e=await fetch("/api/mlm-config/cutoff?action=list");if(!e.ok)throw Error("Failed to fetch cutoffs: ".concat(e.statusText));let t=await e.json();f(t.cutoffs||[])}catch(e){console.error("Error fetching cutoffs:",e),y({type:"error",text:"Failed to load cutoff data. Please try again."})}finally{d(!1)}},C=async()=>{try{let e=await fetch("/api/mlm-config/cutoff?action=current");if(!e.ok)throw Error("Failed to fetch current cutoff: ".concat(e.statusText));let t=await e.json();p(t.cutoff)}catch(e){console.error("Error fetching current cutoff:",e)}},k=async()=>{d(!0),y({type:"",text:""});try{let e=await fetch("/api/mlm-config/cutoff",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"create",...N})});if(!e.ok)throw Error("Failed to create cutoff: ".concat(e.statusText));await e.json(),y({type:"success",text:"Monthly cutoff created successfully."}),j(!1),w({year:new Date().getFullYear(),month:new Date().getMonth()+1,cutoffDay:25,notes:""}),v(),C()}catch(e){console.error("Error creating cutoff:",e),y({type:"error",text:e instanceof Error?e.message:"Failed to create cutoff. Please try again."})}finally{d(!1)}},S=async(e,t)=>{m(!0),y({type:"",text:""});try{let s=await fetch("/api/mlm-config/cutoff",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"process",year:e,month:t})});if(!s.ok)throw Error("Failed to process cutoff: ".concat(s.statusText));let a=await s.json();y({type:"success",text:"Monthly commissions processed successfully. ".concat(a.result.summary.succeeded," users processed, ").concat(a.result.summary.failed," failed.")}),v()}catch(e){console.error("Error processing cutoff:",e),y({type:"error",text:e instanceof Error?e.message:"Failed to process cutoff. Please try again."})}finally{m(!1)}},D=(e,t)=>{w({...N,[e]:t})},A=e=>["January","February","March","April","May","June","July","August","September","October","November","December"][e-1],E=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},F=e=>{switch(e){case"pending":return(0,a.jsx)(o.w_X,{});case"processing":return(0,a.jsx)(o.hW,{className:"animate-spin"});case"completed":return(0,a.jsx)(o.CMH,{});case"failed":return(0,a.jsx)(o.QCr,{});default:return null}};return"loading"===t||i?(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(o.hW,{className:"animate-spin text-green-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Monthly Cutoff Management"}),g.text&&(0,a.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("error"===g.type?"bg-red-100 text-red-700":"success"===g.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:g.text}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden mb-6",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Current/Upcoming Cutoff"})}),(0,a.jsx)("div",{className:"p-6",children:h?(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-xl font-medium",children:[A(h.month)," ",h.year]}),(0,a.jsxs)("p",{className:"text-gray-600 mt-1",children:["Cutoff Day: ",h.cutoffDay]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(E(h.status)),children:[F(h.status),(0,a.jsx)("span",{className:"ml-1",children:h.status.charAt(0).toUpperCase()+h.status.slice(1)})]})})]}),"pending"===h.status&&(0,a.jsx)("button",{type:"button",onClick:()=>S(h.year,h.month),disabled:u,className:"mt-4 md:mt-0 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.hW,{className:"animate-spin inline mr-2"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.gSK,{className:"inline mr-2"}),"Process Now"]})})]}):(0,a.jsx)("div",{className:"text-center py-4 text-gray-500",children:'No current or upcoming cutoff scheduled. Click "Add Cutoff" to create one.'})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Cutoff History"}),(0,a.jsxs)("button",{type:"button",onClick:()=>j(!0),className:"bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:[(0,a.jsx)(o.OiG,{className:"inline mr-1"}),"Add Cutoff"]})]}),(0,a.jsxs)("div",{className:"p-6",children:[b&&(0,a.jsxs)("div",{className:"mb-6 border rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Add New Cutoff"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Year"}),(0,a.jsx)("select",{value:N.year,onChange:e=>D("year",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:Array.from({length:5},(e,t)=>new Date().getFullYear()-1+t).map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Month"}),(0,a.jsx)("select",{value:N.month,onChange:e=>D("month",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:Array.from({length:12},(e,t)=>t+1).map(e=>(0,a.jsx)("option",{value:e,children:A(e)},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cutoff Day"}),(0,a.jsx)("input",{type:"number",min:"1",max:"31",value:N.cutoffDay,onChange:e=>D("cutoffDay",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Notes (optional)"}),(0,a.jsx)("textarea",{value:N.notes,onChange:e=>D("notes",e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",rows:2})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>j(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:k,disabled:i,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.dIn,{className:"inline mr-2"}),"Save Cutoff"]})})]})]}),0===x.length?(0,a.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No cutoff history found."}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Period"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Cutoff Day"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Processed At"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:x.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"font-medium text-gray-900",children:[A(e.month)," ",e.year]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.cutoffDay})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(E(e.status)),children:[F(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.processedAt?new Date(e.processedAt).toLocaleString():"-"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:"pending"===e.status&&(0,a.jsxs)("button",{type:"button",onClick:()=>S(e.year,e.month),disabled:u,className:"text-blue-600 hover:text-blue-900",children:[(0,a.jsx)(o.gSK,{className:"inline"}),(0,a.jsx)("span",{className:"ml-1",children:"Process"})]})})]},e.id))})]})})]})]})]})})}},31249:(e,t,s)=>{Promise.resolve().then(s.bind(s,1856))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(31249)),_N_E=e.O()}]);
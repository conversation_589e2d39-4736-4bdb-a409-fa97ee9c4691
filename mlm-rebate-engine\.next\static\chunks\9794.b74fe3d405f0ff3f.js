"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9794],{99794:(e,l,s)=>{s.r(l),s.d(l,{default:()=>d});var t=s(95155),r=s(12115),i=s(6874),n=s.n(i),a=s(66766),c=s(29911);let d=e=>{let{currentUser:l,downlineMembers:s=[]}=e,[i,d]=(0,r.useState)([l.id]),m=(0,r.useCallback)(e=>{let s={...l,children:[]},t=new Map;for(let l of(t.set(s.id,s),e))t.set(l.id,{...l,children:[]});for(let s of e){let e=t.get(l.id),i=t.get(s.id);if(e&&i){var r;i.position||(i.position=e.children&&e.children.length>0?"right":"left"),null==(r=e.children)||r.push(i)}}return s},[l]),x=(0,r.useMemo)(()=>m(s),[m,s]),o=(0,r.useCallback)(e=>{d(l=>l.includes(e)?l.filter(l=>l!==e):[...l,e])},[]),u=(0,r.useCallback)(e=>{switch(e.toLowerCase()){case"distributor":case"platinum":default:return"bg-gray-100 text-gray-800";case"silver":return"bg-gray-200 text-gray-800";case"gold":return"bg-yellow-100 text-yellow-800";case"diamond":return"bg-blue-100 text-blue-800"}},[]),h=(0,r.useCallback)(function(e){var l;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=i.includes(e.id),n=e.children&&e.children.length>0;return(0,t.jsxs)("div",{className:"ml-".concat(6*s),children:[(0,t.jsxs)("div",{className:"flex items-center p-2 rounded-md ".concat(0===s?"bg-green-50":"hover:bg-gray-50"),children:[n&&(0,t.jsx)("button",{onClick:()=>o(e.id),className:"mr-2 text-gray-500 hover:text-gray-700 focus:outline-none",children:r?(0,t.jsx)(c.Vr3,{size:14}):(0,t.jsx)(c.X6T,{size:14})}),(0,t.jsxs)("div",{className:"flex items-center flex-1",children:[(0,t.jsx)("div",{className:"relative w-8 h-8 rounded-full overflow-hidden bg-gray-200 mr-2",children:e.image?(0,t.jsx)(a.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)(c.x$1,{className:"text-gray-500"})})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900",children:e.name}),e.position&&(0,t.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded ".concat("left"===e.position?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:"left"===e.position?"Left":"Right"})]}),(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded ".concat(u(e.rank)),children:e.rank})})]})]})]}),r&&n&&(0,t.jsx)("div",{className:"ml-6 mt-1 border-l-2 border-gray-200 pl-2",children:null==(l=e.children)?void 0:l.map(e=>h(e,s+1))})]},e.id)},[i,u,o]);return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-b flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,t.jsx)(c.YXz,{className:"mr-2 text-blue-500"})," My Genealogy"]}),(0,t.jsx)(n(),{href:"/genealogy",className:"text-sm text-blue-600 hover:text-blue-800 flex items-center",children:"View Full Tree"})]}),(0,t.jsxs)("div",{className:"p-4",children:[(0,r.useMemo)(()=>h(x),[h,x]),0===s.length&&(0,t.jsxs)("div",{className:"text-center py-6",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 text-blue-500 mb-4",children:(0,t.jsx)(c.NPy,{size:24})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"No Downline Members Yet"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Start building your network by inviting new members"}),(0,t.jsx)(n(),{href:"/referrals",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700",children:"Invite Members"})]})]})]})}}}]);
(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,r,t)=>{"use strict";t.d(r,{CartProvider:()=>a,_:()=>i});var s=t(60687),n=t(43210);let o=(0,n.createContext)(void 0),i=()=>{let e=(0,n.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},a=({children:e})=>{let[r,t]=(0,n.useState)([]);(0,n.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,n.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let i=e=>{t(r=>r.filter(r=>r.id!==e))},a=r.reduce((e,r)=>e+r.quantity,0),l=r.reduce((e,r)=>e+r.price*r.quantity,0),d=r.reduce((e,r)=>e+r.pv*r.quantity,0);return(0,s.jsx)(o.Provider,{value:{items:r,addItem:e=>{t(r=>{let t=r.findIndex(r=>r.id===e.id);if(!(t>=0))return[...r,e];{let s=[...r];return s[t]={...s[t],quantity:s[t].quantity+e.quantity},s}})},removeItem:i,updateQuantity:(e,r)=>{if(r<=0)return void i(e);t(t=>t.map(t=>t.id===e?{...t,quantity:r}:t))},clearCart:()=>{t([])},itemCount:a,subtotal:l,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,r,t)=>{"use strict";t.d(r,{CartProvider:()=>n});var s=t(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},37100:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),n=t(48088),o=t(88170),i=t.n(o),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\login\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41750:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},45851:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});var s=t(60687),n=t(25217),o=t(8693),i=t(43210);function a({children:e}){let[r]=(0,i.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,s.jsx)(o.Ht,{client:r,children:e})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var s=t(60687),n=t(43210);let o=()=>"serviceWorker"in navigator,i=async()=>{if(!o())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),a(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},a=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let r=e.installing;r&&r.addEventListener("statechange",()=>{"installed"===r.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[r,t]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{i();let e=()=>{t(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,s.jsxs)(s.Fragment,{children:[e,r&&(0,s.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,s.jsxs)("div",{className:"mr-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,s.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,s.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,r,t)=>{Promise.resolve().then(t.bind(t,37043)),Promise.resolve().then(t.bind(t,23229)),Promise.resolve().then(t.bind(t,82113)),Promise.resolve().then(t.bind(t,41750))},69488:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),n=t(43210),o=t(82136),i=t(16189),a=t(85814),l=t.n(a),d=t(30474);function c(){let e=(0,i.useRouter)(),[r,t]=(0,n.useState)(""),[a,c]=(0,n.useState)(""),[m,u]=(0,n.useState)(""),[h,x]=(0,n.useState)(!1),[p,v]=(0,n.useState)(!1),[f,g]=(0,n.useState)(!1),[b,w]=(0,n.useState)(!1),[j,y]=(0,n.useState)(!0),[P,C]=(0,n.useState)(!0),N=e=>/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(String(e).toLowerCase()),L=e=>{let r=e.target.value;t(r),r?y(N(r)):y(!0)},k=e=>{let r=e.target.value;c(r),C(r.length>=6)},S=async t=>{t.preventDefault();let s=N(r),n=a.length>=6;if(y(s),C(n),!s||!n)return void u("Please correct the errors in the form");x(!0),u("");try{let t;console.log("Attempting to sign in with:",{email:r,password:a,rememberMe:p}),await new Promise(e=>setTimeout(e,500));try{if(t=await (0,o.signIn)("credentials",{redirect:!1,email:r,password:a,callbackUrl:"/dashboard",remember:p}),console.log("Sign in result:",t),t?.error){console.error("Login error:",t.error);let e="Login failed";e="CredentialsSignin"===t.error?"Invalid email or password":`Login failed: ${t.error}`,u(e),x(!1);return}}catch(e){console.error("Exception during signIn:",e),u(`Login failed: ${e instanceof Error?e.message:"Unknown error"}`),x(!1);return}if(!t?.ok){console.error("Login not OK but no error provided"),u("Login failed for unknown reason"),x(!1);return}p?localStorage.setItem("rememberedEmail",r):localStorage.removeItem("rememberedEmail"),console.log("Login successful, redirecting to dashboard"),e.push("/dashboard")}catch(e){console.error("Exception during login:",e),u(`An error occurred: ${e instanceof Error?e.message:String(e)}`),x(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col md:flex-row",children:[(0,s.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-gradient-to-br from-green-500 to-green-700 text-white p-12 flex-col justify-between relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)("div",{className:"relative w-12 h-12 mr-3",children:(0,s.jsx)(d.default,{src:"/images/20250503.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain invert"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Extreme Life Herbal"})]}),(0,s.jsxs)("div",{className:"mt-16 mb-8",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold mb-6",children:"Welcome Back!"}),(0,s.jsx)("p",{className:"text-xl opacity-90 mb-8",children:"Sign in to access your dashboard and manage your herbal product business."}),(0,s.jsxs)("div",{className:"bg-white/10 p-6 rounded-lg backdrop-blur-sm",children:[(0,s.jsx)("p",{className:"italic text-white/90 mb-4",children:'"Extreme Life Herbal Products changed my life! The business opportunity has provided additional income for my family while promoting health and wellness."'}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-white/30 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Juan Dela Cruz"}),(0,s.jsx)("p",{className:"text-sm opacity-75",children:"Distributor since 2021"})]})]})]})]})]}),(0,s.jsx)("div",{className:"absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -mr-48 -mt-48"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-96 h-96 bg-white/5 rounded-full -ml-48 -mb-48"}),(0,s.jsx)("div",{className:"relative z-10",children:(0,s.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",new Date().getFullYear()," Extreme Life Herbal Products. All rights reserved."]})})]}),(0,s.jsxs)("div",{className:"flex flex-col justify-center md:w-1/2 p-6 sm:p-12 bg-white",children:[(0,s.jsx)("div",{className:"md:hidden flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,s.jsx)(d.default,{src:"/images/20250503.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})}),(0,s.jsx)("h1",{className:"text-xl font-bold text-green-700",children:"Extreme Life Herbal"})]})}),(0,s.jsxs)("div",{className:"max-w-md mx-auto w-full",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Sign In"}),(0,s.jsx)("h3",{className:"text-xl text-green-700 font-medium mb-6",children:"Herbal Product Rewards"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-8",children:["Sign in to your account or"," ",(0,s.jsx)(l(),{href:"/register",className:"font-medium text-green-600 hover:text-green-500 transition-colors",children:"create a new account"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,s.jsxs)("button",{type:"button",className:"flex items-center justify-center py-2.5 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,s.jsxs)("button",{type:"button",className:"flex items-center justify-center py-2.5 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"#1877F2",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Facebook"]})]}),(0,s.jsxs)("div",{className:"relative my-6",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:S,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email-address",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:`appearance-none block w-full px-3 py-2 border ${!j?"border-red-300":f?"border-green-500":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 transition-colors sm:text-sm`,placeholder:"<EMAIL>",value:r,onChange:e=>L(e),onFocus:()=>g(!0),onBlur:()=>g(!1)}),!j&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:"Please enter a valid email address"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)(l(),{href:"/forgot-password",className:"text-sm font-medium text-green-600 hover:text-green-500 transition-colors",children:"Forgot password?"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:`appearance-none block w-full px-3 py-2 border ${!P?"border-red-300":b?"border-green-500":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 transition-colors sm:text-sm`,placeholder:"••••••••",value:a,onChange:e=>k(e),onFocus:()=>w(!0),onBlur:()=>w(!1)}),!P&&a.length>0&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:"Password must be at least 6 characters"})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded transition-colors",checked:p,onChange:e=>v(e.target.checked)}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]})})]}),m&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:m}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:h,className:`w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors ${h?"opacity-70 cursor-not-allowed":""}`,children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Signing in..."]}):"Sign in"})})]}),(0,s.jsxs)("p",{className:"mt-8 text-center text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(l(),{href:"/register",className:"font-medium text-green-600 hover:text-green-500 transition-colors",children:"Register now"})]})]})]})]})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,r,t)=>{Promise.resolve().then(t.bind(t,28253)),Promise.resolve().then(t.bind(t,97695)),Promise.resolve().then(t.bind(t,45851)),Promise.resolve().then(t.bind(t,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},82373:(e,r,t)=>{Promise.resolve().then(t.bind(t,69488))},88453:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h,metadata:()=>u});var s=t(37413),n=t(22376),o=t.n(n),i=t(68726),a=t.n(i);t(61135);var l=t(23229),d=t(37043),c=t(82113),m=t(41750);let u={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function h({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,children:(0,s.jsx)(l.AuthProvider,{children:(0,s.jsx)(c.default,{children:(0,s.jsx)(d.CartProvider,{children:(0,s.jsx)(m.default,{children:e})})})})})})}},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\login\\page.tsx","default")},96111:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},97695:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>o});var s=t(60687),n=t(82136);function o({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,8414,9567,474],()=>t(37100));module.exports=s})();
(()=>{var e={};e.id=5199,e.ids=[5199],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>u,Nh:()=>c,aP:()=>l});var a=r(96330),s=r(13581),n=r(85663),o=r(55511),i=r.n(o);async function u(e){return await n.Ay.hash(e,10)}function l(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new a.PrismaClient;let c={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,s.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new a.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let s=await n.Ay.compare(e.password,r.password);if(console.log("Password valid:",s),!s)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:o,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var s=r(12269);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=s?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(a,n,i):a[n]=e[n]}return a.default=e,r&&r.set(e,a),a}(r(35426));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74913:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>d});var s=r(96559),n=r(48088),o=r(37719),i=r(31183),u=r(32190),l=r(19854),c=r(12909);async function d(e){try{let t=await (0,l.getServerSession)(c.Nh);if(!t||!t.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let r=t.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let a=await i.z.user.findUnique({where:{email:r},select:{id:!0,rankId:!0}});if(!a)return u.NextResponse.json({error:"Authenticated user not found"},{status:404});if(6!==a.rankId)return u.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let s=e.nextUrl.searchParams,n=new Date(s.get("startDate")?s.get("startDate"):new Date().setDate(new Date().getDate()-30)),o=s.get("endDate")?new Date(s.get("endDate")):new Date;o.setHours(23,59,59,999);let d=await i.z.purchase.groupBy({by:["productId"],where:{createdAt:{gte:n,lte:o}},_sum:{totalAmount:!0}}),m=(await i.z.product.findMany({where:{id:{in:d.map(e=>e.productId)}},select:{id:!0,name:!0}})).reduce((e,t)=>(e[t.id]=t.name,e),{}),w={labels:d.map(e=>m[e.productId]||`Product ${e.productId}`),data:d.map(e=>e._sum.totalAmount||0)},g=await p(n,o),f=await i.z.rebate.groupBy({by:["level"],where:{createdAt:{gte:n,lte:o}},_sum:{amount:!0}}),h={labels:f.map(e=>`Level ${e.level}`),data:f.map(e=>e._sum.amount||0)},y=await i.z.user.groupBy({by:["rankId"],_count:{id:!0}}),b=(await i.z.rank.findMany({select:{id:!0,name:!0}})).reduce((e,t)=>(e[t.id]=t.name,e),{}),x={labels:y.map(e=>b[e.rankId]||`Rank ${e.rankId}`),data:y.map(e=>e._count.id)},k=(await i.z.user.findMany({select:{id:!0,name:!0,email:!0,rankId:!0,rank:{select:{name:!0}},_sum:{walletBalance:!0}},orderBy:{walletBalance:"desc"},take:5})).map(e=>({id:e.id,name:e.name,email:e.email,totalRebates:e._sum.walletBalance||0,rank:e.rank.name})),v=(await i.z.user.findMany({select:{id:!0,name:!0,email:!0,rankId:!0,rank:{select:{name:!0}},_count:{downline:!0}},orderBy:{downline:{_count:"desc"}},take:5})).map(e=>({id:e.id,name:e.name,email:e.email,directDownlineCount:e._count.downline,rank:e.rank.name})),_=await i.z.user.count(),j=await i.z.product.count(),A=await i.z.purchase.aggregate({where:{createdAt:{gte:n,lte:o}},_sum:{totalAmount:!0},_count:{id:!0}}),P=A._sum.totalAmount||0,O=A._count.id||0,q=(await i.z.rebate.aggregate({where:{createdAt:{gte:n,lte:o}},_sum:{amount:!0}}))._sum.amount||0;return u.NextResponse.json({salesByProduct:w,salesByDate:g,rebatesByLevel:h,usersByRank:x,topEarners:k,topRecruiters:v,summary:{totalUsers:_,totalProducts:j,totalSales:P,totalRebates:q,averageOrderValue:O>0?P/O:0,conversionRate:.68}})}catch(e){return console.error("Error generating reports:",e),u.NextResponse.json({error:"Failed to generate reports"},{status:500})}}async function p(e,t){let r=[],a=[],s=new Date(e);for(;s<=t;){let e=s.getFullYear(),t=s.getMonth();r.push({year:e,month:t}),a.push(new Date(e,t,1).toLocaleDateString("en-US",{month:"short",year:"numeric"})),s.setMonth(s.getMonth()+1)}let n=[];for(let{year:e,month:t}of r){let r=new Date(e,t,1),a=new Date(e,t+1,0,23,59,59,999),s=await i.z.purchase.aggregate({where:{createdAt:{gte:r,lte:a}},_sum:{totalAmount:!0}});n.push(s._sum.totalAmount||0)}return{labels:a,data:n}}let m=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/reports/route",pathname:"/api/admin/reports",filename:"route",bundlePath:"app/api/admin/reports/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\reports\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:f}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8044,3112],()=>r(74913));module.exports=a})();
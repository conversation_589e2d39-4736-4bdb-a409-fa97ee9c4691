(()=>{var e={};e.id=1763,e.ids=[1763,7072],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>c,aP:()=>l});var s=t(96330),n=t(13581),a=t(85663),o=t(55511),i=t.n(o);async function u(e){return await a.Ay.hash(e,10)}function l(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await a.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:o,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var i=n?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(s,a,i):s[a]=e[a]}return s.default=e,t&&t.set(e,s),s}(t(35426));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})},24525:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>b,serverHooks:()=>v,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>y,GET:()=>h,POST:()=>w,PUT:()=>g});var n=t(96559),a=t(48088),o=t(37719),i=t(31183),u=t(32190),l=t(19854),c=t(12909),d=t(27072),p=t(70762);let f=p.z.object({productId:p.z.number().int().positive(),title:p.z.string().optional(),description:p.z.string().optional(),customImage:p.z.string().optional(),expiresAt:p.z.string().optional().transform(e=>e?new Date(e):void 0)}),m=p.z.object({id:p.z.number().int().positive(),title:p.z.string().optional(),description:p.z.string().optional(),customImage:p.z.string().optional(),isActive:p.z.boolean().optional(),expiresAt:p.z.string().optional().nullable().transform(e=>e?new Date(e):null)});async function h(e){try{let r=await (0,l.getServerSession)(c.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view shareable links"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let n=new URL(e.url),a=n.searchParams.get("productId"),o=n.searchParams.get("type"),p=n.searchParams.get("isActive"),f=n.searchParams.get("limit"),m=n.searchParams.get("offset"),h="true"===n.searchParams.get("includeStats"),w={};a&&(w.productId=parseInt(a)),o&&(w.type=o),null!==p&&(w.isActive="true"===p),f&&(w.limit=parseInt(f)),m&&(w.offset=parseInt(m));let{links:g,total:y}=await (0,d.Yx)(s.id,w),b=null;return h&&(b=await (0,d.qY)(s.id)),u.NextResponse.json({links:g,pagination:{total:y,limit:w.limit,offset:w.offset||0},stats:b})}catch(e){return console.error("Error fetching shareable links:",e),u.NextResponse.json({error:"Failed to fetch shareable links"},{status:500})}}async function w(e){try{let r=await (0,l.getServerSession)(c.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to create a shareable link"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let n=await e.json(),a=f.safeParse(n);if(!a.success)return u.NextResponse.json({error:a.error.errors},{status:400});let{productId:o,title:p,description:m,customImage:h,expiresAt:w}=a.data;if(!await i.z.product.findUnique({where:{id:o}}))return u.NextResponse.json({error:"Product not found"},{status:404});let g=await (0,d.Z3)(s.id,o,{title:p,description:m,customImage:h,expiresAt:w});return u.NextResponse.json({link:g,message:"Shareable link created successfully"})}catch(e){return console.error("Error creating shareable link:",e),u.NextResponse.json({error:"Failed to create shareable link"},{status:500})}}async function g(e){try{let r=await (0,l.getServerSession)(c.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to update a shareable link"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let n=await e.json(),a=m.safeParse(n);if(!a.success)return u.NextResponse.json({error:a.error.errors},{status:400});let{id:o,title:p,description:f,customImage:h,isActive:w,expiresAt:g}=a.data,y=await i.z.shareableLink.findUnique({where:{id:o}});if(!y)return u.NextResponse.json({error:"Shareable link not found"},{status:404});if(y.userId!==s.id)return u.NextResponse.json({error:"You do not have permission to update this link"},{status:403});let b=await (0,d.p1)(o,{title:p,description:f,customImage:h,isActive:w,expiresAt:g});return u.NextResponse.json({link:b,message:"Shareable link updated successfully"})}catch(e){return console.error("Error updating shareable link:",e),u.NextResponse.json({error:"Failed to update shareable link"},{status:500})}}async function y(e){try{let r=await (0,l.getServerSession)(c.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to delete a shareable link"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let n=new URL(e.url).searchParams.get("id");if(!n)return u.NextResponse.json({error:"Link ID is required"},{status:400});let a=parseInt(n),o=await i.z.shareableLink.findUnique({where:{id:a}});if(!o)return u.NextResponse.json({error:"Shareable link not found"},{status:404});if(o.userId!==s.id)return u.NextResponse.json({error:"You do not have permission to delete this link"},{status:403});return await (0,d.nl)(a),u.NextResponse.json({message:"Shareable link deleted successfully"})}catch(e){return console.error("Error deleting shareable link:",e),u.NextResponse.json({error:"Failed to delete shareable link"},{status:500})}}let b=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/shareable-links/route",pathname:"/api/shareable-links",filename:"route",bundlePath:"app/api/shareable-links/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shareable-links\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:k,workUnitAsyncStorage:x,serverHooks:v}=b;function j(){return(0,o.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:x})}},27072:(e,r,t)=>{"use strict";let s,n;t.d(r,{Z3:()=>c,nl:()=>m,getShareableLinkByCode:()=>d,F2:()=>y,qY:()=>b,Yx:()=>p,GI:()=>h,recordReferralPurchase:()=>g,p1:()=>f});var a=t(31183),o=t(55511);let i=e=>{!s||s.length<e?(s=Buffer.allocUnsafe(128*e),o.randomFillSync(s),n=0):n+e>s.length&&(o.randomFillSync(s),n=0),n+=e},u=(e=21)=>{i(e|=0);let r="";for(let t=n-e;t<n;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&s[t]];return r};async function l(){let e=u(8),r=!1;for(;!r;)await a.z.shareableLink.findUnique({where:{code:e}})?e=u(8):r=!0;return e}async function c(e,r,t){let s=await l();return await a.z.shareableLink.create({data:{userId:e,productId:r,code:s,type:"product",title:t?.title,description:t?.description,customImage:t?.customImage,expiresAt:t?.expiresAt,isActive:!0}})}async function d(e){return await a.z.shareableLink.findUnique({where:{code:e}})}async function p(e,r){let t={userId:e};r?.productId!==void 0&&(t.productId=r.productId),r?.type!==void 0&&(t.type=r.type),r?.isActive!==void 0&&(t.isActive=r.isActive);let s=await a.z.shareableLink.count({where:t});return{links:await a.z.shareableLink.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{product:{select:{id:!0,name:!0,price:!0,image:!0,referralCommissionType:!0,referralCommissionValue:!0}}}}),total:s}}async function f(e,r){return await a.z.shareableLink.update({where:{id:e},data:{...r,updatedAt:new Date}})}async function m(e){return await a.z.shareableLink.delete({where:{id:e}})}async function h(e,r){return await a.z.shareableLink.update({where:{id:e},data:{clickCount:{increment:1},updatedAt:new Date}}),await a.z.linkClick.create({data:{linkId:e,ipAddress:r?.ipAddress,userAgent:r?.userAgent,referrer:r?.referrer,utmSource:r?.utmSource,utmMedium:r?.utmMedium,utmCampaign:r?.utmCampaign}})}async function w(e,r){let t=await a.z.product.findUnique({where:{id:e},select:{referralCommissionType:!0,referralCommissionValue:!0}});return t&&t.referralCommissionType&&t.referralCommissionValue?"percentage"===t.referralCommissionType?{amount:r*(t.referralCommissionValue/100),percentage:t.referralCommissionValue}:{amount:t.referralCommissionValue,percentage:t.referralCommissionValue/r*100}:{amount:.05*r,percentage:5}}async function g(e,r){let t=await a.z.purchase.findUnique({where:{id:e},include:{product:!0,user:!0,referralLink:{include:{user:!0}}}});if(!t)throw Error(`Purchase with ID ${e} not found`);if(!t.referralLink)throw Error(`Purchase with ID ${e} has no referral link`);let{amount:s,percentage:n}=await w(t.productId,t.totalAmount),o=await a.z.referralCommission.create({data:{purchaseId:e,linkId:r,referrerId:t.referralLink.userId,buyerId:t.userId,productId:t.productId,amount:s,percentage:n,status:"pending"}});return await a.z.shareableLink.update({where:{id:r},data:{conversionCount:{increment:1},totalRevenue:{increment:t.totalAmount},totalCommission:{increment:s},updatedAt:new Date}}),o}async function y(e,r){let t={referrerId:e};r?.status!==void 0&&(t.status=r.status);let s=await a.z.referralCommission.count({where:t});return{commissions:await a.z.referralCommission.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{purchase:{select:{id:!0,quantity:!0,totalAmount:!0,createdAt:!0}},buyer:{select:{id:!0,name:!0,email:!0}},product:{select:{id:!0,name:!0,price:!0,image:!0}},link:{select:{id:!0,code:!0,type:!0}}}}),total:s}}async function b(e){let r=await a.z.shareableLink.aggregate({where:{userId:e},_sum:{clickCount:!0,conversionCount:!0,totalRevenue:!0,totalCommission:!0},_count:{id:!0}}),t=r._sum.clickCount||0,s=r._sum.conversionCount||0;return{totalLinks:r._count.id,totalClicks:t,totalConversions:s,totalRevenue:r._sum.totalRevenue||0,totalCommission:r._sum.totalCommission||0,conversionRate:t>0?s/t*100:0}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,8381],()=>t(24525));module.exports=s})();
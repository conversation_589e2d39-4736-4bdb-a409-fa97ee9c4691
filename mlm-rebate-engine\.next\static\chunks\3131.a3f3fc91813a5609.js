"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3131],{35512:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var s=a(95155),l=a(12115),r=a(74211),n=a(93306),o=a(77581),i=a(57916);a(11687);var d=a(29911),c=a(22406),u=a(10396);function h(e){let{options:t,onChange:a,onReset:r}=e,[n,o]=(0,l.useState)(!1),[i,c]=(0,l.useState)("layout"),u=(e,s)=>{a({...t,[e]:s})};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,s.jsxs)("button",{onClick:()=>o(!n),className:"w-full px-4 py-3 bg-gray-50 flex items-center justify-between border-b",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.Pcn,{className:"text-blue-500 mr-2"}),(0,s.jsx)("h3",{className:"font-medium",children:"Visualization Settings"})]}),n?(0,s.jsx)(d.Ucs,{}):(0,s.jsx)(d.Vr3,{})]}),n&&(0,s.jsxs)("div",{className:"p-4 animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex border-b mb-4",children:[(0,s.jsxs)("button",{onClick:()=>c("layout"),className:"px-4 py-2 ".concat("layout"===i?"border-b-2 border-blue-500 text-blue-600":"text-gray-500 hover:text-gray-700"),children:[(0,s.jsx)(d.KAn,{className:"inline mr-1"})," Layout"]}),(0,s.jsxs)("button",{onClick:()=>c("appearance"),className:"px-4 py-2 ".concat("appearance"===i?"border-b-2 border-blue-500 text-blue-600":"text-gray-500 hover:text-gray-700"),children:[(0,s.jsx)(d.lV_,{className:"inline mr-1"})," Appearance"]}),(0,s.jsxs)("button",{onClick:()=>c("behavior"),className:"px-4 py-2 ".concat("behavior"===i?"border-b-2 border-blue-500 text-blue-600":"text-gray-500 hover:text-gray-700"),children:[(0,s.jsx)(d.bXz,{className:"inline mr-1"})," Behavior"]})]}),"layout"===i&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Layout Type"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsxs)("button",{onClick:()=>u("layout","vertical"),className:"p-2 border rounded-md flex flex-col items-center ".concat("vertical"===t.layout?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,s.jsxs)("div",{className:"w-12 h-12 flex flex-col justify-center items-center mb-1",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full mb-1"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full"}),(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full"})]})]}),(0,s.jsx)("span",{className:"text-xs",children:"Vertical"}),"vertical"===t.layout&&(0,s.jsx)(d.CMH,{className:"absolute top-1 right-1 text-blue-500",size:12})]}),(0,s.jsxs)("button",{onClick:()=>u("layout","horizontal"),className:"p-2 border rounded-md flex flex-col items-center ".concat("horizontal"===t.layout?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,s.jsx)("div",{className:"w-12 h-12 flex justify-center items-center mb-1",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full mr-1"}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full"}),(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full"})]})]})}),(0,s.jsx)("span",{className:"text-xs",children:"Horizontal"}),"horizontal"===t.layout&&(0,s.jsx)(d.CMH,{className:"absolute top-1 right-1 text-blue-500",size:12})]}),(0,s.jsxs)("button",{onClick:()=>u("layout","radial"),className:"p-2 border rounded-md flex flex-col items-center ".concat("radial"===t.layout?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,s.jsx)("div",{className:"w-12 h-12 flex justify-center items-center mb-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"}),(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full absolute top-0 left-1/2 transform -translate-x-1/2"}),(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full absolute bottom-0 right-0"}),(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-400 rounded-full absolute bottom-0 left-0"})]})}),(0,s.jsx)("span",{className:"text-xs",children:"Radial"}),"radial"===t.layout&&(0,s.jsx)(d.CMH,{className:"absolute top-1 right-1 text-blue-500",size:12})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Node Spacing: ",t.nodeSpacing,"px"]}),(0,s.jsx)("input",{type:"range",min:"20",max:"100",step:"5",value:t.nodeSpacing,onChange:e=>u("nodeSpacing",parseInt(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Level Spacing: ",t.levelSpacing,"px"]}),(0,s.jsx)("input",{type:"range",min:"80",max:"300",step:"10",value:t.levelSpacing,onChange:e=>u("levelSpacing",parseInt(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Node Width: ",t.nodeWidth,"px"]}),(0,s.jsx)("input",{type:"range",min:"150",max:"300",step:"10",value:t.nodeWidth,onChange:e=>u("nodeWidth",parseInt(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Node Height: ",t.nodeHeight,"px"]}),(0,s.jsx)("input",{type:"range",min:"100",max:"250",step:"10",value:t.nodeHeight,onChange:e=>u("nodeHeight",parseInt(e.target.value)),className:"w-full"})]})]})]}),"appearance"===i&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Theme"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsxs)("button",{onClick:()=>u("theme","light"),className:"p-2 border rounded-md flex flex-col items-center ".concat("light"===t.theme?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-white border border-gray-300 rounded-md mb-1 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-6 h-6 bg-blue-100 border border-blue-200 rounded"})}),(0,s.jsx)("span",{className:"text-xs",children:"Light"}),"light"===t.theme&&(0,s.jsx)(d.CMH,{className:"absolute top-1 right-1 text-blue-500",size:12})]}),(0,s.jsxs)("button",{onClick:()=>u("theme","dark"),className:"p-2 border rounded-md flex flex-col items-center ".concat("dark"===t.theme?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-800 border border-gray-700 rounded-md mb-1 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-6 h-6 bg-blue-900 border border-blue-800 rounded"})}),(0,s.jsx)("span",{className:"text-xs",children:"Dark"}),"dark"===t.theme&&(0,s.jsx)(d.CMH,{className:"absolute top-1 right-1 text-blue-500",size:12})]}),(0,s.jsxs)("button",{onClick:()=>u("theme","colorful"),className:"p-2 border rounded-md flex flex-col items-center ".concat("colorful"===t.theme?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 border border-gray-300 rounded-md mb-1 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-200 to-blue-200 border border-blue-200 rounded"})}),(0,s.jsx)("span",{className:"text-xs",children:"Colorful"}),"colorful"===t.theme&&(0,s.jsx)(d.CMH,{className:"absolute top-1 right-1 text-blue-500",size:12})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Node Border Radius: ",t.nodeBorderRadius,"px"]}),(0,s.jsx)("input",{type:"range",min:"0",max:"16",step:"1",value:t.nodeBorderRadius,onChange:e=>u("nodeBorderRadius",parseInt(e.target.value)),className:"w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Connection Type"}),(0,s.jsxs)("select",{value:t.connectionType,onChange:e=>u("connectionType",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"straight",children:"Straight"}),(0,s.jsx)("option",{value:"step",children:"Step"}),(0,s.jsx)("option",{value:"smoothstep",children:"Smooth Step"}),(0,s.jsx)("option",{value:"bezier",children:"Bezier"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Connection Style"}),(0,s.jsxs)("select",{value:t.connectionStyle,onChange:e=>u("connectionStyle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"solid",children:"Solid"}),(0,s.jsx)("option",{value:"dashed",children:"Dashed"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Connection Width: ",t.connectionWidth,"px"]}),(0,s.jsx)("input",{type:"range",min:"0.5",max:"3",step:"0.5",value:t.connectionWidth,onChange:e=>u("connectionWidth",parseFloat(e.target.value)),className:"w-full"})]})]})]}),"behavior"===i&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"showPerformanceMetrics",checked:t.showPerformanceMetrics,onChange:e=>u("showPerformanceMetrics",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"showPerformanceMetrics",className:"ml-2 block text-sm text-gray-700",children:"Show Performance Metrics in Nodes"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"animateChanges",checked:t.animateChanges,onChange:e=>u("animateChanges",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"animateChanges",className:"ml-2 block text-sm text-gray-700",children:"Animate Changes"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"showMinimap",checked:t.showMinimap,onChange:e=>u("showMinimap",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"showMinimap",className:"ml-2 block text-sm text-gray-700",children:"Show Minimap"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"showControls",checked:t.showControls,onChange:e=>u("showControls",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"showControls",className:"ml-2 block text-sm text-gray-700",children:"Show Controls"})]})]}),(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{onClick:r,className:"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:"Reset to Defaults"})})]})]})}let m={userNode:c.A},x={layout:"vertical",nodeSpacing:40,levelSpacing:150,theme:"light",showPerformanceMetrics:!0,animateChanges:!0,showMinimap:!0,showControls:!0,nodeBorderRadius:8,connectionType:"smoothstep",connectionStyle:"solid",connectionWidth:1.5,nodeWidth:200,nodeHeight:150};function b(e){let{userId:t,maxLevel:a=3,initialLayout:c="vertical",allowEditing:b=!1,onSaveChanges:g}=e,[p,f,y]=(0,r.ck)([]),[j,v,N]=(0,r.fM)([]),[w,k]=(0,l.useState)(!0),[C,S]=(0,l.useState)(null),[M,E]=(0,l.useState)(null),[z,W]=(0,l.useState)(new Set),[D,A]=(0,l.useState)({...x,layout:c}),[H,P]=(0,l.useState)(!1),[B,I]=(0,l.useState)(new Set),[T,R]=(0,l.useState)({}),[L,V]=(0,l.useState)(!1),_=(0,r.VH)(),F=(0,l.useRef)(null),G=(0,l.useCallback)(async()=>{k(!0),S(null);try{let e=new URLSearchParams({maxLevel:a.toString(),userId:t.toString(),includePerformanceMetrics:"true"}),s=await fetch("/api/genealogy?".concat(e.toString()));if(!s.ok)throw Error("Failed to fetch genealogy data");let l=await s.json(),{nodes:r,edges:n}=Z(l);V(!0),f(r),v(n),setTimeout(()=>{_.fitView({padding:.2}),V(!1)},300)}catch(e){S(e instanceof Error?e.message:"An unknown error occurred"),V(!1)}finally{k(!1)}},[t,a,f,v,_]);(0,l.useEffect)(()=>{G()},[G]);let Z=(0,l.useCallback)(e=>{let t=[],a=[],s={id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:0,downlineCount:e._count.downline,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics};return t.push({id:s.id.toString(),type:"userNode",position:{x:0,y:0},data:{user:s,onExpand:()=>Q(s.id.toString()),onSelect:()=>E(s),onEdit:b?ee:void 0,onDelete:b?et:void 0,onAdd:b?ea:void 0,isExpanded:!0,hasChildren:e.children&&e.children.length>0,visualOptions:D},draggable:!1}),e.children&&e.children.length>0&&U(e.children,s.id.toString(),0,0,1,t,a),{nodes:t,edges:a}},[D,b]),U=(0,l.useCallback)((e,t,a,s,l,r,n)=>{let o,i,d,c,u=D.nodeWidth,h=D.nodeSpacing,m=D.levelSpacing,x=D.levelSpacing,b=e.length*u+(e.length-1)*h;if("vertical"===D.layout)o=a-b/2+u/2,c=s+m,e.forEach((e,a)=>{q(e,t,d=o+a*(u+h),c,l,r,n)});else if("horizontal"===D.layout)d=a+x,i=s-b/2+u/2,e.forEach((e,a)=>{q(e,t,d,c=i+a*(u+h),l,r,n)});else if("radial"===D.layout){let o=l*x,i=2*Math.PI/e.length;e.forEach((e,u)=>{let h=u*i;q(e,t,d=a+o*Math.cos(h),c=s+o*Math.sin(h),l,r,n)})}},[D]),q=(0,l.useCallback)((e,t,a,s,l,n,o)=>{let i={id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:l,downlineCount:e._count.downline,children:e.children,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics},d=i.id.toString();n.push({id:d,type:"userNode",position:{x:a,y:s},data:{user:i,onExpand:()=>Q(d),onSelect:()=>E(i),onEdit:b?ee:void 0,onDelete:b?et:void 0,onAdd:b?ea:void 0,isExpanded:z.has(d),hasChildren:e.children&&e.children.length>0,visualOptions:D,isDragging:B.has(d)},draggable:b}),o.push({id:"e-".concat(t,"-").concat(d),source:t,target:d,type:K(),animated:!1,style:{stroke:"#888",strokeWidth:D.connectionWidth,strokeDasharray:"dashed"===D.connectionStyle?"5,5":void 0},markerEnd:{type:r.TG.ArrowClosed,width:15,height:15,color:"#888"}}),e.children&&e.children.length>0&&z.has(d)&&U(e.children,d,a,s,l+1,n,o)},[z,D,B,b]),K=(0,l.useCallback)(()=>{switch(D.connectionType){case"straight":return"default";case"step":return"step";case"smoothstep":default:return"smoothstep";case"bezier":return"bezier"}},[D]),Q=(0,l.useCallback)(e=>{W(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})},[]),X=(0,l.useCallback)((e,t)=>{E(t.data.user)},[]),Y=(0,l.useCallback)((e,t)=>{b&&I(e=>{let a=new Set(e);return a.add(t.id),a})},[b]),J=(0,l.useCallback)((e,t)=>{if(!b)return;I(e=>{let a=new Set(e);return a.delete(t.id),a});let{x:a,y:s}=t.position,l=D.nodeWidth,n=D.nodeHeight,o={x:a+l/2,y:s+n/2},i=p.filter(e=>e.id!==t.id&&!O(e.id,t.id)),d=null,c=1/0;if(i.forEach(e=>{let t={x:e.position.x+l/2,y:e.position.y+n/2},a=Math.sqrt(Math.pow(o.x-t.x,2)+Math.pow(o.y-t.y,2));a<c&&(c=a,d=e)}),d&&c<l){let e=j.find(e=>e.target===t.id),a=null==e?void 0:e.source;if(d.id!==a){if(R(e=>({...e,[t.id]:{oldParentId:a,newParentId:d.id}})),e){v(t=>t.filter(t=>t.id!==e.id));let a={id:"e-".concat(d.id,"-").concat(t.id),source:d.id,target:t.id,type:K(),animated:!1,style:{stroke:"#888",strokeWidth:D.connectionWidth,strokeDasharray:"dashed"===D.connectionStyle?"5,5":void 0},markerEnd:{type:r.TG.ArrowClosed,width:15,height:15,color:"#888"}};v(e=>[...e,a])}P(!0)}}},[b,p,j,D,v,K]),O=(0,l.useCallback)((e,t)=>{for(let a of j.filter(e=>e.source===t))if(a.target===e||O(e,a.target))return!0;return!1},[j]),$=(0,l.useCallback)(e=>{y(e),e.filter(e=>"position"===e.type&&e.position).length>0&&P(!0)},[y]),ee=(0,l.useCallback)(e=>{console.log("Edit user:",e),alert("Edit user functionality would be implemented here for user ID: ".concat(e))},[]),et=(0,l.useCallback)(e=>{console.log("Delete user:",e),alert("Delete user functionality would be implemented here for user ID: ".concat(e))},[]),ea=(0,l.useCallback)(e=>{console.log("Add user under parent:",e),alert("Add user functionality would be implemented here under parent ID: ".concat(e))},[]),es=(0,l.useCallback)(()=>{g&&g({nodeChanges:T,nodePositions:p.reduce((e,t)=>(e[t.id]=t.position,e),{})}),P(!1),R({})},[T,p,g]),el=(0,l.useCallback)(()=>{G(),P(!1),R({})},[G]),er=(0,l.useCallback)(e=>{A(e)},[]),en=(0,l.useCallback)(()=>{A({...x,layout:c})},[c]);return w&&0===p.length?(0,s.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,s.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("span",{children:"Loading genealogy data..."})]}):C?(0,s.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,s.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,s.jsx)(d.BS8,{className:"mr-2"}),"Error loading genealogy data"]}),(0,s.jsx)("p",{className:"text-red-600",children:C})]}):(0,s.jsxs)("div",{className:"flex flex-col border border-gray-200 rounded-md",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)(h,{options:D,onChange:er,onReset:en})}),(0,s.jsxs)("div",{className:"h-[600px] relative",ref:F,children:[L&&(0,s.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10",children:(0,s.jsx)(d.hW,{className:"animate-spin text-blue-500 text-2xl"})}),(0,s.jsxs)(r.Gc,{nodes:p,edges:j,onNodesChange:$,onEdgesChange:N,onNodeClick:X,onNodeDragStart:Y,onNodeDragStop:J,nodeTypes:m,fitView:!0,attributionPosition:"bottom-right",connectionLineType:K(),minZoom:.1,maxZoom:1.5,defaultViewport:{x:0,y:0,zoom:.8},children:[D.showControls&&(0,s.jsx)(n.H,{}),D.showMinimap&&(0,s.jsx)(o.o,{nodeColor:e=>{var t;let a=null==(t=e.data)?void 0:t.user;if(!a)return"#eee";if(0===a.level)return"#93c5fd";switch(a.rankName){case"Starter":return"#f3f4f6";case"Bronze":return"#fef3c7";case"Silver":return"#e5e7eb";case"Gold":return"#fef08a";case"Platinum":return"#dbeafe";case"Diamond":return"#f3e8ff";default:return"#eee"}},maskColor:"#ffffff50"}),(0,s.jsx)(i.V,{}),M&&(0,s.jsx)(r.Zk,{position:"top-right",className:"p-0 w-80",children:(0,s.jsx)(u.A,{user:M,onClose:()=>E(null),className:"max-h-[80vh]"})}),H&&b&&(0,s.jsx)(r.Zk,{position:"bottom-center",className:"p-0",children:(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-300 p-3 rounded-md shadow-md flex items-center",children:[(0,s.jsx)(d.__w,{className:"text-yellow-500 mr-2"}),(0,s.jsx)("span",{className:"text-yellow-700",children:"You have unsaved changes to the genealogy structure."}),(0,s.jsxs)("div",{className:"ml-4 flex space-x-2",children:[(0,s.jsxs)("button",{onClick:es,className:"px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center",children:[(0,s.jsx)(d.dIn,{className:"mr-1"})," Save"]}),(0,s.jsxs)("button",{onClick:el,className:"px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center",children:[(0,s.jsx)(d.QCr,{className:"mr-1"})," Discard"]})]})]})})]})]})]})}function g(e){return(0,s.jsx)(r.Ln,{children:(0,s.jsx)(b,{...e})})}}}]);
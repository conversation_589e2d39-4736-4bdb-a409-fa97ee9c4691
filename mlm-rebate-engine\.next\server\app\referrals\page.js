(()=>{var e={};e.id=5663,e.ids=[5663],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36497:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\referrals\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69211:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),i=s(43210),a=s(82136),n=s(16189),l=s(68367),d=s(23877),c=s(30474),o=s(85814),x=s.n(o);function m(){let{data:e,status:t}=(0,a.useSession)();(0,n.useRouter)();let[s,o]=(0,i.useState)(!0),[m,p]=(0,i.useState)([]),[h,u]=(0,i.useState)(null),[g,j]=(0,i.useState)({type:"",text:""}),[v,f]=(0,i.useState)(null),y=async(e,t)=>{try{let s=await fetch("/api/shareable-links",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:e,isActive:t})});if(!s.ok)throw Error(`Failed to update link: ${s.statusText}`);p(m.map(s=>s.id===e?{...s,isActive:t}:s)),j({type:"success",text:`Link ${t?"activated":"deactivated"} successfully`})}catch(e){console.error("Error updating link:",e),j({type:"error",text:"Failed to update link. Please try again."})}},b=async e=>{if(confirm("Are you sure you want to delete this link? This action cannot be undone."))try{let t=await fetch(`/api/shareable-links?id=${e}`,{method:"DELETE"});if(!t.ok)throw Error(`Failed to delete link: ${t.statusText}`);p(m.filter(t=>t.id!==e)),j({type:"success",text:"Link deleted successfully"})}catch(e){console.error("Error deleting link:",e),j({type:"error",text:"Failed to delete link. Please try again."})}},N=async(e,t)=>{try{let s=window.location.origin,r=`${s}/s/${t}`;await navigator.clipboard.writeText(r),f(e),setTimeout(()=>{f(null)},2e3)}catch(e){console.error("Error copying to clipboard:",e),j({type:"error",text:"Failed to copy link to clipboard"})}},w=e=>new Date(e).toLocaleDateString(),k=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return"loading"===t||s?(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,r.jsx)(d.hW,{className:"animate-spin text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"My Referrals"}),g.text&&(0,r.jsx)("div",{className:`mb-6 p-4 rounded-md ${"error"===g.type?"bg-red-100 text-red-700":"success"===g.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:g.text}),h&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,r.jsx)(d.AnD,{className:"text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Links"}),(0,r.jsx)("div",{className:"text-xl font-bold",children:h.totalLinks})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-green-100 p-3 rounded-full mr-4",children:(0,r.jsx)(d.YYR,{className:"text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Clicks / Conversions"}),(0,r.jsxs)("div",{className:"text-xl font-bold",children:[h.totalClicks," / ",h.totalConversions,(0,r.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",h.conversionRate.toFixed(1),"%)"]})]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-purple-100 p-3 rounded-full mr-4",children:(0,r.jsx)(d.AsH,{className:"text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Revenue"}),(0,r.jsx)("div",{className:"text-xl font-bold",children:k(h.totalRevenue)})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full mr-4",children:(0,r.jsx)(d.MxO,{className:"text-yellow-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Commissions"}),(0,r.jsx)("div",{className:"text-xl font-bold",children:k(h.totalCommission)})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"My Shareable Links"})}),(0,r.jsx)("div",{className:"p-4",children:0===m.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("p",{children:"You haven't created any shareable links yet."}),(0,r.jsxs)("p",{className:"mt-2",children:["Visit the ",(0,r.jsx)(x(),{href:"/shop",className:"text-blue-600 hover:underline",children:"shop"})," to share products and earn commissions!"]})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Link"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stats"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Earnings"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[e.product?.image?(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10 mr-3",children:(0,r.jsx)(c.default,{src:e.product.image,alt:e.product.name,width:40,height:40,className:"rounded-md object-cover"})}):(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md mr-3 flex items-center justify-center",children:(0,r.jsx)(d.AsH,{className:"text-gray-500"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.product?.name||e.title||"Product"}),e.product&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:k(e.product.price)})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:`${window.location.origin}/s/${e.code}`}),(0,r.jsx)("button",{type:"button",onClick:()=>N(e.id,e.code),className:"text-blue-600 hover:text-blue-800",title:"Copy link",children:v===e.id?(0,r.jsx)(d.CMH,{className:"text-green-600"}):(0,r.jsx)(d.paH,{})})]}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("span",{className:`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Active":"Inactive"})})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.YYR,{className:"text-blue-500 mr-1"}),(0,r.jsxs)("span",{children:[e.clickCount," clicks"]})]}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)(d.AsH,{className:"text-green-500 mr-1"}),(0,r.jsxs)("span",{children:[e.conversionCount," purchases"]})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{children:["Revenue: ",k(e.totalRevenue)]}),(0,r.jsxs)("div",{className:"font-medium text-green-600",children:["Commission: ",k(e.totalCommission)]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:w(e.createdAt)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("a",{href:`/s/${e.code}`,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",title:"Open link",children:(0,r.jsx)(d.EQc,{})}),(0,r.jsx)("button",{type:"button",onClick:()=>y(e.id,!e.isActive),className:`${e.isActive?"text-red-600 hover:text-red-800":"text-green-600 hover:text-green-800"}`,title:e.isActive?"Deactivate link":"Activate link",children:e.isActive?(0,r.jsx)(d.mx3,{}):(0,r.jsx)(d.Ny1,{})}),(0,r.jsx)("button",{type:"button",onClick:()=>b(e.id),className:"text-red-600 hover:text-red-800",title:"Delete link",children:(0,r.jsx)(d.qbC,{})})]})})]},e.id))})]})})})]}),(0,r.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6 text-center",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Start Earning Today!"}),(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:"Share products with your friends and family and earn commissions on every purchase they make."}),(0,r.jsx)(x(),{href:"/shop",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Browse Products to Share"})]})]})})}},69324:(e,t,s)=>{Promise.resolve().then(s.bind(s,69211))},79551:e=>{"use strict";e.exports=require("url")},82476:(e,t,s)=>{Promise.resolve().then(s.bind(s,36497))},86592:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=s(65239),i=s(48088),a=s(88170),n=s.n(a),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c={children:["",{children:["referrals",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36497)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/referrals/page",pathname:"/referrals",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(86592));module.exports=r})();
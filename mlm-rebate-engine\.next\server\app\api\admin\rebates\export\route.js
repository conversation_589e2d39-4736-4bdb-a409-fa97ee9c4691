(()=>{var e={};e.id=6895,e.ids=[6895],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>l,aP:()=>c});var s=t(96330),o=t(13581),n=t(85663),a=t(55511),i=t.n(a);async function u(e){return await n.Ay.hash(e,10)}function c(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let l={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:a,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=o?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(s,n,i):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51462:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(31183),u=t(32190),c=t(19854),l=t(12909);async function d(e){try{let r=await (0,c.getServerSession)(l.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!s)return u.NextResponse.json({error:"Authenticated user not found"},{status:404});if(6!==s.rankId)return u.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let o=e.nextUrl.searchParams,n=o.get("status"),a=o.get("search"),d=o.get("startDate"),p=o.get("endDate"),m={};if(n&&(m.status=n),a&&(m.OR=[{generator:{OR:[{name:{contains:a,mode:"insensitive"}},{email:{contains:a,mode:"insensitive"}}]}},{receiver:{OR:[{name:{contains:a,mode:"insensitive"}},{email:{contains:a,mode:"insensitive"}}]}}]),d&&(m.createdAt={...m.createdAt||{},gte:new Date(d)}),p){let e=new Date(p);e.setHours(23,59,59,999),m.createdAt={...m.createdAt||{},lte:e}}let g=(await i.z.rebate.findMany({where:m,include:{purchase:{include:{product:!0}},generator:{select:{id:!0,name:!0,email:!0}},receiver:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"}})).map(e=>[e.id,new Date(e.createdAt).toISOString().split("T")[0],e.generator.name,e.generator.email,e.receiver.name,e.receiver.email,e.purchase.product.name,e.purchase.totalAmount.toFixed(2),e.level,e.percentage.toFixed(2)+"%",e.amount.toFixed(2),e.status,e.processedAt?new Date(e.processedAt).toISOString().split("T")[0]:""]),f=["ID,Date,Generator Name,Generator Email,Receiver Name,Receiver Email,Product,Purchase Amount,Level,Percentage,Rebate Amount,Status,Processed At",...g.map(e=>e.map(e=>`"${e}"`).join(","))].join("\n");return new u.NextResponse(f,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="rebates-export-${new Date().toISOString().split("T")[0]}.csv"`}})}catch(e){return console.error("Error exporting rebates:",e),u.NextResponse.json({error:"Failed to export rebates"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/rebates/export/route",pathname:"/api/admin/rebates/export",filename:"route",bundlePath:"app/api/admin/rebates/export/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebates\\export\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=p;function w(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(51462));module.exports=s})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9154],{14687:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(95155),a=t(12115),l=t(12108),i=t(87747),n=t(29911),o=t(6874),c=t.n(o),d=t(32502),m=t(64065);function x(e){var s,t;let{userId:l,timeRange:i="last30days"}=e,[o,c]=(0,a.useState)(null),[d,x]=(0,a.useState)(!0),[u,h]=(0,a.useState)(null),[b,g]=(0,a.useState)(i);(0,a.useEffect)(()=>{(async()=>{x(!0),h(null);try{let e=new URLSearchParams({userId:l.toString(),timeRange:b}),s=await fetch("/api/genealogy/metrics?".concat(e.toString()));if(!s.ok)throw Error("Failed to fetch genealogy metrics");let t=await s.json();c(t)}catch(e){h(e instanceof Error?e.message:"An unknown error occurred")}finally{x(!1)}})()},[l,b]);let p=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),j=e=>new Intl.NumberFormat().format(e),y=e=>{g(e)},f={labels:(null==o?void 0:o.salesByMonth.map(e=>e.month))||[],datasets:[{label:"Personal Sales",data:(null==o?void 0:o.salesByMonth.map(e=>e.personalSales))||[],borderColor:"rgb(53, 162, 235)",backgroundColor:"rgba(53, 162, 235, 0.5)"},{label:"Team Sales",data:(null==o?void 0:o.salesByMonth.map(e=>e.teamSales))||[],borderColor:"rgb(255, 99, 132)",backgroundColor:"rgba(255, 99, 132, 0.5)"}]},v={labels:(null==o?void 0:o.newMembersByMonth.map(e=>e.month))||[],datasets:[{label:"New Members",data:(null==o?void 0:o.newMembersByMonth.map(e=>e.count))||[],backgroundColor:"rgba(75, 192, 192, 0.5)"}]},N={labels:(null==o?void 0:o.rankDistribution.map(e=>e.rankName))||[],datasets:[{label:"Members",data:(null==o?void 0:o.rankDistribution.map(e=>e.count))||[],backgroundColor:["rgba(255, 99, 132, 0.5)","rgba(54, 162, 235, 0.5)","rgba(255, 206, 86, 0.5)","rgba(75, 192, 192, 0.5)","rgba(153, 102, 255, 0.5)","rgba(255, 159, 64, 0.5)"],borderColor:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(255, 159, 64, 1)"],borderWidth:1}]},w={labels:(null==o?void 0:o.networkDepth.map(e=>"Level ".concat(e.level)))||[],datasets:[{label:"Members",data:(null==o?void 0:o.networkDepth.map(e=>e.count))||[],backgroundColor:"rgba(153, 102, 255, 0.5)"}]},k={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}}};return d&&!o?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,r.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading metrics data..."})]}):u?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,r.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,r.jsx)(n.BS8,{className:"mr-2"}),"Error loading metrics data"]}),(0,r.jsx)("p",{className:"text-red-600",children:u})]})}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,r.jsx)(n.YYR,{className:"mr-2 text-blue-500"}),"Genealogy Metrics Dashboard"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.bfZ,{className:"text-gray-500 mr-2"}),(0,r.jsxs)("select",{value:b,onChange:e=>y(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"last90days",children:"Last 90 Days"}),(0,r.jsx)("option",{value:"last6months",children:"Last 6 Months"}),(0,r.jsx)("option",{value:"last12months",children:"Last 12 Months"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(n.YXz,{className:"text-blue-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Total Members"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:j((null==o?void 0:o.totalMembers)||0)}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[j((null==o?void 0:o.activeMembers)||0)," active members"]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(n.NPy,{className:"text-green-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"New Members"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:j((null==o?void 0:o.newMembersLast30Days)||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"in the last 30 days"})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(n.AsH,{className:"text-yellow-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Total Sales"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:p((null==o?void 0:o.totalSales)||0)}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Avg: ",p((null==o?void 0:o.averageSalesPerMember)||0)," per member"]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(n.lcY,{className:"text-purple-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Total Rebates"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:p((null==o?void 0:o.totalRebates)||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"distributed to members"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(n.YYR,{className:"text-blue-500 mr-2"}),"Sales by Month"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.N1,{data:f,options:k})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(n.v$b,{className:"text-green-500 mr-2"}),"New Members by Month"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.yP,{data:v,options:k})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(n.qvi,{className:"text-yellow-500 mr-2"}),"Rank Distribution"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.Fq,{data:N,options:k})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(n.v$b,{className:"text-purple-500 mr-2"}),"Network Depth"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.yP,{data:w,options:k})})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(n.YXz,{className:"text-blue-500 mr-2"}),"Top Performers"]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Member"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Personal Sales"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Team Sales"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Downline Count"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==o?void 0:o.topPerformers.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:p(e.personalSales)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:p(e.teamSales)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:j(e.downlineCount)})})]},e.id))})]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-2 flex items-center",children:[(0,r.jsx)(n.__w,{className:"text-blue-500 mr-2"}),"Insights"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-blue-700",children:[(0,r.jsxs)("li",{children:["Your network has grown by ",j((null==o?void 0:o.newMembersLast30Days)||0)," members in the last 30 days."]}),(0,r.jsxs)("li",{children:["The most common rank in your network is ",(null==o||null==(s=o.rankDistribution[0])?void 0:s.rankName)||"N/A","."]}),(0,r.jsxs)("li",{children:["Your network extends to ",(null==o?void 0:o.networkDepth.length)||0," levels deep."]}),(0,r.jsxs)("li",{children:["Your top performer generated ",p((null==o||null==(t=o.topPerformers[0])?void 0:t.teamSales)||0)," in team sales."]})]})]})]})}function u(){let{data:e,status:s}=(0,l.useSession)(),[t,o]=(0,a.useState)(void 0),[d,m]=(0,a.useState)("last30days"),{data:u,isLoading:h}=(0,i.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let t=await fetch("/api/users/me");if(!t.ok)throw Error("Failed to fetch user data");return await t.json()},enabled:"authenticated"===s});return(u&&!t&&o(u.id),"loading"===s||h)?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(n.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view genealogy metrics."}),(0,r.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Metrics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"View key metrics and analytics for your genealogy network"})]}),(0,r.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(n.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[t?(0,r.jsx)(x,{userId:t,timeRange:d}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,r.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading metrics data..."})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center mb-4",children:[(0,r.jsx)(n.__w,{className:"mr-2 text-blue-500"}),"About Genealogy Metrics"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{children:"The Genealogy Metrics Dashboard provides valuable insights into your network's performance and growth. Use these metrics to identify trends, recognize top performers, and make data-driven decisions to grow your business."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Key Metrics"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"Total Members - The total number of distributors in your network"}),(0,r.jsx)("li",{children:"New Members - Recent additions to your network"}),(0,r.jsx)("li",{children:"Total Sales - Combined sales volume across your network"}),(0,r.jsx)("li",{children:"Total Rebates - Commissions and bonuses distributed"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"Performance Charts"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Sales by Month - Track sales trends over time"}),(0,r.jsx)("li",{children:"New Members by Month - Monitor recruitment performance"}),(0,r.jsx)("li",{children:"Rank Distribution - See the composition of your network"}),(0,r.jsx)("li",{children:"Network Depth - Analyze your network's structure"})]})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"How to Use These Metrics"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Identify Growth Opportunities:"})," Look for trends in sales and recruitment to identify seasonal patterns or growth opportunities."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Recognize Top Performers:"})," Identify and reward your top performers to encourage continued success."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Balance Your Network:"})," Use the rank distribution and network depth charts to identify areas that need development."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Set Goals:"})," Use historical data to set realistic goals for future performance."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Track Progress:"})," Monitor your metrics regularly to track progress toward your goals."]})]})]}),(0,r.jsx)("p",{children:"For more detailed analysis, you can export these metrics or view specific reports in the reporting section. The metrics are updated daily to provide you with the most current information about your network."})]})]})]})]})}d.t1.register(d.PP,d.kc,d.FN,d.No,d.E8,d.Bs,d.hE,d.m_,d.s$)},26436:(e,s,t)=>{Promise.resolve().then(t.bind(t,14687))},74436:(e,s,t)=>{"use strict";t.d(s,{k5:()=>d});var r=t(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=r.createContext&&r.createContext(a),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function o(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);s&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function c(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?o(Object(t),!0).forEach(function(s){var r,a,l;r=e,a=s,l=t[s],(a=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,s||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(a))in r?Object.defineProperty(r,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function d(e){return s=>r.createElement(m,n({attr:c({},e.attr)},s),function e(s){return s&&s.map((s,t)=>r.createElement(s.tag,c({key:t},s.attr),e(s.child)))}(e.child))}function m(e){var s=s=>{var t,{attr:a,size:l,title:o}=e,d=function(e,s){if(null==e)return{};var t,r,a=function(e,s){if(null==e)return{};var t={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(s.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,s);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)t=l[r],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,i),m=l||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),r.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,a,d,{className:t,style:c(c({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),o&&r.createElement("title",null,o),e.children)};return void 0!==l?r.createElement(l.Consumer,null,e=>s(e)):s(a)}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,5647,6874,2108,6967,7747,8579,8441,1684,7358],()=>s(26436)),_N_E=e.O()}]);
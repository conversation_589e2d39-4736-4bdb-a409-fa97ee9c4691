(()=>{var e={};e.id=2854,e.ids=[2854],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7982:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var n=s(65239),a=s(48088),r=s(88170),i=s.n(r),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["admin",{children:["mlm-config",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,11574)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/mlm-config/page",pathname:"/admin/mlm-config",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11574:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\mlm-config\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\mlm-config\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26902:(e,t,s)=>{Promise.resolve().then(s.bind(s,11574))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50462:(e,t,s)=>{Promise.resolve().then(s.bind(s,96060))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},96060:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var n=s(60687),a=s(43210),r=s(82136),i=s(16189),l=s(68367),o=s(23877);function c(){let{data:e,status:t}=(0,r.useSession)();(0,i.useRouter)();let[s,c]=(0,a.useState)(!0),[d,m]=(0,a.useState)(!1),[u,x]=(0,a.useState)(null),[p,g]=(0,a.useState)([]),[f,h]=(0,a.useState)({type:"",text:""}),[b,y]=(0,a.useState)(null),[v,j]=(0,a.useState)(!1),N=async()=>{c(!0);try{let e=await fetch("/api/mlm-config?includePerformanceTiers=true");if(!e.ok)throw Error(`Failed to fetch MLM configuration: ${e.statusText}`);let t=await e.json();x(t.config),g(t.performanceTiers||[])}catch(e){console.error("Error fetching MLM configuration:",e),h({type:"error",text:"Failed to load MLM configuration. Please try again."})}finally{c(!1)}},w=async()=>{if(u){m(!0),h({type:"",text:""});try{let e=await fetch("/api/mlm-config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"updateConfig",config:u})});if(!e.ok)throw Error(`Failed to update MLM configuration: ${e.statusText}`);let t=await e.json();x(t.config),h({type:"success",text:"MLM configuration updated successfully."})}catch(e){console.error("Error updating MLM configuration:",e),h({type:"error",text:"Failed to update MLM configuration. Please try again."})}finally{m(!1)}}},C=async()=>{if(b){m(!0),h({type:"",text:""});try{let e=!b.id,t=await fetch("/api/mlm-config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e?"createPerformanceTier":"updatePerformanceTier",tier:b,tierId:e?void 0:b.id})});if(!t.ok)throw Error(`Failed to ${e?"create":"update"} performance tier: ${t.statusText}`);await t.json(),N(),h({type:"success",text:`Performance tier ${e?"created":"updated"} successfully.`}),y(null),j(!1)}catch(e){console.error("Error saving performance tier:",e),h({type:"error",text:"Failed to save performance tier. Please try again."})}finally{m(!1)}}},k=(e,t)=>{u&&x({...u,[e]:t})},M=(e,t)=>{b&&y({...b,[e]:t})},S=e=>{y({...e}),j(!0)};return"loading"===t||s?(0,n.jsx)(l.A,{children:(0,n.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,n.jsx)(o.hW,{className:"animate-spin text-green-500 mr-2"}),(0,n.jsx)("span",{children:"Loading..."})]})}):(0,n.jsx)(l.A,{children:(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"MLM Configuration"}),f.text&&(0,n.jsx)("div",{className:`mb-6 p-4 rounded-md ${"error"===f.type?"bg-red-100 text-red-700":"success"===f.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:f.text}),u&&(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,n.jsx)("div",{className:"p-4 border-b",children:(0,n.jsx)("h2",{className:"text-lg font-semibold",children:"System Configuration"})}),(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"MLM Structure"}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("button",{type:"button",onClick:()=>k("mlmStructure","binary"),className:`flex items-center px-4 py-2 rounded-md ${"binary"===u.mlmStructure?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"}`,children:[(0,n.jsx)(o.yk7,{className:"mr-2"}),"Binary"]}),(0,n.jsxs)("button",{type:"button",onClick:()=>k("mlmStructure","unilevel"),className:`flex items-center px-4 py-2 rounded-md ${"unilevel"===u.mlmStructure?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"}`,children:[(0,n.jsx)(o.yk7,{className:"mr-2"}),"Unilevel"]})]}),(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"binary"===u.mlmStructure?"Binary structure: Each member can have at most 2 direct downlines (left and right legs)":"Unilevel structure: Each member can have unlimited direct downlines"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"PV Calculation Method"}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("button",{type:"button",onClick:()=>k("pvCalculation","percentage"),className:`flex items-center px-4 py-2 rounded-md ${"percentage"===u.pvCalculation?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"}`,children:[(0,n.jsx)(o.gdQ,{className:"mr-2"}),"Percentage"]}),(0,n.jsxs)("button",{type:"button",onClick:()=>k("pvCalculation","fixed"),className:`flex items-center px-4 py-2 rounded-md ${"fixed"===u.pvCalculation?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"}`,children:[(0,n.jsx)(o.Tsk,{className:"mr-2"}),"Fixed"]})]}),(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"percentage"===u.pvCalculation?"Percentage: PV is calculated as a percentage of the product price":"Fixed: PV is a fixed value set for each product"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Performance Bonus"}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("button",{type:"button",onClick:()=>k("performanceBonusEnabled",!0),className:`flex items-center px-4 py-2 rounded-md ${u.performanceBonusEnabled?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"}`,children:[(0,n.jsx)(o.SBv,{className:"mr-2"}),"Enabled"]}),(0,n.jsxs)("button",{type:"button",onClick:()=>k("performanceBonusEnabled",!1),className:`flex items-center px-4 py-2 rounded-md ${!u.performanceBonusEnabled?"bg-blue-600 text-white":"bg-gray-200 text-gray-700"}`,children:[(0,n.jsx)(o.SBv,{className:"mr-2"}),"Disabled"]})]}),(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:u.performanceBonusEnabled?"Performance bonus is enabled based on personal sales volume":"Performance bonus is disabled"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Monthly Cutoff Day"}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(o.bfZ,{className:"text-gray-400 mr-2"}),(0,n.jsx)("input",{type:"number",min:"1",max:"31",value:u.monthlyCutoffDay,onChange:e=>k("monthlyCutoffDay",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-20"})]}),(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Day of the month when commissions are calculated and paid"})]}),(0,n.jsxs)("div",{className:"md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Binary Max Depth"}),(0,n.jsx)("input",{type:"number",min:"1",max:"10",value:u.binaryMaxDepth,onChange:e=>k("binaryMaxDepth",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"}),(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Maximum depth for binary structure (1-10)"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Unilevel Max Depth"}),(0,n.jsx)("input",{type:"number",min:"1",max:"10",value:u.unilevelMaxDepth,onChange:e=>k("unilevelMaxDepth",parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"}),(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Maximum depth for unilevel structure (1-10)"})]})]})]}),(0,n.jsx)("div",{className:"mt-6 flex justify-end",children:(0,n.jsx)("button",{type:"button",onClick:w,disabled:d,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:d?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.dIn,{className:"inline mr-2"}),"Save Configuration"]})})})]})]}),u.performanceBonusEnabled&&(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,n.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold",children:"Performance Bonus Tiers"}),(0,n.jsxs)("button",{type:"button",onClick:()=>{y({id:0,name:"",minSales:0,maxSales:null,bonusType:"percentage",percentage:0,fixedAmount:0,active:!0}),j(!0)},className:"bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:[(0,n.jsx)(o.OiG,{className:"inline mr-1"}),"Add Tier"]})]}),(0,n.jsxs)("div",{className:"p-6",children:[0===p.length?(0,n.jsx)("div",{className:"text-center py-4 text-gray-500",children:'No performance bonus tiers defined. Click "Add Tier" to create one.'}):(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,n.jsx)("thead",{className:"bg-gray-50",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Sales Range"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bonus Type"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bonus Value"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,n.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,n.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:e.name})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsxs)("div",{className:"text-sm text-gray-900",children:["₱",e.minSales.toFixed(2)," - ",e.maxSales?`₱${e.maxSales.toFixed(2)}`:"Unlimited"]})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)("div",{className:"text-sm text-gray-900",children:"percentage"===e.bonusType?"Percentage":"Fixed Amount"})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)("div",{className:"text-sm text-gray-900",children:"percentage"===e.bonusType?`${e.percentage.toFixed(2)}%`:`₱${e.fixedAmount.toFixed(2)}`})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${e.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.active?"Active":"Inactive"})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,n.jsx)("button",{type:"button",onClick:()=>S(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:(0,n.jsx)(o.uO9,{})})})]},e.id))})]})}),v&&b&&(0,n.jsxs)("div",{className:"mt-6 border rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium mb-4",children:b.id?"Edit Tier":"Add New Tier"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tier Name"}),(0,n.jsx)("input",{type:"text",value:b.name,onChange:e=>M("name",e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,n.jsxs)("select",{value:b.active?"active":"inactive",onChange:e=>M("active","active"===e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:[(0,n.jsx)("option",{value:"active",children:"Active"}),(0,n.jsx)("option",{value:"inactive",children:"Inactive"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Minimum Sales"}),(0,n.jsx)("input",{type:"number",min:"0",step:"0.01",value:b.minSales,onChange:e=>M("minSales",parseFloat(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Maximum Sales (leave empty for unlimited)"}),(0,n.jsx)("input",{type:"number",min:"0",step:"0.01",value:b.maxSales||"",onChange:e=>M("maxSales",e.target.value?parseFloat(e.target.value):null),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bonus Type"}),(0,n.jsxs)("select",{value:b.bonusType,onChange:e=>M("bonusType",e.target.value),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full",children:[(0,n.jsx)("option",{value:"percentage",children:"Percentage"}),(0,n.jsx)("option",{value:"fixed",children:"Fixed Amount"})]})]}),"percentage"===b.bonusType?(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Percentage (%)"}),(0,n.jsx)("input",{type:"number",min:"0",max:"100",step:"0.01",value:b.percentage,onChange:e=>M("percentage",parseFloat(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]}):(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fixed Amount"}),(0,n.jsx)("input",{type:"number",min:"0",step:"0.01",value:b.fixedAmount,onChange:e=>M("fixedAmount",parseFloat(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full"})]})]}),(0,n.jsxs)("div",{className:"mt-4 flex justify-end space-x-3",children:[(0,n.jsx)("button",{type:"button",onClick:()=>{y(null),j(!1)},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Cancel"}),(0,n.jsx)("button",{type:"button",onClick:C,disabled:d,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:d?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.dIn,{className:"inline mr-2"}),"Save Tier"]})})]})]})]})]})]})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(7982));module.exports=n})();
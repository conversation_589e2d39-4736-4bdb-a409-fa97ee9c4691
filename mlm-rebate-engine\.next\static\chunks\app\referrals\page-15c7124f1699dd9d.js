(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5663],{2325:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(95155),l=s(12115),i=s(12108),r=s(35695),c=s(70357),n=s(29911),d=s(66766),o=s(6874),x=s.n(o);function m(){let{data:e,status:t}=(0,i.useSession)(),s=(0,r.useRouter)(),[o,m]=(0,l.useState)(!0),[h,u]=(0,l.useState)([]),[p,j]=(0,l.useState)(null),[g,y]=(0,l.useState)({type:"",text:""}),[v,N]=(0,l.useState)(null);(0,l.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,l.useEffect)(()=>{"authenticated"===t&&f()},[t]);let f=async()=>{m(!0),y({type:"",text:""});try{let e=await fetch("/api/shareable-links?includeStats=true");if(!e.ok)throw Error("Failed to fetch shareable links: ".concat(e.statusText));let t=await e.json();u(t.links||[]),j(t.stats||null)}catch(e){console.error("Error fetching shareable links:",e),y({type:"error",text:"Failed to load shareable links. Please try again."})}finally{m(!1)}},b=async(e,t)=>{try{let s=await fetch("/api/shareable-links",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:e,isActive:t})});if(!s.ok)throw Error("Failed to update link: ".concat(s.statusText));u(h.map(s=>s.id===e?{...s,isActive:t}:s)),y({type:"success",text:"Link ".concat(t?"activated":"deactivated"," successfully")})}catch(e){console.error("Error updating link:",e),y({type:"error",text:"Failed to update link. Please try again."})}},w=async e=>{if(confirm("Are you sure you want to delete this link? This action cannot be undone."))try{let t=await fetch("/api/shareable-links?id=".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete link: ".concat(t.statusText));u(h.filter(t=>t.id!==e)),y({type:"success",text:"Link deleted successfully"})}catch(e){console.error("Error deleting link:",e),y({type:"error",text:"Failed to delete link. Please try again."})}},k=async(e,t)=>{try{let s=window.location.origin,a="".concat(s,"/s/").concat(t);await navigator.clipboard.writeText(a),N(e),setTimeout(()=>{N(null)},2e3)}catch(e){console.error("Error copying to clipboard:",e),y({type:"error",text:"Failed to copy link to clipboard"})}},C=e=>new Date(e).toLocaleDateString(),E=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return"loading"===t||o?(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(n.hW,{className:"animate-spin text-green-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})}):(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"My Referrals"}),g.text&&(0,a.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("error"===g.type?"bg-red-100 text-red-700":"success"===g.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:g.text}),p&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(n.AnD,{className:"text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Links"}),(0,a.jsx)("div",{className:"text-xl font-bold",children:p.totalLinks})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-green-100 p-3 rounded-full mr-4",children:(0,a.jsx)(n.YYR,{className:"text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Clicks / Conversions"}),(0,a.jsxs)("div",{className:"text-xl font-bold",children:[p.totalClicks," / ",p.totalConversions,(0,a.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",p.conversionRate.toFixed(1),"%)"]})]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-purple-100 p-3 rounded-full mr-4",children:(0,a.jsx)(n.AsH,{className:"text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Revenue"}),(0,a.jsx)("div",{className:"text-xl font-bold",children:E(p.totalRevenue)})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full mr-4",children:(0,a.jsx)(n.MxO,{className:"text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Commissions"}),(0,a.jsx)("div",{className:"text-xl font-bold",children:E(p.totalCommission)})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"My Shareable Links"})}),(0,a.jsx)("div",{className:"p-4",children:0===h.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{children:"You haven't created any shareable links yet."}),(0,a.jsxs)("p",{className:"mt-2",children:["Visit the ",(0,a.jsx)(x(),{href:"/shop",className:"text-blue-600 hover:underline",children:"shop"})," to share products and earn commissions!"]})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Link"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stats"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Earnings"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(e=>{var t,s;return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(null==(t=e.product)?void 0:t.image)?(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10 mr-3",children:(0,a.jsx)(d.default,{src:e.product.image,alt:e.product.name,width:40,height:40,className:"rounded-md object-cover"})}):(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md mr-3 flex items-center justify-center",children:(0,a.jsx)(n.AsH,{className:"text-gray-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:(null==(s=e.product)?void 0:s.name)||e.title||"Product"}),e.product&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:E(e.product.price)})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"".concat(window.location.origin,"/s/").concat(e.code)}),(0,a.jsx)("button",{type:"button",onClick:()=>k(e.id,e.code),className:"text-blue-600 hover:text-blue-800",title:"Copy link",children:v===e.id?(0,a.jsx)(n.CMH,{className:"text-green-600"}):(0,a.jsx)(n.paH,{})})]}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.YYR,{className:"text-blue-500 mr-1"}),(0,a.jsxs)("span",{children:[e.clickCount," clicks"]})]}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(n.AsH,{className:"text-green-500 mr-1"}),(0,a.jsxs)("span",{children:[e.conversionCount," purchases"]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("div",{children:["Revenue: ",E(e.totalRevenue)]}),(0,a.jsxs)("div",{className:"font-medium text-green-600",children:["Commission: ",E(e.totalCommission)]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:C(e.createdAt)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)("a",{href:"/s/".concat(e.code),target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",title:"Open link",children:(0,a.jsx)(n.EQc,{})}),(0,a.jsx)("button",{type:"button",onClick:()=>b(e.id,!e.isActive),className:"".concat(e.isActive?"text-red-600 hover:text-red-800":"text-green-600 hover:text-green-800"),title:e.isActive?"Deactivate link":"Activate link",children:e.isActive?(0,a.jsx)(n.mx3,{}):(0,a.jsx)(n.Ny1,{})}),(0,a.jsx)("button",{type:"button",onClick:()=>w(e.id),className:"text-red-600 hover:text-red-800",title:"Delete link",children:(0,a.jsx)(n.qbC,{})})]})})]},e.id)})})]})})})]}),(0,a.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6 text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Start Earning Today!"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"Share products with your friends and family and earn commissions on every purchase they make."}),(0,a.jsx)(x(),{href:"/shop",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Browse Products to Share"})]})]})})}},2372:(e,t,s)=>{Promise.resolve().then(s.bind(s,2325))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(2372)),_N_E=e.O()}]);
(()=>{var e={};e.id=4674,e.ids=[4674],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24018:(e,s,t)=>{Promise.resolve().then(t.bind(t,97780))},25194:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\test-users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-users\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48034:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let o={children:["",{children:["admin",{children:["test-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25194)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-users\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-users\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/test-users/page",pathname:"/admin/test-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},60970:(e,s,t)=>{Promise.resolve().then(t.bind(t,25194))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},97780:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(60687),a=t(43210),n=t(82136),l=t(16189),i=t(68367),d=t(23877);let o=()=>{let[e,s]=(0,a.useState)(!1),[t,n]=(0,a.useState)(null),[l,i]=(0,a.useState)("development"),[o,c]=(0,a.useState)(!0),[m,x]=(0,a.useState)(!1),[u,p]=(0,a.useState)(""),[h,g]=(0,a.useState)(""),[b,f]=(0,a.useState)(!1),[y,j]=(0,a.useState)(30),[v,N]=(0,a.useState)(1),[w,k]=(0,a.useState)(20),[C,S]=(0,a.useState)(5),[P,R]=(0,a.useState)(4),[T,_]=(0,a.useState)(10),[M,F]=(0,a.useState)(!0),[D,E]=(0,a.useState)(!0),[L,A]=(0,a.useState)(!1),U=async()=>{s(!0),g("");try{let e=await fetch(`/api/admin/test-users?environment=${l}`);if(!e.ok)throw Error(`Failed to fetch test users: ${e.statusText}`);let s=await e.json();n(s)}catch(e){g(e.message||"Failed to fetch test users")}finally{s(!1)}},G=async()=>{s(!0),g(""),p("");try{let e=await fetch("/api/admin/test-users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({environment:l,userCount:y,adminCount:v,distributorCount:w,rankedDistributorCount:C,viewerCount:P,maxLevels:T,generatePurchases:M,generateRebates:D})});if(!e.ok)throw Error(`Failed to generate test users: ${e.statusText}`);let s=await e.json();p(s.message),await U()}catch(e){g(e.message||"Failed to generate test users")}finally{s(!1)}},$=async()=>{s(!0),g(""),p("");try{let e=await fetch("/api/admin/test-users",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({environment:l,retainKeyTesters:o,dryRun:m})});if(!e.ok)throw Error(`Failed to delete test users: ${e.statusText}`);let s=await e.json();p(`${m?"[DRY RUN] ":""}${s.message}: ${s.deleted} deleted, ${s.retained} retained`),await U(),f(!1)}catch(e){g(e.message||"Failed to delete test users")}finally{s(!1)}};return(0,a.useEffect)(()=>{U()},[l]),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,r.jsx)(d.YXz,{className:"mr-2 text-blue-500"})," Test User Manager"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("select",{value:l,onChange:e=>i(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",disabled:e,children:[(0,r.jsx)("option",{value:"development",children:"Development"}),(0,r.jsx)("option",{value:"staging",children:"Staging"})]}),(0,r.jsx)("button",{onClick:()=>U(),className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",disabled:e,children:"Refresh"})]})]}),u&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-green-100 text-green-700 rounded-md",children:u}),h&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:h}),t&&(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Users"}),(0,r.jsx)("div",{className:"text-2xl font-semibold",children:t.totalCount})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-4 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Admins"}),(0,r.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.admin.length})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Ranked Distributors"}),(0,r.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.ranked_distributor.length})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Distributors"}),(0,r.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.distributor.length})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Viewers"}),(0,r.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.viewer.length})]})]})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Generate Test Users"}),(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("button",{onClick:()=>A(!L),className:"text-blue-500 text-sm underline mr-2",children:L?"Hide Settings":"Show Settings"}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[y," users, ",T," levels deep"]})]}),L&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Users"}),(0,r.jsx)("input",{type:"number",value:y,onChange:e=>j(parseInt(e.target.value)),min:"1",max:"100",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Levels"}),(0,r.jsx)("input",{type:"number",value:T,onChange:e=>_(parseInt(e.target.value)),min:"1",max:"10",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Admins"}),(0,r.jsx)("input",{type:"number",value:v,onChange:e=>N(parseInt(e.target.value)),min:"0",max:"5",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ranked Distributors"}),(0,r.jsx)("input",{type:"number",value:C,onChange:e=>S(parseInt(e.target.value)),min:"0",max:"20",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Distributors"}),(0,r.jsx)("input",{type:"number",value:w,onChange:e=>k(parseInt(e.target.value)),min:"0",max:"50",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Viewers"}),(0,r.jsx)("input",{type:"number",value:P,onChange:e=>R(parseInt(e.target.value)),min:"0",max:"20",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"generatePurchases",checked:M,onChange:e=>F(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"generatePurchases",className:"ml-2 block text-sm text-gray-700",children:"Generate Purchases"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"generateRebates",checked:D,onChange:e=>E(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"generateRebates",className:"ml-2 block text-sm text-gray-700",children:"Generate Rebates"})]})]}),(0,r.jsx)("button",{onClick:G,disabled:e,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.hW,{className:"animate-spin mr-2"})," Generating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.NPy,{className:"mr-2"})," Generate Test Users"]})})]})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Clean Up Test Users"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"retainKeyTesters",checked:o,onChange:e=>c(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"retainKeyTesters",className:"ml-2 block text-sm text-gray-700",children:"Retain Key Testers (keep_for_dev = true)"})]}),(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"dryRun",checked:m,onChange:e=>x(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"dryRun",className:"ml-2 block text-sm text-gray-700",children:"Dry Run (preview only, no deletion)"})]})]}),b?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{className:"text-sm text-red-600 font-medium",children:["Are you sure you want to delete ",t?.totalCount||0," test users?"]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:$,disabled:e,className:"flex-1 flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.hW,{className:"animate-spin mr-2"})," Deleting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.CMH,{className:"mr-2"})," Confirm"]})}),(0,r.jsxs)("button",{onClick:()=>f(!1),disabled:e,className:"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:[(0,r.jsx)(d.QCr,{className:"mr-2"})," Cancel"]})]})]}):(0,r.jsxs)("button",{onClick:()=>f(!0),disabled:e||!t||0===t.totalCount,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)(d.Dby,{className:"mr-2"})," Delete Test Users"]})]})})]}),t&&t.users.length>0&&(0,r.jsxs)("div",{className:"overflow-x-auto",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Test Users"}),(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Upline"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Balance"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Keep"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.users.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"admin"===e.metadata.role?"bg-purple-100 text-purple-800":"ranked_distributor"===e.metadata.role?"bg-yellow-100 text-yellow-800":"distributor"===e.metadata.role?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:e.metadata.role})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.uplineId||"-"}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",e.walletBalance.toFixed(2)]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.metadata.keepForDev?(0,r.jsx)(d.CMH,{className:"text-green-500"}):(0,r.jsx)(d.QCr,{className:"text-red-500"})})]},e.id))})]})]})]})};function c(){let{data:e,status:s}=(0,n.useSession)();(0,l.useRouter)();let[t,c]=(0,a.useState)(!0),[m,x]=(0,a.useState)(!1);return"loading"===s||t?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})})}):m?(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,r.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,r.jsx)(d.YXz,{className:"mr-2 text-blue-500"})," Test User Management"]})}),(0,r.jsx)(o,{})]})}):(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>t(48034));module.exports=r})();
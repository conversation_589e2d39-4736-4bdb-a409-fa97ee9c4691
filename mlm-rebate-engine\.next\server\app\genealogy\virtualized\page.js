(()=>{var e={};e.id=5922,e.ids=[5922],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},23229:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>l,_:()=>o});var n=r(60687),i=r(43210);let s=(0,i.createContext)(void 0),o=()=>{let e=(0,i.useContext)(s);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},l=({children:e})=>{let[t,r]=(0,i.useState)([]);(0,i.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{r(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,i.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let o=e=>{r(t=>t.filter(t=>t.id!==e))},l=t.reduce((e,t)=>e+t.quantity,0),a=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,n.jsx)(s.Provider,{value:{items:t,addItem:e=>{r(t=>{let r=t.findIndex(t=>t.id===e.id);if(!(r>=0))return[...t,e];{let n=[...t];return n[r]={...n[r],quantity:n[r].quantity+e.quantity},n}})},removeItem:o,updateQuantity:(e,t)=>{if(t<=0)return void o(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},itemCount:l,subtotal:a,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30036:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var n=r(49587),i=r.n(n)},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>i});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},44636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\virtualized\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\virtualized\\page.tsx","default")},45778:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(60687),i=r(43210),s=r(82136),o=r(59391),l=r(23877),a=r(85814),d=r.n(a);let c=(0,r(30036).default)(async()=>{},{loadableGenerated:{modules:["app\\genealogy\\virtualized\\page.tsx -> @/components/genealogy/VirtualizedGenealogyFlow"]},ssr:!1,loading:()=>(0,n.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,n.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,n.jsx)("span",{children:"Loading virtualized visualization..."})]})});function u(){let{data:e,status:t}=(0,s.useSession)(),[r,a]=(0,i.useState)(void 0),[u,m]=(0,i.useState)("vertical"),{data:h,isLoading:p}=(0,o.I)({queryKey:["user"],queryFn:async()=>{if(!e?.user?.email)return null;let t=await fetch("/api/users/me");if(!t.ok)throw Error("Failed to fetch user data");return await t.json()},enabled:"authenticated"===t});return(h&&!r&&a(h.id),"loading"===t||p)?(0,n.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,n.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,n.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===t?(0,n.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,n.jsx)(l.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,n.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,n.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,n.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,n.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold",children:"Virtualized Genealogy Tree"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Optimized for large networks with thousands of members"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsxs)("div",{className:"flex bg-white rounded-md shadow-md overflow-hidden",children:[(0,n.jsxs)("button",{onClick:()=>m("vertical"),className:`px-3 py-2 flex items-center ${"vertical"===u?"bg-blue-100 text-blue-700":"hover:bg-gray-100"}`,children:[(0,n.jsx)(l.aQJ,{className:"mr-2"})," Vertical"]}),(0,n.jsxs)("button",{onClick:()=>m("horizontal"),className:`px-3 py-2 flex items-center ${"horizontal"===u?"bg-blue-100 text-blue-700":"hover:bg-gray-100"}`,children:[(0,n.jsx)(l.aQJ,{className:"mr-2 rotate-90"})," Horizontal"]}),(0,n.jsxs)("button",{onClick:()=>m("radial"),className:`px-3 py-2 flex items-center ${"radial"===u?"bg-blue-100 text-blue-700":"hover:bg-gray-100"}`,children:[(0,n.jsx)(l.aQJ,{className:"mr-2 rotate-45"})," Radial"]})]}),(0,n.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,n.jsx)(l.QVr,{className:"mr-1"}),"Back to Genealogy"]})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:(0,n.jsx)("div",{className:"lg:col-span-4",children:(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:r?(0,n.jsx)(c,{userId:r,maxLevel:6,initialLayout:u,initialPageSize:20}):(0,n.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,n.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,n.jsx)("span",{children:"Loading genealogy data..."})]})})})}),(0,n.jsxs)("div",{className:"mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,n.jsx)("h2",{className:"text-lg font-medium text-blue-800 mb-2",children:"About Virtualized Genealogy"}),(0,n.jsx)("p",{className:"text-blue-700 mb-2",children:"This virtualized genealogy tree is optimized for large networks with thousands of members:"}),(0,n.jsxs)("ul",{className:"list-disc list-inside text-blue-700 space-y-1",children:[(0,n.jsx)("li",{children:"Lazy loading of nodes - only loads data when needed"}),(0,n.jsx)("li",{children:"Virtualization - only renders nodes that are visible on screen"}),(0,n.jsx)("li",{children:"Efficient memory usage - reduces RAM consumption for large trees"}),(0,n.jsx)("li",{children:"Smooth performance even with thousands of members"}),(0,n.jsx)("li",{children:"Multiple layout options for different visualization needs"})]}),(0,n.jsxs)("p",{className:"text-blue-700 mt-2",children:[(0,n.jsx)("strong",{children:"How to use:"})," Click on a node to expand it and load its children. The tree will automatically optimize which nodes are rendered based on your view."]})]}),(0,n.jsxs)("div",{className:"mt-4 bg-yellow-50 p-4 rounded-lg border border-yellow-200",children:[(0,n.jsx)("h2",{className:"text-lg font-medium text-yellow-800 mb-2",children:"Performance Tips"}),(0,n.jsxs)("ul",{className:"list-disc list-inside text-yellow-700 space-y-1",children:[(0,n.jsx)("li",{children:"Only expand nodes you need to see to maintain optimal performance"}),(0,n.jsx)("li",{children:"Use the minimap for navigation in large networks"}),(0,n.jsx)("li",{children:"Adjust your zoom level to see more or fewer nodes at once"}),(0,n.jsx)("li",{children:"Switch layouts to find the best visualization for your network structure"})]})]})]})}},45851:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(60687),i=r(25217),s=r(8693),o=r(43210);function l({children:e}){let[t]=(0,o.useState)(()=>new i.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,n.jsx)(s.Ht,{client:t,children:e})}},49587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(14985)._(r(64963));function i(e,t){var r;let i={};"function"==typeof e&&(i.loader=e);let s={...i,...t};return(0,n.default)({...s,modules:null==(r=s.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return i}});let n=r(81208);function i(e){let{reason:t,children:r}=e;throw Object.defineProperty(new n.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},58936:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=r(65239),i=r(48088),s=r(88170),o=r.n(s),l=r(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let d={children:["",{children:["genealogy",{children:["virtualized",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44636)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\virtualized\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\virtualized\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/genealogy/virtualized/page",pathname:"/genealogy/virtualized",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(60687),i=r(43210);let s=()=>"serviceWorker"in navigator,o=async()=>{if(!s())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),l(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},l=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&a()})})},a=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,r]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{o();let e=()=>{r(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,n.jsxs)(n.Fragment,{children:[e,t&&(0,n.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,n.jsxs)("div",{className:"mr-4",children:[(0,n.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,n.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,n.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,r)=>{Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,23229)),Promise.resolve().then(r.bind(r,82113)),Promise.resolve().then(r.bind(r,41750))},64777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return l}});let n=r(60687),i=r(51215),s=r(29294),o=r(19587);function l(e){let{moduleIds:t}=e,r=s.workAsyncStorage.getStore();if(void 0===r)return null;let l=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;l.push(...t)}}return 0===l.length?null:(0,n.jsx)(n.Fragment,{children:l.map(e=>{let t=r.assetPrefix+"/_next/"+(0,o.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,i.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let n=r(60687),i=r(43210),s=r(56780),o=r(64777);function l(e){return{default:e&&"default"in e?e.default:e}}let a={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},d=function(e){let t={...a,...e},r=(0,i.lazy)(()=>t.loader().then(l)),d=t.loading;function c(e){let l=d?(0,n.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,a=!t.ssr||!!t.loading,c=a?i.Suspense:i.Fragment,u=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.PreloadChunks,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(c,{...a?{fallback:l}:{},children:u})}return c.displayName="LoadableComponent",c}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,r)=>{Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,97695)),Promise.resolve().then(r.bind(r,45851)),Promise.resolve().then(r.bind(r,63345))},77116:(e,t,r)=>{Promise.resolve().then(r.bind(r,45778))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},90684:(e,t,r)=>{Promise.resolve().then(r.bind(r,44636))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>m});var n=r(37413),i=r(22376),s=r.n(i),o=r(68726),l=r.n(o);r(61135);var a=r(23229),d=r(37043),c=r(82113),u=r(41750);let m={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function h({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${s().variable} ${l().variable} antialiased`,children:(0,n.jsx)(a.AuthProvider,{children:(0,n.jsx)(c.default,{children:(0,n.jsx)(d.CartProvider,{children:(0,n.jsx)(u.default,{children:e})})})})})})}},96111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},97695:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var n=r(60687),i=r(82136);function s({children:e}){return(0,n.jsx)(i.SessionProvider,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,8414,9567,3877,9391],()=>r(58936));module.exports=n})();
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7402],{15567:(e,s,t)=>{t.d(s,{A:()=>l});var a=t(95155),r=t(12115),n=t(29911);function l(e){let{onRefresh:s,onSearch:t,onToggleFilters:l,showFilters:i,isLoading:c}=e,[d,o]=(0,r.useState)("");return(0,a.jsxs)("div",{className:"bg-white p-3 border-b flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:l,className:"flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:[(0,a.jsx)(n.YsJ,{className:"mr-1"}),"Filters",i?(0,a.jsx)(n.Ucs,{className:"ml-1"}):(0,a.jsx)(n.Vr3,{className:"ml-1"})]}),(0,a.jsxs)("button",{onClick:s,className:"flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200",disabled:c,children:[(0,a.jsx)(n.Swo,{className:"mr-1 ".concat(c?"animate-spin":"")}),c?"Refreshing...":"Refresh"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(d)},className:"flex items-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search users...",value:d,onChange:e=>o(e.target.value),className:"pl-9 pr-4 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-48 md:w-64"}),(0,a.jsx)("div",{className:"absolute left-3 top-2 text-gray-400",children:(0,a.jsx)(n.KSO,{})})]}),(0,a.jsx)("button",{type:"submit",className:"ml-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Search"})]})]})}},27402:(e,s,t)=>{t.r(s),t.d(s,{default:()=>g});var a=t(95155),r=t(12115),n=t(74211),l=t(93306),i=t(77581),c=t(57916);t(11687);var d=t(29911);let o=(0,r.memo)(function(e){let{data:s,isConnectable:t}=e,{user:r,onExpand:l,onSelect:i,isExpanded:c,hasChildren:o}=s,m=0===r.level;return(0,a.jsxs)("div",{className:"\n      p-3 rounded-md shadow-md w-[180px]\n      ".concat(m?"bg-blue-50 border border-blue-200":"bg-white border border-gray-200","\n    "),children:[(0,a.jsx)(n.h7,{type:"source",position:n.yX.Bottom,isConnectable:t,className:"w-3 h-3 bg-gray-400"}),!m&&(0,a.jsx)(n.h7,{type:"target",position:n.yX.Top,isConnectable:t,className:"w-3 h-3 bg-gray-400"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-2 ".concat(m?"bg-blue-100":"bg-gray-100"),children:m?(0,a.jsx)(d.x$1,{className:"text-blue-500"}):(0,a.jsx)("span",{className:"text-sm font-medium",children:r.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 truncate",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate",title:r.name,children:r.name}),(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate",title:r.email,children:r.email})]})]}),(0,a.jsx)("div",{className:"text-xs px-2 py-1 rounded-full flex items-center justify-center mb-2 bg-gray-100 text-gray-800",children:(0,a.jsx)("span",{children:r.rankName})}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"ID:"}),(0,a.jsx)("span",{className:"font-medium",children:r.id})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Downline:"}),(0,a.jsx)("span",{className:"font-medium",children:r.downlineCount})]}),r.level>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Level:"}),(0,a.jsx)("span",{className:"font-medium",children:r.level})]})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-3 pt-2 border-t border-gray-200",children:[o?(0,a.jsx)("button",{onClick:l,className:"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded flex items-center hover:bg-gray-200",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Vr3,{className:"mr-1"})," Collapse"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.X6T,{className:"mr-1"})," Expand"]})}):(0,a.jsx)("div",{className:"text-xs px-2 py-1 text-gray-400",children:"No children"}),(0,a.jsxs)("button",{onClick:i,className:"text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded flex items-center hover:bg-blue-200",children:[(0,a.jsx)(d.__w,{className:"mr-1"})," Details"]})]})]})]})});var m=t(10396),x=t(15567),u=t(98369);let h={userNode:o};function g(e){let{userId:s,maxLevel:t=3}=e,[o,g,p]=(0,n.ck)([]),[b,j,f]=(0,n.fM)([]),[y,N]=(0,r.useState)(!0),[v,w]=(0,r.useState)(null),[k,S]=(0,r.useState)(null),[C,A]=(0,r.useState)(new Set),[D,E]=(0,r.useState)(!1),[B,F]=(0,r.useState)({sortBy:"createdAt",sortDirection:"desc"}),[I,L]=(0,r.useState)(""),R=(0,r.useCallback)(async()=>{N(!0),w(null);try{let e=new URLSearchParams({maxLevel:t.toString(),userId:s.toString()});B.rankId&&e.append("rankId",B.rankId.toString()),B.sortBy&&e.append("sortBy",B.sortBy),B.sortDirection&&e.append("sortDirection",B.sortDirection),I&&e.append("search",I);let a=await fetch("/api/genealogy?".concat(e.toString()));if(!a.ok)throw Error("Failed to fetch genealogy data");let r=await a.json(),{nodes:n,edges:l}=_(r);g(n),j(l)}catch(e){w(e instanceof Error?e.message:"An unknown error occurred")}finally{N(!1)}},[s,t,B,I,g,j]);(0,r.useEffect)(()=>{R()},[R]);let _=e=>{let s=[],t=[],a={id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:0,downlineCount:e._count.downline,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics};return s.push({id:a.id.toString(),type:"userNode",position:{x:0,y:0},data:{user:a,onExpand:()=>M(a.id.toString()),onSelect:()=>S(a),isExpanded:!0,hasChildren:e.children&&e.children.length>0}}),e.children&&e.children.length>0&&T(e.children,a.id.toString(),0,0,1,s,t),{nodes:s,edges:t}},T=(e,s,t,a,r,l,i)=>{let c=t-(180*e.length+(e.length-1)*30)/2+90,d=a+150;e.forEach((e,t)=>{let a={id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:r,downlineCount:e._count.downline,children:e.children,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics},o=c+210*t,m=a.id.toString();l.push({id:m,type:"userNode",position:{x:o,y:d},data:{user:a,onExpand:()=>M(m),onSelect:()=>S(a),isExpanded:C.has(m),hasChildren:e.children&&e.children.length>0}}),i.push({id:"e-".concat(s,"-").concat(m),source:s,target:m,type:"smoothstep",animated:!1,style:{stroke:"#888"},markerEnd:{type:n.TG.ArrowClosed,width:15,height:15,color:"#888"}}),e.children&&e.children.length>0&&C.has(m)&&T(e.children,m,o,d,r+1,l,i)})},M=(0,r.useCallback)(e=>{A(s=>{let t=new Set(s);return t.has(e)?t.delete(e):t.add(e),t})},[]),V=(0,r.useCallback)((e,s)=>{S(s.data.user)},[]),U=(0,r.useCallback)(e=>{L(e)},[]),X=(0,r.useCallback)(()=>{E(e=>!e)},[]),G=(0,r.useCallback)(e=>{F(e)},[]);return y&&0===o.length?(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading genealogy data..."})]}):v?(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,a.jsx)("h3",{className:"text-red-800 font-medium",children:"Error loading genealogy data"}),(0,a.jsx)("p",{className:"text-red-600",children:v})]}):(0,a.jsxs)("div",{className:"flex flex-col border border-gray-200 rounded-md",children:[(0,a.jsx)(x.A,{onRefresh:R,onSearch:U,onToggleFilters:X,showFilters:D,isLoading:y}),D&&(0,a.jsx)(u.A,{onApplyFilters:G}),(0,a.jsx)("div",{className:"h-[600px]",children:(0,a.jsxs)(n.Gc,{nodes:o,edges:b,onNodesChange:p,onEdgesChange:f,onNodeClick:V,nodeTypes:h,fitView:!0,attributionPosition:"bottom-right",connectionLineType:n.Do.SmoothStep,children:[(0,a.jsx)(l.H,{}),(0,a.jsx)(i.o,{}),(0,a.jsx)(c.V,{}),k&&(0,a.jsx)(n.Zk,{position:"top-right",className:"p-0 w-80",children:(0,a.jsx)(m.A,{user:k,onClose:()=>S(null),className:"max-h-[80vh]"})})]})})]})}},98369:(e,s,t)=>{t.d(s,{A:()=>l});var a=t(95155),r=t(12115),n=t(29911);function l(e){let{onApplyFilters:s,className:t=""}=e,[l,i]=(0,r.useState)({sortBy:"createdAt",sortDirection:"desc"}),[c,d]=(0,r.useState)([]),[o,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{m(!0);try{let e=await fetch("/api/ranks");if(!e.ok)throw Error("Failed to fetch ranks");let s=await e.json();d(s)}catch(e){console.error("Error fetching ranks:",e)}finally{m(!1)}})()},[]);let x=(e,s)=>{i(t=>({...t,[e]:s}))};return(0,a.jsxs)("div",{className:"bg-gray-50 p-4 border-b ".concat(t),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsx)("h3",{className:"font-medium",children:"Filter & Sort Options"}),(0,a.jsx)("button",{onClick:()=>{i({sortBy:"createdAt",sortDirection:"desc"}),s({sortBy:"createdAt",sortDirection:"desc"})},className:"text-sm text-blue-600 hover:text-blue-800",children:"Reset Filters"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Rank"}),(0,a.jsxs)("select",{value:l.rankId||"",onChange:e=>x("rankId",e.target.value?parseInt(e.target.value):void 0),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Ranks"}),o?(0,a.jsx)("option",{disabled:!0,children:"Loading ranks..."}):c.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort By"}),(0,a.jsxs)("select",{value:l.sortBy||"createdAt",onChange:e=>x("sortBy",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"name",children:"Name"}),(0,a.jsx)("option",{value:"createdAt",children:"Join Date"}),(0,a.jsx)("option",{value:"rank",children:"Rank"}),(0,a.jsx)("option",{value:"downlineCount",children:"Downline Count"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort Direction"}),(0,a.jsx)("button",{onClick:()=>{x("sortDirection","asc"===l.sortDirection?"desc":"asc")},className:"w-full flex items-center justify-center border border-gray-300 rounded-md px-3 py-2 hover:bg-gray-50",children:"asc"===l.sortDirection?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.HL0,{className:"mr-2"})," Ascending"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.EDF,{className:"mr-2"})," Descending"]})})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{s(l)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Apply Filters"})})]})}}}]);
(()=>{var e={};e.id=7568,e.ids=[7568],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>d,Nh:()=>c,aP:()=>l});var s=t(96330),i=t(13581),o=t(85663),a=t(55511),n=t.n(a);async function d(e){return await o.Ay.hash(e,10)}function l(){let e=n().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,i.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let i=await o.Ay.compare(e.password,t.password);if(console.log("Password valid:",i),!i)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:a,...n}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},25849:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>u});var i=t(96559),o=t(48088),a=t(37719),n=t(32190),d=t(35426),l=t(12909),c=t(31183);async function u(e){try{let r=await (0,d.getServerSession)(l.Nh);if(!r||!r.user)return n.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=await c.z.user.findUnique({where:{email:r.user.email}});if(!t)return n.NextResponse.json({error:"User not found"},{status:404});let s=new URL(e.url),i=parseInt(s.searchParams.get("limit")||"10"),o=await c.z.shareableLink.findMany({where:{userId:t.id},select:{id:!0,code:!0,productId:!0}}),a=o.map(e=>e.id),u=await c.z.linkClick.findMany({where:{linkId:{in:a}},orderBy:{createdAt:"desc"},take:i,select:{id:!0,linkId:!0,createdAt:!0}}),p=await c.z.referralCommission.findMany({where:{linkId:{in:a}},orderBy:{createdAt:"desc"},take:i,select:{id:!0,linkId:!0,productId:!0,amount:!0,createdAt:!0}}),m=[...u.map(e=>{let r=o.find(r=>r.id===e.linkId);return{id:`click_${e.id}`,type:"click",linkId:e.linkId,linkCode:r?.code||"",productId:r?.productId||null,createdAt:e.createdAt}}),...p.map(e=>{let r=o.find(r=>r.id===e.linkId);return{id:`commission_${e.id}`,type:"commission",linkId:e.linkId,linkCode:r?.code||"",productId:e.productId,amount:e.amount,createdAt:e.createdAt}})].sort((e,r)=>new Date(r.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,i),w=m.filter(e=>e.productId).map(e=>e.productId),g=await c.z.product.findMany({where:{id:{in:w}},select:{id:!0,name:!0,image:!0}}),f=m.map(e=>{if(!e.productId)return e;let r=g.find(r=>r.id===e.productId);return r?{...e,productName:r.name,productImage:r.image}:e});return n.NextResponse.json({activities:f})}catch(e){return console.error("Error fetching referral activity:",e),n.NextResponse.json({error:"Failed to fetch referral activity"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/referrals/activity/route",pathname:"/api/referrals/activity",filename:"route",bundlePath:"app/api/referrals/activity/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\referrals\\activity\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:g}=p;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(25849));module.exports=s})();
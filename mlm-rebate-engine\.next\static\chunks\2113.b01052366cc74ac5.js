"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2113],{62113:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var n=r(95155),s=r(12115);let a=e=>{let{showInProduction:t=!1}=e,[r,a]=(0,s.useState)({fps:0,memory:null,timing:{pageLoadTime:0,domReadyTime:0,networkLatency:0},resourceCount:0,renderCount:0}),[i,o]=(0,s.useState)(!1),[c,l]=(0,s.useState)(!1),m=(0,s.useRef)(0),d=(0,s.useRef)(performance.now()),u=(0,s.useRef)(0);if(u.current+=1,(0,s.useEffect)(()=>{o(t)},[t]),(0,s.useEffect)(()=>{let e;if(!i)return;let t=r=>{m.current+=1;let n=r-d.current;if(n>=1e3){let e=Math.round(1e3*m.current/n);a(t=>({...t,fps:e,renderCount:u.current})),m.current=0,d.current=r}e=requestAnimationFrame(t)};return e=requestAnimationFrame(t),()=>{cancelAnimationFrame(e)}},[i]),(0,s.useEffect)(()=>{if(!i)return;let e=setInterval(()=>{if("memory"in performance){let e=performance.memory;a(t=>({...t,memory:{usedJSHeapSize:e.usedJSHeapSize,totalJSHeapSize:e.totalJSHeapSize,jsHeapSizeLimit:e.jsHeapSizeLimit}}))}},2e3);return()=>{clearInterval(e)}},[i]),(0,s.useEffect)(()=>{if(!i||!window.performance||!window.performance.timing)return;let e=window.performance.timing,t=e.loadEventEnd-e.navigationStart,r=e.domComplete-e.domLoading,n=e.responseEnd-e.requestStart;a(e=>({...e,timing:{pageLoadTime:t,domReadyTime:r,networkLatency:n},resourceCount:performance.getEntriesByType("resource").length}))},[i]),!i)return null;let p=e=>{if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return"".concat(parseFloat((e/Math.pow(1024,t)).toFixed(2))," ").concat(["B","KB","MB","GB"][t])};return(0,n.jsxs)("div",{className:"fixed bottom-0 right-0 z-50 bg-black bg-opacity-80 text-white p-2 text-xs font-mono rounded-tl-md transition-all duration-300 ".concat(c?"w-64":"w-auto"),children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,n.jsx)("button",{onClick:()=>l(!c),className:"text-xs hover:text-blue-300 focus:outline-none",children:c?"Performance ▼":"Perf ▶"}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{className:"inline-block w-2 h-2 rounded-full mr-1 ".concat(r.fps>50?"bg-green-500":r.fps>30?"bg-yellow-500":"bg-red-500")}),(0,n.jsxs)("span",{children:[r.fps," FPS"]})]})]}),c&&(0,n.jsx)("div",{className:"space-y-1 text-xs",children:(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-1",children:[(0,n.jsx)("span",{className:"text-gray-400",children:"Renders:"}),(0,n.jsx)("span",{children:r.renderCount}),(0,n.jsx)("span",{className:"text-gray-400",children:"Resources:"}),(0,n.jsx)("span",{children:r.resourceCount}),(0,n.jsx)("span",{className:"text-gray-400",children:"Page Load:"}),(0,n.jsxs)("span",{children:[r.timing.pageLoadTime," ms"]}),(0,n.jsx)("span",{className:"text-gray-400",children:"DOM Ready:"}),(0,n.jsxs)("span",{children:[r.timing.domReadyTime," ms"]}),(0,n.jsx)("span",{className:"text-gray-400",children:"Network:"}),(0,n.jsxs)("span",{children:[r.timing.networkLatency," ms"]}),r.memory&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:"text-gray-400",children:"Memory:"}),(0,n.jsxs)("span",{children:[p(r.memory.usedJSHeapSize)," / ",p(r.memory.totalJSHeapSize)]})]})]})})]})}}}]);
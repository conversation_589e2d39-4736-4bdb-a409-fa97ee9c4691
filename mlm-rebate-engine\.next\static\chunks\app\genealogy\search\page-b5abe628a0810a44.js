(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7017],{35695:(e,s,t)=>{"use strict";var a=t(18999);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},38739:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(95155),l=t(12115),r=t(12108),n=t(35695),i=t(29911),d=t(6874),c=t.n(d);function o(e){var s;let{onSearch:t,ranks:r,isLoading:n=!1}=e,[d,c]=(0,l.useState)(""),[o,m]=(0,l.useState)(!1),[x,u]=(0,l.useState)({}),h=e=>{e.preventDefault(),t(d,x)},b=(e,s)=>{if(""===s||void 0===s){let s={...x};delete s[e],u(s)}else u(t=>({...t,[e]:s}))},g=Object.keys(x).length;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,a.jsx)("form",{onSubmit:h,className:"mb-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search by ID, name, or email...",value:d,onChange:e=>c(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,a.jsx)(i.KSO,{})}),d&&(0,a.jsx)("button",{type:"button",onClick:()=>c(""),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(i.QCr,{})})]}),(0,a.jsx)("button",{type:"submit",disabled:n,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:n?"Searching...":"Search"})]})}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>m(!o),className:"flex items-center text-sm text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(i.YsJ,{className:"mr-1"}),"Advanced Filters",o?(0,a.jsx)(i.Ucs,{className:"ml-1"}):(0,a.jsx)(i.Vr3,{className:"ml-1"}),g>0&&(0,a.jsx)("span",{className:"ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs",children:g})]}),g>0&&(0,a.jsx)("button",{type:"button",onClick:()=>{u({})},className:"text-sm text-red-600 hover:text-red-800",children:"Clear All Filters"})]}),o&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md mt-2 animate-fade-in",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name Contains"}),(0,a.jsx)("input",{type:"text",value:x.name||"",onChange:e=>b("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter name..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Contains"}),(0,a.jsx)("input",{type:"text",value:x.email||"",onChange:e=>b("email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter email..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,a.jsxs)("select",{value:x.rankId||"",onChange:e=>b("rankId",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Any Rank"}),r.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Downline"}),(0,a.jsx)("input",{type:"number",min:"0",value:x.minDownline||"",onChange:e=>b("minDownline",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Min"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Downline"}),(0,a.jsx)("input",{type:"number",min:"0",value:x.maxDownline||"",onChange:e=>b("maxDownline",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Max"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Balance"}),(0,a.jsx)("input",{type:"number",min:"0",value:x.minWalletBalance||"",onChange:e=>b("minWalletBalance",e.target.value?parseFloat(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Min ₱"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Balance"}),(0,a.jsx)("input",{type:"number",min:"0",value:x.maxWalletBalance||"",onChange:e=>b("maxWalletBalance",e.target.value?parseFloat(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Max ₱"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Joined After"}),(0,a.jsx)("input",{type:"date",value:x.joinedAfter||"",onChange:e=>b("joinedAfter",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Joined Before"}),(0,a.jsx)("input",{type:"date",value:x.joinedBefore||"",onChange:e=>b("joinedBefore",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),g>0&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Active Filters:"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[x.name&&(0,a.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Name: ",x.name,(0,a.jsx)("button",{onClick:()=>b("name",void 0),className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,a.jsx)(i.QCr,{size:10})})]}),x.email&&(0,a.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Email: ",x.email,(0,a.jsx)("button",{onClick:()=>b("email",void 0),className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,a.jsx)(i.QCr,{size:10})})]}),void 0!==x.rankId&&(0,a.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Rank: ",(null==(s=r.find(e=>e.id===x.rankId))?void 0:s.name)||x.rankId,(0,a.jsx)("button",{onClick:()=>b("rankId",void 0),className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,a.jsx)(i.QCr,{size:10})})]}),(void 0!==x.minDownline||void 0!==x.maxDownline)&&(0,a.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Downline: ",x.minDownline||"0"," - ",x.maxDownline||"∞",(0,a.jsx)("button",{onClick:()=>{b("minDownline",void 0),b("maxDownline",void 0)},className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,a.jsx)(i.QCr,{size:10})})]}),(void 0!==x.minWalletBalance||void 0!==x.maxWalletBalance)&&(0,a.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Balance: ₱",x.minWalletBalance||"0"," - ₱",x.maxWalletBalance||"∞",(0,a.jsx)("button",{onClick:()=>{b("minWalletBalance",void 0),b("maxWalletBalance",void 0)},className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,a.jsx)(i.QCr,{size:10})})]}),(void 0!==x.joinedAfter||void 0!==x.joinedBefore)&&(0,a.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Joined: ",x.joinedAfter||"Any"," to ",x.joinedBefore||"Now",(0,a.jsx)("button",{onClick:()=>{b("joinedAfter",void 0),b("joinedBefore",void 0)},className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,a.jsx)(i.QCr,{size:10})})]})]})]}),(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)("button",{type:"button",onClick:h,disabled:n,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:n?"Applying...":"Apply Filters"})})]})]})}function m(e){let{results:s,pagination:t,isLoading:r,onPageChange:n,onViewGenealogy:d}=e,[o,m]=(0,l.useState)(new Set),x=e=>{m(s=>{let t=new Set(s);return t.has(e)?t.delete(e):t.add(e),t})},u=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),h=e=>e?new Date(e).toLocaleDateString():"N/A";return r?(0,a.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,a.jsx)(i.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading search results..."})]}):0===s.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)("div",{className:"text-gray-500 mb-4",children:"No users found matching your search criteria."}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"Try adjusting your search filters or query."})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 border-b border-blue-100 flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"font-medium",children:["Search Results (",t.totalItems," users found)"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Page ",t.page," of ",t.totalPages]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Downline"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)(React.Fragment,{children:[(0,a.jsxs)("tr",{className:o.has(e.id)?"bg-blue-50":"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>x(e.id),className:"mr-3 text-gray-400 hover:text-gray-600",children:o.has(e.id)?(0,a.jsx)(i.Vr3,{}):(0,a.jsx)(i.X6T,{})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:e.rank.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e._count.downline}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:void 0!==e.walletBalance?u(e.walletBalance):"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:h(e.createdAt)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,a.jsx)("button",{onClick:()=>d(e.id),className:"text-blue-600 hover:text-blue-900 mr-3",children:"View Genealogy"}),(0,a.jsx)(c(),{href:"/users/".concat(e.id),className:"text-indigo-600 hover:text-indigo-900",children:"Profile"})]})]}),o.has(e.id)&&(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"px-6 py-4 bg-blue-50",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-white p-3 rounded-md shadow-sm",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"User Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"ID:"})," ",e.id]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Rank:"})," ",e.rank.name]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Upline ID:"})," ",e.uplineId||"None"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Downline Count:"})," ",e._count.downline]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Joined:"})," ",h(e.createdAt)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Wallet Balance:"})," ",void 0!==e.walletBalance?u(e.walletBalance):"N/A"]})]})]}),e.performanceMetrics?(0,a.jsxs)("div",{className:"bg-white p-3 rounded-md shadow-sm",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Performance Metrics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.AsH,{className:"text-green-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,a.jsx)("div",{className:"font-medium",children:u(e.performanceMetrics.personalSales)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.YXz,{className:"text-blue-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,a.jsx)("div",{className:"font-medium",children:u(e.performanceMetrics.teamSales)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.lcY,{className:"text-yellow-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Rebates Earned"}),(0,a.jsx)("div",{className:"font-medium",children:u(e.performanceMetrics.rebatesEarned)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.NPy,{className:"text-purple-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"New Members (30d)"}),(0,a.jsx)("div",{className:"font-medium",children:e.performanceMetrics.newTeamMembers})]})]})]})]}):(0,a.jsx)("div",{className:"bg-white p-3 rounded-md shadow-sm flex items-center justify-center text-gray-500 text-sm",children:"No performance metrics available"})]})})})]},e.id))})]})}),(0,a.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>n(t.page-1),disabled:!t.hasPreviousPage,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>n(t.page+1),disabled:!t.hasNextPage,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,a.jsx)("span",{className:"font-medium",children:(t.page-1)*t.pageSize+1})," to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(t.page*t.pageSize,t.totalItems)})," ","of ",(0,a.jsx)("span",{className:"font-medium",children:t.totalItems})," results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>n(t.page-1),disabled:!t.hasPreviousPage,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:Math.min(5,t.totalPages)},(e,s)=>{let l;return l=t.totalPages<=5||t.page<=3?s+1:t.page>=t.totalPages-2?t.totalPages-4+s:t.page-2+s,(0,a.jsx)("button",{onClick:()=>n(l),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(t.page===l?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:l},l)}),(0,a.jsxs)("button",{onClick:()=>n(t.page+1),disabled:!t.hasNextPage,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})}function x(){let{data:e,status:s}=(0,r.useSession)(),t=(0,n.useRouter)(),[d,x]=(0,l.useState)([]),[u,h]=(0,l.useState)({page:1,pageSize:20,totalItems:0,totalPages:0,hasNextPage:!1,hasPreviousPage:!1}),[b,g]=(0,l.useState)(!1),[p,f]=(0,l.useState)(null),[j,v]=(0,l.useState)(!1),[y,N]=(0,l.useState)([]);(0,l.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/ranks");if(!e.ok)throw Error("Failed to fetch ranks");let s=await e.json();N(s)}catch(e){console.error("Error fetching ranks:",e)}};"authenticated"===s&&e()},[s]);let w=async function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if("authenticated"===s){g(!0),f(null);try{let s=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,...t,page:a,pageSize:20,includePerformanceMetrics:!0})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to search genealogy")}let l=await s.json();x(l.users),h(l.pagination),v(!0)}catch(e){f(e instanceof Error?e.message:"An unknown error occurred")}finally{g(!1)}}};return"loading"===s?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(i.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})}):"unauthenticated"===s?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,a.jsx)(i.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to search the genealogy."}),(0,a.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Advanced Genealogy Search"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Search for users in your genealogy with advanced filtering options"})]}),(0,a.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(i.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(o,{onSearch:w,ranks:y,isLoading:b})}),p&&(0,a.jsxs)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:[(0,a.jsx)("p",{className:"font-medium",children:"Error"}),(0,a.jsx)("p",{children:p})]}),j?(0,a.jsx)(m,{results:d,pagination:u,isLoading:b,onPageChange:e=>{w("",{},e)},onViewGenealogy:e=>{t.push("/genealogy?userId=".concat(e))}}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)("div",{className:"text-6xl text-gray-300 mb-4",children:(0,a.jsx)(i.KSO,{className:"inline"})}),(0,a.jsx)("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"Search the Genealogy"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Use the search form above to find users in your genealogy."}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"You can search by name, email, rank, and more."})]})]})}},74436:(e,s,t)=>{"use strict";t.d(s,{k5:()=>o});var a=t(12115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=a.createContext&&a.createContext(l),n=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e}).apply(this,arguments)}function d(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);s&&(a=a.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,a)}return t}function c(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?d(Object(t),!0).forEach(function(s){var a,l,r;a=e,l=s,r=t[s],(l=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,s||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(l))in a?Object.defineProperty(a,l,{value:r,enumerable:!0,configurable:!0,writable:!0}):a[l]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):d(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function o(e){return s=>a.createElement(m,i({attr:c({},e.attr)},s),function e(s){return s&&s.map((s,t)=>a.createElement(s.tag,c({key:t},s.attr),e(s.child)))}(e.child))}function m(e){var s=s=>{var t,{attr:l,size:r,title:d}=e,o=function(e,s){if(null==e)return{};var t,a,l=function(e,s){if(null==e)return{};var t={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(s.indexOf(a)>=0)continue;t[a]=e[a]}return t}(e,s);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)t=r[a],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(l[t]=e[t])}return l}(e,n),m=r||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),a.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,l,o,{className:t,style:c(c({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),d&&a.createElement("title",null,d),e.children)};return void 0!==r?a.createElement(r.Consumer,null,e=>s(e)):s(l)}},84153:(e,s,t)=>{Promise.resolve().then(t.bind(t,38739))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,8441,1684,7358],()=>s(84153)),_N_E=e.O()}]);
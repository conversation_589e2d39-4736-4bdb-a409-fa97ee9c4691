"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6113],{6115:(t,n,e)=>{var r=e(12115),i=e(49033),o="function"==typeof Object.is?Object.is:function(t,n){return t===n&&(0!==t||1/t==1/n)||t!=t&&n!=n},u=i.useSyncExternalStore,a=r.useRef,s=r.useEffect,l=r.useMemo,c=r.useDebugValue;n.useSyncExternalStoreWithSelector=function(t,n,e,r,i){var f=a(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var p=u(t,(f=l(function(){function t(t){if(!s){if(s=!0,u=t,t=r(t),void 0!==i&&h.hasValue){var n=h.value;if(i(n,t))return a=n}return a=t}if(n=a,o(u,t))return n;var e=r(t);return void 0!==i&&i(n,e)?(u=t,n):(u=t,a=e)}var u,a,s=!1,l=void 0===e?null:e;return[function(){return t(n())},null===l?void 0:function(){return t(l())}]},[n,e,r,i]))[0],f[1]);return s(function(){h.hasValue=!0,h.value=p},[p]),c(p),p}},9393:(t,n,e)=>{e.d(n,{A:()=>i,j:()=>o});var r=e(27271);function i(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):o(this.node(),t)}function o(t,n){return t.style.getPropertyValue(n)||(0,r.A)(t).getComputedStyle(t,null).getPropertyValue(n)}},14897:(t,n,e)=>{e.d(n,{A:()=>r});function r(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}},16672:(t,n,e)=>{e.d(n,{A:()=>d});var r=e(61235),i=e(82903),o=e(14897),u=e(29204),a=e(50806);let s=t=>()=>t;function l(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:u,y:a,dx:s,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:u,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function c(t){return!t.ctrlKey&&!t.button}function f(){return this.parentNode}function h(t,n){return null==n?{x:t.x,y:t.y}:n}function p(){return navigator.maxTouchPoints||"ontouchstart"in this}function d(){var t,n,e,d,v=c,y=f,g=h,m=p,_={},w=(0,r.A)("start","drag","end"),x=0,b=0;function A(t){t.on("mousedown.drag",k).filter(m).on("touchstart.drag",S).on("touchmove.drag",N,a.vr).on("touchend.drag touchcancel.drag",z).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function k(r,o){if(!d&&v.call(this,r,o)){var s=$(this,y.call(this,r,o),r,o,"mouse");s&&((0,i.A)(r.view).on("mousemove.drag",E,a.Rw).on("mouseup.drag",M,a.Rw),(0,u.A)(r.view),(0,a.GK)(r),e=!1,t=r.clientX,n=r.clientY,s("start",r))}}function E(r){if((0,a.Ay)(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>b}_.mouse("drag",r)}function M(t){(0,i.A)(t.view).on("mousemove.drag mouseup.drag",null),(0,u.y)(t.view,e),(0,a.Ay)(t),_.mouse("end",t)}function S(t,n){if(v.call(this,t,n)){var e,r,i=t.changedTouches,o=y.call(this,t,n),u=i.length;for(e=0;e<u;++e)(r=$(this,o,t,n,i[e].identifier,i[e]))&&((0,a.GK)(t),r("start",t,i[e]))}}function N(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=_[r[n].identifier])&&((0,a.Ay)(t),e("drag",t,r[n]))}function z(t){var n,e,r=t.changedTouches,i=r.length;for(d&&clearTimeout(d),d=setTimeout(function(){d=null},500),n=0;n<i;++n)(e=_[r[n].identifier])&&((0,a.GK)(t),e("end",t,r[n]))}function $(t,n,e,r,i,u){var a,s,c,f=w.copy(),h=(0,o.A)(u||e,n);if(null!=(c=g.call(t,new l("beforestart",{sourceEvent:e,target:A,identifier:i,active:x,x:h[0],y:h[1],dx:0,dy:0,dispatch:f}),r)))return a=c.x-h[0]||0,s=c.y-h[1]||0,function e(u,p,d){var v,y=h;switch(u){case"start":_[i]=e,v=x++;break;case"end":delete _[i],--x;case"drag":h=(0,o.A)(d||p,n),v=x}f.call(u,t,new l(u,{sourceEvent:p,subject:c,target:A,identifier:i,active:v,x:h[0]+a,y:h[1]+s,dx:h[0]-y[0],dy:h[1]-y[1],dispatch:f}),r)}}return A.filter=function(t){return arguments.length?(v="function"==typeof t?t:s(!!t),A):v},A.container=function(t){return arguments.length?(y="function"==typeof t?t:s(t),A):y},A.subject=function(t){return arguments.length?(g="function"==typeof t?t:s(t),A):g},A.touchable=function(t){return arguments.length?(m="function"==typeof t?t:s(!!t),A):m},A.on=function(){var t=w.on.apply(w,arguments);return t===w?A:t},A.clickDistance=function(t){return arguments.length?(b=(t*=1)*t,A):Math.sqrt(b)},A}l.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t}},22436:(t,n,e)=>{var r=e(12115),i="function"==typeof Object.is?Object.is:function(t,n){return t===n&&(0!==t||1/t==1/n)||t!=t&&n!=n},o=r.useState,u=r.useEffect,a=r.useLayoutEffect,s=r.useDebugValue;function l(t){var n=t.getSnapshot;t=t.value;try{var e=n();return!i(t,e)}catch(t){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,n){return n()}:function(t,n){var e=n(),r=o({inst:{value:e,getSnapshot:n}}),i=r[0].inst,c=r[1];return a(function(){i.value=e,i.getSnapshot=n,l(i)&&c({inst:i})},[t,e,n]),u(function(){return l(i)&&c({inst:i}),t(function(){l(i)&&c({inst:i})})},[t]),s(e),e};n.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},27271:(t,n,e)=>{e.d(n,{A:()=>r});function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}},29204:(t,n,e)=>{e.d(n,{A:()=>o,y:()=>u});var r=e(82903),i=e(50806);function o(t){var n=t.document.documentElement,e=(0,r.A)(t).on("dragstart.drag",i.Ay,i.Rw);"onselectstart"in n?e.on("selectstart.drag",i.Ay,i.Rw):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function u(t,n){var e=t.document.documentElement,o=(0,r.A)(t).on("dragstart.drag",null);n&&(o.on("click.drag",i.Ay,i.Rw),setTimeout(function(){o.on("click.drag",null)},0)),"onselectstart"in e?o.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}},33036:(t,n,e)=>{e.d(n,{h:()=>h,n:()=>c});var r=e(12115),i=e(45643);let o=t=>{let n,e=new Set,r=(t,r)=>{let i="function"==typeof t?t(n):t;if(!Object.is(i,n)){let t=n;n=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},n,i),e.forEach(e=>e(n,t))}},i=()=>n,o={setState:r,getState:i,getInitialState:()=>u,subscribe:t=>(e.add(t),()=>e.delete(t)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),e.clear()}},u=n=t(r,i,o);return o},u=t=>t?o(t):o,{useDebugValue:a}=r,{useSyncExternalStoreWithSelector:s}=i,l=t=>t;function c(t,n=l,e){let r=s(t.subscribe,t.getState,t.getServerState||t.getInitialState,n,e);return a(r),r}let f=(t,n)=>{let e=u(t),r=(t,r=n)=>c(e,t,r);return Object.assign(r,e),r},h=(t,n)=>t?f(t,n):f},35131:(t,n,e)=>{e.d(n,{A:()=>i,g:()=>r});var r="http://www.w3.org/1999/xhtml";let i={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},45643:(t,n,e)=>{t.exports=e(6115)},49033:(t,n,e)=>{t.exports=e(22436)},50806:(t,n,e)=>{e.d(n,{Ay:()=>u,GK:()=>o,Rw:()=>i,vr:()=>r});let r={passive:!1},i={capture:!0,passive:!1};function o(t){t.stopImmediatePropagation()}function u(t){t.preventDefault(),t.stopImmediatePropagation()}},60394:(t,n,e)=>{function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}e.d(n,{A:()=>i})},61235:(t,n,e)=>{e.d(n,{A:()=>a});var r={value:()=>{}};function i(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function u(t,n,e){for(var i=0,o=t.length;i<o;++i)if(t[i].name===n){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=e&&t.push({name:n,value:e}),t}o.prototype=i.prototype={constructor:o,on:function(t,n){var e,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:n}}),o=-1,a=i.length;if(arguments.length<2){for(;++o<a;)if((e=(t=i[o]).type)&&(e=function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(r[e],t.name)))return e;return}if(null!=n&&"function"!=typeof n)throw Error("invalid callback: "+n);for(;++o<a;)if(e=(t=i[o]).type)r[e]=u(r[e],t.name,n);else if(null==n)for(e in r)r[e]=u(r[e],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new o(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,e=r.length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};let a=i},66102:(t,n,e)=>{e.d(n,{A:()=>i});var r=e(35131);function i(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),r.A.hasOwnProperty(n)?{space:r.A[n],local:t}:t}},69293:(t,n,e)=>{function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}e.d(n,{A:()=>i})},74498:(t,n,e)=>{e.d(n,{LN:()=>R,Ay:()=>D,zr:()=>q});var r=e(60394),i=e(69293),o=e(83875),u=Array.prototype.find;function a(){return this.firstElementChild}var s=Array.prototype.filter;function l(){return Array.from(this.children)}function c(t){return Array(t.length)}function f(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function h(t,n,e,r,i,o){for(var u,a=0,s=n.length,l=o.length;a<l;++a)(u=n[a])?(u.__data__=o[a],r[a]=u):e[a]=new f(t,o[a]);for(;a<s;++a)(u=n[a])&&(i[a]=u)}function p(t,n,e,r,i,o,u){var a,s,l,c=new Map,h=n.length,p=o.length,d=Array(h);for(a=0;a<h;++a)(s=n[a])&&(d[a]=l=u.call(s,s.__data__,a,n)+"",c.has(l)?i[a]=s:c.set(l,s));for(a=0;a<p;++a)l=u.call(t,o[a],a,o)+"",(s=c.get(l))?(r[a]=s,s.__data__=o[a],c.delete(l)):e[a]=new f(t,o[a]);for(a=0;a<h;++a)(s=n[a])&&c.get(d[a])===s&&(i[a]=s)}function d(t){return t.__data__}function v(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}f.prototype={constructor:f,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var y=e(66102),g=e(9393);function m(t){return t.trim().split(/^|\s+/)}function _(t){return t.classList||new w(t)}function w(t){this._node=t,this._names=m(t.getAttribute("class")||"")}function x(t,n){for(var e=_(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function b(t,n){for(var e=_(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function A(){this.textContent=""}function k(){this.innerHTML=""}function E(){this.nextSibling&&this.parentNode.appendChild(this)}function M(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}w.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var S=e(35131);function N(t){var n=(0,y.A)(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===S.g&&n.documentElement.namespaceURI===S.g?n.createElement(t):n.createElementNS(e,t)}})(n)}function z(){return null}function $(){var t=this.parentNode;t&&t.removeChild(this)}function T(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function j(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function O(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)(e=n[r],t.type&&e.type!==t.type||e.name!==t.name)?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function C(t,n,e){return function(){var r,i=this.__on,o=function(t){n.call(this,t,this.__data__)};if(i){for(var u=0,a=i.length;u<a;++u)if((r=i[u]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),r.value=n;return}}this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}var P=e(27271);function X(t,n,e){var r=(0,P.A)(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}var q=[null];function R(t,n){this._groups=t,this._parents=n}function Y(){return new R([[document.documentElement]],q)}R.prototype=Y.prototype={constructor:R,select:function(t){"function"!=typeof t&&(t=(0,r.A)(t));for(var n=this._groups,e=n.length,i=Array(e),o=0;o<e;++o)for(var u,a,s=n[o],l=s.length,c=i[o]=Array(l),f=0;f<l;++f)(u=s[f])&&(a=t.call(u,u.__data__,f,s))&&("__data__"in u&&(a.__data__=u.__data__),c[f]=a);return new R(i,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){var t;return t=n.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=(0,i.A)(t);for(var e=this._groups,r=e.length,o=[],u=[],a=0;a<r;++a)for(var s,l=e[a],c=l.length,f=0;f<c;++f)(s=l[f])&&(o.push(t.call(s,s.__data__,f,l)),u.push(s));return new R(o,u)},selectChild:function(t){var n;return this.select(null==t?a:(n="function"==typeof t?t:(0,o.j)(t),function(){return u.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?l:(n="function"==typeof t?t:(0,o.j)(t),function(){return s.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=(0,o.A)(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var u,a=n[i],s=a.length,l=r[i]=[],c=0;c<s;++c)(u=a[c])&&t.call(u,u.__data__,c,a)&&l.push(u);return new R(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,d);var e=n?p:h,r=this._parents,i=this._groups;"function"!=typeof t&&(x=t,t=function(){return x});for(var o=i.length,u=Array(o),a=Array(o),s=Array(o),l=0;l<o;++l){var c=r[l],f=i[l],v=f.length,y="object"==typeof(w=t.call(c,c&&c.__data__,l,r))&&"length"in w?w:Array.from(w),g=y.length,m=a[l]=Array(g),_=u[l]=Array(g);e(c,f,m,_,s[l]=Array(v),y,n);for(var w,x,b,A,k=0,E=0;k<g;++k)if(b=m[k]){for(k>=E&&(E=k+1);!(A=_[E])&&++E<g;);b._next=A||null}}return(u=new R(u,r))._enter=a,u._exit=s,u},enter:function(){return new R(this._enter||this._groups.map(c),this._parents)},exit:function(){return new R(this._exit||this._groups.map(c),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,u=Math.min(i,o),a=Array(i),s=0;s<u;++s)for(var l,c=e[s],f=r[s],h=c.length,p=a[s]=Array(h),d=0;d<h;++d)(l=c[d]||f[d])&&(p[d]=l);for(;s<i;++s)a[s]=e[s];return new R(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=v);for(var e=this._groups,r=e.length,i=Array(r),o=0;o<r;++o){for(var u,a=e[o],s=a.length,l=i[o]=Array(s),c=0;c<s;++c)(u=a[c])&&(l[c]=u);l.sort(n)}return new R(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=(0,y.A)(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:g.A,property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=m(t+"");if(arguments.length<2){for(var r=_(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?x:b)(this,t)}}:n?function(t){return function(){x(this,t)}}:function(t){return function(){b(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?A:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?k:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(E)},lower:function(){return this.each(M)},append:function(t){var n="function"==typeof t?t:N(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:N(t),i=null==n?z:"function"==typeof n?n:(0,r.A)(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each($)},clone:function(t){return this.select(t?j:T)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),u=o.length;if(arguments.length<2){var a=this.node().__on;if(a){for(var s,l=0,c=a.length;l<c;++l)for(r=0,s=a[l];r<u;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value}return}for(r=0,a=n?C:O;r<u;++r)this.each(a(o[r],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return X(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return X(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,u=i.length;o<u;++o)(r=i[o])&&(yield r)}};let D=Y},75694:(t,n,e)=>{e.d(n,{A:()=>function t(n){if("string"==typeof n||"number"==typeof n)return""+n;let e="";if(Array.isArray(n))for(let r=0,i;r<n.length;r++)""!==(i=t(n[r]))&&(e+=(e&&" ")+i);else for(let t in n)n[t]&&(e+=(e&&" ")+t);return e}})},82903:(t,n,e)=>{e.d(n,{A:()=>i});var r=e(74498);function i(t){return"string"==typeof t?new r.LN([[document.querySelector(t)]],[document.documentElement]):new r.LN([[t]],r.zr)}},83875:(t,n,e)=>{function r(t){return function(){return this.matches(t)}}function i(t){return function(n){return n.matches(t)}}e.d(n,{A:()=>r,j:()=>i})},92816:(t,n,e)=>{function r(t,n){if(Object.is(t,n))return!0;if("object"!=typeof t||null===t||"object"!=typeof n||null===n)return!1;if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(let[e,r]of t)if(!Object.is(r,n.get(e)))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(let e of t)if(!n.has(e))return!1;return!0}let e=Object.keys(t);if(e.length!==Object.keys(n).length)return!1;for(let r of e)if(!Object.prototype.hasOwnProperty.call(n,r)||!Object.is(t[r],n[r]))return!1;return!0}e.d(n,{x:()=>r})},93219:(t,n,e)=>{e.d(n,{s_:()=>tZ,GS:()=>tB});var r,i=e(61235),o=e(29204);function u(t){return((t=Math.exp(t))+1/t)/2}let a=function t(n,e,r){function i(t,i){var o,a,s=t[0],l=t[1],c=t[2],f=i[0],h=i[1],p=i[2],d=f-s,v=h-l,y=d*d+v*v;if(y<1e-12)a=Math.log(p/c)/n,o=function(t){return[s+t*d,l+t*v,c*Math.exp(n*t*a)]};else{var g=Math.sqrt(y),m=(p*p-c*c+r*y)/(2*c*e*g),_=(p*p-c*c-r*y)/(2*p*e*g),w=Math.log(Math.sqrt(m*m+1)-m);a=(Math.log(Math.sqrt(_*_+1)-_)-w)/n,o=function(t){var r,i,o=t*a,f=u(w),h=c/(e*g)*(f*(((r=Math.exp(2*(r=n*o+w)))-1)/(r+1))-((i=Math.exp(i=w))-1/i)/2);return[s+h*d,l+h*v,c*f/u(n*o+w)]}}return o.duration=1e3*a*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4);var s,l,c=e(82903),f=e(14897),h=e(74498),p=0,d=0,v=0,y=0,g=0,m=0,_="object"==typeof performance&&performance.now?performance:Date,w="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function x(){return g||(w(b),g=_.now()+m)}function b(){g=0}function A(){this._call=this._time=this._next=null}function k(t,n,e){var r=new A;return r.restart(t,n,e),r}function E(){g=(y=_.now())+m,p=d=0;try{x(),++p;for(var t,n=s;n;)(t=g-n._time)>=0&&n._call.call(void 0,t),n=n._next;--p}finally{p=0,function(){for(var t,n,e=s,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:s=n);l=t,S(r)}(),g=0}}function M(){var t=_.now(),n=t-y;n>1e3&&(m-=n,y=t)}function S(t){!p&&(d&&(d=clearTimeout(d)),t-g>24?(t<1/0&&(d=setTimeout(E,t-_.now()-m)),v&&(v=clearInterval(v))):(v||(y=_.now(),v=setInterval(M,1e3)),p=1,w(E)))}function N(t,n,e){var r=new A;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}A.prototype=k.prototype={constructor:A,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?x():+e)+(null==n?0:+n),this._next||l===this||(l?l._next=this:s=this,l=this),this._call=t,this._time=e,S()},stop:function(){this._call&&(this._call=null,this._time=1/0,S())}};var z=(0,i.A)("start","end","cancel","interrupt"),$=[];function T(t,n,e,r,i,o){var u=t.__transition;if(u){if(e in u)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(s){var l,c,f,h;if(1!==e.state)return a();for(l in i)if((h=i[l]).name===e.name){if(3===h.state)return N(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[l]):+l<n&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[l])}if(N(function(){3===e.state&&(e.state=4,e.timer.restart(u,e.delay,e.time),u(s))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(l=0,e.state=3,r=Array(f=e.tween.length),c=-1;l<f;++l)(h=e.tween[l].value.call(t,t.__data__,e.index,e.group))&&(r[++c]=h);r.length=c+1}}function u(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(a),e.state=5,1),o=-1,u=r.length;++o<u;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),a())}function a(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=k(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:z,tween:$,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function j(t,n){var e=C(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function O(t,n){var e=C(t,n);if(e.state>3)throw Error("too late; already running");return e}function C(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function P(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o){if((e=o[i]).name!==n){u=!1;continue}r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]}u&&delete t.__transition}}function X(t,n){return t*=1,n*=1,function(e){return t*(1-e)+n*e}}var q=180/Math.PI,R={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Y(t,n,e,r,i,o){var u,a,s;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(s=t*e+n*r)&&(e-=t*s,r-=n*s),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,s/=a),t*r<n*e&&(t=-t,n=-n,s=-s,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*q,skewX:Math.atan(s)*q,scaleX:u,scaleY:a}}function D(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,u){var a,s,l,c,f=[],h=[];return o=t(o),u=t(u),!function(t,r,i,o,u,a){if(t!==i||r!==o){var s=u.push("translate(",null,n,null,e);a.push({i:s-4,x:X(t,i)},{i:s-2,x:X(r,o)})}else(i||o)&&u.push("translate("+i+n+o+e)}(o.translateX,o.translateY,u.translateX,u.translateY,f,h),a=o.rotate,s=u.rotate,a!==s?(a-s>180?s+=360:s-a>180&&(a+=360),h.push({i:f.push(i(f)+"rotate(",null,r)-2,x:X(a,s)})):s&&f.push(i(f)+"rotate("+s+r),l=o.skewX,c=u.skewX,l!==c?h.push({i:f.push(i(f)+"skewX(",null,r)-2,x:X(l,c)}):c&&f.push(i(f)+"skewX("+c+r),!function(t,n,e,r,o,u){if(t!==e||n!==r){var a=o.push(i(o)+"scale(",null,",",null,")");u.push({i:a-4,x:X(t,e)},{i:a-2,x:X(n,r)})}else(1!==e||1!==r)&&o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,u.scaleX,u.scaleY,f,h),o=u=null,function(t){for(var n,e=-1,r=h.length;++e<r;)f[(n=h[e]).i]=n.x(t);return f.join("")}}}var I=D(function(t){let n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?R:Y(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),V=D(function(t){return null==t?R:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?Y((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):R},", ",")",")"),L=e(66102);function B(t,n,e){var r=t._id;return t.each(function(){var t=O(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return C(t,r).value[n]}}function H(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function G(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function K(){}var U="\\s*([+-]?\\d+)\\s*",W="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",F="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Q=/^#([0-9a-f]{3,8})$/,J=RegExp(`^rgb\\(${U},${U},${U}\\)$`),Z=RegExp(`^rgb\\(${F},${F},${F}\\)$`),tt=RegExp(`^rgba\\(${U},${U},${U},${W}\\)$`),tn=RegExp(`^rgba\\(${F},${F},${F},${W}\\)$`),te=RegExp(`^hsl\\(${W},${F},${F}\\)$`),tr=RegExp(`^hsla\\(${W},${F},${F},${W}\\)$`),ti={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function to(){return this.rgb().formatHex()}function tu(){return this.rgb().formatRgb()}function ta(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=Q.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?ts(n):3===e?new tf(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?tl(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?tl(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=J.exec(t))?new tf(n[1],n[2],n[3],1):(n=Z.exec(t))?new tf(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=tt.exec(t))?tl(n[1],n[2],n[3],n[4]):(n=tn.exec(t))?tl(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=te.exec(t))?tg(n[1],n[2]/100,n[3]/100,1):(n=tr.exec(t))?tg(n[1],n[2]/100,n[3]/100,n[4]):ti.hasOwnProperty(t)?ts(ti[t]):"transparent"===t?new tf(NaN,NaN,NaN,0):null}function ts(t){return new tf(t>>16&255,t>>8&255,255&t,1)}function tl(t,n,e,r){return r<=0&&(t=n=e=NaN),new tf(t,n,e,r)}function tc(t,n,e,r){var i;return 1==arguments.length?((i=t)instanceof K||(i=ta(i)),i)?new tf((i=i.rgb()).r,i.g,i.b,i.opacity):new tf:new tf(t,n,e,null==r?1:r)}function tf(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function th(){return`#${ty(this.r)}${ty(this.g)}${ty(this.b)}`}function tp(){let t=td(this.opacity);return`${1===t?"rgb(":"rgba("}${tv(this.r)}, ${tv(this.g)}, ${tv(this.b)}${1===t?")":`, ${t})`}`}function td(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tv(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function ty(t){return((t=tv(t))<16?"0":"")+t.toString(16)}function tg(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new t_(t,n,e,r)}function tm(t){if(t instanceof t_)return new t_(t.h,t.s,t.l,t.opacity);if(t instanceof K||(t=ta(t)),!t)return new t_;if(t instanceof t_)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),u=NaN,a=o-i,s=(o+i)/2;return a?(u=n===o?(e-r)/a+(e<r)*6:e===o?(r-n)/a+2:(n-e)/a+4,a/=s<.5?o+i:2-o-i,u*=60):a=s>0&&s<1?0:u,new t_(u,a,s,t.opacity)}function t_(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function tw(t){return(t=(t||0)%360)<0?t+360:t}function tx(t){return Math.max(0,Math.min(1,t||0))}function tb(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}function tA(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}H(K,ta,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:to,formatHex:to,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return tm(this).formatHsl()},formatRgb:tu,toString:tu}),H(tf,tc,G(K,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tf(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tf(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new tf(tv(this.r),tv(this.g),tv(this.b),td(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:th,formatHex:th,formatHex8:function(){return`#${ty(this.r)}${ty(this.g)}${ty(this.b)}${ty((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:tp,toString:tp})),H(t_,function(t,n,e,r){return 1==arguments.length?tm(t):new t_(t,n,e,null==r?1:r)},G(K,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new t_(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new t_(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new tf(tb(t>=240?t-240:t+120,i,r),tb(t,i,r),tb(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new t_(tw(this.h),tx(this.s),tx(this.l),td(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=td(this.opacity);return`${1===t?"hsl(":"hsla("}${tw(this.h)}, ${100*tx(this.s)}%, ${100*tx(this.l)}%${1===t?")":`, ${t})`}`}}));let tk=t=>()=>t;function tE(t,n){var e,r,i=n-t;return i?(e=t,r=i,function(t){return e+t*r}):tk(isNaN(t)?n:t)}let tM=function t(n){var e,r=1==(e=+n)?tE:function(t,n){var r,i,o;return n-t?(r=t,i=n,r=Math.pow(r,o=e),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):tk(isNaN(t)?n:t)};function i(t,n){var e=r((t=tc(t)).r,(n=tc(n)).r),i=r(t.g,n.g),o=r(t.b,n.b),u=tE(t.opacity,n.opacity);return function(n){return t.r=e(n),t.g=i(n),t.b=o(n),t.opacity=u(n),t+""}}return i.gamma=t,i}(1);function tS(t){return function(n){var e,r,i=n.length,o=Array(i),u=Array(i),a=Array(i);for(e=0;e<i;++e)r=tc(n[e]),o[e]=r.r||0,u[e]=r.g||0,a[e]=r.b||0;return o=t(o),u=t(u),a=t(a),r.opacity=1,function(t){return r.r=o(t),r.g=u(t),r.b=a(t),r+""}}}tS(function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],u=r>0?t[r-1]:2*i-o,a=r<n-1?t[r+2]:2*o-i;return tA((e-r/n)*n,u,i,o,a)}}),tS(function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return tA((e-r/n)*n,i,o,u,a)}});var tN=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tz=RegExp(tN.source,"g");function t$(t,n){var e;return("number"==typeof n?X:n instanceof ta?tM:(e=ta(n))?(n=e,tM):function(t,n){var e,r,i,o,u,a=tN.lastIndex=tz.lastIndex=0,s=-1,l=[],c=[];for(t+="",n+="";(i=tN.exec(t))&&(o=tz.exec(n));)(u=o.index)>a&&(u=n.slice(a,u),l[s]?l[s]+=u:l[++s]=u),(i=i[0])===(o=o[0])?l[s]?l[s]+=o:l[++s]=o:(l[++s]=null,c.push({i:s,x:X(i,o)})),a=tz.lastIndex;return a<n.length&&(u=n.slice(a),l[s]?l[s]+=u:l[++s]=u),l.length<2?c[0]?(e=c[0].x,function(t){return e(t)+""}):(r=n,function(){return r}):(n=c.length,function(t){for(var e,r=0;r<n;++r)l[(e=c[r]).i]=e.x(t);return l.join("")})})(t,n)}var tT=e(83875),tj=e(60394),tO=e(69293),tC=h.Ay.prototype.constructor,tP=e(9393);function tX(t){return function(){this.style.removeProperty(t)}}var tq=0;function tR(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}var tY=h.Ay.prototype;tR.prototype=(function(t){return(0,h.Ay)().transition(t)}).prototype={constructor:tR,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,tj.A)(t));for(var r=this._groups,i=r.length,o=Array(i),u=0;u<i;++u)for(var a,s,l=r[u],c=l.length,f=o[u]=Array(c),h=0;h<c;++h)(a=l[h])&&(s=t.call(a,a.__data__,h,l))&&("__data__"in a&&(s.__data__=a.__data__),f[h]=s,T(f[h],n,e,h,f,C(a,e)));return new tR(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,tO.A)(t));for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a)for(var s,l=r[a],c=l.length,f=0;f<c;++f)if(s=l[f]){for(var h,p=t.call(s,s.__data__,f,l),d=C(s,e),v=0,y=p.length;v<y;++v)(h=p[v])&&T(h,n,e,v,p,d);o.push(p),u.push(s)}return new tR(o,u,n,e)},selectChild:tY.selectChild,selectChildren:tY.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,tT.A)(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,s=r[i]=[],l=0;l<a;++l)(o=u[l])&&t.call(o,o.__data__,l,u)&&s.push(o);return new tR(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=Array(r),a=0;a<o;++a)for(var s,l=n[a],c=e[a],f=l.length,h=u[a]=Array(f),p=0;p<f;++p)(s=l[p]||c[p])&&(h[p]=s);for(;a<r;++a)u[a]=n[a];return new tR(u,this._parents,this._name,this._id)},selection:function(){return new tC(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++tq,r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],s=a.length,l=0;l<s;++l)if(u=a[l]){var c=C(u,n);T(u,t,e,l,a,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new tR(r,this._parents,t,e)},call:tY.call,nodes:tY.nodes,node:tY.node,size:tY.size,empty:tY.empty,each:tY.each,on:function(t,n){var e,r,i,o,u,a,s=this._id;return arguments.length<2?C(this.node(),s).on.on(t):this.each((e=s,r=t,i=n,a=(r+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?j:O,function(){var t=a(this,e),n=t.on;n!==o&&(u=(o=n).copy()).on(r,i),t.on=u}))},attr:function(t,n){var e=(0,L.A)(t),r="transform"===e?V:t$;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var u,a,s=e(this);return null==s?void this.removeAttributeNS(t.space,t.local):(u=this.getAttributeNS(t.space,t.local))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,s))}}:function(t,n,e){var r,i,o;return function(){var u,a,s=e(this);return null==s?void this.removeAttribute(t):(u=this.getAttribute(t))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,s))}})(e,r,B(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=n(r=u,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=n(r=u,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var r=(0,L.A)(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttributeNS(t.space,t.local,i.call(this,n))}),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttribute(t,i.call(this,n))}),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r,i,o,u,a,s,l,c,f,h,p,d,v,y,g,m,_,w,x,b,A,k="transform"==(t+="")?I:t$;return null==n?this.styleTween(t,(r=t,function(){var t=(0,tP.j)(this,r),n=(this.style.removeProperty(r),(0,tP.j)(this,r));return t===n?null:t===i&&n===o?u:u=k(i=t,o=n)})).on("end.style."+t,tX(t)):"function"==typeof n?this.styleTween(t,(a=t,s=B(this,"style."+t,n),function(){var t=(0,tP.j)(this,a),n=s(this),e=n+"";return null==n&&(this.style.removeProperty(a),e=n=(0,tP.j)(this,a)),t===e?null:t===l&&e===c?f:(c=e,f=k(l=t,n))})).each((h=this._id,_="end."+(m="style."+(p=t)),function(){var t=O(this,h),n=t.on,e=null==t.value[m]?g||(g=tX(p)):void 0;(n!==d||y!==e)&&(v=(d=n).copy()).on(_,y=e),t.on=v})):this.styleTween(t,(w=t,A=n+"",function(){var t=(0,tP.j)(this,w);return t===A?null:t===x?b:b=k(x=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw Error();return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=B(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){this.textContent=r.call(this,t)}),n}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=C(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=O(this,t),o=i.tween;if(o!==e){r=e=o;for(var u=0,a=r.length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw Error();return function(){var o=O(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},s=0,l=i.length;s<l;++s)if(i[s].name===n){i[s]=a;break}s===l&&i.push(a)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){j(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){j(this,t).delay=n}})(n,t)):C(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){O(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){O(this,t).duration=n}})(n,t)):C(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){O(this,t).ease=n}}(n,t)):C(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();O(this,n).ease=e}))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},s={value:function(){0==--i&&o()}};e.each(function(){var e=O(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(s)),e.on=n}),0===i&&o()})},[Symbol.iterator]:tY[Symbol.iterator]};var tD={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};h.Ay.prototype.interrupt=function(t){return this.each(function(){P(this,t)})},h.Ay.prototype.transition=function(t){var n,e;t instanceof tR?(n=t._id,t=t._name):(n=++tq,(e=tD).time=x(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],s=a.length,l=0;l<s;++l)(u=a[l])&&T(u,t,n,l,a,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error(`transition ${n} not found`);return e}(u,n));return new tR(r,this._parents,t,n)};let tI=t=>()=>t;function tV(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function tL(t,n,e){this.k=t,this.x=n,this.y=e}tL.prototype={constructor:tL,scale:function(t){return 1===t?this:new tL(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new tL(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var tB=new tL(1,0,0);function tH(t){t.stopImmediatePropagation()}function tG(t){t.preventDefault(),t.stopImmediatePropagation()}function tK(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function tU(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function tW(){return this.__zoom||tB}function tF(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function tQ(){return navigator.maxTouchPoints||"ontouchstart"in this}function tJ(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}function tZ(){var t,n,e,r=tK,u=tU,s=tJ,l=tF,h=tQ,p=[0,1/0],d=[[-1/0,-1/0],[1/0,1/0]],v=250,y=a,g=(0,i.A)("start","zoom","end"),m=0,_=10;function w(t){t.property("__zoom",tW).on("wheel.zoom",S,{passive:!1}).on("mousedown.zoom",N).on("dblclick.zoom",z).filter(h).on("touchstart.zoom",$).on("touchmove.zoom",T).on("touchend.zoom touchcancel.zoom",j).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(t,n){return(n=Math.max(p[0],Math.min(p[1],n)))===t.k?t:new tL(n,t.x,t.y)}function b(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new tL(t.k,r,i)}function A(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function k(t,n,e,r){t.on("start.zoom",function(){E(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){E(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,i=E(this,t).event(r),o=u.apply(this,t),a=null==e?A(o):"function"==typeof e?e.apply(this,t):e,s=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),l=this.__zoom,c="function"==typeof n?n.apply(this,t):n,f=y(l.invert(a).concat(s/l.k),c.invert(a).concat(s/c.k));return function(t){if(1===t)t=c;else{var n=f(t),e=s/n[2];t=new tL(e,a[0]-n[0]*e,a[1]-n[1]*e)}i.zoom(null,t)}})}function E(t,n,e){return!e&&t.__zooming||new M(t,n)}function M(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=u.apply(t,n),this.taps=0}function S(t,...n){if(r.apply(this,arguments)){var e=E(this,n).event(t),i=this.__zoom,o=Math.max(p[0],Math.min(p[1],i.k*Math.pow(2,l.apply(this,arguments)))),u=(0,f.A)(t);if(e.wheel)(e.mouse[0][0]!==u[0]||e.mouse[0][1]!==u[1])&&(e.mouse[1]=i.invert(e.mouse[0]=u)),clearTimeout(e.wheel);else{if(i.k===o)return;e.mouse=[u,i.invert(u)],P(this),e.start()}tG(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",s(b(x(i,o),e.mouse[0],e.mouse[1]),e.extent,d))}}function N(t,...n){if(!e&&r.apply(this,arguments)){var i=t.currentTarget,u=E(this,n,!0).event(t),a=(0,c.A)(t.view).on("mousemove.zoom",function(t){if(tG(t),!u.moved){var n=t.clientX-h,e=t.clientY-p;u.moved=n*n+e*e>m}u.event(t).zoom("mouse",s(b(u.that.__zoom,u.mouse[0]=(0,f.A)(t,i),u.mouse[1]),u.extent,d))},!0).on("mouseup.zoom",function(t){a.on("mousemove.zoom mouseup.zoom",null),(0,o.y)(t.view,u.moved),tG(t),u.event(t).end()},!0),l=(0,f.A)(t,i),h=t.clientX,p=t.clientY;(0,o.A)(t.view),tH(t),u.mouse=[l,this.__zoom.invert(l)],P(this),u.start()}}function z(t,...n){if(r.apply(this,arguments)){var e=this.__zoom,i=(0,f.A)(t.changedTouches?t.changedTouches[0]:t,this),o=e.invert(i),a=e.k*(t.shiftKey?.5:2),l=s(b(x(e,a),i,o),u.apply(this,n),d);tG(t),v>0?(0,c.A)(this).transition().duration(v).call(k,l,i,t):(0,c.A)(this).call(w.transform,l,i,t)}}function $(e,...i){if(r.apply(this,arguments)){var o,u,a,s,l=e.touches,c=l.length,h=E(this,i,e.changedTouches.length===c).event(e);for(tH(e),u=0;u<c;++u)a=l[u],s=[s=(0,f.A)(a,this),this.__zoom.invert(s),a.identifier],h.touch0?h.touch1||h.touch0[2]===s[2]||(h.touch1=s,h.taps=0):(h.touch0=s,o=!0,h.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(h.taps<2&&(n=s[0],t=setTimeout(function(){t=null},500)),P(this),h.start())}}function T(t,...n){if(this.__zooming){var e,r,i,o,u=E(this,n).event(t),a=t.changedTouches,l=a.length;for(tG(t),e=0;e<l;++e)r=a[e],i=(0,f.A)(r,this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var c=u.touch0[0],h=u.touch0[1],p=u.touch1[0],v=u.touch1[1],y=(y=p[0]-c[0])*y+(y=p[1]-c[1])*y,g=(g=v[0]-h[0])*g+(g=v[1]-h[1])*g;r=x(r,Math.sqrt(y/g)),i=[(c[0]+p[0])/2,(c[1]+p[1])/2],o=[(h[0]+v[0])/2,(h[1]+v[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],o=u.touch0[1]}u.zoom("touch",s(b(r,i,o),u.extent,d))}}function j(t,...r){if(this.__zooming){var i,o,u=E(this,r).event(t),a=t.changedTouches,s=a.length;for(tH(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),i=0;i<s;++i)o=a[i],u.touch0&&u.touch0[2]===o.identifier?delete u.touch0:u.touch1&&u.touch1[2]===o.identifier&&delete u.touch1;if(u.touch1&&!u.touch0&&(u.touch0=u.touch1,delete u.touch1),u.touch0)u.touch0[1]=this.__zoom.invert(u.touch0[0]);else if(u.end(),2===u.taps&&(o=(0,f.A)(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<_)){var l=(0,c.A)(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return w.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",tW),t!==i?k(t,n,e,r):i.interrupt().each(function(){E(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},w.scaleBy=function(t,n,e,r){w.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,r)},w.scaleTo=function(t,n,e,r){w.transform(t,function(){var t=u.apply(this,arguments),r=this.__zoom,i=null==e?A(t):"function"==typeof e?e.apply(this,arguments):e,o=r.invert(i),a="function"==typeof n?n.apply(this,arguments):n;return s(b(x(r,a),i,o),t,d)},e,r)},w.translateBy=function(t,n,e,r){w.transform(t,function(){return s(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),u.apply(this,arguments),d)},null,r)},w.translateTo=function(t,n,e,r,i){w.transform(t,function(){var t=u.apply(this,arguments),i=this.__zoom,o=null==r?A(t):"function"==typeof r?r.apply(this,arguments):r;return s(tB.translate(o[0],o[1]).scale(i.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,d)},r,i)},M.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=(0,c.A)(this.that).datum();g.call(t,this.that,new tV(t,{sourceEvent:this.sourceEvent,target:w,type:t,transform:this.that.__zoom,dispatch:g}),n)}},w.wheelDelta=function(t){return arguments.length?(l="function"==typeof t?t:tI(+t),w):l},w.filter=function(t){return arguments.length?(r="function"==typeof t?t:tI(!!t),w):r},w.touchable=function(t){return arguments.length?(h="function"==typeof t?t:tI(!!t),w):h},w.extent=function(t){return arguments.length?(u="function"==typeof t?t:tI([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),w):u},w.scaleExtent=function(t){return arguments.length?(p[0]=+t[0],p[1]=+t[1],w):[p[0],p[1]]},w.translateExtent=function(t){return arguments.length?(d[0][0]=+t[0][0],d[1][0]=+t[1][0],d[0][1]=+t[0][1],d[1][1]=+t[1][1],w):[[d[0][0],d[0][1]],[d[1][0],d[1][1]]]},w.constrain=function(t){return arguments.length?(s=t,w):s},w.duration=function(t){return arguments.length?(v=+t,w):v},w.interpolate=function(t){return arguments.length?(y=t,w):y},w.on=function(){var t=g.on.apply(g,arguments);return t===g?w:t},w.clickDistance=function(t){return arguments.length?(m=(t*=1)*t,w):Math.sqrt(m)},w.tapDistance=function(t){return arguments.length?(_=+t,w):_},w}tL.prototype}}]);
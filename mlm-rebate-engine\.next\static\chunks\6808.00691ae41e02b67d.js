(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6808],{10396:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(95155),r=s(29911);function l(e){let{data:t,className:s=""}=e,l=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return(0,a.jsxs)("div",{className:"space-y-4 ".concat(s),children:[(0,a.jsxs)("h3",{className:"text-md font-semibold flex items-center",children:[(0,a.jsx)(r.YYR,{className:"mr-2 text-blue-500"})," Performance Metrics"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(r.AsH,{className:"text-blue-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Personal Sales"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold",children:l(t.personalSales)})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(r.YXz,{className:"text-green-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Team Sales"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold",children:l(t.teamSales)})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(r.lcY,{className:"text-yellow-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Rebates Earned"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold",children:l(t.rebatesEarned)})]}),(0,a.jsxs)("div",{className:"bg-purple-50 p-3 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center mb-1",children:[(0,a.jsx)(r.v$b,{className:"text-purple-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Activity Score"})]}),(0,a.jsxs)("div",{className:"text-lg font-semibold",children:[t.activityScore,"/100"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Team Metrics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Team Size"}),(0,a.jsxs)("div",{className:"font-medium",children:[t.teamSize," members"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"New Members (30d)"}),(0,a.jsxs)("div",{className:"font-medium",children:[t.newTeamMembers," members"]})]})]})]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Activity Score"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[t.activityScore,"%"]})]}),(0,a.jsx)("div",{className:"h-2 bg-gray-200 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full rounded-full ".concat(t.activityScore>75?"bg-green-500":t.activityScore>50?"bg-blue-500":t.activityScore>25?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(t.activityScore,"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsx)("span",{children:"Low"}),(0,a.jsx)("span",{children:"Medium"}),(0,a.jsx)("span",{children:"High"})]})]})]})}function n(e){var t;let s,{user:n,onClose:i,className:c=""}=e;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-y-auto ".concat(c),children:[(0,a.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center bg-gray-50",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"User Details"}),(0,a.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(r.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:n.name.charAt(0).toUpperCase()}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:n.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:n.email}),(0,a.jsx)("div",{className:"mt-1 text-xs px-2 py-0.5 inline-block rounded-full bg-blue-100 text-blue-800",children:n.rankName})]})]}),(0,a.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.AWX,{className:"mt-1 mr-3 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"User ID"}),(0,a.jsx)("div",{children:n.id})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.M5n,{className:"mt-1 mr-3 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Downline Members"}),(0,a.jsx)("div",{children:n.downlineCount})]})]}),void 0!==n.walletBalance&&(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.AWX,{className:"mt-1 mr-3 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Wallet Balance"}),(0,a.jsx)("div",{children:(s=n.walletBalance,new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(s))})]})]}),n.createdAt&&(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.bfZ,{className:"mt-1 mr-3 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Member Since"}),(0,a.jsx)("div",{children:(t=n.createdAt)?new Date(t).toLocaleDateString():"N/A"})]})]}),n.level>0&&(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.M5n,{className:"mt-1 mr-3 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Network Level"}),(0,a.jsxs)("div",{children:["Level ",n.level]})]})]})]}),n.performanceMetrics&&(0,a.jsx)(l,{data:n.performanceMetrics}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{className:"px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm",children:"View Full Profile"}),(0,a.jsx)("button",{className:"px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm",children:"Send Message"})]})})]})]})}},11687:()=>{},57916:(e,t,s)=>{"use strict";s.d(t,{V:()=>v});var a,r=s(12115),l=s(75694),n=s(74211),i=s(92816);function c({color:e,dimensions:t,lineWidth:s}){return r.createElement("path",{stroke:e,strokeWidth:s,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function m({color:e,radius:t}){return r.createElement("circle",{cx:t,cy:t,r:t,fill:e})}!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(a||(a={}));let o={[a.Dots]:"#91919a",[a.Lines]:"#eee",[a.Cross]:"#e2e2e2"},d={[a.Dots]:1,[a.Lines]:1,[a.Cross]:6},x=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function h({id:e,variant:t=a.Dots,gap:s=20,size:h,lineWidth:v=1,offset:u=2,color:g,style:f,className:N}){let j=(0,r.useRef)(null),{transform:b,patternId:p}=(0,n.Pj)(x,i.x),w=g||o[t],y=h||d[t],E=t===a.Dots,M=t===a.Cross,k=Array.isArray(s)?s:[s,s],_=[k[0]*b[2]||1,k[1]*b[2]||1],C=y*b[2],S=M?[C,C]:_,z=E?[C/u,C/u]:[S[0]/u,S[1]/u];return r.createElement("svg",{className:(0,l.A)(["react-flow__background",N]),style:{...f,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:j,"data-testid":"rf__background"},r.createElement("pattern",{id:p+e,x:b[0]%_[0],y:b[1]%_[1],width:_[0],height:_[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${z[0]},-${z[1]})`},E?r.createElement(m,{color:w,radius:C/u}):r.createElement(c,{dimensions:S,color:w,lineWidth:v})),r.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${p+e})`}))}h.displayName="Background";var v=(0,r.memo)(h)},77581:(e,t,s)=>{"use strict";s.d(t,{o:()=>N});var a=s(12115),r=s(75694),l=s(92816),n=s(93219),i=s(82903),c=s(14897),m=s(74211);let o=({id:e,x:t,y:s,width:l,height:n,style:i,color:c,strokeColor:m,strokeWidth:o,className:d,borderRadius:x,shapeRendering:h,onClick:v,selected:u})=>{let{background:g,backgroundColor:f}=i||{};return a.createElement("rect",{className:(0,r.A)(["react-flow__minimap-node",{selected:u},d]),x:t,y:s,rx:x,ry:x,width:l,height:n,fill:c||g||f,stroke:m,strokeWidth:o,shapeRendering:h,onClick:v?t=>v(t,e):void 0})};o.displayName="MiniMapNode";var d=(0,a.memo)(o);let x=e=>e.nodeOrigin,h=e=>e.getNodes().filter(e=>!e.hidden&&e.width&&e.height),v=e=>e instanceof Function?e:()=>e;var u=(0,a.memo)(function({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:s="",nodeBorderRadius:r=5,nodeStrokeWidth:n=2,nodeComponent:i=d,onClick:c}){let o=(0,m.Pj)(h,l.x),u=(0,m.Pj)(x),g=v(t),f=v(e),N=v(s),j="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return a.createElement(a.Fragment,null,o.map(e=>{let{x:t,y:s}=(0,m.Cz)(e,u).positionAbsolute;return a.createElement(i,{key:e.id,x:t,y:s,width:e.width,height:e.height,style:e.style,selected:e.selected,className:N(e),color:g(e),borderRadius:r,strokeColor:f(e),strokeWidth:n,shapeRendering:j,onClick:c,id:e.id})}))});let g=e=>{let t=e.getNodes(),s={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:s,boundingRect:t.length>0?(0,m.Mi)((0,m.Jo)(t,e.nodeOrigin),s):s,rfId:e.rfId}};function f({style:e,className:t,nodeStrokeColor:s="transparent",nodeColor:o="#e2e2e2",nodeClassName:d="",nodeBorderRadius:x=5,nodeStrokeWidth:h=2,nodeComponent:v,maskColor:f="rgb(240, 240, 240, 0.6)",maskStrokeColor:N="none",maskStrokeWidth:j=1,position:b="bottom-right",onClick:p,onNodeClick:w,pannable:y=!1,zoomable:E=!1,ariaLabel:M="React Flow mini map",inversePan:k=!1,zoomStep:_=10,offsetScale:C=5}){let S=(0,m.PI)(),z=(0,a.useRef)(null),{boundingRect:A,viewBB:$,rfId:H}=(0,m.Pj)(g,l.x),P=e?.width??200,V=e?.height??150,D=Math.max(A.width/P,A.height/V),R=D*P,B=D*V,I=C*D,F=A.x-(R-A.width)/2-I,W=A.y-(B-A.height)/2-I,L=R+2*I,Z=B+2*I,T=`react-flow__minimap-desc-${H}`,Y=(0,a.useRef)(0);Y.current=D,(0,a.useEffect)(()=>{if(z.current){let e=(0,i.A)(z.current),t=(0,n.s_)().on("zoom",y?e=>{let{transform:t,d3Selection:s,d3Zoom:a,translateExtent:r,width:l,height:i}=S.getState();if("mousemove"!==e.sourceEvent.type||!s||!a)return;let c=Y.current*Math.max(1,t[2])*(k?-1:1),m={x:t[0]-e.sourceEvent.movementX*c,y:t[1]-e.sourceEvent.movementY*c},o=n.GS.translate(m.x,m.y).scale(t[2]),d=a.constrain()(o,[[0,0],[l,i]],r);a.transform(s,d)}:null).on("zoom.wheel",E?e=>{let{transform:t,d3Selection:s,d3Zoom:a}=S.getState();if("wheel"!==e.sourceEvent.type||!s||!a)return;let r=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*_,l=t[2]*Math.pow(2,r);a.scaleTo(s,l)}:null);return e.call(t),()=>{e.on("zoom",null)}}},[y,E,k,_]);let U=p?e=>{let t=(0,c.A)(e);p(e,{x:t[0],y:t[1]})}:void 0,X=w?(e,t)=>{w(e,S.getState().nodeInternals.get(t))}:void 0;return a.createElement(m.Zk,{position:b,style:e,className:(0,r.A)(["react-flow__minimap",t]),"data-testid":"rf__minimap"},a.createElement("svg",{width:P,height:V,viewBox:`${F} ${W} ${L} ${Z}`,role:"img","aria-labelledby":T,ref:z,onClick:U},M&&a.createElement("title",{id:T},M),a.createElement(u,{onClick:X,nodeColor:o,nodeStrokeColor:s,nodeBorderRadius:x,nodeClassName:d,nodeStrokeWidth:h,nodeComponent:v}),a.createElement("path",{className:"react-flow__minimap-mask",d:`M${F-I},${W-I}h${L+2*I}v${Z+2*I}h${-L-2*I}z
        M${$.x},${$.y}h${$.width}v${$.height}h${-$.width}z`,fill:f,fillRule:"evenodd",stroke:N,strokeWidth:j,pointerEvents:"none"})))}f.displayName="MiniMap";var N=(0,a.memo)(f)},93306:(e,t,s)=>{"use strict";s.d(t,{H:()=>u});var a=s(12115),r=s(75694),l=s(92816),n=s(74211);function i(){return a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},a.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function c(){return a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},a.createElement("path",{d:"M0 0h32v4.2H0z"}))}function m(){return a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},a.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function o(){return a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},a.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function d(){return a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},a.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}let x=({children:e,className:t,...s})=>a.createElement("button",{type:"button",className:(0,r.A)(["react-flow__controls-button",t]),...s},e);x.displayName="ControlButton";let h=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),v=({style:e,showZoom:t=!0,showFitView:s=!0,showInteractive:v=!0,fitViewOptions:u,onZoomIn:g,onZoomOut:f,onFitView:N,onInteractiveChange:j,className:b,children:p,position:w="bottom-left"})=>{let y=(0,n.PI)(),[E,M]=(0,a.useState)(!1),{isInteractive:k,minZoomReached:_,maxZoomReached:C}=(0,n.Pj)(h,l.x),{zoomIn:S,zoomOut:z,fitView:A}=(0,n.VH)();return((0,a.useEffect)(()=>{M(!0)},[]),E)?a.createElement(n.Zk,{className:(0,r.A)(["react-flow__controls",b]),position:w,style:e,"data-testid":"rf__controls"},t&&a.createElement(a.Fragment,null,a.createElement(x,{onClick:()=>{S(),g?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:C},a.createElement(i,null)),a.createElement(x,{onClick:()=>{z(),f?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:_},a.createElement(c,null))),s&&a.createElement(x,{className:"react-flow__controls-fitview",onClick:()=>{A(u),N?.()},title:"fit view","aria-label":"fit view"},a.createElement(m,null)),v&&a.createElement(x,{className:"react-flow__controls-interactive",onClick:()=>{y.setState({nodesDraggable:!k,nodesConnectable:!k,elementsSelectable:!k}),j?.(!k)},title:"toggle interactivity","aria-label":"toggle interactivity"},k?a.createElement(d,null):a.createElement(o,null)),p):null};v.displayName="Controls";var u=(0,a.memo)(v)}}]);
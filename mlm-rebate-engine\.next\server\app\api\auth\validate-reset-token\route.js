(()=>{var e={};e.id=8479,e.ids=[8479],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},97331:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>u});var n=t(96559),o=t(48088),a=t(37719),i=t(32190);async function u(e){try{let r=new URL(e.url).searchParams.get("token");if(!r)return i.NextResponse.json({error:"Token is required"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/db'");throw e.code="MODULE_NOT_FOUND",e}()).passwordReset.findFirst({where:{token:r,expiresAt:{gt:new Date}}}))return i.NextResponse.json({error:"Invalid or expired token"},{status:400});return i.NextResponse.json({valid:!0},{status:200})}catch(e){return console.error("Error in validate-reset-token API:",e),i.NextResponse.json({error:"An error occurred while processing your request"},{status:500})}}!function(){var e=Error("Cannot find module '@/lib/db'");throw e.code="MODULE_NOT_FOUND",e}();let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/validate-reset-token/route",pathname:"/api/auth/validate-reset-token",filename:"route",bundlePath:"app/api/auth/validate-reset-token/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\validate-reset-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:c,serverHooks:l}=d;function x(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:c})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580],()=>t(97331));module.exports=s})();
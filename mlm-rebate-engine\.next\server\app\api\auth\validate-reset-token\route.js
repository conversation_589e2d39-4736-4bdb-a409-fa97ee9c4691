(()=>{var e={};e.id=8479,e.ids=[8479],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(96330);let a=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},97331:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),u=t(31183);async function p(e){try{let r=new URL(e.url).searchParams.get("token");if(!r)return i.NextResponse.json({error:"Token is required"},{status:400});if(!await u.z.passwordReset.findFirst({where:{token:r,expiresAt:{gt:new Date}}}))return i.NextResponse.json({error:"Invalid or expired token"},{status:400});return i.NextResponse.json({valid:!0},{status:200})}catch(e){return console.error("Error in validate-reset-token API:",e),i.NextResponse.json({error:"An error occurred while processing your request"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/validate-reset-token/route",pathname:"/api/auth/validate-reset-token",filename:"route",bundlePath:"app/api/auth/validate-reset-token/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\validate-reset-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:x}=d;function v(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580],()=>t(97331));module.exports=s})();
(()=>{var e={};e.id=7341,e.ids=[7341],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4729:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(60687),r=s(43210),n=s(82136),i=s(16189),l=s(68367),d=s(23877);function c(){let{data:e,status:t}=(0,n.useSession)();(0,i.useRouter)();let[s,c]=(0,r.useState)(!0),[o,x]=(0,r.useState)(!1),[m,p]=(0,r.useState)([]),[u,h]=(0,r.useState)({totalRebates:0,totalAmount:0,pendingAmount:0,processedAmount:0,failedAmount:0,pendingCount:0,processedCount:0,failedCount:0}),[g,b]=(0,r.useState)(""),[j,f]=(0,r.useState)(""),[y,v]=(0,r.useState)({startDate:"",endDate:""}),[N,w]=(0,r.useState)(!1),[S,D]=(0,r.useState)(!1),[C,P]=(0,r.useState)(""),[k,L]=(0,r.useState)(""),[A,F]=(0,r.useState)(1),[R,M]=(0,r.useState)(10),[_,E]=(0,r.useState)(1),[$,q]=(0,r.useState)(0),T=async()=>{c(!0);try{let e=new URLSearchParams;e.append("page",A.toString()),e.append("pageSize",R.toString()),g&&e.append("status",g),j&&e.append("search",j),y.startDate&&e.append("startDate",y.startDate),y.endDate&&e.append("endDate",y.endDate);let t=await fetch(`/api/admin/rebates?${e.toString()}`);if(!t.ok)throw Error(`Failed to fetch rebates: ${t.statusText}`);let s=await t.json();p(s.rebates),E(s.pagination.totalPages),q(s.pagination.totalItems);let a=await fetch("/api/admin/rebates/stats");if(!a.ok)throw Error(`Failed to fetch rebate stats: ${a.statusText}`);let r=await a.json();h(r)}catch(e){console.error("Error fetching rebates:",e)}finally{c(!1)}},G=async()=>{D(!0),P("Processing pending rebates..."),L("");try{let e=await fetch("/api/admin/rebates/process",{method:"POST"});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to process rebates")}let t=await e.json();P(`Successfully processed ${t.processed} rebates`),T()}catch(e){L(e.message||"An error occurred while processing rebates")}finally{setTimeout(()=>{D(!1),P("")},3e3)}},U=e=>{let{name:t,value:s}=e.target;v(e=>({...e,[t]:s}))},I=e=>{e>0&&e<=_&&F(e)},O=async()=>{try{let e=new URLSearchParams;g&&e.append("status",g),j&&e.append("search",j),y.startDate&&e.append("startDate",y.startDate),y.endDate&&e.append("endDate",y.endDate),e.append("export","true");let t=await fetch(`/api/admin/rebates/export?${e.toString()}`);if(!t.ok)throw Error(`Failed to export rebates: ${t.statusText}`);let s=await t.blob(),a=window.URL.createObjectURL(s),r=document.createElement("a");r.style.display="none",r.href=a,r.download=`rebates-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}catch(e){console.error("Error exporting rebates:",e),alert("Failed to export rebates. Please try again.")}};return"loading"===t||s?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}):o?(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,a.jsx)(d.lcY,{className:"mr-2 text-blue-500"})," Rebate Management"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:O,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center",children:[(0,a.jsx)(d.WCW,{className:"mr-2"})," Export"]}),(0,a.jsxs)("button",{onClick:G,disabled:S||0===u.pendingCount,className:`px-4 py-2 text-white rounded-md flex items-center ${S||0===u.pendingCount?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"}`,children:[S?(0,a.jsx)(d.hW,{className:"animate-spin mr-2"}):(0,a.jsx)(d.DIg,{className:"mr-2"}),"Process Rebates"]})]})]}),C&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-green-100 text-green-700 rounded-md",children:C}),k&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-100 text-red-700 rounded-md",children:k}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,a.jsx)(d.lcY,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",u.totalAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[u.totalRebates," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,a.jsx)(d.CMH,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Processed"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",u.processedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[u.processedCount," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4",children:(0,a.jsx)(d.DIg,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Pending"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",u.pendingAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[u.pendingCount," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-red-100 text-red-500 mr-4",children:(0,a.jsx)(d.BS8,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Failed"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",u.failedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[u.failedCount," transactions"]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search by user name or email",value:j,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(d.KSO,{className:"text-gray-400"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:g,onChange:e=>b(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"processed",children:"Processed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"})]}),(0,a.jsxs)("button",{onClick:()=>w(!N),className:"px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center",children:[(0,a.jsx)(d.YsJ,{className:"mr-2"}),N?"Hide Filters":"More Filters"]})]})]}),N&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,a.jsx)(d.bfZ,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"date",name:"startDate",value:y.startDate,onChange:U,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,a.jsx)(d.bfZ,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"date",name:"endDate",value:y.endDate,onChange:U,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]})]}),(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{f(""),b(""),v({startDate:"",endDate:""})},className:"px-4 py-2 text-gray-700 hover:text-gray-900",children:"Reset Filters"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Rebate Transactions"})}),(0,a.jsx)("div",{className:"p-6",children:s?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading rebates..."})]}):m.length>0?(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Generator"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Receiver"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Processed At"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:(0,a.jsx)(d.x$1,{className:"text-gray-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.generator.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.generator.email})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:(0,a.jsx)(d.x$1,{className:"text-gray-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.receiver.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.receiver.email})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.purchase.product.name}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["Level ",e.level]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600",children:["₱",e.amount.toFixed(2)]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"processed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.processedAt?new Date(e.processedAt).toLocaleString():"-"})]},e.id))})]})}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No rebates found matching your criteria."})}),m.length>0&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-700 mr-2",children:"Rows per page:"}),(0,a.jsxs)("select",{value:R,onChange:e=>{M(parseInt(e.target.value)),F(1)},className:"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"10",children:"10"}),(0,a.jsx)("option",{value:"25",children:"25"}),(0,a.jsx)("option",{value:"50",children:"50"}),(0,a.jsx)("option",{value:"100",children:"100"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700 mr-4",children:[A," of ",_," pages (",$," total rebates)"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>I(1),disabled:1===A,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"First"}),(0,a.jsx)("button",{onClick:()=>I(A-1),disabled:1===A,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>I(A+1),disabled:A===_,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"}),(0,a.jsx)("button",{onClick:()=>I(_),disabled:A===_,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Last"})]})]})]})]})]})}):(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19805:(e,t,s)=>{Promise.resolve().then(s.bind(s,22119))},22119:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\rebates\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebates\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},60160:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["rebates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,22119)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebates\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebates\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/rebates/page",pathname:"/admin/rebates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},89949:(e,t,s)=>{Promise.resolve().then(s.bind(s,4729))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(60160));module.exports=a})();
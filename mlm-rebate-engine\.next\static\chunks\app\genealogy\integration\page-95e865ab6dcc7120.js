(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1689],{28359:(e,s,t)=>{Promise.resolve().then(t.bind(t,60629))},32383:()=>{},60629:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(95155),l=t(12115),n=t(12108),a=t(29911),i=t(6874),c=t.n(i),d=t(3925);function o(e){let{onImportComplete:s}=e,[t,n]=(0,l.useState)(null),[i,c]=(0,l.useState)("excel"),[o,m]=(0,l.useState)(!1),[x,u]=(0,l.useState)(null),[h,p]=(0,l.useState)(null),[j,y]=(0,l.useState)(null),[g,b]=(0,l.useState)(!1),[f,N]=(0,l.useState)({}),[v,w]=(0,l.useState)([]),[S]=(0,l.useState)(["id","name","email","uplineId","rankName"]),O=(0,l.useRef)(null),C=async()=>{if(t){m(!0),u(null),p(null);try{let e=[];if("excel"===i||"csv"===i?e=await I(t):"json"===i&&(e=await E(t)),!e||0===e.length)throw Error("No data found in the file.");y(e),e.length>0&&w(Object.keys(e[0])),b(!0),u(!0)}catch(e){console.error("Upload error:",e),p(e instanceof Error?e.message:"An unknown error occurred"),u(!1)}finally{m(!1)}}},I=e=>new Promise((s,t)=>{let r=new FileReader;r.onload=e=>{try{var r;let l=null==(r=e.target)?void 0:r.result;if(!l)return void t(Error("Failed to read file."));let n=d.LF(l,{type:"binary"}),a=n.SheetNames[0],i=n.Sheets[a],c=d.Wp.sheet_to_json(i);s(c)}catch(e){t(e)}},r.onerror=()=>{t(Error("Failed to read file."))},r.readAsBinaryString(e)}),E=e=>new Promise((s,t)=>{let r=new FileReader;r.onload=e=>{try{var r;let l=null==(r=e.target)?void 0:r.result;if(!l)return void t(Error("Failed to read file."));let n=JSON.parse(l);if(!Array.isArray(n))return void t(Error("JSON file must contain an array of objects."));s(n)}catch(e){t(e)}},r.onerror=()=>{t(Error("Failed to read file."))},r.readAsText(e)}),k=(e,s)=>{N(t=>({...t,[e]:s}))},P=async()=>{if(j){m(!0);try{let e=j.map(e=>{let s={};return Object.entries(f).forEach(t=>{let[r,l]=t;l&&void 0!==e[l]&&(s[r]=e[l])}),s}),t=[];if(S.forEach(e=>{f[e]||t.push(e)}),t.length>0)throw Error("Missing required fields: ".concat(t.join(", ")));s&&s(e),n(null),y(null),b(!1),N({}),u(!0),O.current&&(O.current.value="")}catch(e){console.error("Import error:",e),p(e instanceof Error?e.message:"An unknown error occurred"),u(!1)}finally{m(!1)}}};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,r.jsx)(a.PiR,{className:"mr-2 text-blue-500"}),"Import Genealogy Data"]}),g?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("h4",{className:"font-medium mb-2 flex items-center",children:[(0,r.jsx)(a.BS8,{className:"mr-2 text-yellow-500"}),"Map Fields"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Please map the fields from your file to the required fields in our system."}),(0,r.jsx)("div",{className:"space-y-3",children:S.map(e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("div",{className:"w-1/3 font-medium text-sm",children:[e.charAt(0).toUpperCase()+e.slice(1),":"]}),(0,r.jsx)("div",{className:"w-2/3",children:(0,r.jsxs)("select",{value:f[e]||"",onChange:s=>k(e,s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"-- Select Field --"}),v.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})})]},e))})]}),j&&j.length>0&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Data Preview"}),(0,r.jsxs)("div",{className:"overflow-x-auto",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsx)("tr",{children:Object.keys(j[0]).slice(0,5).map(e=>(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e},e))})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:j.slice(0,3).map((e,s)=>(0,r.jsx)("tr",{children:Object.keys(e).slice(0,5).map(s=>(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:String(e[s])},s))},s))})]}),j.length>3&&(0,r.jsxs)("div",{className:"text-center text-sm text-gray-500 mt-2",children:["Showing 3 of ",j.length," rows"]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{type:"button",onClick:()=>{n(null),y(null),b(!1),N({}),u(null),p(null),O.current&&(O.current.value="")},className:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"button",onClick:P,disabled:o,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:o?(0,r.jsx)(a.hW,{className:"animate-spin"}):"Import Data"})]}),!1===x&&(0,r.jsx)("div",{className:"mt-4 p-3 rounded-md bg-red-50 text-red-700",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(a.QCr,{className:"mr-2"}),(0,r.jsxs)("span",{children:["Import failed: ",h]})]})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select File to Import"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"file",ref:O,onChange:e=>{var s,t;let r=null==(s=e.target.files)?void 0:s[0];if(!r)return;n(r),u(null),p(null),y(null);let l=null==(t=r.name.split(".").pop())?void 0:t.toLowerCase();"xlsx"===l||"xls"===l?c("excel"):"csv"===l?c("csv"):"json"===l?c("json"):(p("Unsupported file type. Please upload an Excel, CSV, or JSON file."),n(null))},accept:".xlsx,.xls,.csv,.json",className:"hidden",id:"file-upload"}),(0,r.jsx)("label",{htmlFor:"file-upload",className:"flex-1 px-4 py-2 border border-gray-300 rounded-l-md bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer truncate",children:t?t.name:"Choose file..."}),(0,r.jsx)("button",{type:"button",onClick:C,disabled:!t||o,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:o?(0,r.jsx)(a.hW,{className:"animate-spin"}):"Upload"})]}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Supported file types: Excel (.xlsx, .xls), CSV (.csv), JSON (.json)"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"p-3 border rounded-md flex flex-col items-center",children:[(0,r.jsx)(a.Ru,{className:"text-green-500 text-2xl mb-2"}),(0,r.jsx)("div",{className:"text-sm font-medium",children:"Excel"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:".xlsx, .xls"})]}),(0,r.jsxs)("div",{className:"p-3 border rounded-md flex flex-col items-center",children:[(0,r.jsx)(a.QaY,{className:"text-blue-500 text-2xl mb-2"}),(0,r.jsx)("div",{className:"text-sm font-medium",children:"CSV"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:".csv"})]}),(0,r.jsxs)("div",{className:"p-3 border rounded-md flex flex-col items-center",children:[(0,r.jsx)(a.reA,{className:"text-yellow-500 text-2xl mb-2"}),(0,r.jsx)("div",{className:"text-sm font-medium",children:"JSON"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:".json"})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md mb-4",children:[(0,r.jsxs)("h4",{className:"font-medium text-blue-700 mb-2 flex items-center",children:[(0,r.jsx)(a.__w,{className:"mr-1"}),"Import Instructions"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700 text-sm",children:[(0,r.jsx)("li",{children:"Prepare your data in Excel, CSV, or JSON format"}),(0,r.jsx)("li",{children:"Ensure your file includes required fields: ID, Name, Email, Upline ID, and Rank"}),(0,r.jsx)("li",{children:"Upload the file and map the fields to the required fields"}),(0,r.jsx)("li",{children:"Review the data before importing"}),(0,r.jsx)("li",{children:"Click Import to add the data to your genealogy"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Import from External Systems"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("button",{type:"button",className:"p-3 border rounded-md flex items-center justify-center hover:bg-gray-50",onClick:()=>alert("CRM integration would be implemented here"),children:[(0,r.jsx)(a.nBS,{className:"mr-2 text-purple-500"}),(0,r.jsx)("span",{children:"Import from CRM"})]}),(0,r.jsxs)("button",{type:"button",className:"p-3 border rounded-md flex items-center justify-center hover:bg-gray-50",onClick:()=>alert("Cloud integration would be implemented here"),children:[(0,r.jsx)(a.BzO,{className:"mr-2 text-blue-500"}),(0,r.jsx)("span",{children:"Import from Cloud"})]})]})]}),null!==x&&(0,r.jsx)("div",{className:"mt-4 p-3 rounded-md ".concat(x?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:(0,r.jsx)("div",{className:"flex items-center",children:x?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.CMH,{className:"mr-2"}),(0,r.jsx)("span",{children:"File uploaded successfully! Please map the fields."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.QCr,{className:"mr-2"}),(0,r.jsxs)("span",{children:["Upload failed: ",h]})]})})})]})]})}function m(e){let{onSyncComplete:s}=e,[t,n]=(0,l.useState)([{id:"crm1",name:"Sales CRM",type:"crm",status:"connected",lastSync:"2023-10-15T14:30:00Z",icon:(0,r.jsx)(a.nBS,{className:"text-purple-500"})},{id:"cloud1",name:"Cloud Storage",type:"cloud",status:"connected",lastSync:"2023-10-14T09:15:00Z",icon:(0,r.jsx)(a.tmj,{className:"text-blue-500"})},{id:"api1",name:"External API",type:"api",status:"disconnected",icon:(0,r.jsx)(a.Pcn,{className:"text-gray-500"})}]),[i,c]=(0,l.useState)(null),[d,o]=(0,l.useState)(null),[m,x]=(0,l.useState)(null),[u,h]=(0,l.useState)([{systemId:"crm1",timestamp:"2023-10-15T14:30:00Z",success:!0,message:"Successfully synchronized 156 records"},{systemId:"cloud1",timestamp:"2023-10-14T09:15:00Z",success:!0,message:"Successfully synchronized 142 records"},{systemId:"api1",timestamp:"2023-10-10T11:45:00Z",success:!1,message:"Connection timeout"}]),[p,j]=(0,l.useState)(!1),y=async e=>{c(e.id),o(null),x(null);try{if(await new Promise(e=>setTimeout(e,2e3)),!(Math.random()>.2))throw Error("Failed to synchronize with external system.");let r=t.map(s=>s.id===e.id?{...s,lastSync:new Date().toISOString()}:s);n(r),h(s=>[{systemId:e.id,timestamp:new Date().toISOString(),success:!0,message:"Successfully synchronized records"},...s]),o(!0),s&&s(e)}catch(s){console.error("Sync error:",s),h(t=>[{systemId:e.id,timestamp:new Date().toISOString(),success:!1,message:s instanceof Error?s.message:"An unknown error occurred"},...t]),x(s instanceof Error?s.message:"An unknown error occurred"),o(!1)}finally{c(null)}},g=async e=>{c(e.id),o(null),x(null);try{await new Promise(e=>setTimeout(e,2e3));let s=t.map(s=>s.id===e.id?{...s,status:"connected"}:s);n(s),h(s=>[{systemId:e.id,timestamp:new Date().toISOString(),success:!0,message:"Successfully connected to ".concat(e.name)},...s]),o(!0)}catch(s){console.error("Connect error:",s),h(t=>[{systemId:e.id,timestamp:new Date().toISOString(),success:!1,message:s instanceof Error?s.message:"An unknown error occurred"},...t]),x(s instanceof Error?s.message:"An unknown error occurred"),o(!1)}finally{c(null)}},b=e=>new Date(e).toLocaleString(),f=e=>{if(!e)return"Never";let s=new Date(e),t=Math.floor(Math.floor((new Date().getTime()-s.getTime())/1e3)/60),r=Math.floor(t/60),l=Math.floor(r/24);return l>0?"".concat(l," day").concat(l>1?"s":""," ago"):r>0?"".concat(r," hour").concat(r>1?"s":""," ago"):t>0?"".concat(t," minute").concat(t>1?"s":""," ago"):"Just now"};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(a.DIg,{className:"mr-2 text-blue-500"}),"Sync with External Systems"]}),(0,r.jsxs)("button",{onClick:()=>j(!p),className:"text-sm text-blue-600 flex items-center",children:[(0,r.jsx)(a.OKX,{className:"mr-1"}),p?"Hide History":"Show History"]})]}),p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("h4",{className:"font-medium mb-2 flex items-center",children:[(0,r.jsx)(a.OKX,{className:"mr-2 text-blue-500"}),"Synchronization History"]}),0===u.length?(0,r.jsx)("div",{className:"text-center text-gray-500 p-4 border rounded-md",children:"No synchronization history available."}):(0,r.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"System"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Message"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map((e,s)=>{let l=t.find(s=>s.id===e.systemId);return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-2",children:null==l?void 0:l.icon}),(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(null==l?void 0:l.name)||e.systemId})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:b(e.timestamp)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.success?(0,r.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"Success"}):(0,r.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:"Failed"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.message})]},s)})})]})})]}),(0,r.jsx)("button",{onClick:()=>j(!1),className:"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:"Back to Systems"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"space-y-4 mb-4",children:t.map(e=>(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-3 text-xl",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Last sync: ",e.lastSync?f(e.lastSync):"Never"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-3",children:"connected"===e.status?(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"Connected"}):"error"===e.status?(0,r.jsx)("span",{className:"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full",children:"Error"}):(0,r.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full",children:"Disconnected"})}),"connected"===e.status?(0,r.jsx)("button",{onClick:()=>y(e),disabled:i===e.id,className:"px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed text-sm flex items-center",children:i===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.hW,{className:"animate-spin mr-1"}),"Syncing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.DIg,{className:"mr-1"}),"Sync Now"]})}):(0,r.jsx)("button",{onClick:()=>g(e),disabled:i===e.id,className:"px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-green-300 disabled:cursor-not-allowed text-sm flex items-center",children:i===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.hW,{className:"animate-spin mr-1"}),"Connecting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.CMH,{className:"mr-1"}),"Connect"]})})]})]}),"connected"===e.status&&(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t grid grid-cols-2 gap-2",children:[(0,r.jsxs)("button",{onClick:()=>alert("Import from ".concat(e.name," would be implemented here")),className:"p-2 border rounded-md flex items-center justify-center hover:bg-gray-50 text-sm",children:[(0,r.jsx)(a.mSE,{className:"mr-2 text-blue-500"}),(0,r.jsxs)("span",{children:["Import from ",e.name]})]}),(0,r.jsxs)("button",{onClick:()=>alert("Export to ".concat(e.name," would be implemented here")),className:"p-2 border rounded-md flex items-center justify-center hover:bg-gray-50 text-sm",children:[(0,r.jsx)(a.BzO,{className:"mr-2 text-green-500"}),(0,r.jsxs)("span",{children:["Export to ",e.name]})]})]})]},e.id))}),(0,r.jsx)("div",{className:"border rounded-md p-4 border-dashed flex items-center justify-center",children:(0,r.jsxs)("button",{onClick:()=>alert("Add new external system would be implemented here"),className:"p-2 text-blue-600 flex items-center",children:[(0,r.jsx)(FaPlus,{className:"mr-2"}),"Add New External System"]})}),(0,r.jsxs)("div",{className:"mt-4 bg-blue-50 p-3 rounded-md",children:[(0,r.jsxs)("h4",{className:"font-medium text-blue-700 mb-2 flex items-center",children:[(0,r.jsx)(a.__w,{className:"mr-1"}),"About Synchronization"]}),(0,r.jsx)("p",{className:"text-sm text-blue-700 mb-2",children:"Synchronizing with external systems allows you to keep your genealogy data up-to-date across multiple platforms."}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700 text-sm",children:[(0,r.jsx)("li",{children:"Connect to your CRM, cloud storage, or other systems"}),(0,r.jsx)("li",{children:"Import new members and updates from external systems"}),(0,r.jsx)("li",{children:"Export your genealogy data to external systems"}),(0,r.jsx)("li",{children:"Schedule automatic synchronization"})]})]}),null!==d&&(0,r.jsx)("div",{className:"mt-4 p-3 rounded-md ".concat(d?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:(0,r.jsx)("div",{className:"flex items-center",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.CMH,{className:"mr-2"}),(0,r.jsx)("span",{children:"Synchronization completed successfully!"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.QCr,{className:"mr-2"}),(0,r.jsxs)("span",{children:["Synchronization failed: ",m]})]})})})]})]})}function x(){let{data:e,status:s}=(0,n.useSession)(),[t,i]=(0,l.useState)("import");return"loading"===s?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(a.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})}):"unauthenticated"===s?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(a.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to access genealogy integration features."}),(0,r.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Integration"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Import, export, and synchronize your genealogy data with external systems"})]}),(0,r.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(a.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsxs)("button",{onClick:()=>i("import"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("import"===t?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,r.jsx)(a.PiR,{className:"inline mr-2"}),"Import Data"]}),(0,r.jsxs)("button",{onClick:()=>i("sync"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("sync"===t?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,r.jsx)(a.DIg,{className:"inline mr-2"}),"Sync External Systems"]})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:"import"===t?(0,r.jsx)(o,{onImportComplete:e=>{console.log("Import complete:",e),alert("Import completed successfully! ".concat(e.length," records imported."))}}):(0,r.jsx)(m,{onSyncComplete:e=>{console.log("Sync complete:",e),alert("Synchronization with ".concat(e.name," completed successfully!"))}})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,r.jsx)(a.O2x,{className:"mr-2 text-blue-500"}),"Integration Information"]}),(0,r.jsx)("div",{className:"space-y-4",children:"import"===t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"Import your genealogy data from external sources to keep your network up-to-date."}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-700 mb-2",children:"Supported Import Formats"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"Excel (.xlsx, .xls) - Spreadsheet format"}),(0,r.jsx)("li",{children:"CSV (.csv) - Comma-separated values"}),(0,r.jsx)("li",{children:"JSON (.json) - JavaScript Object Notation"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-700 mb-2",children:"Required Fields"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsx)("li",{children:"ID - Unique identifier for each member"}),(0,r.jsx)("li",{children:"Name - Member's full name"}),(0,r.jsx)("li",{children:"Email - Member's email address"}),(0,r.jsx)("li",{children:"Upline ID - ID of the member's upline"}),(0,r.jsx)("li",{children:"Rank Name - Member's rank in the organization"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-green-700 mb-2",children:"Optional Fields"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Wallet Balance - Member's current wallet balance"}),(0,r.jsx)("li",{children:"Join Date - Date when the member joined"}),(0,r.jsx)("li",{children:"Phone - Member's phone number"}),(0,r.jsx)("li",{children:"Address - Member's address"}),(0,r.jsx)("li",{children:"Performance Metrics - Sales, rebates, etc."})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"Synchronize your genealogy data with external systems to maintain consistency across platforms."}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-700 mb-2",children:"Supported Systems"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"CRM Systems - Salesforce, HubSpot, etc."}),(0,r.jsx)("li",{children:"Cloud Storage - Google Drive, Dropbox, etc."}),(0,r.jsx)("li",{children:"External APIs - Custom integrations"}),(0,r.jsx)("li",{children:"Other MLM Platforms - Data migration"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-700 mb-2",children:"Sync Options"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsx)("li",{children:"One-way Import - Import data from external system"}),(0,r.jsx)("li",{children:"One-way Export - Export data to external system"}),(0,r.jsx)("li",{children:"Two-way Sync - Keep both systems in sync"}),(0,r.jsx)("li",{children:"Scheduled Sync - Automate synchronization"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-green-700 mb-2",children:"Benefits"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Data Consistency - Same data across all systems"}),(0,r.jsx)("li",{children:"Time Saving - Avoid manual data entry"}),(0,r.jsx)("li",{children:"Error Reduction - Minimize human errors"}),(0,r.jsx)("li",{children:"Real-time Updates - Keep data current"})]})]})]})})]})})]})]})}},74436:(e,s,t)=>{"use strict";t.d(s,{k5:()=>o});var r=t(12115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=r.createContext&&r.createContext(l),a=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function c(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);s&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function d(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?c(Object(t),!0).forEach(function(s){var r,l,n;r=e,l=s,n=t[s],(l=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,s||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(l))in r?Object.defineProperty(r,l,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[l]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function o(e){return s=>r.createElement(m,i({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,t)=>r.createElement(s.tag,d({key:t},s.attr),e(s.child)))}(e.child))}function m(e){var s=s=>{var t,{attr:l,size:n,title:c}=e,o=function(e,s){if(null==e)return{};var t,r,l=function(e,s){if(null==e)return{};var t={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(s.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)t=n[r],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(l[t]=e[t])}return l}(e,a),m=n||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,l,o,{className:t,style:d(d({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),e.children)};return void 0!==n?r.createElement(n.Consumer,null,e=>s(e)):s(l)}},83686:()=>{}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,3524,6874,2108,8441,1684,7358],()=>s(28359)),_N_E=e.O()}]);
(()=>{var e={};e.id=6550,e.ids=[6550],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>l,aP:()=>d});var a=t(96330),n=t(13581),s=t(85663),o=t(55511),i=t.n(o);async function u(e){return await s.Ay.hash(e,10)}function d(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new a.PrismaClient;let l={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new a.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await s.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:o,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},21820:e=>{"use strict";e.exports=require("os")},24976:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>k,routeModule:()=>z,serverHooks:()=>I,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>M});var a={};t.r(a),t.d(a,{GET:()=>v,POST:()=>b});var n=t(96559),s=t(48088),o=t(37719),i=t(32190),u=t(19854),d=t(12909),l=t(69812),c=t(31183);async function p(e,r,t,a,n){try{let s=await c.z.rebateConfig.findMany({where:{productId:t},orderBy:{level:"asc"}});if(0===s.length)return console.log(`No rebate configuration found for product ${t}`),{success:!0,rebatesCreated:0,message:"No rebate configuration found",rebates:[]};let o=await m(r,6);if(0===o.length)return console.log(`No upline found for user ${r}`),{success:!0,rebatesCreated:0,message:"No upline found",rebates:[]};let i=o.map(e=>e.user.id),u=(await c.z.user.findMany({where:{id:{in:i}},include:{rank:!0}})).reduce((e,r)=>{let t=r.rank.level>=4?1.2:1;return e[r.id]=t,e},{}),d=[];for(let{user:t,level:i}of o){let o=s.find(e=>e.level===i);if(o){let s=0,l=0,c=u[t.id]||1;"fixed"===o.rewardType?(s=o.fixedAmount*c,l=.01*n*c):(s=a*(o.percentage*c)/100,l=n*(o.percentage*c)/100),d.push({purchaseId:e,receiverId:t.id,generatorId:r,level:i,rewardType:o.rewardType,percentage:o.percentage,amount:s,pvAmount:l,status:"pending"})}}let l=await c.z.$transaction(d.map(e=>c.z.rebate.create({data:e})));return{success:!0,rebatesCreated:l.length,message:`Created ${l.length} rebate records`,rebates:l}}catch(e){return console.error("Error calculating PV rebates:",e),{success:!1,rebatesCreated:0,message:`Error: ${e instanceof Error?e.message:"Unknown error"}`,rebates:[]}}}async function m(e,r=6){let t=[],a=await c.z.user.findUnique({where:{id:e},include:{upline:!0}}),n=1;for(;a?.upline&&n<=r;)t.push({user:a.upline,level:n}),a=await c.z.user.findUnique({where:{id:a.upline.id},include:{upline:!0}}),n++;return t}t(61904);var h=t(27942),g=t(92509),f=t(50013);async function y(e){try{let r=await c.z.product.findUnique({where:{id:e.productId}});if(!r)throw Error(`Product with ID ${e.productId} not found`);let t=r.price*e.quantity,a=await (0,h.$v)(t,r.pv*e.quantity);if(e.paymentMethodId){let r=await (0,g.XE)(e.paymentMethodId,e.paymentDetails||{});if(!r.isValid)throw Error(`Invalid payment details: ${r.errors?.join(", ")}`)}if(e.shippingMethodId){let r=await (0,f.Vy)(e.shippingMethodId,e.shippingDetails||{});if(!r.isValid)throw Error(`Invalid shipping details: ${r.errors?.join(", ")}`);void 0===e.shippingFee&&(e.shippingFee=await (0,f.$W)(e.shippingMethodId,e.shippingDetails||{},e.productId,e.quantity))}let n=await c.z.$transaction(async r=>await r.purchase.create({data:{userId:e.userId,productId:e.productId,quantity:e.quantity,totalAmount:t,totalPV:a,paymentMethodId:e.paymentMethodId,paymentDetails:e.paymentDetails?JSON.stringify(e.paymentDetails):null,referenceNumber:e.referenceNumber,shippingMethodId:e.shippingMethodId,shippingDetails:e.shippingDetails?JSON.stringify(e.shippingDetails):null,shippingAddress:e.shippingAddress,shippingFee:e.shippingFee,shippingStatus:e.shippingMethodId?"pending":null,referralLinkId:e.referralLinkId,referralSource:e.referralSource,referralData:e.referralData}})),s=await p(n.id,e.userId,e.productId,t,a);return{purchase:n,rebateResult:s}}catch(e){throw console.error("Error creating purchase:",e),e}}async function w(e,r=10,t=0){try{let a=await c.z.purchase.findMany({where:{userId:e},include:{product:!0,paymentMethod:!0},orderBy:{createdAt:"desc"},take:r,skip:t}),n=await c.z.purchase.count({where:{userId:e}});return{purchases:a,pagination:{total:n,limit:r,offset:t,hasMore:t+a.length<n}}}catch(e){throw console.error("Error getting user purchases:",e),e}}async function b(e){try{let r=await (0,u.getServerSession)(d.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to make a purchase"},{status:401});let a=await e.json(),n=l.Ld.safeParse(a);if(!n.success)return i.NextResponse.json({error:n.error.errors},{status:400});let{productId:s,quantity:o,paymentMethodId:c,paymentDetails:p,referenceNumber:m,shippingMethodId:h,shippingDetails:g,shippingAddress:f,shippingFee:w,referralCode:b}=a,v=parseInt(r.user.id),z=null,x=null,M=null;if(b){let{getShareableLinkByCode:e}=await t.e(7072).then(t.bind(t,27072)),r=await e(b);r&&r.userId!==v&&(z=r.id,x="link",M=JSON.stringify({code:b,referrerId:r.userId}))}let I=await y({userId:v,productId:s,quantity:o,paymentMethodId:c,paymentDetails:p,referenceNumber:m,shippingMethodId:h,shippingDetails:g,shippingAddress:f,shippingFee:w,referralLinkId:z,referralSource:x,referralData:M});if(z)try{let{recordReferralPurchase:e}=await t.e(7072).then(t.bind(t,27072));await e(I.purchase.id,z)}catch(e){console.error("Error processing referral commission:",e)}return i.NextResponse.json(I,{status:201})}catch(e){return console.error("Error creating purchase:",e),i.NextResponse.json({error:e instanceof Error?e.message:"Failed to create purchase"},{status:500})}}async function v(e){try{let r=await (0,u.getServerSession)(d.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to view purchases"},{status:401});let t=parseInt(r.user.id),a="admin"===r.user.role,n=new URL(e.url),s=n.searchParams.get("userId"),o=n.searchParams.get("limit"),l=n.searchParams.get("offset"),c=o?parseInt(o):10,p=l?parseInt(l):0,m=t;a&&s&&(m=parseInt(s));let h=await w(m,c,p);return i.NextResponse.json(h)}catch(e){return console.error("Error fetching purchases:",e),i.NextResponse.json({error:"Failed to fetch purchases"},{status:500})}}let z=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/purchases/route",pathname:"/api/purchases",filename:"route",bundlePath:"app/api/purchases/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\purchases\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:x,workUnitAsyncStorage:M,serverHooks:I}=z;function k(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:M})}},27910:e=>{"use strict";e.exports=require("stream")},27942:(e,r,t)=>{"use strict";t.d(r,{$v:()=>m,A4:()=>d,DW:()=>o,J0:()=>u,QX:()=>i,Xr:()=>n,Xu:()=>l,_8:()=>c,cs:()=>s,cv:()=>p,qd:()=>h});var a=t(31183);async function n(){let e=(await a.z.systemConfig.findMany()).reduce((e,r)=>(e[r.key]=r.value,e),{});return{mlmStructure:e.mlm_structure||"binary",pvCalculation:e.pv_calculation||"percentage",performanceBonusEnabled:"true"===e.performance_bonus_enabled,monthlyCutoffDay:parseInt(e.monthly_cutoff_day||"25"),binaryMaxDepth:parseInt(e.binary_max_depth||"6"),unilevelMaxDepth:parseInt(e.unilevel_max_depth||"6")}}async function s(e){let r=[];for(let t of(void 0!==e.mlmStructure&&r.push({key:"mlm_structure",value:e.mlmStructure}),void 0!==e.pvCalculation&&r.push({key:"pv_calculation",value:e.pvCalculation}),void 0!==e.performanceBonusEnabled&&r.push({key:"performance_bonus_enabled",value:e.performanceBonusEnabled.toString()}),void 0!==e.monthlyCutoffDay&&r.push({key:"monthly_cutoff_day",value:e.monthlyCutoffDay.toString()}),void 0!==e.binaryMaxDepth&&r.push({key:"binary_max_depth",value:e.binaryMaxDepth.toString()}),void 0!==e.unilevelMaxDepth&&r.push({key:"unilevel_max_depth",value:e.unilevelMaxDepth.toString()}),r))await a.z.systemConfig.update({where:{key:t.key},data:{value:t.value,updatedAt:new Date}});return await n()}async function o(e=!0){return await a.z.performanceBonusTier.findMany({where:e?{active:!0}:void 0,orderBy:{minSales:"asc"}})}async function i(e){return await a.z.performanceBonusTier.create({data:e})}async function u(e,r){return await a.z.performanceBonusTier.update({where:{id:e},data:{...r,updatedAt:new Date}})}async function d(e,r){let t={};return void 0!==e&&(t.year=e),void 0!==r&&(t.month=r),await a.z.monthlyCutoff.findMany({where:t,orderBy:[{year:"desc"},{month:"desc"}]})}async function l(e){if(await a.z.monthlyCutoff.findUnique({where:{year_month:{year:e.year,month:e.month}}}))throw Error(`A cutoff already exists for ${e.year}-${e.month}`);return await a.z.monthlyCutoff.create({data:{year:e.year,month:e.month,cutoffDay:e.cutoffDay,notes:e.notes,status:"pending"}})}async function c(e,r){return await a.z.monthlyCutoff.update({where:{id:e},data:{...r,updatedAt:new Date}})}async function p(){let e=new Date,r=e.getFullYear(),t=e.getMonth()+1,n=await a.z.monthlyCutoff.findUnique({where:{year_month:{year:r,month:t}}});return n||(n=await a.z.monthlyCutoff.findFirst({where:{OR:[{year:r,month:{gt:t}},{year:{gt:r}}]},orderBy:[{year:"asc"},{month:"asc"}]}))}async function m(e,r){return"fixed"===(await n()).pvCalculation?r:.5*e}async function h(e){if(!(await n()).performanceBonusEnabled)return 0;let r=(await o(!0)).find(r=>e>=r.minSales&&(null===r.maxSales||e<=r.maxSales));return r?"percentage"===r.bonusType?e*(r.percentage/100):r.fixedAmount:0}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var a=t(96330);let n=global.prisma||new a.PrismaClient({log:["query"]})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50013:(e,r,t)=>{"use strict";t.d(r,{$W:()=>i,Uv:()=>s,Vy:()=>o,_1:()=>n});var a=t(31183);async function n(e=!0){return await a.z.shippingMethod.findMany({where:e?{isActive:!0}:void 0,orderBy:{name:"asc"}})}async function s(e){return await a.z.shippingMethod.findUnique({where:{id:e}})}async function o(e,r){try{let t=await s(e);if(!t)return{isValid:!1,errors:["Shipping method not found"]};if(!t.requiresDetails||!t.detailsSchema)return{isValid:!0};let a=JSON.parse(t.detailsSchema),n=[];if(a.required&&Array.isArray(a.required))for(let e of a.required)r[e]||n.push(`${e} is required`);if(a.properties&&"object"==typeof a.properties)for(let[e,t]of Object.entries(a.properties))void 0!==r[e]&&("number"===t.type&&"number"!=typeof r[e]?n.push(`${e} must be a number`):"string"===t.type&&"string"!=typeof r[e]?n.push(`${e} must be a string`):"boolean"===t.type&&"boolean"!=typeof r[e]&&n.push(`${e} must be a boolean`));return{isValid:0===n.length,errors:n.length>0?n:void 0}}catch(e){return console.error("Error validating shipping details:",e),{isValid:!1,errors:["Error validating shipping details"]}}}async function i(e,r,t,a){try{let r=await s(e);if(!r)throw Error("Shipping method not found");if("pickup"===r.code)return 0;return r.baseFee}catch(e){throw console.error("Error calculating shipping fee:",e),e}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61904:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let a=t(49526).createTransport({host:process.env.EMAIL_HOST||"smtp.example.com",port:parseInt(process.env.EMAIL_PORT||"587"),secure:"true"===process.env.EMAIL_SECURE,auth:{user:process.env.EMAIL_USER||"<EMAIL>",pass:process.env.EMAIL_PASSWORD||"password"}}),n={rebateReceived:e=>({subject:`You've Received a Rebate of $${e.amount.toFixed(2)}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">New Rebate Received!</h2>
          <p>Hello ${e.userName},</p>
          <p>Great news! You've received a rebate of <strong style="color: #48bb78;">$${e.amount.toFixed(2)}</strong>.</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Rebate Details:</h3>
            <ul style="padding-left: 20px;">
              <li>Amount: <strong>$${e.amount.toFixed(2)}</strong></li>
              <li>From: <strong>${e.generatorName}</strong></li>
              <li>Level: <strong>${e.level}</strong></li>
              <li>Product: <strong>${e.productName}</strong></li>
            </ul>
          </div>
          <p>This rebate has been added to your wallet balance. You can view your rebate details and wallet balance by logging into your account.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/wallet" style="background-color: #4299e1; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Wallet</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for being part of our MLM network!</p>
        </div>
      `}),rankAdvancement:e=>({subject:`Congratulations on Your Rank Advancement to ${e.newRank}!`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">Rank Advancement Achievement!</h2>
          <p>Hello ${e.userName},</p>
          <p>Congratulations! You've advanced from <strong>${e.oldRank}</strong> to <strong style="color: #805ad5;">${e.newRank}</strong>!</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Your New Benefits:</h3>
            <ul style="padding-left: 20px;">
              ${e.benefits.map(e=>`<li>${e}</li>`).join("")}
            </ul>
          </div>
          <p>Keep up the great work! As you continue to grow your network and increase your sales, you'll unlock even more benefits and higher rebate percentages.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" style="background-color: #805ad5; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Dashboard</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for your dedication and commitment to our MLM network!</p>
        </div>
      `})};async function s(e,r,t){try{let{subject:s,html:o}=n[r](t),i={from:process.env.EMAIL_FROM||"MLM Rebate Engine <<EMAIL>>",to:e,subject:s,html:o},u=await a.sendMail(i);return console.log("Email sent:",u.messageId),{success:!0,messageId:u.messageId}}catch(e){return console.error("Error sending email:",e),{success:!1,error:e}}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69812:(e,r,t)=>{"use strict";t.d(r,{HU:()=>o,Ld:()=>i,q8:()=>s,tf:()=>u,zK:()=>n});var a=t(70762);a.z.object({email:a.z.string().email("Invalid email address"),password:a.z.string().min(8,"Password must be at least 8 characters"),csrfToken:a.z.string().optional()});let n=a.z.object({name:a.z.string().min(2,"Name must be at least 2 characters"),email:a.z.string().email("Invalid email address"),password:a.z.string().min(8,"Password must be at least 8 characters"),confirmPassword:a.z.string().min(8,"Confirm password must be at least 8 characters"),phone:a.z.string().optional(),birthdate:a.z.string().optional(),address:a.z.string().optional(),city:a.z.string().optional(),region:a.z.string().optional(),postalCode:a.z.string().optional(),uplineId:a.z.string().optional(),profileImage:a.z.string().optional(),preferredPaymentMethod:a.z.string().optional(),bankName:a.z.string().optional(),bankAccountNumber:a.z.string().optional(),bankAccountName:a.z.string().optional(),gcashNumber:a.z.string().optional(),payMayaNumber:a.z.string().optional(),receiveUpdates:a.z.boolean().optional().default(!1),agreeToTerms:a.z.boolean()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>!0===e.agreeToTerms,{message:"You must agree to the terms and conditions",path:["agreeToTerms"]}).refine(e=>"bank"===e.preferredPaymentMethod?!!e.bankName&&!!e.bankAccountNumber&&!!e.bankAccountName:"gcash"===e.preferredPaymentMethod?!!e.gcashNumber:"paymaya"!==e.preferredPaymentMethod||!!e.payMayaNumber,{message:"Payment details are required for the selected payment method",path:["preferredPaymentMethod"]}),s=a.z.object({name:a.z.string().min(2,"Name must be at least 2 characters").optional(),phone:a.z.string().optional(),currentPassword:a.z.string().optional(),newPassword:a.z.string().min(8,"New password must be at least 8 characters").optional(),confirmNewPassword:a.z.string().optional(),profileImage:a.z.string().optional()}).refine(e=>!e.newPassword||e.newPassword===e.confirmNewPassword,{message:"New passwords don't match",path:["confirmNewPassword"]}).refine(e=>!e.newPassword||!!e.currentPassword,{message:"Current password is required to set a new password",path:["currentPassword"]}),o=a.z.object({name:a.z.string().min(2,"Product name must be at least 2 characters"),description:a.z.string().optional(),price:a.z.number().positive("Price must be positive"),image:a.z.string().optional(),rebateConfigs:a.z.array(a.z.object({level:a.z.number().int().positive("Level must be a positive integer"),percentage:a.z.number().positive("Percentage must be positive").max(100,"Percentage cannot exceed 100%")})).min(1,"At least one rebate configuration is required")}),i=a.z.object({productId:a.z.number().int().positive("Product ID must be a positive integer"),quantity:a.z.number().int().positive("Quantity must be a positive integer"),paymentMethodId:a.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:a.z.record(a.z.any()).optional(),referenceNumber:a.z.string().optional(),shippingMethodId:a.z.number().int().positive("Shipping method ID must be a positive integer").optional(),shippingDetails:a.z.record(a.z.any()).optional(),shippingAddress:a.z.string().optional(),shippingFee:a.z.number().nonnegative("Shipping fee must be a non-negative number").optional(),referralCode:a.z.string().optional()});function u(e,r){try{let t=e.parse(r);return{success:!0,data:t}}catch(e){if(e instanceof a.z.ZodError){let r={};return e.errors.forEach(e=>{r[e.path.join(".")]=e.message}),{success:!1,errors:r}}return{success:!1,errors:{_error:"An unexpected error occurred during validation"}}}}a.z.object({amount:a.z.number().positive("Amount must be positive"),type:a.z.enum(["withdrawal","deposit"],{errorMap:()=>({message:"Invalid transaction type"})}),description:a.z.string().optional(),paymentMethodId:a.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:a.z.record(a.z.any()).optional(),referenceNumber:a.z.string().optional()}),a.z.object({checkAll:a.z.boolean().optional()})},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92509:(e,r,t)=>{"use strict";t.d(r,{Gb:()=>i,Gx:()=>n,XE:()=>l,_:()=>o,cE:()=>u,gY:()=>s,w9:()=>d});var a=t(31183);async function n(e=!0){return await a.z.paymentMethod.findMany({where:e?{isActive:!0}:void 0,orderBy:{name:"asc"}})}async function s(e){return await a.z.paymentMethod.findUnique({where:{id:e}})}async function o(e){return await a.z.userPaymentMethod.findMany({where:{userId:e},include:{paymentMethod:!0},orderBy:[{isDefault:"desc"},{createdAt:"desc"}]})}async function i(e,r,t,n=!1){return n&&await a.z.userPaymentMethod.updateMany({where:{userId:e,isDefault:!0},data:{isDefault:!1}}),await a.z.userPaymentMethod.create({data:{userId:e,paymentMethodId:r,details:t,isDefault:n},include:{paymentMethod:!0}})}async function u(e,r,t){let n=await a.z.userPaymentMethod.findUnique({where:{id:e},select:{userId:!0}});if(!n)throw Error(`User payment method with ID ${e} not found`);return t&&await a.z.userPaymentMethod.updateMany({where:{userId:n.userId,isDefault:!0,id:{not:e}},data:{isDefault:!1}}),await a.z.userPaymentMethod.update({where:{id:e},data:{details:void 0!==r?r:void 0,isDefault:void 0!==t?t:void 0,updatedAt:new Date},include:{paymentMethod:!0}})}async function d(e){return await a.z.userPaymentMethod.delete({where:{id:e},include:{paymentMethod:!0}})}async function l(e,r){let t=await s(e);if(!t)return{isValid:!1,errors:["Payment method not found"]};if(!t.requiresDetails||!t.detailsSchema)return{isValid:!0};try{let e=JSON.parse(t.detailsSchema);if(e.required&&Array.isArray(e.required)){let t=[];for(let a of e.required)r[a]||t.push(`Field "${a}" is required`);if(t.length>0)return{isValid:!1,errors:t}}return{isValid:!0}}catch(e){return console.error("Error validating payment details:",e),{isValid:!1,errors:["Invalid schema format"]}}}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4243,580,8044,3112,8381,4079],()=>t(24976));module.exports=a})();
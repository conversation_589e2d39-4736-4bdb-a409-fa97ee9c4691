"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8453],{98453:(e,s,a)=>{a.r(s),a.d(s,{default:()=>i});var r=a(95155),t=a(12115),l=a(29911);let i=e=>{let{metrics:s=[],period:a="month"}=e,[i,n]=(0,t.useState)(a),o=(e,s)=>{switch(s){case"currency":return"₱".concat(e.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2}));case"percentage":return"".concat(e,"%");default:return e.toLocaleString()}},c=(e,s)=>0===s?100*(e>0):(e-s)/s*100,m=(e,s)=>{let a=c(e,s),t=Math.abs(a).toFixed(1);return a>0?(0,r.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,r.jsx)(l.uCC,{className:"mr-1"}),(0,r.jsxs)("span",{children:[t,"%"]})]}):a<0?(0,r.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,r.jsx)(l.$TP,{className:"mr-1"}),(0,r.jsxs)("span",{children:[t,"%"]})]}):(0,r.jsxs)("div",{className:"flex items-center text-gray-500",children:[(0,r.jsx)(l.VsL,{className:"mr-1"}),(0,r.jsx)("span",{children:"0%"})]})},u=s.length>0?s:[{label:"Total Earnings",value:12500,previousValue:10800,format:"currency",info:"Sum of all rebates and bonuses earned"},{label:"Group Volume",value:45e3,previousValue:38e3,format:"number",info:"Total Point Value (PV) from your entire downline"},{label:"Personal Volume",value:5200,previousValue:4800,format:"number",info:"Point Value (PV) from your personal purchases"},{label:"Conversion Rate",value:68,previousValue:62,format:"percentage",info:"Percentage of referrals who became distributors"}];return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-b flex justify-between items-center",children:[(0,r.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,r.jsx)(l.YYR,{className:"mr-2 text-green-500"})," Performance Summary"]}),(0,r.jsxs)("select",{value:i,onChange:e=>n(e.target.value),className:"text-sm border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",children:[(0,r.jsx)("option",{value:"week",children:"This Week"}),(0,r.jsx)("option",{value:"month",children:"This Month"}),(0,r.jsx)("option",{value:"year",children:"This Year"})]})]}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:u.map((e,s)=>(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:e.label}),e.info&&(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)(l.__w,{className:"text-gray-400 hover:text-gray-600 cursor-help"}),(0,r.jsx)("div",{className:"absolute right-0 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-10",children:e.info})]})]}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900 mb-1",children:o(e.value,e.format)}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-500 mr-2",children:["vs previous ",i,":"]}),m(e.value,e.previousValue)]})]},s))})})]})}}}]);
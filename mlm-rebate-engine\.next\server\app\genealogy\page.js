(()=>{var e={};e.id=2292,e.ids=[2292],e.modules={204:(e,s,l)=>{"use strict";l.r(s),l.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=l(65239),t=l(48088),a=l(88170),n=l.n(a),i=l(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);l.d(s,o);let d={children:["",{children:["genealogy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,26130)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(l.bind(l,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(l.t.bind(l,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(l.t.bind(l,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(l.t.bind(l,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\page.tsx"],x={require:l,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/genealogy/page",pathname:"/genealogy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26130:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>r});let r=(0,l(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\page.tsx","default")},26721:(e,s,l)=>{"use strict";l.d(s,{A:()=>c});var r=l(60687),t=l(43210),a=l(23877);let n={Starter:{color:"bg-gray-100 text-gray-800",borderColor:"border-gray-300",icon:(0,r.jsx)(a.gt3,{className:"text-gray-400"}),benefits:["Basic commission rates","Access to product catalog","Personal dashboard"]},Bronze:{color:"bg-yellow-100 text-yellow-800",borderColor:"border-yellow-300",icon:(0,r.jsx)(a.gt3,{className:"text-yellow-600"}),benefits:["5% commission on direct referrals","Access to training materials","Monthly team reports"]},Silver:{color:"bg-gray-200 text-gray-800",borderColor:"border-gray-400",icon:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.gt3,{className:"text-gray-500"}),(0,r.jsx)(a.gt3,{className:"text-gray-500 ml-0.5"})]}),benefits:["7% commission on direct referrals","3% on level 2","Quarterly bonus eligibility"]},Gold:{color:"bg-yellow-200 text-yellow-800",borderColor:"border-yellow-400",icon:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.gt3,{className:"text-yellow-600"}),(0,r.jsx)(a.gt3,{className:"text-yellow-600 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-yellow-600 ml-0.5"})]}),benefits:["10% commission on direct referrals","5% on level 2","3% on level 3","Leadership training access"]},Platinum:{color:"bg-blue-100 text-blue-800",borderColor:"border-blue-300",icon:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.gt3,{className:"text-blue-500"}),(0,r.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"})]}),benefits:["12% commission on direct referrals","7% on level 2","5% on level 3","3% on levels 4-5","Annual retreat invitation"]},Diamond:{color:"bg-purple-100 text-purple-800",borderColor:"border-purple-300",icon:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.gt3,{className:"text-purple-500"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"})]}),benefits:["15% commission on direct referrals","10% on level 2","7% on level 3","5% on levels 4-5","3% on levels 6-10","Car bonus program","Executive leadership council"]}},i=e=>n[e]||n.Starter,o=({user:e,onClose:s})=>{if(!e)return null;let l=i(e.rank.name);return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:`p-6 ${l.color} rounded-t-lg border-b ${l.borderColor}`,children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-white flex items-center justify-center text-2xl shadow-md",children:e.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold",children:e.name}),(0,r.jsx)("div",{className:"flex items-center mt-1",children:(0,r.jsxs)("span",{className:"flex items-center text-sm",children:[l.icon,(0,r.jsx)("span",{className:"ml-1",children:e.rank.name})]})})]})]}),(0,r.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700 focus:outline-none",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"User Information"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.AWX,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"User ID"}),(0,r.jsx)("div",{children:e.id})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.maD,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Email"}),(0,r.jsx)("div",{children:e.email})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.M5n,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Downline Members"}),(0,r.jsx)("div",{children:e._count.downline})]})]}),void 0!==e.walletBalance&&(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.lcY,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Wallet Balance"}),(0,r.jsxs)("div",{children:["₱",e.walletBalance.toFixed(2)]})]})]}),e.level>0&&(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.YXz,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Network Level"}),(0,r.jsxs)("div",{children:["Level ",e.level]})]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Rank Benefits"}),(0,r.jsx)("ul",{className:"space-y-2",children:l.benefits.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2 mt-0.5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),(0,r.jsx)("span",{children:e})]},s))})]})]}),(0,r.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Actions"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"View Full Profile"}),(0,r.jsx)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:"Send Message"}),0===e.level&&(0,r.jsx)("button",{className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",children:"Generate Referral Link"})]})]})]})]})})},d=({user:e,isRoot:s=!1,depth:l,maxDepth:n,initialExpandedLevels:o,onUserSelect:c,viewMode:x,highlightTerm:m})=>{let[u,g]=(0,t.useState)(l<o),h=e.children&&e.children.length>0,p=h&&l<n,b=i(e.rank.name),j=m&&(e.name.toLowerCase().includes(m.toLowerCase())||e.email.toLowerCase().includes(m.toLowerCase())||e.id.toString().includes(m));return(0,r.jsxs)("div",{className:`mb-2 ${s?"":"ml-6"}`,children:[(0,r.jsxs)("div",{className:`
          flex items-center p-3 rounded-md
          ${s?"bg-blue-50 border border-blue-200":"bg-white border border-gray-200"}
          ${j?"ring-2 ring-yellow-400 shadow-md":""}
          hover:shadow-md transition-shadow duration-200
        `,children:[p&&(0,r.jsx)("button",{onClick:()=>g(!u),className:"mr-2 text-gray-500 hover:text-gray-700 focus:outline-none","aria-label":u?"Collapse":"Expand",children:u?(0,r.jsx)(a.Vr3,{}):(0,r.jsx)(a.X6T,{})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[s?(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2",children:(0,r.jsx)(a.x$1,{className:"text-blue-500"})}):(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2",children:e.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),"detailed"===x&&(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.email})]}),(0,r.jsxs)("span",{className:`ml-2 text-xs px-2 py-0.5 rounded-full flex items-center ${b.color}`,children:[b.icon,(0,r.jsx)("span",{className:"ml-1",children:e.rank.name})]})]}),"detailed"===x&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1 flex flex-wrap gap-x-3",children:[(0,r.jsxs)("span",{children:["ID: ",e.id]}),(0,r.jsxs)("span",{children:["Downline: ",e._count.downline]}),!s&&(0,r.jsxs)("span",{children:["Level ",e.level]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[!s&&"compact"===x&&(0,r.jsxs)("div",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:["Lvl ",e.level]}),(0,r.jsx)("button",{onClick:()=>c(e),className:"p-1.5 text-blue-500 hover:bg-blue-50 rounded-full","aria-label":"View details",children:(0,r.jsx)(a.__w,{})})]})]}),h&&u&&(0,r.jsx)("div",{className:"mt-2 border-l-2 border-gray-200 pl-2",children:e.children.map(e=>(0,r.jsx)(d,{user:e,depth:l+1,maxDepth:n,initialExpandedLevels:o,onUserSelect:c,viewMode:x,highlightTerm:m},e.id))})]})},c=({data:e,maxDepth:s=10,initialExpandedLevels:l=2,onUserSelect:c})=>{let[x,m]=(0,t.useState)(""),[u,g]=(0,t.useState)(!1),[h,p]=(0,t.useState)("detailed"),[b,j]=(0,t.useState)(null),[f,v]=(0,t.useState)(null),[N,y]=(0,t.useState)(""),w=(0,t.useRef)(null);(0,t.useEffect)(()=>{let e=setTimeout(()=>{y(x)},300);return()=>clearTimeout(e)},[x]);let k=(e,s,l)=>{let r=!s||e.name.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase())||e.id.toString().includes(s),t=!l||e.rank.name===l;if(r&&t)return e;if(e.children&&e.children.length>0){let r=e.children.map(e=>k(e,s,l)).filter(Boolean);if(r.length>0)return{...e,children:r}}return null},C=(x||f)&&k(e,x,f)||e;return(0,r.jsxs)("div",{className:"genealogy-tree",children:[(0,r.jsx)("div",{className:"mb-6 bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{ref:w,type:"text",placeholder:"Search by name, email, or ID...",value:x,onChange:e=>m(e.target.value),className:"w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"}),(0,r.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,r.jsx)(a.KSO,{className:"h-5 w-5"})}),x&&(0,r.jsx)("button",{onClick:()=>{m(""),y(""),w.current&&w.current.focus()},className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>g(!u),className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:u?"Collapse All":"Expand All"}),(0,r.jsxs)("button",{onClick:()=>p("compact"===h?"detailed":"compact"),className:"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center",children:["compact"===h?(0,r.jsx)(a.Ny1,{className:"mr-1"}):(0,r.jsx)(a.mx3,{className:"mr-1"}),"compact"===h?"Detailed":"Compact"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,r.jsx)(a.YsJ,{className:"mr-1"})," Filter by rank:"]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("button",{onClick:()=>v(null),className:`text-xs px-3 py-1 rounded-full ${null===f?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"All"}),Object.keys(n).map(e=>(0,r.jsxs)("button",{onClick:()=>v(e===f?null:e),className:`text-xs px-3 py-1 rounded-full flex items-center ${f===e?"bg-green-600 text-white":`${i(e).color} hover:bg-opacity-80`}`,children:[i(e).icon,(0,r.jsx)("span",{className:"ml-1",children:e})]},e))]})]})]})}),(0,r.jsxs)("div",{className:"mb-6 bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-semibold mb-2 text-gray-500",children:"Rank Legend"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-3",children:Object.entries(n).map(([e,s])=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:`w-3 h-3 inline-block rounded-full ${s.color.split(" ")[0]} mr-1`}),(0,r.jsxs)("span",{className:"text-sm flex items-center",children:[s.icon,(0,r.jsx)("span",{className:"ml-1",children:e})]})]},e))})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6 overflow-x-auto",children:(0,r.jsx)(d,{user:C,isRoot:!0,depth:0,maxDepth:s,initialExpandedLevels:u?s:l,onUserSelect:e=>{j(e),c&&c(e)},viewMode:h,highlightTerm:N})}),b&&(0,r.jsx)(o,{user:b,onClose:()=>j(null)})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35963:(e,s,l)=>{"use strict";l.d(s,{A:()=>o});var r=l(60687),t=l(43210),a=l(23877);let n=e=>{switch(e){case"Starter":default:return"bg-gray-100 text-gray-800";case"Bronze":return"bg-yellow-100 text-yellow-800";case"Silver":return"bg-gray-200 text-gray-800";case"Gold":return"bg-yellow-200 text-yellow-800";case"Platinum":return"bg-blue-100 text-blue-800";case"Diamond":return"bg-purple-100 text-purple-800"}},i=({user:e,isRoot:s=!1,depth:l,maxDepth:o,initialExpandedLevels:d})=>{let[c,x]=(0,t.useState)(l<d),m=e.children&&e.children.length>0,u=m&&l<o,g=n(e.rank.name);return(0,r.jsxs)("div",{className:`mb-2 ${s?"":"ml-6"}`,children:[(0,r.jsxs)("div",{className:`flex items-center p-3 rounded-md ${s?"bg-blue-50 border border-blue-200":"bg-white border border-gray-200"}`,children:[u&&(0,r.jsx)("button",{onClick:()=>x(!c),className:"mr-2 text-gray-500 hover:text-gray-700 focus:outline-none","aria-label":c?"Collapse":"Expand",children:c?(0,r.jsx)(a.Vr3,{}):(0,r.jsx)(a.X6T,{})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[s?(0,r.jsx)(a.x$1,{className:"mr-2 text-blue-500"}):(0,r.jsx)(a.YXz,{className:"mr-2 text-blue-500"}),(0,r.jsx)("span",{className:"font-medium",children:e.name}),(0,r.jsx)("span",{className:`ml-2 text-xs px-2 py-0.5 rounded-full ${g}`,children:e.rank.name})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[(0,r.jsxs)("span",{className:"mr-3",children:["ID: ",e.id]}),(0,r.jsxs)("span",{className:"mr-3",children:["Downline: ",e._count.downline]}),!s&&(0,r.jsxs)("span",{children:["Level ",e.level]})]})]}),!s&&(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:["Level ",e.level]})})]}),m&&c&&(0,r.jsx)("div",{className:"mt-2 border-l-2 border-gray-200 pl-2",children:e.children.map(e=>(0,r.jsx)(i,{user:e,depth:l+1,maxDepth:o,initialExpandedLevels:d},e.id))})]})},o=({data:e,maxDepth:s=10,initialExpandedLevels:l=2})=>{let[a,n]=(0,t.useState)(""),[o,d]=(0,t.useState)(!1),c=(e,s)=>{if(e.name.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase()))return e;if(e.children&&e.children.length>0){let l=e.children.map(e=>c(e,s)).filter(Boolean);if(l.length>0)return{...e,children:l}}return null},x=a&&c(e,a)||e;return(0,r.jsxs)("div",{className:"genealogy-tree",children:[(0,r.jsxs)("div",{className:"mb-4 flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name or email...",value:a,onChange:e=>n(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pl-10"}),(0,r.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{onClick:()=>d(!o),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:o?"Collapse All":"Expand All"})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("div",{className:"mb-4 flex items-center",children:(0,r.jsxs)("div",{className:"flex space-x-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-gray-100 mr-1"}),(0,r.jsx)("span",{children:"Starter"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-yellow-100 mr-1"}),(0,r.jsx)("span",{children:"Bronze"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-gray-200 mr-1"}),(0,r.jsx)("span",{children:"Silver"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-yellow-200 mr-1"}),(0,r.jsx)("span",{children:"Gold"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-blue-100 mr-1"}),(0,r.jsx)("span",{children:"Platinum"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-purple-100 mr-1"}),(0,r.jsx)("span",{children:"Diamond"})]})]})}),(0,r.jsx)(i,{user:x,isRoot:!0,depth:0,maxDepth:s,initialExpandedLevels:o?s:l})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72521:(e,s,l)=>{Promise.resolve().then(l.bind(l,90216))},79551:e=>{"use strict";e.exports=require("url")},82249:(e,s,l)=>{Promise.resolve().then(l.bind(l,26130))},90216:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>u});var r=l(60687),t=l(43210),a=l(82136),n=l(16189),i=l(85814),o=l.n(i),d=l(68367),c=l(35963),x=l(26721),m=l(23877);function u(){let{data:e,status:s}=(0,a.useSession)();(0,n.useRouter)();let[l,i]=(0,t.useState)(null),[u,g]=(0,t.useState)(!0),[h,p]=(0,t.useState)(""),[b,j]=(0,t.useState)(10),[f,v]=(0,t.useState)(""),[N,y]=(0,t.useState)(null),[w,k]=(0,t.useState)(null),[C,L]=(0,t.useState)(!0),[S,D]=(0,t.useState)(!0),M=async e=>{try{let s=new URLSearchParams;if(s.append("maxLevel",b.toString()),s.append("format",e),N&&s.append("userId",N.toString()),"csv"===e)return void window.open(`/api/genealogy/export?${s.toString()}`,"_blank");let l=await fetch(`/api/genealogy/export?${s.toString()}`);if(!l.ok)throw Error(`Failed to export genealogy: ${l.statusText}`);let r=await l.json(),t=new Blob([JSON.stringify(r,null,2)],{type:"application/json"}),a=URL.createObjectURL(t),n=document.createElement("a");n.href=a,n.download=`genealogy_export_${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(a)}catch(e){console.error("Error exporting genealogy:",e),alert("Failed to export genealogy data. Please try again.")}};return"loading"===s||u?(0,r.jsx)(d.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,r.jsx)(d.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center mb-6 gap-4",children:[(0,r.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,r.jsx)(m.YXz,{className:"mr-2 text-green-500"})," Network Genealogy"]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("button",{onClick:()=>D(!0),className:`px-4 py-2 rounded-md ${S?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"Enhanced View"}),(0,r.jsx)("button",{onClick:()=>D(!1),className:`px-4 py-2 rounded-md ${!S?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"Standard View"}),(0,r.jsxs)(o(),{href:"/genealogy/optimized",className:"px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 flex items-center",children:[(0,r.jsx)(m.aQJ,{className:"mr-2"})," Try New Optimized View"]}),(0,r.jsxs)(o(),{href:"/genealogy/basic-flow",className:"px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700 flex items-center",children:[(0,r.jsx)(m.aQJ,{className:"mr-2"})," Basic Flow View"]}),(0,r.jsxs)(o(),{href:"/genealogy/enhanced-flow",className:"px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 flex items-center",children:[(0,r.jsx)(m.aQJ,{className:"mr-2"})," Enhanced Flow View"]}),(0,r.jsxs)(o(),{href:"/genealogy/compare",className:"px-4 py-2 rounded-md bg-yellow-600 text-white hover:bg-yellow-700 flex items-center",children:[(0,r.jsx)(m.yk7,{className:"mr-2"})," Compare Views"]}),(0,r.jsxs)(o(),{href:"/genealogy/search",className:"px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 flex items-center",children:[(0,r.jsx)(m.KSO,{className:"mr-2"})," Advanced Search"]}),(0,r.jsxs)(o(),{href:"/genealogy/export",className:"px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 flex items-center",children:[(0,r.jsx)(m.Mbn,{className:"mr-2"})," Export Data"]}),(0,r.jsxs)(o(),{href:"/genealogy/interactive",className:"px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700 flex items-center",children:[(0,r.jsx)(m.uO9,{className:"mr-2"})," Interactive Tree"]}),(0,r.jsxs)(o(),{href:"/genealogy/virtualized",className:"px-4 py-2 rounded-md bg-teal-600 text-white hover:bg-teal-700 flex items-center",children:[(0,r.jsx)(m.aQJ,{className:"mr-2"})," Virtualized Tree"]}),(0,r.jsxs)(o(),{href:"/genealogy/mobile",className:"px-4 py-2 rounded-md bg-orange-600 text-white hover:bg-orange-700 flex items-center",children:[(0,r.jsx)(m.q5F,{className:"mr-2"})," Mobile View"]}),(0,r.jsxs)(o(),{href:"/genealogy/integration",className:"px-4 py-2 rounded-md bg-pink-600 text-white hover:bg-pink-700 flex items-center",children:[(0,r.jsx)(m.O2x,{className:"mr-2"})," Integration"]}),(0,r.jsxs)(o(),{href:"/genealogy/metrics",className:"px-4 py-2 rounded-md bg-cyan-600 text-white hover:bg-cyan-700 flex items-center",children:[(0,r.jsx)(m.YYR,{className:"mr-2"})," Metrics"]}),(0,r.jsxs)(o(),{href:"/genealogy/notifications",className:"px-4 py-2 rounded-md bg-amber-600 text-white hover:bg-amber-700 flex items-center",children:[(0,r.jsx)(m.jNV,{className:"mr-2"})," Notifications"]}),(0,r.jsxs)(o(),{href:"/genealogy/compare-users",className:"px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 flex items-center",children:[(0,r.jsx)(m.yk7,{className:"mr-2"})," Compare Users"]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),""===f.trim())y(null);else{let e=parseInt(f);if(isNaN(e))return void alert("Please enter a valid user ID");y(e)}},className:"flex gap-2",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("input",{type:"text",placeholder:"Search by User ID",value:f,onChange:e=>v(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})}),(0,r.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[(0,r.jsx)(m.KSO,{className:"mr-2 inline-block"})," Search"]})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>{v(""),y(null)},className:"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"View My Network"}),(0,r.jsxs)("select",{value:b,onChange:e=>j(parseInt(e.target.value)),className:"px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"1",children:"1 Level"}),(0,r.jsx)("option",{value:"2",children:"2 Levels"}),(0,r.jsx)("option",{value:"3",children:"3 Levels"}),(0,r.jsx)("option",{value:"5",children:"5 Levels"}),(0,r.jsx)("option",{value:"10",children:"10 Levels"})]})]})]})}),!N&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,r.jsx)(m.NPy,{className:"mr-2 text-green-500"})," Your Referral Link"]}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",value:h,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(h),alert("Referral link copied to clipboard!")},className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700",children:"Copy"})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Share this link with others to invite them to join your downline."})]}),w&&C&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,r.jsx)(m.v$b,{className:"mr-2 text-blue-500"})," Network Statistics"]}),(0,r.jsx)("button",{onClick:()=>L(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("span",{className:"text-sm",children:"Hide"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Members"}),(0,r.jsx)("div",{className:"text-xl font-semibold",children:w.totalUsers})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Direct Downline"}),(0,r.jsx)("div",{className:"text-xl font-semibold",children:w.directDownlineCount})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-3 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Network Levels"}),(0,r.jsx)("div",{className:"text-xl font-semibold",children:Object.keys(w.levelCounts).length})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Downline Earnings"}),(0,r.jsxs)("div",{className:"text-xl font-semibold",children:["₱",w.totalDownlineBalance.toFixed(2)]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"Members by Level"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:Object.entries(w.levelCounts).map(([e,s])=>(0,r.jsxs)("div",{className:"bg-gray-50 p-2 rounded-md text-center",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",e]}),(0,r.jsx)("div",{className:"font-medium",children:s})]},e))})]}),w.rankDistribution&&w.rankDistribution.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"Members by Rank"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:w.rankDistribution.map(e=>(0,r.jsxs)("div",{className:"bg-gray-50 p-2 rounded-md text-center",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.rankName}),(0,r.jsx)("div",{className:"font-medium",children:e.count})]},e.rankId))})]})]}),w.lastUpdated&&(0,r.jsxs)("div",{className:"mt-3 text-xs text-gray-500 text-right",children:["Last updated: ",new Date(w.lastUpdated).toLocaleString()]})]}),!C&&w&&(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-3 mb-6",children:(0,r.jsxs)("button",{onClick:()=>L(!0),className:"text-green-600 hover:text-green-700 flex items-center",children:[(0,r.jsx)(m.v$b,{className:"mr-2"}),(0,r.jsx)("span",{children:"Show Network Statistics"}),(0,r.jsx)(m.Vr3,{className:"ml-2"})]})}),(0,r.jsxs)("div",{className:"mb-6 flex flex-wrap gap-2",children:[(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[(0,r.jsx)(m.WCW,{className:"mr-2"})," Export Genealogy"]}),(0,r.jsx)("div",{className:"absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden group-hover:block z-10",children:(0,r.jsxs)("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[(0,r.jsx)("button",{onClick:()=>M("csv"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as CSV"}),(0,r.jsx)("button",{onClick:()=>M("json"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as JSON"})]})})]}),(0,r.jsxs)("button",{onClick:()=>window.print(),className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:[(0,r.jsx)(m.n4o,{className:"mr-2"})," Print View"]}),(0,r.jsxs)("button",{onClick:()=>{if(l){let e=`Check out my Extreme Life Herbal Products network with ${w?.totalUsers||0} members!`;navigator.share?navigator.share({title:"My Extreme Life Network",text:e,url:window.location.href}).catch(e=>console.error("Error sharing:",e)):(navigator.clipboard.writeText(`${e} ${window.location.href}`),alert("Network link copied to clipboard!"))}},className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:[(0,r.jsx)(m.Zzu,{className:"mr-2"})," Share Network"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:N?`User #${N}'s Downline`:"Your Downline"}),u?(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)(m.hW,{className:"animate-spin text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Loading genealogy data..."})]}):l?S?(0,r.jsx)(x.A,{data:l,maxDepth:b,initialExpandedLevels:2,onUserSelect:e=>console.log("Selected user:",e)}):(0,r.jsx)(c.A,{data:l,maxDepth:b,initialExpandedLevels:2}):(0,r.jsx)("p",{className:"text-gray-500",children:"No genealogy data available."})]})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var l=e=>s(s.s=e),r=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>l(204));module.exports=r})();
"use strict";(()=>{var e={};e.id=3019,e.ids=[3019],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")},98888:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>h,serverHooks:()=>v,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(31183),p=t(32190),u=t(19854),l=t(12909),m=t(47697),c=t(70762);let f=c.z.object({year:c.z.number().int().min(2e3).max(2100),month:c.z.number().int().min(1).max(12),format:c.z.enum(["csv","json"]),type:c.z.enum(["personal","team","top"])});async function d(e){try{let r=await (0,u.getServerSession)(l.Nh);if(!r||!r.user)return p.NextResponse.json({error:"You must be logged in to export reports"},{status:401});let t=r.user.email;if(!t)return p.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!s)return p.NextResponse.json({error:"User not found"},{status:404});let o=new URL(e.url),n=o.searchParams.get("year"),a=o.searchParams.get("month"),c=o.searchParams.get("format")||"csv",d=o.searchParams.get("type")||"personal",h=o.searchParams.get("userId"),x=o.searchParams.get("limit");if(!n||!a)return p.NextResponse.json({error:"Year and month parameters are required"},{status:400});let g=parseInt(n),v=parseInt(a),P=x?parseInt(x):10,R=f.safeParse({year:g,month:v,format:c,type:d});if(!R.success)return p.NextResponse.json({error:R.error.errors},{status:400});let{format:j,type:y}=R.data,V=s.id;if(h){if(!(s.rankId>=6)&&parseInt(h)!==s.id)return p.NextResponse.json({error:"You do not have permission to export reports for other users"},{status:403});V=parseInt(h)}switch(y){case"personal":let N=await (0,m.aL)(V,g,v);if(0===N.length)return p.NextResponse.json({error:"No performance data found for the specified period"},{status:404});let w=await i.z.user.findUnique({where:{id:V},select:{id:!0,name:!0,email:!0,rank:{select:{name:!0}}}});if("csv"!==j)return p.NextResponse.json({user:w,performance:N});{let e=[];e.push("User ID,Name,Email,Rank,Year,Month,Personal PV,Left Leg PV,Right Leg PV,Total Group PV,Direct Referral Bonus,Level Commissions,Group Volume Bonus,Total Earnings"),N.forEach(r=>{e.push([w?.id||"",`"${w?.name||""}"`,`"${w?.email||""}"`,`"${w?.rank?.name||""}"`,r.year,r.month,r.personalPV,r.leftLegPV,r.rightLegPV,r.totalGroupPV,r.directReferralBonus,r.levelCommissions,r.groupVolumeBonus,r.totalEarnings].join(","))});let r=e.join("\n");return new p.NextResponse(r,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="personal_performance_${g}_${v}.csv"`}})}case"team":let k=await i.z.user.findMany({where:{uplineId:V},select:{id:!0,name:!0,email:!0,rankId:!0,rank:{select:{name:!0}}}}),q=[];for(let e of k){let r=await (0,m.aL)(e.id,g,v);r.length>0&&q.push({user:e,performance:r[0]})}if(0===q.length)return p.NextResponse.json({error:"No team performance data found for the specified period"},{status:404});if("csv"!==j)return p.NextResponse.json({teamPerformance:q});{let e=[];e.push("User ID,Name,Email,Rank,Year,Month,Personal PV,Left Leg PV,Right Leg PV,Total Group PV,Direct Referral Bonus,Level Commissions,Group Volume Bonus,Total Earnings"),q.forEach(r=>{e.push([r.user.id,`"${r.user.name}"`,`"${r.user.email}"`,`"${r.user.rank.name}"`,r.performance.year,r.performance.month,r.performance.personalPV,r.performance.leftLegPV,r.performance.rightLegPV,r.performance.totalGroupPV,r.performance.directReferralBonus,r.performance.levelCommissions,r.performance.groupVolumeBonus,r.performance.totalEarnings].join(","))});let r=e.join("\n");return new p.NextResponse(r,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="team_performance_${g}_${v}.csv"`}})}case"top":if(!(s.rankId>=6))return p.NextResponse.json({error:"You do not have permission to view top earners report"},{status:403});let L=await (0,m.Qh)(g,v,P);if(0===L.length)return p.NextResponse.json({error:"No top earners data found for the specified period"},{status:404});if("csv"!==j)return p.NextResponse.json({topEarners:L});{let e=[];e.push("Rank,User ID,Name,Email,User Rank,Year,Month,Personal PV,Total Group PV,Direct Referral Bonus,Level Commissions,Group Volume Bonus,Total Earnings"),L.forEach((r,t)=>{e.push([t+1,r.user.id,`"${r.user.name}"`,`"${r.user.email}"`,`"${r.user.rank.name}"`,r.year,r.month,r.personalPV,r.totalGroupPV,r.directReferralBonus,r.levelCommissions,r.groupVolumeBonus,r.totalEarnings].join(","))});let r=e.join("\n");return new p.NextResponse(r,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="top_earners_${g}_${v}.csv"`}})}default:return p.NextResponse.json({error:"Invalid report type"},{status:400})}}catch(e){return console.error("Error exporting report:",e),p.NextResponse.json({error:"Failed to export report"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/binary-mlm/export/route",pathname:"/api/binary-mlm/export",filename:"route",bundlePath:"app/api/binary-mlm/export/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\binary-mlm\\export\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:v}=h;function P(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,8381,2610],()=>t(98888));module.exports=s})();
"use strict";exports.id=4558,exports.ids=[4558],exports.modules={64558:(e,s,l)=>{l.r(s),l.d(s,{default:()=>c});var t=l(60687),r=l(43210),i=l(85814),a=l.n(i),n=l(30474),d=l(23877);let c=({currentUser:e,downlineMembers:s=[]})=>{let[l,i]=(0,r.useState)([e.id]),c=(0,r.useCallback)(s=>{let l={...e,children:[]},t=new Map;for(let e of(t.set(l.id,l),s))t.set(e.id,{...e,children:[]});for(let l of s){let s=t.get(e.id),r=t.get(l.id);s&&r&&(r.position||(r.position=s.children&&s.children.length>0?"right":"left"),s.children?.push(r))}return l},[e]),x=(0,r.useMemo)(()=>c(s),[c,s]),m=(0,r.useCallback)(e=>{i(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},[]),o=(0,r.useCallback)(e=>{switch(e.toLowerCase()){case"distributor":case"platinum":default:return"bg-gray-100 text-gray-800";case"silver":return"bg-gray-200 text-gray-800";case"gold":return"bg-yellow-100 text-yellow-800";case"diamond":return"bg-blue-100 text-blue-800"}},[]),u=(0,r.useCallback)((e,s=0)=>{let r=l.includes(e.id),i=e.children&&e.children.length>0;return(0,t.jsxs)("div",{className:`ml-${6*s}`,children:[(0,t.jsxs)("div",{className:`flex items-center p-2 rounded-md ${0===s?"bg-green-50":"hover:bg-gray-50"}`,children:[i&&(0,t.jsx)("button",{onClick:()=>m(e.id),className:"mr-2 text-gray-500 hover:text-gray-700 focus:outline-none",children:r?(0,t.jsx)(d.Vr3,{size:14}):(0,t.jsx)(d.X6T,{size:14})}),(0,t.jsxs)("div",{className:"flex items-center flex-1",children:[(0,t.jsx)("div",{className:"relative w-8 h-8 rounded-full overflow-hidden bg-gray-200 mr-2",children:e.image?(0,t.jsx)(n.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover"}):(0,t.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,t.jsx)(d.x$1,{className:"text-gray-500"})})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900",children:e.name}),e.position&&(0,t.jsx)("span",{className:`ml-2 text-xs px-2 py-0.5 rounded ${"left"===e.position?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"}`,children:"left"===e.position?"Left":"Right"})]}),(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)("span",{className:`text-xs px-1.5 py-0.5 rounded ${o(e.rank)}`,children:e.rank})})]})]})]}),r&&i&&(0,t.jsx)("div",{className:"ml-6 mt-1 border-l-2 border-gray-200 pl-2",children:e.children?.map(e=>u(e,s+1))})]},e.id)},[l,o,m]);return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,t.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-b flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,t.jsx)(d.YXz,{className:"mr-2 text-blue-500"})," My Genealogy"]}),(0,t.jsx)(a(),{href:"/genealogy",className:"text-sm text-blue-600 hover:text-blue-800 flex items-center",children:"View Full Tree"})]}),(0,t.jsxs)("div",{className:"p-4",children:[(0,r.useMemo)(()=>u(x),[u,x]),0===s.length&&(0,t.jsxs)("div",{className:"text-center py-6",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 text-blue-500 mb-4",children:(0,t.jsx)(d.NPy,{size:24})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:"No Downline Members Yet"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Start building your network by inviting new members"}),(0,t.jsx)(a(),{href:"/referrals",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700",children:"Invite Members"})]})]})]})}}};
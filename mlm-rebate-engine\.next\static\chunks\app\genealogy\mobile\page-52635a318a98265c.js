(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3147],{41644:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var l=a(95155),t=a(12115),r=a(12108),i=a(87747),n=a(29911),c=a(6874),d=a.n(c);function m(e){var s,a,r;let{userId:i,maxLevel:c=3,initialPageSize:d=10}=e,[m,o]=(0,t.useState)(!0),[x,h]=(0,t.useState)(null),[u,j]=(0,t.useState)(null),[b,f]=(0,t.useState)(null),[g,v]=(0,t.useState)([]),[p,N]=(0,t.useState)(new Set),[y,w]=(0,t.useState)(""),[S,k]=(0,t.useState)([]),[C,O]=(0,t.useState)(!1),[M,P]=(0,t.useState)(!1),[E,D]=(0,t.useState)(null);(0,t.useEffect)(()=>{(async()=>{o(!0),h(null);try{var e;let s=new URLSearchParams({userId:i.toString(),maxLevel:"1",pageSize:d.toString(),includePerformanceMetrics:"true"}),a=await fetch("/api/genealogy?".concat(s.toString()));if(!a.ok)throw Error("Failed to fetch genealogy data");let l=await a.json(),t={id:l.id,name:l.name,email:l.email,rankName:l.rank.name,level:0,downlineCount:l._count.downline,createdAt:l.createdAt,walletBalance:l.walletBalance,performanceMetrics:l.performanceMetrics,children:null==(e=l.children)?void 0:e.map(e=>({id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:1,downlineCount:e._count.downline,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics}))};j(t),f(t)}catch(e){h(e instanceof Error?e.message:"An unknown error occurred")}finally{o(!1)}})()},[i,d]);let A=(0,t.useCallback)(async e=>{if(e.children)return e.children;try{var s;let a=new URLSearchParams({userId:e.id.toString(),maxLevel:"1",pageSize:d.toString(),includePerformanceMetrics:"true"}),l=await fetch("/api/genealogy?".concat(a.toString()));if(!l.ok)throw Error("Failed to fetch user children");return(null==(s=(await l.json()).children)?void 0:s.map(s=>({id:s.id,name:s.name,email:s.email,rankName:s.rank.name,level:e.level+1,downlineCount:s._count.downline,createdAt:s.createdAt,walletBalance:s.walletBalance,performanceMetrics:s.performanceMetrics})))||[]}catch(e){return console.error("Error fetching user children:",e),[]}},[d]),B=(0,t.useCallback)(async e=>{if(p.has(e.id))N(s=>{let a=new Set(s);return a.delete(e.id),a});else if(N(s=>{let a=new Set(s);return a.add(e.id),a}),!e.children){o(!0);try{let s=await A(e);b&&b.id===e.id&&f({...b,children:s}),u&&u.id===e.id&&j({...u,children:s}),v(a=>a.map(a=>a.id===e.id?{...a,children:s}:a))}catch(e){console.error("Error expanding node:",e)}finally{o(!1)}}},[p,b,u,A]),z=(0,t.useCallback)(async e=>{o(!0);try{let s={...e};if(!e.children){let a=await A(e);s={...e,children:a}}b&&v(e=>[...e,b]),f(s)}catch(e){console.error("Error navigating to child:",e)}finally{o(!1)}},[b,A]),L=(0,t.useCallback)(()=>{if(0===g.length)return;let e=g[g.length-1];v(e=>e.slice(0,-1)),f(e)},[g]),F=(0,t.useCallback)(async()=>{if(y.trim()){O(!0);try{let e=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:y,page:1,pageSize:10})});if(!e.ok)throw Error("Failed to search genealogy");let s=await e.json();k(s.users)}catch(e){console.error("Error searching genealogy:",e)}finally{O(!1)}}},[y]),T=(0,t.useCallback)(async e=>{o(!0);try{var s;let a=new URLSearchParams({userId:e.id.toString(),maxLevel:"1",pageSize:d.toString(),includePerformanceMetrics:"true"}),l=await fetch("/api/genealogy?".concat(a.toString()));if(!l.ok)throw Error("Failed to fetch user data");let t=await l.json(),r={id:t.id,name:t.name,email:t.email,rankName:t.rank.name,level:0,downlineCount:t._count.downline,createdAt:t.createdAt,walletBalance:t.walletBalance,performanceMetrics:t.performanceMetrics,children:null==(s=t.children)?void 0:s.map(e=>({id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:1,downlineCount:e._count.downline,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics}))};v([]),f(r),P(!1)}catch(e){console.error("Error selecting user from search:",e)}finally{o(!1)}},[d]),I=e=>void 0===e?"":new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);return m&&!b?(0,l.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,l.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading genealogy data..."})]}):x?(0,l.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,l.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,l.jsx)(n.BS8,{className:"mr-2"}),"Error loading genealogy data"]}),(0,l.jsx)("p",{className:"text-red-600",children:x})]}):E?(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,l.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center bg-gray-50",children:[(0,l.jsxs)("button",{onClick:()=>D(null),className:"flex items-center text-blue-600",children:[(0,l.jsx)(n.QVr,{className:"mr-2"}),"Back"]}),(0,l.jsx)("h3",{className:"font-semibold",children:"User Details"}),(0,l.jsx)("div",{className:"w-6"})," "]}),(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:E.name.charAt(0).toUpperCase()}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-lg font-semibold",children:E.name}),(0,l.jsx)("div",{className:"text-sm text-gray-500",children:E.email}),(0,l.jsx)("div",{className:"mt-1 text-xs px-2 py-0.5 inline-block rounded-full bg-blue-100 text-blue-800",children:E.rankName})]})]}),(0,l.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"User ID"}),(0,l.jsx)("div",{children:E.id})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Downline Members"}),(0,l.jsx)("div",{children:E.downlineCount})]}),void 0!==E.walletBalance&&(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Wallet Balance"}),(0,l.jsx)("div",{children:I(E.walletBalance)})]}),E.createdAt&&(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Member Since"}),(0,l.jsx)("div",{children:(r=E.createdAt)?new Date(r).toLocaleDateString():"N/A"})]}),E.level>0&&(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Network Level"}),(0,l.jsxs)("div",{children:["Level ",E.level]})]})]}),E.performanceMetrics&&(0,l.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,l.jsx)("h3",{className:"font-medium mb-3",children:"Performance Metrics"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,l.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,l.jsxs)("div",{className:"flex items-center mb-1",children:[(0,l.jsx)(n.AsH,{className:"text-blue-500 mr-2"}),(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Personal Sales"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold",children:I(E.performanceMetrics.personalSales)})]}),(0,l.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,l.jsxs)("div",{className:"flex items-center mb-1",children:[(0,l.jsx)(n.YXz,{className:"text-green-500 mr-2"}),(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Team Sales"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold",children:I(E.performanceMetrics.teamSales)})]}),(0,l.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,l.jsxs)("div",{className:"flex items-center mb-1",children:[(0,l.jsx)(n.lcY,{className:"text-yellow-500 mr-2"}),(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Rebates Earned"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold",children:I(E.performanceMetrics.rebatesEarned)})]}),(0,l.jsxs)("div",{className:"bg-purple-50 p-3 rounded-md",children:[(0,l.jsxs)("div",{className:"flex items-center mb-1",children:[(0,l.jsx)(n.YXz,{className:"text-purple-500 mr-2"}),(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"New Members (30d)"})]}),(0,l.jsx)("div",{className:"text-lg font-semibold",children:E.performanceMetrics.newTeamMembers})]})]})]}),(0,l.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:(0,l.jsx)("button",{onClick:()=>{D(null),z(E)},className:"px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm",children:"View Downline"})})})]})]}):M?(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,l.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center bg-gray-50",children:[(0,l.jsxs)("button",{onClick:()=>P(!1),className:"flex items-center text-blue-600",children:[(0,l.jsx)(n.QVr,{className:"mr-2"}),"Back"]}),(0,l.jsx)("h3",{className:"font-semibold",children:"Search Genealogy"}),(0,l.jsx)("div",{className:"w-6"})," "]}),(0,l.jsx)("div",{className:"p-4 border-b",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID...",value:y,onChange:e=>w(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,l.jsx)("button",{onClick:F,disabled:C,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:C?(0,l.jsx)(n.hW,{className:"animate-spin"}):(0,l.jsx)(n.KSO,{})})]})}),(0,l.jsx)("div",{className:"divide-y",children:0===S.length?(0,l.jsx)("div",{className:"p-4 text-center text-gray-500",children:C?(0,l.jsxs)("div",{className:"flex items-center justify-center",children:[(0,l.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Searching..."})]}):y?(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{children:"No results found."}),(0,l.jsx)("p",{className:"text-sm mt-1",children:"Try a different search term."})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)(n.KSO,{className:"text-4xl text-gray-300 mx-auto mb-2"}),(0,l.jsx)("p",{children:"Enter a search term to find users."})]})}):S.map(e=>(0,l.jsx)("div",{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:e.name.charAt(0).toUpperCase()}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("div",{className:"font-medium",children:e.name}),(0,l.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,l.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]}),(0,l.jsx)("button",{onClick:()=>T(e),className:"px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 text-sm",children:"View"})]})},e.id))})]}):(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,l.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center bg-gray-50",children:[g.length>0?(0,l.jsxs)("button",{onClick:L,className:"flex items-center text-blue-600",children:[(0,l.jsx)(n.QVr,{className:"mr-2"}),"Back"]}):(0,l.jsx)("div",{}),(0,l.jsx)("h3",{className:"font-semibold",children:"Mobile Genealogy"}),(0,l.jsx)("button",{onClick:()=>P(!0),className:"text-blue-600",children:(0,l.jsx)(n.KSO,{})})]}),b&&(0,l.jsxs)("div",{className:"p-4 border-b bg-blue-50",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-3",children:(0,l.jsx)(n.x$1,{className:"text-blue-500"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("div",{className:"font-medium",children:b.name}),(0,l.jsx)("div",{className:"text-sm text-gray-500",children:b.email}),(0,l.jsx)("div",{className:"mt-1 text-xs px-2 py-0.5 inline-block rounded-full bg-blue-100 text-blue-800",children:b.rankName})]}),(0,l.jsx)("button",{onClick:()=>D(b),className:"px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm",children:"Details"})]}),(0,l.jsxs)("div",{className:"mt-3 grid grid-cols-3 gap-2 text-center text-sm",children:[(0,l.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,l.jsx)("div",{className:"font-medium",children:b.downlineCount}),(0,l.jsx)("div",{className:"text-xs text-gray-500",children:"Downline"})]}),b.performanceMetrics&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,l.jsx)("div",{className:"font-medium",children:I(b.performanceMetrics.personalSales)}),(0,l.jsx)("div",{className:"text-xs text-gray-500",children:"Personal"})]}),(0,l.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,l.jsx)("div",{className:"font-medium",children:I(b.performanceMetrics.teamSales)}),(0,l.jsx)("div",{className:"text-xs text-gray-500",children:"Team"})]})]})]})]}),(0,l.jsxs)("div",{className:"divide-y",children:[m&&(0,l.jsxs)("div",{className:"p-4 flex items-center justify-center",children:[(0,l.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading..."})]}),!m&&(null==b||null==(s=b.children)?void 0:s.length)===0&&(0,l.jsx)("div",{className:"p-4 text-center text-gray-500",children:(0,l.jsx)("p",{children:"No downline members found."})}),!m&&(null==b||null==(a=b.children)?void 0:a.map(e=>(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:e.name.charAt(0).toUpperCase()}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("div",{className:"font-medium",children:e.name}),(0,l.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,l.jsxs)("div",{className:"flex items-center text-xs text-gray-400 mt-1",children:[(0,l.jsxs)("span",{className:"mr-2",children:["ID: ",e.id]}),(0,l.jsxs)("span",{children:["Downline: ",e.downlineCount]})]})]}),(0,l.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,l.jsx)("button",{onClick:()=>D(e),className:"p-1 text-blue-600",children:(0,l.jsx)(n.__w,{})}),e.downlineCount>0&&(0,l.jsx)("button",{onClick:()=>z(e),className:"p-1 text-green-600",children:(0,l.jsx)(n.YXz,{})})]})]}),e.downlineCount>0&&(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsx)("button",{onClick:()=>B(e),className:"flex items-center text-sm text-blue-600",children:p.has(e.id)?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.Vr3,{className:"mr-1"}),"Hide Details"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.X6T,{className:"mr-1"}),"Show Details"]})}),p.has(e.id)&&(0,l.jsxs)("div",{className:"mt-2 pl-4 border-l-2 border-gray-200",children:[e.performanceMetrics&&(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,l.jsxs)("div",{className:"bg-gray-50 p-2 rounded",children:[(0,l.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,l.jsx)("div",{className:"font-medium",children:I(e.performanceMetrics.personalSales)})]}),(0,l.jsxs)("div",{className:"bg-gray-50 p-2 rounded",children:[(0,l.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,l.jsx)("div",{className:"font-medium",children:I(e.performanceMetrics.teamSales)})]})]}),(0,l.jsxs)("button",{onClick:()=>z(e),className:"w-full mt-2 px-3 py-1.5 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 text-sm flex items-center justify-center",children:[(0,l.jsx)(n.YXz,{className:"mr-2"}),"View Downline (",e.downlineCount,")"]})]})]})]},e.id)))]})]})}function o(){let{data:e,status:s}=(0,r.useSession)(),[a,c]=(0,t.useState)(void 0),{data:o,isLoading:x}=(0,i.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let a=await fetch("/api/users/me");if(!a.ok)throw Error("Failed to fetch user data");return await a.json()},enabled:"authenticated"===s});return(o&&!a&&c(o.id),"loading"===s||x)?(0,l.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,l.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,l.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,l.jsx)(n.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,l.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,l.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,l.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,l.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Mobile Genealogy View"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Optimized for mobile devices and touch screens"})]}),(0,l.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,l.jsx)(n.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsx)("div",{className:"md:col-span-1",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,l.jsxs)("div",{className:"p-4 bg-gray-50 border-b border-gray-200 flex items-center",children:[(0,l.jsx)(n.q5F,{className:"text-blue-500 mr-2"}),(0,l.jsx)("h2",{className:"font-medium",children:"Mobile Genealogy View"})]}),(0,l.jsx)("div",{className:"max-w-sm mx-auto my-4",children:a?(0,l.jsx)(m,{userId:a,maxLevel:3,initialPageSize:10}):(0,l.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,l.jsx)(n.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading genealogy data..."})]})})]})}),(0,l.jsx)("div",{className:"md:col-span-1",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,l.jsxs)("div",{className:"p-4 bg-gray-50 border-b border-gray-200 flex items-center",children:[(0,l.jsx)(n.z4D,{className:"text-blue-500 mr-2"}),(0,l.jsx)("h2",{className:"font-medium",children:"About Mobile View"})]}),(0,l.jsx)("div",{className:"p-4",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{children:"The mobile genealogy view is designed specifically for mobile devices and touch screens. It provides a simplified, list-based view of your genealogy that's easy to navigate on smaller screens."}),(0,l.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,l.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Key Features"}),(0,l.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,l.jsx)("li",{children:"Touch-friendly navigation"}),(0,l.jsx)("li",{children:"Simplified list view of your downline"}),(0,l.jsx)("li",{children:"Expandable details for each member"}),(0,l.jsx)("li",{children:"Quick access to performance metrics"}),(0,l.jsx)("li",{children:"Search functionality to find specific members"}),(0,l.jsx)("li",{children:"Optimized for small screens and mobile data connections"})]})]}),(0,l.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,l.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"How to Use"}),(0,l.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,l.jsx)("li",{children:"Tap on a member's name to see their details"}),(0,l.jsx)("li",{children:'Use the "Show Details" button to expand information'}),(0,l.jsx)("li",{children:'Tap "View Downline" to navigate to a member\'s downline'}),(0,l.jsx)("li",{children:"Use the back button to return to previous views"}),(0,l.jsx)("li",{children:"Use the search icon to find specific members"})]})]}),(0,l.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,l.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"When to Use Mobile View"}),(0,l.jsx)("p",{className:"text-yellow-700",children:"The mobile view is ideal for:"}),(0,l.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700 mt-2",children:[(0,l.jsx)("li",{children:"Checking your genealogy on the go"}),(0,l.jsx)("li",{children:"Devices with smaller screens"}),(0,l.jsx)("li",{children:"Touch-based navigation"}),(0,l.jsx)("li",{children:"Limited bandwidth connections"}),(0,l.jsx)("li",{children:"Quick lookups of specific members"})]})]}),(0,l.jsx)("p",{children:"For a more comprehensive visualization with advanced features, switch to the desktop view when you're on a larger screen."})]})})]})})]})]})}},55231:(e,s,a)=>{Promise.resolve().then(a.bind(a,41644))},74436:(e,s,a)=>{"use strict";a.d(s,{k5:()=>m});var l=a(12115),t={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=l.createContext&&l.createContext(t),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var a=arguments[s];for(var l in a)Object.prototype.hasOwnProperty.call(a,l)&&(e[l]=a[l])}return e}).apply(this,arguments)}function c(e,s){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);s&&(l=l.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),a.push.apply(a,l)}return a}function d(e){for(var s=1;s<arguments.length;s++){var a=null!=arguments[s]?arguments[s]:{};s%2?c(Object(a),!0).forEach(function(s){var l,t,r;l=e,t=s,r=a[s],(t=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var l=a.call(e,s||"default");if("object"!=typeof l)return l;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(t))in l?Object.defineProperty(l,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):l[t]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(a,s))})}return e}function m(e){return s=>l.createElement(o,n({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,a)=>l.createElement(s.tag,d({key:a},s.attr),e(s.child)))}(e.child))}function o(e){var s=s=>{var a,{attr:t,size:r,title:c}=e,m=function(e,s){if(null==e)return{};var a,l,t=function(e,s){if(null==e)return{};var a={};for(var l in e)if(Object.prototype.hasOwnProperty.call(e,l)){if(s.indexOf(l)>=0)continue;a[l]=e[l]}return a}(e,s);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(l=0;l<r.length;l++)a=r[l],!(s.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(t[a]=e[a])}return t}(e,i),o=r||s.size||"1em";return s.className&&(a=s.className),e.className&&(a=(a?a+" ":"")+e.className),l.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,t,m,{className:a,style:d(d({color:e.color||s.color},s.style),e.style),height:o,width:o,xmlns:"http://www.w3.org/2000/svg"}),c&&l.createElement("title",null,c),e.children)};return void 0!==r?l.createElement(r.Consumer,null,e=>s(e)):s(t)}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6967,7747,8441,1684,7358],()=>s(55231)),_N_E=e.O()}]);
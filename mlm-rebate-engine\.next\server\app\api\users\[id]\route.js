(()=>{var e={};e.id=6100,e.ids=[6100],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>u,Nh:()=>c,aP:()=>l});var s=r(96330),o=r(13581),n=r(85663),a=r(55511),i=r.n(a);async function u(e){return await n.Ay.hash(e,10)}function l(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new s.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,r.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:a,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var o=r(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=o?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(s,n,i):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(35426));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var s=r(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69812:(e,t,r)=>{"use strict";r.d(t,{HU:()=>a,Ld:()=>i,q8:()=>n,tf:()=>u,zK:()=>o});var s=r(70762);s.z.object({email:s.z.string().email("Invalid email address"),password:s.z.string().min(8,"Password must be at least 8 characters"),csrfToken:s.z.string().optional()});let o=s.z.object({name:s.z.string().min(2,"Name must be at least 2 characters"),email:s.z.string().email("Invalid email address"),password:s.z.string().min(8,"Password must be at least 8 characters"),confirmPassword:s.z.string().min(8,"Confirm password must be at least 8 characters"),phone:s.z.string().optional(),birthdate:s.z.string().optional(),address:s.z.string().optional(),city:s.z.string().optional(),region:s.z.string().optional(),postalCode:s.z.string().optional(),uplineId:s.z.string().optional(),profileImage:s.z.string().optional(),preferredPaymentMethod:s.z.string().optional(),bankName:s.z.string().optional(),bankAccountNumber:s.z.string().optional(),bankAccountName:s.z.string().optional(),gcashNumber:s.z.string().optional(),payMayaNumber:s.z.string().optional(),receiveUpdates:s.z.boolean().optional().default(!1),agreeToTerms:s.z.boolean()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>!0===e.agreeToTerms,{message:"You must agree to the terms and conditions",path:["agreeToTerms"]}).refine(e=>"bank"===e.preferredPaymentMethod?!!e.bankName&&!!e.bankAccountNumber&&!!e.bankAccountName:"gcash"===e.preferredPaymentMethod?!!e.gcashNumber:"paymaya"!==e.preferredPaymentMethod||!!e.payMayaNumber,{message:"Payment details are required for the selected payment method",path:["preferredPaymentMethod"]}),n=s.z.object({name:s.z.string().min(2,"Name must be at least 2 characters").optional(),phone:s.z.string().optional(),currentPassword:s.z.string().optional(),newPassword:s.z.string().min(8,"New password must be at least 8 characters").optional(),confirmNewPassword:s.z.string().optional(),profileImage:s.z.string().optional()}).refine(e=>!e.newPassword||e.newPassword===e.confirmNewPassword,{message:"New passwords don't match",path:["confirmNewPassword"]}).refine(e=>!e.newPassword||!!e.currentPassword,{message:"Current password is required to set a new password",path:["currentPassword"]}),a=s.z.object({name:s.z.string().min(2,"Product name must be at least 2 characters"),description:s.z.string().optional(),price:s.z.number().positive("Price must be positive"),image:s.z.string().optional(),rebateConfigs:s.z.array(s.z.object({level:s.z.number().int().positive("Level must be a positive integer"),percentage:s.z.number().positive("Percentage must be positive").max(100,"Percentage cannot exceed 100%")})).min(1,"At least one rebate configuration is required")}),i=s.z.object({productId:s.z.number().int().positive("Product ID must be a positive integer"),quantity:s.z.number().int().positive("Quantity must be a positive integer"),paymentMethodId:s.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:s.z.record(s.z.any()).optional(),referenceNumber:s.z.string().optional(),shippingMethodId:s.z.number().int().positive("Shipping method ID must be a positive integer").optional(),shippingDetails:s.z.record(s.z.any()).optional(),shippingAddress:s.z.string().optional(),shippingFee:s.z.number().nonnegative("Shipping fee must be a non-negative number").optional(),referralCode:s.z.string().optional()});function u(e,t){try{let r=e.parse(t);return{success:!0,data:r}}catch(e){if(e instanceof s.z.ZodError){let t={};return e.errors.forEach(e=>{t[e.path.join(".")]=e.message}),{success:!1,errors:t}}return{success:!1,errors:{_error:"An unexpected error occurred during validation"}}}}s.z.object({amount:s.z.number().positive("Amount must be positive"),type:s.z.enum(["withdrawal","deposit"],{errorMap:()=>({message:"Invalid transaction type"})}),description:s.z.string().optional(),paymentMethodId:s.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:s.z.record(s.z.any()).optional(),referenceNumber:s.z.string().optional()}),s.z.object({checkAll:s.z.boolean().optional()})},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89605:(e,t,r)=>{"use strict";r.d(t,{_L:()=>i,g3:()=>a,ic:()=>u});class s{constructor(e=3e5){this.cache=new Map,this.defaultTTL=e}set(e,t,r){let s=Date.now()+(r||this.defaultTTL);this.cache.set(e,{value:t,expiry:s})}get(e){let t=this.cache.get(e);if(t)return Date.now()>t.expiry?void this.cache.delete(e):t.value}has(e){let t=this.cache.get(e);return!!t&&(!(Date.now()>t.expiry)||(this.cache.delete(e),!1))}delete(e){this.cache.delete(e)}clear(){this.cache.clear()}async getOrSet(e,t,r){let s=this.get(e);if(void 0!==s)return s;let o=await t();return this.set(e,o,r),o}cleanup(){let e=Date.now();for(let[t,r]of this.cache.entries())e>r.expiry&&this.cache.delete(t)}}let o=new s,n=e=>({set:(t,r,s)=>o.set(`${e}:${t}`,r,s),get:t=>o.get(`${e}:${t}`),has:t=>o.has(`${e}:${t}`),delete:t=>o.delete(`${e}:${t}`),getOrSet:(t,r,s)=>o.getOrSet(`${e}:${t}`,r,s),clearNamespace:()=>{for(let[t]of o.cache.entries())t.startsWith(`${e}:`)&&o.delete(t)}}),a=n("user"),i=n("product"),u=n("genealogy");n("rebate"),setInterval(()=>{o.cleanup()},36e5)},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},99435:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>w,serverHooks:()=>z,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{DELETE:()=>f,GET:()=>g,PATCH:()=>h});var o=r(96559),n=r(48088),a=r(37719),i=r(31183),u=r(32190),l=r(19854),c=r(12909),p=r(89605),d=r(85663),m=r(69812);async function g(e,{params:t}){try{let e=await (0,l.getServerSession)(c.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let r=parseInt(t.id);if(isNaN(r))return u.NextResponse.json({error:"Invalid user ID"},{status:400});let s=`user:${r}`,o=await p.g3.getOrSet(s,async()=>{let e=await i.z.user.findUnique({where:{id:r},select:{id:!0,name:!0,email:!0,phone:!0,profileImage:!0,rankId:!0,uplineId:!0,walletBalance:!0,createdAt:!0,rank:{select:{id:!0,name:!0,level:!0}},upline:{select:{id:!0,name:!0,email:!0}},_count:{select:{downline:!0,purchases:!0,rebatesReceived:!0}}}});if(!e)return null;let t=await i.z.$transaction([i.z.purchase.aggregate({where:{userId:r},_sum:{totalAmount:!0}}),i.z.rebate.aggregate({where:{receiverId:r},_sum:{amount:!0}}),i.z.rebate.aggregate({where:{generatorId:r},_sum:{amount:!0}}),i.z.user.count({where:{uplineId:r}})]);return{...e,stats:{totalPurchases:t[0]._sum.totalAmount||0,totalRebatesReceived:t[1]._sum.amount||0,totalRebatesGenerated:t[2]._sum.amount||0,directDownlineCount:t[3]}}},3e5);if(!o)return u.NextResponse.json({error:"User not found"},{status:404});return u.NextResponse.json(o)}catch(e){return console.error("Error fetching user:",e),u.NextResponse.json({error:"Failed to fetch user"},{status:500})}}async function h(e,{params:t}){try{let r=await (0,l.getServerSession)(c.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let s=parseInt(t.id);if(isNaN(s))return u.NextResponse.json({error:"Invalid user ID"},{status:400});let o=r.user.email;if(!o)return u.NextResponse.json({error:"User email not found in session"},{status:400});let n=await i.z.user.findUnique({where:{email:o},select:{id:!0}});if(!n)return u.NextResponse.json({error:"Authenticated user not found"},{status:404});if(n.id!==s)return u.NextResponse.json({error:"You can only update your own profile"},{status:403});let a=await e.json(),g=(0,m.tf)(m.q8,a);if(!g.success)return u.NextResponse.json({errors:g.errors},{status:400});let{name:h,phone:f,currentPassword:w,newPassword:b,profileImage:y}=g.data,z={};if(h&&(z.name=h),f&&(z.phone=f),y&&(z.profileImage=y),b){if(!w)return u.NextResponse.json({error:"Current password is required to set a new password"},{status:400});let e=await i.z.user.findUnique({where:{id:s},select:{password:!0}});if(!e)return u.NextResponse.json({error:"User not found"},{status:404});if(!await d.Ay.compare(w,e.password))return u.NextResponse.json({error:"Current password is incorrect"},{status:400});z.password=await d.Ay.hash(b,10)}let v=await i.z.user.update({where:{id:s},data:z,select:{id:!0,name:!0,email:!0,phone:!0,profileImage:!0,rankId:!0,createdAt:!0,updatedAt:!0}});return p.g3.delete(`user:${s}`),p.g3.clearNamespace(),u.NextResponse.json(v)}catch(e){return console.error("Error updating user:",e),u.NextResponse.json({error:"Failed to update user"},{status:500})}}async function f(e,{params:t}){try{let e=await (0,l.getServerSession)(c.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});return u.NextResponse.json({error:"This feature is not yet implemented"},{status:501})}catch(e){return console.error("Error deleting user:",e),u.NextResponse.json({error:"Failed to delete user"},{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:"app/api/users/[id]/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:y,serverHooks:z}=w;function v(){return(0,a.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:y})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,8044,3112,8381],()=>r(99435));module.exports=s})();
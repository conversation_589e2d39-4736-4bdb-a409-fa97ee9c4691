"use strict";exports.id=9342,exports.ids=[9342],exports.modules={69342:(e,t,r)=>{r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),i=r(85814),l=r.n(i),c=r(30474),n=r(23877);let d=()=>{let[e,t]=(0,a.useState)(!0),[r,i]=(0,a.useState)([]),[d,m]=(0,a.useState)(null);(0,a.useEffect)(()=>{o()},[]);let o=async()=>{try{t(!0);let e=await fetch("/api/referrals/activity?limit=5");if(!e.ok)throw Error("Failed to fetch referral activity");let r=await e.json();i(r.activities||[])}catch(e){console.error("Error fetching referral activity:",e),m("Failed to load recent activity. Please try again.")}finally{t(!1)}},x=e=>new Date(e).toLocaleDateString("en-PH",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),u=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),h=e=>{switch(e){case"click":return(0,s.jsx)(n.EQc,{className:"text-blue-500"});case"purchase":return(0,s.jsx)(n.YXz,{className:"text-purple-500"});case"commission":return(0,s.jsx)(n.MxO,{className:"text-green-500"});default:return(0,s.jsx)(n.YXz,{className:"text-gray-500"})}},f=e=>{switch(e.type){case"click":return"Someone clicked your referral link";case"purchase":return`Someone purchased ${e.productName||"a product"} through your link`;case"commission":return`You earned ${u(e.amount||0)} commission`;default:return"Referral activity"}};return e?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 flex items-center justify-center h-64",children:[(0,s.jsx)(n.hW,{className:"animate-spin text-purple-500 mr-2"}),(0,s.jsx)("span",{children:"Loading recent activity..."})]}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsxs)("div",{className:"p-4 bg-purple-50 border-b border-purple-100",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,s.jsx)(n.YXz,{className:"mr-2 text-purple-600"}),"Recent Referral Activity"]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Track clicks, purchases, and commissions from your referral links"})]}),(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:0===r.length?(0,s.jsxs)("div",{className:"p-6 text-center text-gray-500",children:[(0,s.jsx)("p",{children:"No recent referral activity."}),(0,s.jsx)("p",{className:"mt-2 text-sm",children:"Start sharing your referral links to see activity here!"})]}):r.map(e=>(0,s.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:h(e.type)}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:f(e)}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:x(e.createdAt)})]}),e.productName&&(0,s.jsxs)("div",{className:"mt-1 flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-6 w-6 bg-gray-100 rounded overflow-hidden mr-2",children:e.productImage?(0,s.jsx)(c.default,{src:e.productImage,alt:e.productName,width:24,height:24,className:"object-cover w-full h-full"}):(0,s.jsx)("div",{className:"w-full h-full"})}),(0,s.jsx)("span",{className:"text-xs text-gray-600 truncate",children:e.productName})]}),(0,s.jsxs)("div",{className:"mt-1 text-xs text-gray-500",children:["Link: ",window.location.origin,"/s/",e.linkCode]})]})]})},e.id))}),(0,s.jsx)("div",{className:"p-4 bg-gray-50 border-t border-gray-200",children:(0,s.jsxs)(l(),{href:"/referrals",className:"text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center justify-center",children:["View all referral activity",(0,s.jsx)(n.Z0P,{className:"ml-1"})]})})]})}}};
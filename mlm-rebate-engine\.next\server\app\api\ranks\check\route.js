(()=>{var e={};e.id=2054,e.ids=[2054],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>l,Nh:()=>u,aP:()=>c});var o=t(96330),n=t(13581),s=t(85663),a=t(55511),i=t.n(a);async function l(e){return await s.Ay.hash(e,10)}function c(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new o.PrismaClient;let u={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new o.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await s.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:a,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var o=t(96330);let n=global.prisma||new o.PrismaClient({log:["query"]})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61904:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let o=t(49526).createTransport({host:process.env.EMAIL_HOST||"smtp.example.com",port:parseInt(process.env.EMAIL_PORT||"587"),secure:"true"===process.env.EMAIL_SECURE,auth:{user:process.env.EMAIL_USER||"<EMAIL>",pass:process.env.EMAIL_PASSWORD||"password"}}),n={rebateReceived:e=>({subject:`You've Received a Rebate of $${e.amount.toFixed(2)}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">New Rebate Received!</h2>
          <p>Hello ${e.userName},</p>
          <p>Great news! You've received a rebate of <strong style="color: #48bb78;">$${e.amount.toFixed(2)}</strong>.</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Rebate Details:</h3>
            <ul style="padding-left: 20px;">
              <li>Amount: <strong>$${e.amount.toFixed(2)}</strong></li>
              <li>From: <strong>${e.generatorName}</strong></li>
              <li>Level: <strong>${e.level}</strong></li>
              <li>Product: <strong>${e.productName}</strong></li>
            </ul>
          </div>
          <p>This rebate has been added to your wallet balance. You can view your rebate details and wallet balance by logging into your account.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/wallet" style="background-color: #4299e1; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Wallet</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for being part of our MLM network!</p>
        </div>
      `}),rankAdvancement:e=>({subject:`Congratulations on Your Rank Advancement to ${e.newRank}!`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">Rank Advancement Achievement!</h2>
          <p>Hello ${e.userName},</p>
          <p>Congratulations! You've advanced from <strong>${e.oldRank}</strong> to <strong style="color: #805ad5;">${e.newRank}</strong>!</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Your New Benefits:</h3>
            <ul style="padding-left: 20px;">
              ${e.benefits.map(e=>`<li>${e}</li>`).join("")}
            </ul>
          </div>
          <p>Keep up the great work! As you continue to grow your network and increase your sales, you'll unlock even more benefits and higher rebate percentages.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" style="background-color: #805ad5; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Dashboard</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for your dedication and commitment to our MLM network!</p>
        </div>
      `})};async function s(e,r,t){try{let{subject:s,html:a}=n[r](t),i={from:process.env.EMAIL_FROM||"MLM Rebate Engine <<EMAIL>>",to:e,subject:s,html:a},l=await o.sendMail(i);return console.log("Email sent:",l.messageId),{success:!0,messageId:l.messageId}}catch(e){return console.error("Error sending email:",e),{success:!1,error:e}}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},88803:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>v,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>k});var o={};t.r(o),t.d(o,{POST:()=>f});var n=t(96559),s=t(48088),a=t(37719),i=t(32190),l=t(19854),c=t(12909),u=t(31183),d=t(61904);let p=[{id:1,name:"Starter",level:1,directDownlineCount:0,totalDownlineCount:0,personalSalesAmount:0},{id:2,name:"Bronze",level:2,directDownlineCount:3,totalDownlineCount:5,personalSalesAmount:500},{id:3,name:"Silver",level:3,directDownlineCount:5,totalDownlineCount:15,personalSalesAmount:1e3},{id:4,name:"Gold",level:4,directDownlineCount:8,totalDownlineCount:30,personalSalesAmount:2e3},{id:5,name:"Platinum",level:5,directDownlineCount:10,totalDownlineCount:50,personalSalesAmount:3e3},{id:6,name:"Diamond",level:6,directDownlineCount:15,totalDownlineCount:100,personalSalesAmount:5e3}],m={Starter:["Basic rebate percentages","Access to product shop","Personal dashboard"],Bronze:["5% higher rebate percentages","Access to basic training materials","Monthly team performance reports"],Silver:["10% higher rebate percentages","Access to advanced training materials","Priority customer support"],Gold:["15% higher rebate percentages","Invitation to quarterly virtual events","Personalized marketing materials"],Platinum:["20% higher rebate percentages","Invitation to annual in-person conference","One-on-one coaching sessions"],Diamond:["25% higher rebate percentages","Leadership council membership","Exclusive Diamond-only products and promotions","Profit sharing opportunities"]};async function g(e){try{let r=await u.z.user.findUnique({where:{id:e},include:{rank:!0}});if(!r)throw Error(`User with ID ${e} not found`);let t=await u.z.user.count({where:{uplineId:e}}),o=(await h(e)).length,n=(await u.z.purchase.aggregate({where:{userId:e},_sum:{totalAmount:!0}}))._sum.totalAmount||0,s=p[0];for(let e of p)if(t>=e.directDownlineCount&&o>=e.totalDownlineCount&&n>=e.personalSalesAmount)s=e;else break;if(s.level>r.rank.level){let t=r.rank,o=await u.z.user.update({where:{id:e},data:{rankId:s.id},include:{rank:!0}});return r.email&&await (0,d.Z)(r.email,"rankAdvancement",{userName:r.name,oldRank:t.name,newRank:s.name,benefits:m[s.name]}),{advanced:!0,oldRank:t,newRank:o.rank}}return{advanced:!1,currentRank:r.rank}}catch(r){throw console.error(`Error checking rank advancement for user ${e}:`,r),r}}async function h(e){let r=(await u.z.user.findMany({where:{uplineId:e},select:{id:!0}})).map(e=>e.id);if(0===r.length)return[];let t=[];for(let e of r){let r=await h(e);t.push(...r)}return[...r,...t]}async function w(){try{let e=await u.z.user.findMany({select:{id:!0}}),r=[];for(let t of e)try{let e=await g(t.id);r.push({userId:t.id,...e})}catch(e){console.error(`Error processing rank advancement for user ${t.id}:`,e),r.push({userId:t.id,error:!0,message:e.message})}return r}catch(e){throw console.error("Error checking rank advancement for all users:",e),e}}async function f(e){try{let r=await (0,l.getServerSession)(c.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to check rank advancement"},{status:401});let{checkAll:t}=await e.json();if(t){if("1"!==r.user.id)return i.NextResponse.json({error:"Only administrators can check rank advancement for all users"},{status:403});let e=await w();return i.NextResponse.json({results:e})}{let e=parseInt(r.user.id),t=await g(e);return i.NextResponse.json(t)}}catch(e){return console.error("Error checking rank advancement:",e),i.NextResponse.json({error:"Failed to check rank advancement"},{status:500})}}let v=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/ranks/check/route",pathname:"/api/ranks/check",filename:"route",bundlePath:"app/api/ranks/check/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\ranks\\check\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:x,workUnitAsyncStorage:k,serverHooks:y}=v;function b(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:k})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4243,580,8044,3112,4079],()=>t(88803));module.exports=o})();
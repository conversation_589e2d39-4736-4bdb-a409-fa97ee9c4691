import React, { useState } from 'react';
import Image from 'next/image';

const ProductImageGallery = ({ images, productName }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  
  // If no images are provided, use a placeholder
  const imageList = images && images.length > 0 
    ? images 
    : ['/images/products/placeholder.jpg'];

  return (
    <div className="product-gallery">
      {/* Main image display */}
      <div className="relative w-full h-96 mb-4 border rounded-lg overflow-hidden">
        <Image
          src={imageList[selectedImage]}
          alt={`${productName} - Image ${selectedImage + 1}`}
          fill
          style={{ objectFit: 'contain' }}
          priority
        />
      </div>
      
      {/* Thumbnail navigation */}
      {imageList.length > 1 && (
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {imageList.map((image, index) => (
            <button
              key={index}
              onClick={() => setSelectedImage(index)}
              className={`relative w-20 h-20 border-2 rounded-md overflow-hidden flex-shrink-0 transition-all ${
                selectedImage === index 
                  ? 'border-indigo-600 opacity-100' 
                  : 'border-gray-200 opacity-70 hover:opacity-100'
              }`}
            >
              <Image
                src={image}
                alt={`${productName} - Thumbnail ${index + 1}`}
                fill
                style={{ objectFit: 'cover' }}
              />
            </button>
          ))}
        </div>
      )}
      
      {/* Zoom and navigation controls */}
      {imageList.length > 1 && (
        <div className="flex justify-center mt-4 space-x-4">
          <button
            onClick={() => setSelectedImage(prev => (prev === 0 ? imageList.length - 1 : prev - 1))}
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            aria-label="Previous image"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          
          <button
            onClick={() => setSelectedImage(prev => (prev === imageList.length - 1 ? 0 : prev + 1))}
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            aria-label="Next image"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductImageGallery;

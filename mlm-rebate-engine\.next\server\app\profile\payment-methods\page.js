(()=>{var e={};e.id=2006,e.ids=[2006],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39266:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687),a=s(43210),n=s(82136),i=s(16189),l=s(68367),d=s(23877);function o(){let{data:e,status:t}=(0,n.useSession)();(0,i.useRouter)();let[s,o]=(0,a.useState)(!0),[c,m]=(0,a.useState)([]),[u,p]=(0,a.useState)([]),[h,x]=(0,a.useState)({type:"",text:""}),[y,f]=(0,a.useState)(!1),[b,g]=(0,a.useState)(null),[j,v]=(0,a.useState)({}),[N,w]=(0,a.useState)(!1),M=async()=>{o(!0),x({type:"",text:""});try{let e=await fetch("/api/payment-methods?includeUserMethods=true");if(!e.ok)throw Error(`Failed to fetch payment methods: ${e.statusText}`);let t=await e.json();m(t.paymentMethods||[]),p(t.userPaymentMethods||[])}catch(e){console.error("Error fetching payment methods:",e),x({type:"error",text:"Failed to load payment methods. Please try again."})}finally{o(!1)}},P=()=>{f(!0),g(null),v({})},C=async()=>{if(!b)return void x({type:"error",text:"Please select a payment method"});w(!0),x({type:"",text:""});try{let e=await fetch("/api/payment-methods/user",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:b,details:j,isDefault:0===u.length})});if(!e.ok){let t=await e.json();throw Error(t.error||`Failed to add payment method: ${e.statusText}`)}await M(),f(!1),x({type:"success",text:"Payment method added successfully"})}catch(e){console.error("Error adding payment method:",e),x({type:"error",text:e instanceof Error?e.message:"Failed to add payment method"})}finally{w(!1)}},S=async e=>{if(confirm("Are you sure you want to delete this payment method?")){o(!0),x({type:"",text:""});try{let t=await fetch(`/api/payment-methods/user?id=${e}`,{method:"DELETE"});if(!t.ok)throw Error(`Failed to delete payment method: ${t.statusText}`);await M(),x({type:"success",text:"Payment method deleted successfully"})}catch(e){console.error("Error deleting payment method:",e),x({type:"error",text:"Failed to delete payment method"})}finally{o(!1)}}},k=async e=>{o(!0),x({type:"",text:""});try{let t=await fetch("/api/payment-methods/user",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:e,isDefault:!0})});if(!t.ok)throw Error(`Failed to set default payment method: ${t.statusText}`);await M(),x({type:"success",text:"Default payment method updated successfully"})}catch(e){console.error("Error setting default payment method:",e),x({type:"error",text:"Failed to set default payment method"})}finally{o(!1)}};return"loading"===t||s?(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,r.jsx)(d.hW,{className:"animate-spin text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Payment Methods"}),(0,r.jsxs)("button",{type:"button",onClick:P,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[(0,r.jsx)(d.OiG,{className:"inline mr-2"}),"Add Payment Method"]})]}),h.text&&(0,r.jsx)("div",{className:`mb-6 p-4 rounded-md ${"error"===h.type?"bg-red-100 text-red-700":"success"===h.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:h.text}),y&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Add Payment Method"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Method"}),(0,r.jsxs)("select",{value:b||"",onChange:e=>g(e.target.value?parseInt(e.target.value):null),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select a payment method"}),c.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),b&&(e=>{if(!e.requiresDetails||!e.detailsSchema)return null;try{let t=JSON.parse(e.detailsSchema);if(!t.properties)return null;return(0,r.jsx)("div",{className:"mt-4 space-y-4",children:Object.entries(t.properties).map(([e,s])=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[s.description||e,t.required?.includes(e)&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",value:j[e]||"",onChange:t=>v({...j,[e]:t.target.value}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",required:t.required?.includes(e)})]},e))})}catch(e){return console.error("Error parsing schema:",e),(0,r.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing schema"})}})(c.find(e=>e.id===b)),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{type:"button",onClick:()=>{f(!1),g(null),v({})},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"button",onClick:C,disabled:N||!b,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):"Save"})]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:(0,r.jsx)("div",{className:"p-6",children:0===u.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("p",{children:"You don't have any payment methods yet."}),(0,r.jsxs)("button",{type:"button",onClick:P,className:"mt-4 text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(d.OiG,{className:"inline mr-2"}),"Add your first payment method"]})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.map(e=>(0,r.jsxs)("div",{className:`border rounded-lg p-4 ${e.isDefault?"border-green-500 bg-green-50":"border-gray-200"}`,children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("div",{className:"mr-3",children:["gcash"===e.paymentMethod.code&&(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold",children:"G"}),"maya"===e.paymentMethod.code&&(0,r.jsx)("div",{className:"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold",children:"M"}),"cash"===e.paymentMethod.code&&(0,r.jsx)("div",{className:"w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold",children:"₱"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:e.paymentMethod.name}),e.isDefault&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,r.jsx)(d.CMH,{className:"mr-1"}),"Default"]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[!e.isDefault&&(0,r.jsx)("button",{type:"button",onClick:()=>k(e.id),className:"text-blue-600 hover:text-blue-800",title:"Set as default",children:"Set Default"}),(0,r.jsx)("button",{type:"button",onClick:()=>S(e.id),className:"text-red-600 hover:text-red-800",title:"Delete",children:(0,r.jsx)(d.qbC,{})})]})]}),e.paymentMethod.requiresDetails&&(0,r.jsx)("div",{className:"mt-3 text-sm text-gray-600",children:(()=>{try{let t=JSON.parse(e.details);return(0,r.jsx)("div",{className:"space-y-1",children:Object.entries(t).map(([e,t])=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("span",{className:"font-medium",children:[e,":"]})," ",t]},e))})}catch{return"Invalid details"}})()}),(0,r.jsxs)("div",{className:"mt-3 text-xs text-gray-500",children:["Added on ",new Date(e.createdAt).toLocaleDateString()]})]},e.id))})})})]})})}},60744:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\profile\\\\payment-methods\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\payment-methods\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68380:(e,t,s)=>{Promise.resolve().then(s.bind(s,39266))},72176:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["profile",{children:["payment-methods",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60744)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\payment-methods\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\payment-methods\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/payment-methods/page",pathname:"/profile/payment-methods",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},81948:(e,t,s)=>{Promise.resolve().then(s.bind(s,60744))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(72176));module.exports=r})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7618],{11780:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(95155),l=a(12115),r=a(12108),i=a(35695),n=a(70357),d=a(99526),c=a(29911),o=a(32502),x=a(64065);function m(){let{data:e,status:s}=(0,r.useSession)(),a=(0,i.useRouter)(),[o,m]=(0,l.useState)(!0),[h,p]=(0,l.useState)(null),[g,b]=(0,l.useState)({startDate:new Date(new Date().setDate(new Date().getDate()-30)).toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0]});(0,l.useEffect)(()=>{"unauthenticated"===s&&a.push("/login")},[s,a]);let[j,u]=(0,l.useState)(!1);(0,l.useEffect)(()=>{"authenticated"===s&&(async()=>{try{let e=await fetch("/api/users/me"),s=await e.json(),a=6===s.rankId;u(a),a?N():m(!1)}catch(e){console.error("Error checking admin status:",e),m(!1)}})()},[s]),(0,l.useEffect)(()=>{"authenticated"===s&&j&&N()},[s,j,g]);let N=async()=>{m(!0);try{let e=new URLSearchParams;g.startDate&&e.append("startDate",g.startDate),g.endDate&&e.append("endDate",g.endDate);let s=await fetch("/api/admin/reports?".concat(e.toString()));if(!s.ok)throw Error("Failed to fetch report data: ".concat(s.statusText));let a=await s.json();p(a),m(!1)}catch(e){console.error("Error fetching report data:",e),p({salesByProduct:{labels:["Basic Package","Premium Package","Elite Package"],data:[4500,12e3,8500]},salesByDate:{labels:["Jan","Feb","Mar","Apr","May","Jun"],data:[3e3,3500,4200,5100,4800,5500]},rebatesByLevel:{labels:["Level 1","Level 2","Level 3","Level 4","Level 5","Level 6+"],data:[8500,4200,2100,1500,800,400]},usersByRank:{labels:["Starter","Bronze","Silver","Gold","Platinum","Diamond"],data:[120,45,25,12,5,2]},topEarners:[{id:1,name:"John Doe",email:"<EMAIL>",totalRebates:2500,rank:"Diamond"},{id:2,name:"Jane Smith",email:"<EMAIL>",totalRebates:1800,rank:"Platinum"},{id:3,name:"Bob Johnson",email:"<EMAIL>",totalRebates:1200,rank:"Gold"},{id:4,name:"Alice Brown",email:"<EMAIL>",totalRebates:950,rank:"Silver"},{id:5,name:"Charlie Wilson",email:"<EMAIL>",totalRebates:820,rank:"Silver"}],topRecruiters:[{id:1,name:"John Doe",email:"<EMAIL>",directDownlineCount:15,rank:"Diamond"},{id:2,name:"Jane Smith",email:"<EMAIL>",directDownlineCount:12,rank:"Platinum"},{id:3,name:"Bob Johnson",email:"<EMAIL>",directDownlineCount:8,rank:"Gold"},{id:4,name:"Alice Brown",email:"<EMAIL>",directDownlineCount:6,rank:"Silver"},{id:5,name:"Charlie Wilson",email:"<EMAIL>",directDownlineCount:5,rank:"Silver"}],summary:{totalUsers:209,totalProducts:3,totalSales:25e3,totalRebates:17500,averageOrderValue:250,conversionRate:.68}}),m(!1)}},y=e=>{let{name:s,value:a}=e.target;b(e=>({...e,[s]:a}))},v=e=>{alert("Exporting ".concat(e," report to CSV..."))};return"loading"===s||o?(0,t.jsx)(d.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-xl",children:"Loading..."})})}):j?(0,t.jsx)(d.A,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold",children:"Reports & Analytics"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("label",{htmlFor:"startDate",className:"text-sm text-gray-600",children:"From:"}),(0,t.jsx)("input",{type:"date",id:"startDate",name:"startDate",value:g.startDate,onChange:y,className:"px-2 py-1 border border-gray-300 rounded-md text-sm"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("label",{htmlFor:"endDate",className:"text-sm text-gray-600",children:"To:"}),(0,t.jsx)("input",{type:"date",id:"endDate",name:"endDate",value:g.endDate,onChange:y,className:"px-2 py-1 border border-gray-300 rounded-md text-sm"})]})]})]}),h&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,t.jsx)(c.YXz,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Users"}),(0,t.jsx)("p",{className:"text-xl font-semibold",children:h.summary.totalUsers})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,t.jsx)(c.AsH,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Sales"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:["₱",h.summary.totalSales.toLocaleString()]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-purple-100 text-purple-500 mr-4",children:(0,t.jsx)(c.lcY,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:["₱",h.summary.totalRebates.toLocaleString()]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4",children:(0,t.jsx)(c.AsH,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Avg. Order Value"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:["₱",h.summary.averageOrderValue.toLocaleString()]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-red-100 text-red-500 mr-4",children:(0,t.jsx)(c.qvi,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Conversion Rate"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:[(100*h.summary.conversionRate).toFixed(1),"%"]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-indigo-100 text-indigo-500 mr-4",children:(0,t.jsx)(c.bfZ,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Date Range"}),(0,t.jsxs)("p",{className:"text-sm font-semibold",children:[g.startDate," to ",g.endDate]})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(c.v$b,{className:"mr-2 text-blue-500"})," Sales by Product"]}),(0,t.jsxs)("button",{onClick:()=>v("sales-by-product"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,t.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,t.jsx)("div",{className:"h-80",children:(0,t.jsx)(x.yP,{data:{labels:h.salesByProduct.labels,datasets:[{label:"Sales (₱)",data:h.salesByProduct.data,backgroundColor:"rgba(59, 130, 246, 0.5)",borderColor:"rgb(59, 130, 246)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!1}}}})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(c.YYR,{className:"mr-2 text-green-500"})," Sales Trend"]}),(0,t.jsxs)("button",{onClick:()=>v("sales-trend"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,t.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,t.jsx)("div",{className:"h-80",children:(0,t.jsx)(x.N1,{data:{labels:h.salesByDate.labels,datasets:[{label:"Sales (₱)",data:h.salesByDate.data,borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.5)",tension:.3}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!1}}}})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(c.v$b,{className:"mr-2 text-purple-500"})," Rebates by Level"]}),(0,t.jsxs)("button",{onClick:()=>v("rebates-by-level"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,t.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,t.jsx)("div",{className:"h-80",children:(0,t.jsx)(x.yP,{data:{labels:h.rebatesByLevel.labels,datasets:[{label:"Rebates (₱)",data:h.rebatesByLevel.data,backgroundColor:"rgba(139, 92, 246, 0.5)",borderColor:"rgb(139, 92, 246)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!1}}}})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(c.qvi,{className:"mr-2 text-yellow-500"})," Users by Rank"]}),(0,t.jsxs)("button",{onClick:()=>v("users-by-rank"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,t.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,t.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,t.jsx)("div",{className:"w-64 h-64",children:(0,t.jsx)(x.Fq,{data:{labels:h.usersByRank.labels,datasets:[{label:"Users",data:h.usersByRank.data,backgroundColor:["rgba(209, 213, 219, 0.8)","rgba(251, 191, 36, 0.8)","rgba(156, 163, 175, 0.8)","rgba(234, 179, 8, 0.8)","rgba(59, 130, 246, 0.8)","rgba(139, 92, 246, 0.8)"],borderColor:["rgb(209, 213, 219)","rgb(251, 191, 36)","rgb(156, 163, 175)","rgb(234, 179, 8)","rgb(59, 130, 246)","rgb(139, 92, 246)"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"},title:{display:!1}}}})})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(c.lcY,{className:"mr-2 text-green-500"})," Top Earners"]}),(0,t.jsxs)("button",{onClick:()=>v("top-earners"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,t.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Rebates"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.topEarners.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("Diamond"===e.rank?"bg-purple-100 text-purple-800":"Platinum"===e.rank?"bg-blue-100 text-blue-800":"Gold"===e.rank?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.rank})}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["₱",e.totalRebates.toLocaleString()]})]},e.id))})]})})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(c.YXz,{className:"mr-2 text-blue-500"})," Top Recruiters"]}),(0,t.jsxs)("button",{onClick:()=>v("top-recruiters"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,t.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Direct Downline"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.topRecruiters.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("Diamond"===e.rank?"bg-purple-100 text-purple-800":"Platinum"===e.rank?"bg-blue-100 text-blue-800":"Gold"===e.rank?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.rank})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.directDownlineCount})]},e.id))})]})})})]})]})]})]})}):(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}o.t1.register(o.PP,o.kc,o.E8,o.FN,o.No,o.Bs,o.hE,o.m_,o.s$)},48452:(e,s,a)=>{Promise.resolve().then(a.bind(a,11780))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,5647,6874,2108,6766,5557,1694,8579,357,9526,8441,1684,7358],()=>s(48452)),_N_E=e.O()}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2406],{22406:(e,r,l)=>{l.d(r,{A:()=>d});var t=l(95155),o=l(12115),s=l(74211),a=l(29911);let c={Starter:{color:"bg-gray-100 text-gray-800",borderColor:"border-gray-300",icon:(0,t.jsx)(a.gt3,{className:"text-gray-400"}),darkColor:"bg-gray-700 text-gray-200",darkBorderColor:"border-gray-600",colorfulColor:"bg-blue-100 text-blue-800",colorfulBorderColor:"border-blue-300"},Bronze:{color:"bg-yellow-100 text-yellow-800",borderColor:"border-yellow-300",icon:(0,t.jsx)(a.gt3,{className:"text-yellow-600"}),darkColor:"bg-yellow-900 text-yellow-200",darkBorderColor:"border-yellow-800",colorfulColor:"bg-amber-100 text-amber-800",colorfulBorderColor:"border-amber-300"},Silver:{color:"bg-gray-200 text-gray-800",borderColor:"border-gray-400",icon:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(a.gt3,{className:"text-gray-500"}),(0,t.jsx)(a.gt3,{className:"text-gray-500 ml-0.5"})]}),darkColor:"bg-gray-600 text-gray-200",darkBorderColor:"border-gray-500",colorfulColor:"bg-slate-200 text-slate-800",colorfulBorderColor:"border-slate-400"},Gold:{color:"bg-yellow-200 text-yellow-800",borderColor:"border-yellow-400",icon:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(a.gt3,{className:"text-yellow-600"}),(0,t.jsx)(a.gt3,{className:"text-yellow-600 ml-0.5"}),(0,t.jsx)(a.gt3,{className:"text-yellow-600 ml-0.5"})]}),darkColor:"bg-yellow-800 text-yellow-200",darkBorderColor:"border-yellow-700",colorfulColor:"bg-amber-200 text-amber-800",colorfulBorderColor:"border-amber-400"},Platinum:{color:"bg-blue-100 text-blue-800",borderColor:"border-blue-300",icon:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(a.gt3,{className:"text-blue-500"}),(0,t.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"}),(0,t.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"}),(0,t.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"})]}),darkColor:"bg-blue-900 text-blue-200",darkBorderColor:"border-blue-800",colorfulColor:"bg-cyan-100 text-cyan-800",colorfulBorderColor:"border-cyan-300"},Diamond:{color:"bg-purple-100 text-purple-800",borderColor:"border-purple-300",icon:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(a.gt3,{className:"text-purple-500"}),(0,t.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,t.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,t.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,t.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"})]}),darkColor:"bg-purple-900 text-purple-200",darkBorderColor:"border-purple-800",colorfulColor:"bg-fuchsia-100 text-fuchsia-800",colorfulBorderColor:"border-fuchsia-300"}},n=(e,r)=>{let l=c[e]||c.Starter;return"dark"===r?{color:l.darkColor,borderColor:l.darkBorderColor,icon:l.icon}:"colorful"===r?{color:l.colorfulColor,borderColor:l.colorfulBorderColor,icon:l.icon}:{color:l.color,borderColor:l.borderColor,icon:l.icon}},d=(0,o.memo)(function(e){let{data:r,isConnectable:l}=e,{user:c,onExpand:d,onSelect:i,onEdit:x,onDelete:m,onAdd:b,isExpanded:u,hasChildren:g,visualOptions:h,isDragging:f}=r,j=n(c.rankName,h.theme),[y,p]=(0,o.useState)(!1),[N,C]=(0,o.useState)(!1),[w,v]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=setTimeout(()=>v(!1),1e3);return()=>clearTimeout(e)},[]);let k=0===c.level,B=e=>void 0===e?"":new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),F="dark"===h.theme?{bg:k?"bg-blue-900":"bg-gray-800",text:"text-gray-200",border:k?"border-blue-700":"border-gray-700",highlight:"bg-gray-700"}:"colorful"===h.theme?{bg:k?"bg-gradient-to-br from-blue-100 to-purple-100":"bg-gradient-to-br from-gray-50 to-blue-50",text:"text-gray-800",border:k?"border-blue-300":"border-gray-300",highlight:"bg-white bg-opacity-50"}:{bg:k?"bg-blue-50":"bg-white",text:"text-gray-800",border:k?"border-blue-300":"border-gray-200",highlight:"bg-gray-50"};return(0,t.jsxs)("div",{className:"\n        p-3 rounded-md shadow-md transition-all duration-200\n        ".concat(!h.animateChanges?"":w?"animate-fade-in":f?"opacity-50":"","\n        ").concat(y?"shadow-lg transform scale-105":"","\n        ").concat(F.bg," ").concat(F.text," border ").concat(F.border,"\n      "),style:{width:"".concat(h.nodeWidth,"px"),height:"".concat(h.nodeHeight,"px"),borderRadius:"".concat(h.nodeBorderRadius,"px")},onMouseEnter:()=>{p(!0),C(!0)},onMouseLeave:()=>{p(!1),C(!1)},children:[(0,t.jsx)(s.h7,{type:"source",position:s.yX.Bottom,isConnectable:l,className:"w-3 h-3 bg-gray-400"}),!k&&(0,t.jsx)(s.h7,{type:"target",position:s.yX.Top,isConnectable:l,className:"w-3 h-3 bg-gray-400"}),(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-2 ".concat(k?"bg-blue-100":j.color),children:k?(0,t.jsx)(a.x$1,{className:"text-blue-500"}):(0,t.jsx)("span",{className:"text-sm font-medium",children:c.name.charAt(0).toUpperCase()})}),(0,t.jsxs)("div",{className:"flex-1 truncate",children:[(0,t.jsx)("div",{className:"font-medium text-sm truncate",title:c.name,children:c.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500 truncate",title:c.email,children:c.email})]})]}),(0,t.jsxs)("div",{className:"text-xs px-2 py-1 rounded-full flex items-center justify-center mb-2 ".concat(j.color),children:[j.icon,(0,t.jsx)("span",{className:"ml-1",children:c.rankName})]}),h.showPerformanceMetrics&&c.performanceMetrics&&(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,t.jsxs)("div",{className:"".concat(F.highlight," p-1 rounded text-xs flex flex-col items-center"),children:[(0,t.jsxs)("div",{className:"flex items-center text-green-600 mb-0.5",children:[(0,t.jsx)(a.AsH,{className:"mr-1",size:10}),(0,t.jsx)("span",{children:"Personal"})]}),(0,t.jsx)("span",{className:"font-medium",children:B(c.performanceMetrics.personalSales)})]}),(0,t.jsxs)("div",{className:"".concat(F.highlight," p-1 rounded text-xs flex flex-col items-center"),children:[(0,t.jsxs)("div",{className:"flex items-center text-blue-600 mb-0.5",children:[(0,t.jsx)(a.YXz,{className:"mr-1",size:10}),(0,t.jsx)("span",{children:"Team"})]}),(0,t.jsx)("span",{className:"font-medium",children:B(c.performanceMetrics.teamSales)})]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-600 space-y-1 flex-grow",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"ID:"}),(0,t.jsx)("span",{className:"font-medium",children:c.id})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Downline:"}),(0,t.jsx)("span",{className:"font-medium",children:c.downlineCount})]}),void 0!==c.walletBalance&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(a.lcY,{className:"mr-1",size:10}),"Balance:"]}),(0,t.jsx)("span",{className:"font-medium",children:B(c.walletBalance)})]}),c.level>0&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Level:"}),(0,t.jsx)("span",{className:"font-medium",children:c.level})]})]}),(0,t.jsxs)("div",{className:"flex justify-between mt-3 pt-2 border-t border-gray-200",children:[g?(0,t.jsx)("button",{onClick:d,className:"text-xs px-2 py-1 rounded flex items-center ".concat(u?"bg-blue-100 text-blue-700 hover:bg-blue-200":"".concat(F.highlight," hover:bg-gray-200")),children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(a.Vr3,{className:"mr-1"})," Collapse"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(a.X6T,{className:"mr-1"})," Expand"]})}):(0,t.jsx)("div",{className:"text-xs px-2 py-1 text-gray-400",children:"No children"}),(0,t.jsxs)("button",{onClick:i,className:"text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded flex items-center hover:bg-blue-200",children:[(0,t.jsx)(a.__w,{className:"mr-1"})," Details"]})]}),N&&(x||m||b)&&(0,t.jsxs)("div",{className:"absolute -right-2 top-1/2 transform -translate-y-1/2 flex flex-col space-y-2",children:[x&&(0,t.jsx)("button",{onClick:()=>x(c.id),className:"w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center hover:bg-blue-600 shadow-md",title:"Edit User",children:(0,t.jsx)(a.uO9,{})}),b&&(0,t.jsx)("button",{onClick:()=>b(c.id),className:"w-8 h-8 rounded-full bg-green-500 text-white flex items-center justify-center hover:bg-green-600 shadow-md",title:"Add Child",children:(0,t.jsx)(a.OiG,{})}),m&&(0,t.jsx)("button",{onClick:()=>m(c.id),className:"w-8 h-8 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600 shadow-md",title:"Delete User",children:(0,t.jsx)(a.qbC,{})})]})]})]})})}}]);
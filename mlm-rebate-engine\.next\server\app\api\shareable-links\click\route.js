(()=>{var e={};e.id=6390,e.ids=[6390,7072],e.modules={1677:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var i={};t.r(i),t.d(i,{POST:()=>l});var a=t(96559),n=t(48088),s=t(37719),o=t(31183),u=t(32190),c=t(27072);async function l(e){try{let{code:r}=await e.json();if(!r)return u.NextResponse.json({error:"Link code is required"},{status:400});let t=await (0,c.getShareableLinkByCode)(r);if(!t)return u.NextResponse.json({error:"Shareable link not found"},{status:404});if(!t.isActive)return u.NextResponse.json({error:"This link is no longer active"},{status:400});if(t.expiresAt&&new Date(t.expiresAt)<new Date)return u.NextResponse.json({error:"This link has expired"},{status:400});let i=e.headers,a=i.get("user-agent"),n=i.get("referer"),s=i.get("x-forwarded-for")||"unknown",l=new URL(e.url),d=l.searchParams.get("utm_source"),m=l.searchParams.get("utm_medium"),p=l.searchParams.get("utm_campaign");await (0,c.GI)(t.id,{ipAddress:s,userAgent:a||void 0,referrer:n||void 0,utmSource:d||void 0,utmMedium:m||void 0,utmCampaign:p||void 0});let f=null;t.productId&&(f=await o.z.product.findUnique({where:{id:t.productId},select:{id:!0,name:!0,description:!0,price:!0,image:!0}}));let h=await o.z.user.findUnique({where:{id:t.userId},select:{id:!0,name:!0}});return u.NextResponse.json({link:{id:t.id,code:t.code,type:t.type,title:t.title||(f?f.name:null),description:t.description||(f?f.description:null),image:t.customImage||(f?f.image:null)},product:f,sharedBy:h,message:"Click recorded successfully"})}catch(e){return console.error("Error recording link click:",e),u.NextResponse.json({error:"Failed to record link click"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/shareable-links/click/route",pathname:"/api/shareable-links/click",filename:"route",bundlePath:"app/api/shareable-links/click/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shareable-links\\click\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=d;function h(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27072:(e,r,t)=>{"use strict";let i,a;t.d(r,{Z3:()=>l,nl:()=>f,getShareableLinkByCode:()=>d,F2:()=>g,qY:()=>y,Yx:()=>m,GI:()=>h,recordReferralPurchase:()=>w,p1:()=>p});var n=t(31183),s=t(55511);let o=e=>{!i||i.length<e?(i=Buffer.allocUnsafe(128*e),s.randomFillSync(i),a=0):a+e>i.length&&(s.randomFillSync(i),a=0),a+=e},u=(e=21)=>{o(e|=0);let r="";for(let t=a-e;t<a;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&i[t]];return r};async function c(){let e=u(8),r=!1;for(;!r;)await n.z.shareableLink.findUnique({where:{code:e}})?e=u(8):r=!0;return e}async function l(e,r,t){let i=await c();return await n.z.shareableLink.create({data:{userId:e,productId:r,code:i,type:"product",title:t?.title,description:t?.description,customImage:t?.customImage,expiresAt:t?.expiresAt,isActive:!0}})}async function d(e){return await n.z.shareableLink.findUnique({where:{code:e}})}async function m(e,r){let t={userId:e};r?.productId!==void 0&&(t.productId=r.productId),r?.type!==void 0&&(t.type=r.type),r?.isActive!==void 0&&(t.isActive=r.isActive);let i=await n.z.shareableLink.count({where:t});return{links:await n.z.shareableLink.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{product:{select:{id:!0,name:!0,price:!0,image:!0,referralCommissionType:!0,referralCommissionValue:!0}}}}),total:i}}async function p(e,r){return await n.z.shareableLink.update({where:{id:e},data:{...r,updatedAt:new Date}})}async function f(e){return await n.z.shareableLink.delete({where:{id:e}})}async function h(e,r){return await n.z.shareableLink.update({where:{id:e},data:{clickCount:{increment:1},updatedAt:new Date}}),await n.z.linkClick.create({data:{linkId:e,ipAddress:r?.ipAddress,userAgent:r?.userAgent,referrer:r?.referrer,utmSource:r?.utmSource,utmMedium:r?.utmMedium,utmCampaign:r?.utmCampaign}})}async function k(e,r){let t=await n.z.product.findUnique({where:{id:e},select:{referralCommissionType:!0,referralCommissionValue:!0}});return t&&t.referralCommissionType&&t.referralCommissionValue?"percentage"===t.referralCommissionType?{amount:r*(t.referralCommissionValue/100),percentage:t.referralCommissionValue}:{amount:t.referralCommissionValue,percentage:t.referralCommissionValue/r*100}:{amount:.05*r,percentage:5}}async function w(e,r){let t=await n.z.purchase.findUnique({where:{id:e},include:{product:!0,user:!0,referralLink:{include:{user:!0}}}});if(!t)throw Error(`Purchase with ID ${e} not found`);if(!t.referralLink)throw Error(`Purchase with ID ${e} has no referral link`);let{amount:i,percentage:a}=await k(t.productId,t.totalAmount),s=await n.z.referralCommission.create({data:{purchaseId:e,linkId:r,referrerId:t.referralLink.userId,buyerId:t.userId,productId:t.productId,amount:i,percentage:a,status:"pending"}});return await n.z.shareableLink.update({where:{id:r},data:{conversionCount:{increment:1},totalRevenue:{increment:t.totalAmount},totalCommission:{increment:i},updatedAt:new Date}}),s}async function g(e,r){let t={referrerId:e};r?.status!==void 0&&(t.status=r.status);let i=await n.z.referralCommission.count({where:t});return{commissions:await n.z.referralCommission.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{purchase:{select:{id:!0,quantity:!0,totalAmount:!0,createdAt:!0}},buyer:{select:{id:!0,name:!0,email:!0}},product:{select:{id:!0,name:!0,price:!0,image:!0}},link:{select:{id:!0,code:!0,type:!0}}}}),total:i}}async function y(e){let r=await n.z.shareableLink.aggregate({where:{userId:e},_sum:{clickCount:!0,conversionCount:!0,totalRevenue:!0,totalCommission:!0},_count:{id:!0}}),t=r._sum.clickCount||0,i=r._sum.conversionCount||0;return{totalLinks:r._count.id,totalClicks:t,totalConversions:i,totalRevenue:r._sum.totalRevenue||0,totalCommission:r._sum.totalCommission||0,conversionRate:t>0?i/t*100:0}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var i=t(96330);let a=global.prisma||new i.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4243,580],()=>t(1677));module.exports=i})();
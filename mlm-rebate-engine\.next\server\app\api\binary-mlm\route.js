"use strict";(()=>{var e={};e.id=7322,e.ids=[7322],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},24661:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>g,serverHooks:()=>N,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>f,POST:()=>h});var a=t(96559),n=t(48088),o=t(37719),i=t(31183),u=t(32190),p=t(19854),l=t(12909),d=t(47697),c=t(70762);let m=c.z.object({startDate:c.z.string().refine(e=>!isNaN(Date.parse(e)),{message:"Invalid start date format"}),endDate:c.z.string().refine(e=>!isNaN(Date.parse(e)),{message:"Invalid end date format"})}),x=c.z.object({year:c.z.number().int().min(2e3).max(2100),month:c.z.number().int().min(1).max(12)});async function f(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view binary MLM data"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let a=new URL(e.url),n=a.searchParams.get("action"),o=a.searchParams.get("maxDepth"),c=o?parseInt(o):6,x=a.searchParams.get("userId"),f=x?parseInt(x):s.id;switch(n){case"tree":let h=await (0,d.o8)(f,c);return u.NextResponse.json({tree:h});case"downline":let g=await (0,d.W$)(f,c);return u.NextResponse.json({downline:g});case"pv":let j=a.searchParams.get("startDate"),w=a.searchParams.get("endDate");if(!j||!w)return u.NextResponse.json({error:"startDate and endDate parameters are required for PV calculation"},{status:400});let N=m.safeParse({startDate:j,endDate:w});if(!N.success)return u.NextResponse.json({error:N.error.errors},{status:400});let R=new Date(N.data.startDate),y=new Date(N.data.endDate),D=await (0,d.pv)(f,R,y);return u.NextResponse.json({pv:D});case"commissions":let v=a.searchParams.get("startDate"),q=a.searchParams.get("endDate");if(!v||!q)return u.NextResponse.json({error:"startDate and endDate parameters are required for commission calculation"},{status:400});let b=m.safeParse({startDate:v,endDate:q});if(!b.success)return u.NextResponse.json({error:b.error.errors},{status:400});let P=new Date(b.data.startDate),I=new Date(b.data.endDate),M=await (0,d.Jr)(f,P,I);return u.NextResponse.json({commissions:M});case"performance":let k,L,z=a.searchParams.get("year"),U=a.searchParams.get("month");z&&(k=parseInt(z)),U&&(L=parseInt(U));let S=await (0,d.aL)(f,k,L);return u.NextResponse.json({performance:S});default:let A=await i.z.user.findUnique({where:{id:f},select:{id:!0,name:!0,email:!0,rankId:!0,uplineId:!0,leftLegId:!0,rightLegId:!0,placementPosition:!0,walletBalance:!0,rank:{select:{id:!0,name:!0,level:!0}}}});return u.NextResponse.json({user:A})}}catch(e){return console.error("Error fetching binary MLM data:",e),u.NextResponse.json({error:"Failed to fetch binary MLM data"},{status:500})}}async function h(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to perform this action"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let a=s.rankId>=6,n=await e.json();if("simulate"!==n.action)return u.NextResponse.json({error:"Invalid action"},{status:400});{let e=n.userId||s.id;if(!a&&e!==s.id)return u.NextResponse.json({error:"You do not have permission to simulate earnings for other users"},{status:403});let r=x.safeParse({year:n.year,month:n.month});if(!r.success)return u.NextResponse.json({error:r.error.errors},{status:400});let{year:t,month:o}=r.data,i=await (0,d.Qx)(e,t,o);return u.NextResponse.json({simulation:i})}}catch(e){return console.error("Error processing binary MLM action:",e),u.NextResponse.json({error:"Failed to process binary MLM action"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/binary-mlm/route",pathname:"/api/binary-mlm",filename:"route",bundlePath:"app/api/binary-mlm/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\binary-mlm\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:w,serverHooks:N}=g;function R(){return(0,o.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:w})}},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,8381,2610],()=>t(24661));module.exports=s})();
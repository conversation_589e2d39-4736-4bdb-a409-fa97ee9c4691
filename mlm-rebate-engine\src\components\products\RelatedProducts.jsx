import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const RelatedProducts = ({ currentProductId }) => {
  // Mock related products data
  const relatedProducts = [
    {
      id: 'veggie-coffee',
      name: 'Veggie Coffee',
      price: 1200,
      pv: 120,
      image: '/images/products/veggie-coffee.jpg',
      description: 'A healthy coffee alternative made with organic vegetables.'
    },
    {
      id: 'shield-soap',
      name: 'Shield Soap',
      price: 250,
      pv: 25,
      image: '/images/products/shield-soap.jpg',
      description: 'Antibacterial soap with natural ingredients for skin protection.'
    },
    {
      id: 'biogen-extreme',
      name: 'Biogen Extreme',
      price: 1800,
      pv: 180,
      image: '/images/products/biogen-extreme.jpg',
      description: 'Advanced health supplement for improved energy and vitality.'
    }
  ].filter(product => product.id !== currentProductId);

  return (
    <div className="mt-16">
      <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {relatedProducts.map((product) => (
          <div key={product.id} className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
            <Link href={`/products/${product.id}`}>
              <div className="relative h-48 w-full">
                {product.image ? (
                  <Image 
                    src={product.image}
                    alt={product.name}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400">No image</span>
                  </div>
                )}
              </div>
              
              <div className="p-4">
                <h3 className="text-lg font-semibold">{product.name}</h3>
                <p className="text-gray-600 text-sm mt-1 line-clamp-2">{product.description}</p>
                
                <div className="mt-3 flex justify-between items-center">
                  <div>
                    <p className="text-lg font-bold">₱{product.price.toLocaleString()}</p>
                    <p className="text-sm text-gray-500">{product.pv} PV</p>
                  </div>
                  
                  <button className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    View Details
                  </button>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RelatedProducts;

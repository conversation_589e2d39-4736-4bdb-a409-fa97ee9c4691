(()=>{var e={};e.id=8731,e.ids=[7072,8731],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>c,aP:()=>l});var s=t(96330),i=t(13581),n=t(85663),o=t(55511),a=t.n(o);async function u(e){return await n.Ay.hash(e,10)}function l(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,i.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let i=await n.Ay.compare(e.password,t.password);if(console.log("Password valid:",i),!i)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:o,...a}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var i=t(12269);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=i?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(35426));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})},27072:(e,r,t)=>{"use strict";let s,i;t.d(r,{Z3:()=>c,nl:()=>f,getShareableLinkByCode:()=>d,F2:()=>y,qY:()=>v,Yx:()=>p,GI:()=>w,recordReferralPurchase:()=>h,p1:()=>m});var n=t(31183),o=t(55511);let a=e=>{!s||s.length<e?(s=Buffer.allocUnsafe(128*e),o.randomFillSync(s),i=0):i+e>s.length&&(o.randomFillSync(s),i=0),i+=e},u=(e=21)=>{a(e|=0);let r="";for(let t=i-e;t<i;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&s[t]];return r};async function l(){let e=u(8),r=!1;for(;!r;)await n.z.shareableLink.findUnique({where:{code:e}})?e=u(8):r=!0;return e}async function c(e,r,t){let s=await l();return await n.z.shareableLink.create({data:{userId:e,productId:r,code:s,type:"product",title:t?.title,description:t?.description,customImage:t?.customImage,expiresAt:t?.expiresAt,isActive:!0}})}async function d(e){return await n.z.shareableLink.findUnique({where:{code:e}})}async function p(e,r){let t={userId:e};r?.productId!==void 0&&(t.productId=r.productId),r?.type!==void 0&&(t.type=r.type),r?.isActive!==void 0&&(t.isActive=r.isActive);let s=await n.z.shareableLink.count({where:t});return{links:await n.z.shareableLink.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{product:{select:{id:!0,name:!0,price:!0,image:!0,referralCommissionType:!0,referralCommissionValue:!0}}}}),total:s}}async function m(e,r){return await n.z.shareableLink.update({where:{id:e},data:{...r,updatedAt:new Date}})}async function f(e){return await n.z.shareableLink.delete({where:{id:e}})}async function w(e,r){return await n.z.shareableLink.update({where:{id:e},data:{clickCount:{increment:1},updatedAt:new Date}}),await n.z.linkClick.create({data:{linkId:e,ipAddress:r?.ipAddress,userAgent:r?.userAgent,referrer:r?.referrer,utmSource:r?.utmSource,utmMedium:r?.utmMedium,utmCampaign:r?.utmCampaign}})}async function g(e,r){let t=await n.z.product.findUnique({where:{id:e},select:{referralCommissionType:!0,referralCommissionValue:!0}});return t&&t.referralCommissionType&&t.referralCommissionValue?"percentage"===t.referralCommissionType?{amount:r*(t.referralCommissionValue/100),percentage:t.referralCommissionValue}:{amount:t.referralCommissionValue,percentage:t.referralCommissionValue/r*100}:{amount:.05*r,percentage:5}}async function h(e,r){let t=await n.z.purchase.findUnique({where:{id:e},include:{product:!0,user:!0,referralLink:{include:{user:!0}}}});if(!t)throw Error(`Purchase with ID ${e} not found`);if(!t.referralLink)throw Error(`Purchase with ID ${e} has no referral link`);let{amount:s,percentage:i}=await g(t.productId,t.totalAmount),o=await n.z.referralCommission.create({data:{purchaseId:e,linkId:r,referrerId:t.referralLink.userId,buyerId:t.userId,productId:t.productId,amount:s,percentage:i,status:"pending"}});return await n.z.shareableLink.update({where:{id:r},data:{conversionCount:{increment:1},totalRevenue:{increment:t.totalAmount},totalCommission:{increment:s},updatedAt:new Date}}),o}async function y(e,r){let t={referrerId:e};r?.status!==void 0&&(t.status=r.status);let s=await n.z.referralCommission.count({where:t});return{commissions:await n.z.referralCommission.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{purchase:{select:{id:!0,quantity:!0,totalAmount:!0,createdAt:!0}},buyer:{select:{id:!0,name:!0,email:!0}},product:{select:{id:!0,name:!0,price:!0,image:!0}},link:{select:{id:!0,code:!0,type:!0}}}}),total:s}}async function v(e){let r=await n.z.shareableLink.aggregate({where:{userId:e},_sum:{clickCount:!0,conversionCount:!0,totalRevenue:!0,totalCommission:!0},_count:{id:!0}}),t=r._sum.clickCount||0,s=r._sum.conversionCount||0;return{totalLinks:r._count.id,totalClicks:t,totalConversions:s,totalRevenue:r._sum.totalRevenue||0,totalCommission:r._sum.totalCommission||0,conversionRate:t>0?s/t*100:0}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=global.prisma||new s.PrismaClient({log:["query"]})},38266:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(96559),n=t(48088),o=t(37719),a=t(31183),u=t(32190),l=t(19854),c=t(12909),d=t(27072);async function p(e){try{let r=await (0,l.getServerSession)(c.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view referral commissions"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await a.z.user.findUnique({where:{email:t},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let i=new URL(e.url),n=i.searchParams.get("status"),o=i.searchParams.get("limit"),p=i.searchParams.get("offset"),m={};n&&(m.status=n),o&&(m.limit=parseInt(o)),p&&(m.offset=parseInt(p));let{commissions:f,total:w}=await (0,d.F2)(s.id,m);return u.NextResponse.json({commissions:f,pagination:{total:w,limit:m.limit,offset:m.offset||0}})}catch(e){return console.error("Error fetching referral commissions:",e),u.NextResponse.json({error:"Failed to fetch referral commissions"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/referrals/commissions/route",pathname:"/api/referrals/commissions",filename:"route",bundlePath:"app/api/referrals/commissions/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\referrals\\commissions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:g}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(38266));module.exports=s})();
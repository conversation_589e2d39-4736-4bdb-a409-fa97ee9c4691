exports.id=6719,exports.ids=[6719],exports.modules={6375:(e,t,a)=>{"use strict";a.d(t,{Op:()=>l,RB:()=>d,Z3:()=>c,dO:()=>o,yM:()=>i});var r=a(31183),n=a(89605);async function i(e,t=10,a=1,l=10,o={}){let c=`downline:${e}:${t}:${a}:${l}:${JSON.stringify(o)}`;return await n.ic.getOrSet(c,async()=>{let n=await r.z.user.findUnique({where:{id:e},select:{id:!0,name:!0,email:!0,rankId:!0,walletBalance:!0,createdAt:!0,rank:{select:{id:!0,name:!0}},_count:{select:{downline:!0}}}});if(!n)throw Error(`User with ID ${e} not found`);let i=(a-1)*l,c={uplineId:e};o.filterRank&&(c.rankId=o.filterRank),(o.filterJoinedAfter||o.filterJoinedBefore)&&(c.createdAt={},o.filterJoinedAfter&&(c.createdAt.gte=o.filterJoinedAfter),o.filterJoinedBefore&&(c.createdAt.lte=o.filterJoinedBefore));let u={createdAt:"desc"};if(o.sortBy){let e=o.sortDirection||"desc";switch(o.sortBy){case"name":u={name:e};break;case"rank":u={rankId:e};break;default:u={createdAt:e}}}let w=await r.z.user.findMany({where:c,select:{id:!0,name:!0,email:!0,rankId:!0,walletBalance:!0,createdAt:!0,rank:{select:{id:!0,name:!0}},_count:{select:{downline:!0}}},skip:i,take:l,orderBy:u}),h=await r.z.user.count({where:c}),f=Math.ceil(h/l),m=[],g=o.lazyLoadLevels?1:t-1;for(let e of w){let a=t>1&&g>0?await s(e.id,2,g+1):[],r=null;o.includePerformanceMetrics&&(r=await d(e.id)),m.push({...e,level:1,children:a,performanceMetrics:r,hasMoreChildren:t>g+1&&e._count.downline>0})}let p=null;return o.includePerformanceMetrics&&(p=await d(e)),{user:{...n,level:0,performanceMetrics:p},downline:m,pagination:{page:a,pageSize:l,totalItems:h,totalPages:f},metadata:{maxLevel:t,initialDepthLoaded:g,lazyLoading:o.lazyLoadLevels||!1,filters:{rank:o.filterRank,joinedAfter:o.filterJoinedAfter,joinedBefore:o.filterJoinedBefore},sorting:{by:o.sortBy||"createdAt",direction:o.sortDirection||"desc"}}}},3e5)}async function s(e,t,a){if(t>a)return[];let i=`recursive:${e}:${t}:${a}`;return await n.ic.getOrSet(i,async()=>{let n=await r.z.user.findMany({where:{uplineId:e},select:{id:!0,name:!0,email:!0,rankId:!0,walletBalance:!0,createdAt:!0,rank:{select:{id:!0,name:!0}},_count:{select:{downline:!0}}},orderBy:{createdAt:"desc"}}),i=[];for(let e of n){let r=t<a?await s(e.id,t+1,a):[];i.push({...e,level:t,children:r.length>0?r:void 0})}return i},3e5)}async function l(e){let t=[],a=[e];for(;a.length>0;){let e=(await r.z.user.findMany({where:{uplineId:{in:a}},select:{id:!0}})).map(e=>e.id);if(0===e.length)break;t.push(...e),a=e}return t}async function o(e,t=10){let a=`levelCounts:${e}:${t}`;return await n.ic.getOrSet(a,async()=>{let a={};for(let n of(await r.z.$queryRaw`
      WITH RECURSIVE downline AS (
        -- Base case: direct downline of the user
        SELECT id, 1 AS level
        FROM User
        WHERE uplineId = ${e}

        UNION ALL

        -- Recursive case: downline of downline
        SELECT u.id, d.level + 1
        FROM User u
        JOIN downline d ON u.uplineId = d.id
        WHERE d.level < ${t}
      )
      -- Count users at each level
      SELECT level, COUNT(*) as count
      FROM downline
      GROUP BY level
      ORDER BY level
    `))a[n.level]=Number(n.count);return a},6e5)}async function d(e){let t=`metrics:${e}`;return await n.ic.getOrSet(t,async()=>{let t=r.z.$queryRaw`
      WITH RECURSIVE downline AS (
        -- Base case: direct downline of the user
        SELECT id
        FROM User
        WHERE uplineId = ${e}

        UNION ALL

        -- Recursive case: downline of downline
        SELECT u.id
        FROM User u
        JOIN downline d ON u.uplineId = d.id
      )
      SELECT id FROM downline
    `,a=new Date;a.setMonth(a.getMonth()-1);let[n,i,s,l,o]=await Promise.all([t,r.z.purchase.aggregate({where:{userId:e},_sum:{totalAmount:!0}}),r.z.rebate.aggregate({where:{receiverId:e},_sum:{amount:!0}}),r.z.user.count({where:{uplineId:e,createdAt:{gte:a}}}),Promise.resolve([{rankId:1,rank:{name:"Starter"},createdAt:new Date(Date.now()-7776e6)},{rankId:2,rank:{name:"Bronze"},createdAt:new Date(Date.now()-5184e6)},{rankId:3,rank:{name:"Silver"},createdAt:new Date(Date.now()-2592e6)}])]),d=await r.z.purchase.aggregate({where:{userId:{in:n.map(e=>e.id)}},_sum:{totalAmount:!0}}),c=i._sum.totalAmount||0,u=d._sum.totalAmount||0,w=s._sum.amount||0,h=n.length,f=Math.min(100,Math.floor(c/1e3*20+u/5e3*30+w/500*20+l/5*30));return{personalSales:c,teamSales:u,totalSales:c+u,rebatesEarned:w,teamSize:h,newTeamMembers:l,rankHistory:o.map(e=>({rankId:e.rankId,rankName:e.rank.name,achievedAt:e.createdAt})),activityScore:f,lastUpdated:new Date}},9e5)}async function c(e,t,a){let r=`additionalLevels:${e}:${t}:${a}`;return await n.ic.getOrSet(r,async()=>({children:await s(e,t+1,a),metadata:{userId:e,currentLevel:t,maxLevel:a,loadedAt:new Date}}),3e5)}},12909:(e,t,a)=>{"use strict";a.d(t,{Er:()=>o,Nh:()=>c,aP:()=>d});var r=a(96330),n=a(13581),i=a(85663),s=a(55511),l=a.n(s);async function o(e){return await i.Ay.hash(e,10)}function d(){let e=l().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new r.PrismaClient;let c={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new r.PrismaClient,a=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!a)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",a.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await i.Ay.compare(e.password,a.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",a.id);let{password:s,...l}=a;return{id:a.id.toString(),email:a.email,name:a.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},31183:(e,t,a)=>{"use strict";a.d(t,{z:()=>n});var r=a(96330);let n=global.prisma||new r.PrismaClient({log:["query"]})},78335:()=>{},89605:(e,t,a)=>{"use strict";a.d(t,{_L:()=>l,g3:()=>s,ic:()=>o});class r{constructor(e=3e5){this.cache=new Map,this.defaultTTL=e}set(e,t,a){let r=Date.now()+(a||this.defaultTTL);this.cache.set(e,{value:t,expiry:r})}get(e){let t=this.cache.get(e);if(t)return Date.now()>t.expiry?void this.cache.delete(e):t.value}has(e){let t=this.cache.get(e);return!!t&&(!(Date.now()>t.expiry)||(this.cache.delete(e),!1))}delete(e){this.cache.delete(e)}clear(){this.cache.clear()}async getOrSet(e,t,a){let r=this.get(e);if(void 0!==r)return r;let n=await t();return this.set(e,n,a),n}cleanup(){let e=Date.now();for(let[t,a]of this.cache.entries())e>a.expiry&&this.cache.delete(t)}}let n=new r,i=e=>({set:(t,a,r)=>n.set(`${e}:${t}`,a,r),get:t=>n.get(`${e}:${t}`),has:t=>n.has(`${e}:${t}`),delete:t=>n.delete(`${e}:${t}`),getOrSet:(t,a,r)=>n.getOrSet(`${e}:${t}`,a,r),clearNamespace:()=>{for(let[t]of n.cache.entries())t.startsWith(`${e}:`)&&n.delete(t)}}),s=i("user"),l=i("product"),o=i("genealogy");i("rebate"),setInterval(()=>{n.cleanup()},36e5)},96487:()=>{}};
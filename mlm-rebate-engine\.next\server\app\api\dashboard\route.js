(()=>{var e={};e.id=4618,e.ids=[4618],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>l,Nh:()=>c,aP:()=>u});var a=r(96330),s=r(13581),n=r(85663),o=r(55511),i=r.n(o);async function l(e){return await n.Ay.hash(e,10)}function u(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new a.PrismaClient;let c={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,s.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new a.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let s=await n.Ay.compare(e.password,r.password);if(console.log("Password valid:",s),!s)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:o,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var s=r(12269);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=s?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(a,n,i):a[n]=e[n]}return a.default=e,r&&r.set(e,a),a}(r(35426));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76887:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>h});var s=r(96559),n=r(48088),o=r(37719),i=r(31183),l=r(32190),u=r(19854),c=r(12909);class d{constructor(e=6e4){this.cache=new Map,this.defaultTTL=e}get(e){let t=this.cache.get(e);if(t)return Date.now()>t.expiry?void this.cache.delete(e):t.value}set(e,t,r=this.defaultTTL){let a=Date.now()+r;this.cache.set(e,{value:t,expiry:a})}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}async getOrSet(e,t,r=this.defaultTTL){let a=this.get(e);if(void 0!==a)return a;let s=await t();return this.set(e,s,r),s}size(){return this.cache.size}prune(){let e=Date.now(),t=0;for(let[r,a]of this.cache.entries())e>a.expiry&&(this.cache.delete(r),t++);return t}has(e){let t=this.cache.get(e);return!!t&&(!(Date.now()>t.expiry)||(this.cache.delete(e),!1))}keys(){return Array.from(this.cache.keys())}ttl(e){let t=this.cache.get(e);if(!t)return -1;let r=t.expiry-Date.now();return r<=0?(this.cache.delete(e),-1):r}}let p=new d;async function h(e){try{let t=await (0,u.getServerSession)(c.Nh);if(!t||!t.user)return l.NextResponse.json({error:"You must be logged in to view dashboard data"},{status:401});let r=t.user.email;if(!r)return l.NextResponse.json({error:"User email not found in session"},{status:400});let a=new URL(e.url).searchParams.get("timeframe")||"month",s=`dashboard:${r}:${a}`;return await p.getOrSet(s,async()=>{let e=await i.z.user.findUnique({where:{email:r},select:{id:!0,name:!0,email:!0,walletBalance:!0,rankId:!0,rank:{select:{name:!0,level:!0}},profileImage:!0}});if(!e)return l.NextResponse.json({error:"User not found"},{status:404});let t=e.id,s=new Date,n=new Date;"week"===a?n.setDate(s.getDate()-7):"month"===a?n.setMonth(s.getMonth()-1):"year"===a&&n.setFullYear(s.getFullYear()-1);let[o,u,c,d,p,h]=await Promise.all([i.z.rebate.findMany({where:{receiverId:t,createdAt:{gte:n}},include:{generator:{select:{name:!0}}},orderBy:{createdAt:"desc"}}),i.z.purchase.findMany({where:{userId:t,createdAt:{gte:n}},include:{product:!0},orderBy:{createdAt:"desc"}}),i.z.user.findMany({where:{uplineId:t},select:{id:!0,name:!0,rankId:!0,rank:{select:{name:!0}},createdAt:!0}}),i.z.user.groupBy({by:["rankId"],where:{uplineId:t},_count:{id:!0}}),i.z.rebate.findMany({where:{receiverId:t},include:{generator:{select:{name:!0}}},orderBy:{createdAt:"desc"},take:5}),i.z.rank.findMany()]),g=o.reduce((e,t)=>e+t.amount,0),w=c.length,m=u.length,y=new Map(h.map(e=>[e.id,e.name])),b=d.reduce((e,t)=>(e[y.get(t.rankId)||"Unknown"]=t._count.id,e),{}),v=f(o,a,"amount"),x=f(u,a,"totalAmount");return l.NextResponse.json({user:{id:e.id,name:e.name,email:e.email,rank:e.rank?.name||"Distributor",rankLevel:e.rank?.level||1,profileImage:e.profileImage},stats:{walletBalance:e.walletBalance||0,totalRebates:g,downlineCount:w,purchaseCount:m},charts:{rebates:v,sales:x,rankDistribution:b},recentData:{rebates:p,purchases:u.slice(0,5)}})},3e5)}catch(e){return console.error("Error fetching dashboard data:",e),l.NextResponse.json({error:"Failed to fetch dashboard data"},{status:500})}}function f(e,t,r){let a=new Date,s={};if("week"===t){let t=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];for(let e=6;e>=0;e--){let r=new Date(a);r.setDate(a.getDate()-e),s[t[r.getDay()]]=0}e.forEach(e=>{let a=t[new Date(e.createdAt).getDay()];s[a]+=e[r]})}else if("month"===t){let t=new Date(a.getFullYear(),a.getMonth()+1,0).getDate();for(let e=1;e<=t;e++)s[e.toString()]=0;e.forEach(e=>{let t=new Date(e.createdAt).getDate().toString();s[t]=(s[t]||0)+e[r]})}else if("year"===t){let t=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];t.forEach(e=>{s[e]=0}),e.forEach(e=>{let a=t[new Date(e.createdAt).getMonth()];s[a]+=e[r]})}return s}"undefined"!=typeof setInterval&&setInterval(()=>{p.prune()},6e4);let g=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/dashboard/route",pathname:"/api/dashboard",filename:"route",bundlePath:"app/api/dashboard/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\dashboard\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:y}=g;function b(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8044,3112],()=>r(76887));module.exports=a})();
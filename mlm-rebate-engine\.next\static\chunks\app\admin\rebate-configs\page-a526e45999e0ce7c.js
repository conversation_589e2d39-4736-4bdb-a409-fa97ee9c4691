(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2614],{802:(e,s,r)=>{Promise.resolve().then(r.bind(r,18376))},18376:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(95155),a=r(12115),n=r(12108),l=r(35695),i=r(70357),d=r(29911);function c(){let{data:e,status:s}=(0,n.useSession)(),r=(0,l.useRouter)(),[c,o]=(0,a.useState)(!0),[u,m]=(0,a.useState)([]),[x,g]=(0,a.useState)([]),[b,p]=(0,a.useState)(!1),[f,h]=(0,a.useState)(null),[y,j]=(0,a.useState)(null),[N,v]=(0,a.useState)(!1),[w,k]=(0,a.useState)(null),[C,T]=(0,a.useState)(null);(0,a.useEffect)(()=>{"unauthenticated"===s&&r.push("/login")},[s,r]),(0,a.useEffect)(()=>{"authenticated"===s&&E()},[s]);let E=async()=>{try{let e=await fetch("/api/users/me"),s=await e.json(),r=6===s.rankId;p(r),r?(A(),F()):o(!1)}catch(e){console.error("Error checking admin status:",e),o(!1)}},A=async()=>{try{let e=await fetch("/api/admin/rebate-configs");if(!e.ok)throw Error("Failed to fetch rebate configurations");let s=await e.json();m(s.rebateConfigs)}catch(e){console.error("Error fetching rebate configurations:",e),k("Failed to load rebate configurations")}finally{o(!1)}},F=async()=>{try{let e=await fetch("/api/admin/products");if(!e.ok)throw Error("Failed to fetch products");let s=await e.json();g(s.products)}catch(e){console.error("Error fetching products:",e)}},S=e=>{h({...e}),j(null)},P=()=>{h(null),j(null)},I=(e,s)=>{let{name:r,value:t,type:a}=e.target,n="number"===a?parseFloat(t):t;"editing"===s&&f?h({...f,[r]:n}):"new"===s&&y&&j({...y,[r]:n})},L=(e,s)=>{"editing"===s&&f?h({...f,rewardType:e}):"new"===s&&y&&j({...y,rewardType:e})},O=async e=>{v(!0),k(null),T(null);try{let s="editing"===e?f:y;if(!s)throw Error("No configuration data to save");let r="editing"===e?"/api/admin/rebate-configs/".concat(s.id):"/api/admin/rebate-configs",t=await fetch(r,{method:"editing"===e?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to save configuration")}await A(),h(null),j(null),T("editing"===e?"Configuration updated successfully":"Configuration created successfully")}catch(e){console.error("Error saving configuration:",e),k(e instanceof Error?e.message:"Failed to save configuration")}finally{v(!1)}},Q=async e=>{if(confirm("Are you sure you want to delete this configuration?")){k(null),T(null);try{let s=await fetch("/api/admin/rebate-configs/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete configuration")}await A(),T("Configuration deleted successfully")}catch(e){console.error("Error deleting configuration:",e),k(e instanceof Error?e.message:"Failed to delete configuration")}}},_=e=>{let s=x.find(s=>s.id===e);return s?s.name:"Product ".concat(e)};return"loading"===s||c?(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("div",{className:"text-xl",children:"Loading..."})]})}):b?(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,t.jsx)(d.Pcn,{className:"mr-2 text-blue-500"})," Rebate Configurations"]}),(0,t.jsxs)("button",{onClick:()=>{var e;j({productId:(null==(e=x[0])?void 0:e.id)||0,level:1,rewardType:"percentage",percentage:5,fixedAmount:0}),h(null)},className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center",disabled:!!y||!!f,children:[(0,t.jsx)(d.OiG,{className:"mr-2"})," Add Configuration"]})]}),w&&(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center",children:[(0,t.jsx)(d.QCr,{className:"mr-2"})," ",w]}),C&&(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center",children:[(0,t.jsx)(FaCheck,{className:"mr-2"})," ",C]}),y&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Add New Configuration"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product"}),(0,t.jsx)("select",{name:"productId",value:y.productId,onChange:e=>I(e,"new"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:x.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Level"}),(0,t.jsx)("input",{type:"number",name:"level",value:y.level,onChange:e=>I(e,"new"),min:"1",max:"10",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reward Type"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("button",{type:"button",onClick:()=>L("percentage","new"),className:"px-4 py-2 rounded-md flex items-center ".concat("percentage"===y.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"),children:[(0,t.jsx)(d.gdQ,{className:"mr-2"})," Percentage"]}),(0,t.jsxs)("button",{type:"button",onClick:()=>L("fixed","new"),className:"px-4 py-2 rounded-md flex items-center ".concat("fixed"===y.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"),children:[(0,t.jsx)(d.Tsk,{className:"mr-2"})," Fixed Amount"]})]})]}),"percentage"===y.rewardType?(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Percentage"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"number",name:"percentage",value:y.percentage,onChange:e=>I(e,"new"),min:"0",max:"100",step:"0.1",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pr-10"}),(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)(d.gdQ,{className:"text-gray-400"})})]})]}):(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fixed Amount"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("span",{className:"text-gray-500",children:"₱"})}),(0,t.jsx)("input",{type:"number",name:"fixedAmount",value:y.fixedAmount,onChange:e=>I(e,"new"),min:"0",step:"0.01",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,t.jsx)("button",{onClick:P,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Cancel"}),(0,t.jsx)("button",{onClick:()=>O("new"),disabled:N,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:N?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.hW,{className:"animate-spin mr-2"})," Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.dIn,{className:"mr-2"})," Save"]})})]})]}),f&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Edit Configuration"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product"}),(0,t.jsx)("select",{name:"productId",value:f.productId,onChange:e=>I(e,"editing"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:x.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Level"}),(0,t.jsx)("input",{type:"number",name:"level",value:f.level,onChange:e=>I(e,"editing"),min:"1",max:"10",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reward Type"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("button",{type:"button",onClick:()=>L("percentage","editing"),className:"px-4 py-2 rounded-md flex items-center ".concat("percentage"===f.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"),children:[(0,t.jsx)(d.gdQ,{className:"mr-2"})," Percentage"]}),(0,t.jsxs)("button",{type:"button",onClick:()=>L("fixed","editing"),className:"px-4 py-2 rounded-md flex items-center ".concat("fixed"===f.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"),children:[(0,t.jsx)(d.Tsk,{className:"mr-2"})," Fixed Amount"]})]})]}),"percentage"===f.rewardType?(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Percentage"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"number",name:"percentage",value:f.percentage,onChange:e=>I(e,"editing"),min:"0",max:"100",step:"0.1",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pr-10"}),(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)(d.gdQ,{className:"text-gray-400"})})]})]}):(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fixed Amount"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("span",{className:"text-gray-500",children:"₱"})}),(0,t.jsx)("input",{type:"number",name:"fixedAmount",value:f.fixedAmount,onChange:e=>I(e,"editing"),min:"0",step:"0.01",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,t.jsx)("button",{onClick:P,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Cancel"}),(0,t.jsx)("button",{onClick:()=>O("editing"),disabled:N,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:N?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.hW,{className:"animate-spin mr-2"})," Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.dIn,{className:"mr-2"})," Save"]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b",children:(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"All Configurations"})}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reward Type"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Value"}),(0,t.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:u.length>0?u.map(e=>{var s;return(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(null==(s=e.product)?void 0:s.name)||_(e.productId)})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Level ",e.level]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("percentage"===e.rewardType?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"),children:["percentage"===e.rewardType?(0,t.jsx)(d.gdQ,{className:"mr-1"}):(0,t.jsx)(d.Tsk,{className:"mr-1"}),e.rewardType.charAt(0).toUpperCase()+e.rewardType.slice(1)]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"text-sm text-gray-900",children:"percentage"===e.rewardType?"".concat(e.percentage,"%"):"₱".concat(e.fixedAmount.toFixed(2))})}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,t.jsx)("button",{onClick:()=>S(e),className:"text-blue-600 hover:text-blue-900 mr-3",disabled:!!f||!!y,children:(0,t.jsx)(d.uO9,{})}),(0,t.jsx)("button",{onClick:()=>Q(e.id),className:"text-red-600 hover:text-red-900",disabled:!!f||!!y,children:(0,t.jsx)(d.qbC,{})})]})]},e.id)}):(0,t.jsx)("tr",{children:(0,t.jsx)("td",{colSpan:5,className:"px-6 py-4 text-center text-gray-500",children:"No rebate configurations found"})})})]})})]})]})}):(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(802)),_N_E=e.O()}]);
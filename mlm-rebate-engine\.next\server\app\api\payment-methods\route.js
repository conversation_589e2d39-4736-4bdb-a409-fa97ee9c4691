(()=>{var e={};e.id=1657,e.ids=[1657],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>d,aP:()=>l});var s=t(96330),n=t(13581),o=t(85663),a=t(55511),i=t.n(a);async function u(e){return await o.Ay.hash(e,10)}function l(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let d={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await o.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:a,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(s,o,i):s[o]=e[o]}return s.default=e,t&&t.set(e,s),s}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},92490:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),o=t(48088),a=t(37719),i=t(31183),u=t(32190),l=t(19854),d=t(12909),c=t(92509);async function p(e){try{let r=await (0,l.getServerSession)(d.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view payment methods"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let n=new URL(e.url),o="true"===n.searchParams.get("includeUserMethods"),a=await (0,c.Gx)(!0),p=null;return o&&(p=await (0,c._)(s.id)),u.NextResponse.json({paymentMethods:a,userPaymentMethods:p})}catch(e){return console.error("Error fetching payment methods:",e),u.NextResponse.json({error:"Failed to fetch payment methods"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/payment-methods/route",pathname:"/api/payment-methods",filename:"route",bundlePath:"app/api/payment-methods/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\payment-methods\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:y}=f;function w(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},92509:(e,r,t)=>{"use strict";t.d(r,{Gb:()=>i,Gx:()=>n,XE:()=>d,_:()=>a,cE:()=>u,gY:()=>o,w9:()=>l});var s=t(31183);async function n(e=!0){return await s.z.paymentMethod.findMany({where:e?{isActive:!0}:void 0,orderBy:{name:"asc"}})}async function o(e){return await s.z.paymentMethod.findUnique({where:{id:e}})}async function a(e){return await s.z.userPaymentMethod.findMany({where:{userId:e},include:{paymentMethod:!0},orderBy:[{isDefault:"desc"},{createdAt:"desc"}]})}async function i(e,r,t,n=!1){return n&&await s.z.userPaymentMethod.updateMany({where:{userId:e,isDefault:!0},data:{isDefault:!1}}),await s.z.userPaymentMethod.create({data:{userId:e,paymentMethodId:r,details:t,isDefault:n},include:{paymentMethod:!0}})}async function u(e,r,t){let n=await s.z.userPaymentMethod.findUnique({where:{id:e},select:{userId:!0}});if(!n)throw Error(`User payment method with ID ${e} not found`);return t&&await s.z.userPaymentMethod.updateMany({where:{userId:n.userId,isDefault:!0,id:{not:e}},data:{isDefault:!1}}),await s.z.userPaymentMethod.update({where:{id:e},data:{details:void 0!==r?r:void 0,isDefault:void 0!==t?t:void 0,updatedAt:new Date},include:{paymentMethod:!0}})}async function l(e){return await s.z.userPaymentMethod.delete({where:{id:e},include:{paymentMethod:!0}})}async function d(e,r){let t=await o(e);if(!t)return{isValid:!1,errors:["Payment method not found"]};if(!t.requiresDetails||!t.detailsSchema)return{isValid:!0};try{let e=JSON.parse(t.detailsSchema);if(e.required&&Array.isArray(e.required)){let t=[];for(let s of e.required)r[s]||t.push(`Field "${s}" is required`);if(t.length>0)return{isValid:!1,errors:t}}return{isValid:!0}}catch(e){return console.error("Error validating payment details:",e),{isValid:!1,errors:["Invalid schema format"]}}}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(92490));module.exports=s})();
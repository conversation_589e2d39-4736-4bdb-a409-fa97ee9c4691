(()=>{var e={};e.id=7575,e.ids=[7575],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38571:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\products\\page.tsx","default")},41009:(e,t,s)=>{Promise.resolve().then(s.bind(s,97526))},46242:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38571)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\products\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\products\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},50737:(e,t,s)=>{Promise.resolve().then(s.bind(s,38571))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},97526:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(43210),l=s(82136),i=s(16189),n=s(68367),d=s(30474),c=s(23877);let o=({className:e=""})=>(0,r.jsx)("div",{className:`w-full h-full flex items-center justify-center bg-gray-100 ${e}`,children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.dkL,{className:"mx-auto text-gray-400 text-4xl mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No image available"})]})});function m(){let{data:e,status:t}=(0,l.useSession)();(0,i.useRouter)();let[s,m]=(0,a.useState)([]),[x,u]=(0,a.useState)(!0),[p,h]=(0,a.useState)(!1),[g,b]=(0,a.useState)(null),[f,j]=(0,a.useState)({name:"",description:"",price:"",image:""}),[v,y]=(0,a.useState)(null),[N,w]=(0,a.useState)(null),[C,P]=(0,a.useState)(!1),k=(0,a.useRef)(null),[S,A]=(0,a.useState)([{level:1,percentage:"10"}]),[F,L]=(0,a.useState)({type:"",text:""}),[E,R]=(0,a.useState)(null),[M,$]=(0,a.useState)(""),[_,D]=(0,a.useState)(!1),[G,q]=(0,a.useState)(null),[I,O]=(0,a.useState)("createdAt"),[T,U]=(0,a.useState)("desc"),[z,J]=(0,a.useState)(null),[V,B]=(0,a.useState)(!1),[W,Z]=(0,a.useState)(!1),[H,K]=(0,a.useState)(null),Q=async()=>{u(!0);try{let e=new URLSearchParams;M&&e.append("search",M),null!==G&&e.append("isActive",G.toString()),e.append("sortBy",I),e.append("sortOrder",T);let t=await fetch(`/api/products?${e.toString()}`);if(!t.ok)throw Error(`Failed to fetch products: ${t.statusText}`);let s=await t.json(),r=Array.isArray(s)?s:s.products||[];m(r),u(!1)}catch(e){console.error("Error fetching products:",e),u(!1)}},X=e=>{let{name:t,value:s}=e.target;j(e=>({...e,[t]:s}))},Y=async()=>{if(!v)return null;P(!0);try{let e=new FormData;e.append("file",v);let t=await fetch("/api/upload",{method:"POST",body:e}),s=await t.json();if(!t.ok)throw Error(s.error||"Failed to upload image");return P(!1),s.url}catch(e){return L({type:"error",text:e.message||"An error occurred while uploading the image"}),P(!1),null}},ee=(e,t,s)=>{let r=[...S];r[e]={...r[e],[t]:"level"===t?parseInt(s):s},A(r)},et=e=>{let t=[...S];t.splice(e,1),A(t)},es=()=>{j({name:"",description:"",price:"",image:""}),A([{level:1,percentage:"10"}]),b(null),h(!1),y(null),w(null),k.current&&(k.current.value="")},er=e=>{b(e),j({name:e.name,description:e.description||"",price:e.price.toString(),image:e.image||""}),e.image?w(e.image):w(null),y(null),k.current&&(k.current.value="");let t=e.rebateConfigs.map(e=>({level:e.level,percentage:e.percentage.toString()}));A(t.length>0?t:[{level:1,percentage:"10"}]),h(!0)},ea=async e=>{e.preventDefault(),L({type:"",text:""});try{let e;if(!f.name||!f.price)return void L({type:"error",text:"Name and price are required"});for(let e of S)if(e.level<=0||0>=parseFloat(e.percentage))return void L({type:"error",text:"Rebate level and percentage must be greater than 0"});let t=S.map(e=>e.level);if(new Set(t).size!==t.length)return void L({type:"error",text:"Duplicate rebate levels are not allowed"});let s=f.image;if(v){let e=await Y();e&&(s=e)}let r={...f,price:parseFloat(f.price),image:s,rebateConfigs:S.map(e=>({level:e.level,percentage:parseFloat(e.percentage)}))};e=g?await fetch(`/api/products/${g.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}):await fetch("/api/products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});let a=await e.json();if(!e.ok)throw Error(a.error||"Failed to save product");L({type:"success",text:g?"Product updated successfully":"Product created successfully"}),Q(),es()}catch(e){L({type:"error",text:e.message||"An error occurred while saving the product"})}},el=e=>{R(E===e?null:e)};return"loading"===t||x?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"Product Management"}),(0,r.jsxs)("button",{onClick:()=>h(!p),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center",children:[(0,r.jsx)(c.OiG,{className:"mr-2"})," ",p?"Cancel":"Add Product"]})]}),F.text&&(0,r.jsx)("div",{className:`mb-6 p-4 rounded-md ${"success"===F.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,children:F.text}),p&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:g?"Edit Product":"Add New Product"}),(0,r.jsxs)("form",{onSubmit:ea,children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:f.name,onChange:X,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"price",className:"block text-sm font-medium text-gray-700 mb-1",children:"Price *"}),(0,r.jsx)("input",{type:"number",id:"price",name:"price",value:f.price,onChange:X,min:"0.01",step:"0.01",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Image"}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[N&&(0,r.jsxs)("div",{className:"relative w-full h-40 mb-2 border rounded-md overflow-hidden",children:[(0,r.jsx)(d.default,{src:N,alt:"Product preview",fill:!0,className:"object-contain"}),(0,r.jsx)("button",{type:"button",onClick:()=>{w(null),y(null),j(e=>({...e,image:""})),k.current&&(k.current.value="")},className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600",title:"Remove image",children:(0,r.jsx)(c.QCr,{size:14})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"file",id:"imageUpload",ref:k,onChange:e=>{let t=e.target.files?.[0];if(!t)return;if(!["image/jpeg","image/png","image/webp","image/gif"].includes(t.type))return void L({type:"error",text:"File type not allowed. Please upload a JPEG, PNG, WebP, or GIF image."});if(t.size>5242880)return void L({type:"error",text:"File size exceeds the 5MB limit"});y(t);let s=new FileReader;s.onloadend=()=>{w(s.result)},s.readAsDataURL(t)},accept:"image/jpeg,image/png,image/webp,image/gif",className:"hidden"}),(0,r.jsxs)("button",{type:"button",onClick:()=>k.current?.click(),className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 flex items-center",disabled:C,children:[C?(0,r.jsx)(c.hW,{className:"mr-2 animate-spin"}):(0,r.jsx)(c.HVe,{className:"mr-2"}),v?"Change Image":"Upload Image"]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("input",{type:"text",id:"image",name:"image",value:f.image,onChange:X,placeholder:"Or enter image URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Upload a JPEG, PNG, WebP, or GIF image (max 5MB)"})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,r.jsx)("textarea",{id:"description",name:"description",value:f.description,onChange:X,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("h3",{className:"text-md font-semibold",children:"Rebate Configuration"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{let e=S.length>0?Math.max(...S.map(e=>e.level))+1:1;A([...S,{level:e,percentage:"5"}])},className:"px-2 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center",children:[(0,r.jsx)(c.OiG,{className:"mr-1"})," Add Level"]})]}),(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:S.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center mb-2 last:mb-0",children:[(0,r.jsxs)("div",{className:"w-1/3 pr-2",children:[(0,r.jsx)("label",{htmlFor:`level-${t}`,className:"block text-xs font-medium text-gray-700 mb-1",children:"Level"}),(0,r.jsx)("input",{type:"number",id:`level-${t}`,value:e.level,onChange:e=>ee(t,"level",e.target.value),min:"1",max:"10",className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),(0,r.jsxs)("div",{className:"w-1/3 px-2",children:[(0,r.jsx)("label",{htmlFor:`percentage-${t}`,className:"block text-xs font-medium text-gray-700 mb-1",children:"Percentage (%)"}),(0,r.jsx)("input",{type:"number",id:`percentage-${t}`,value:e.percentage,onChange:e=>ee(t,"percentage",e.target.value),min:"0.1",step:"0.1",max:"100",className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),(0,r.jsx)("div",{className:"w-1/3 pl-2 flex items-end",children:(0,r.jsx)("button",{type:"button",onClick:()=>et(t),disabled:1===S.length,className:"mt-5 px-2 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-red-300 text-sm",children:"Remove"})})]},t))})]}),(0,r.jsxs)("div",{className:"flex justify-end",children:[(0,r.jsx)("button",{type:"button",onClick:es,className:"px-4 py-2 border border-gray-300 rounded-md mr-2 hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:g?"Update Product":"Create Product"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search products by name or description",value:M,onChange:e=>$(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"text-gray-400"})})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("button",{onClick:()=>D(!_),className:"flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:[(0,r.jsx)(c.YsJ,{className:"mr-2"}),"Filters",_?(0,r.jsx)(c.Ucs,{className:"ml-2"}):(0,r.jsx)(c.Vr3,{className:"ml-2"})]})})]}),_&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>q(!0!==G||null),className:`px-3 py-1 rounded-md flex items-center ${!0===G?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-700 border border-gray-200"}`,children:[!0===G?(0,r.jsx)(c.RIx,{className:"mr-2"}):(0,r.jsx)(c.jZj,{className:"mr-2"}),"Active"]}),(0,r.jsxs)("button",{onClick:()=>q(!1===G&&null),className:`px-3 py-1 rounded-md flex items-center ${!1===G?"bg-red-100 text-red-800 border border-red-300":"bg-gray-100 text-gray-700 border border-gray-200"}`,children:[!1===G?(0,r.jsx)(c.RIx,{className:"mr-2"}):(0,r.jsx)(c.jZj,{className:"mr-2"}),"Inactive"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort By"}),(0,r.jsxs)("select",{value:I,onChange:e=>O(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"name",children:"Name"}),(0,r.jsx)("option",{value:"price",children:"Price"}),(0,r.jsx)("option",{value:"createdAt",children:"Date Created"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort Order"}),(0,r.jsxs)("select",{value:T,onChange:e=>U(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"asc",children:"Ascending"}),(0,r.jsx)("option",{value:"desc",children:"Descending"})]})]})]}),(0,r.jsx)("div",{className:"mt-4 flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{$(""),q(null),O("createdAt"),U("desc")},className:"px-4 py-2 text-gray-700 hover:text-gray-900",children:"Reset Filters"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Products"})}),(0,r.jsx)("div",{className:"p-6",children:s.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rebate Levels"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>el(e.id),className:"mr-2 text-gray-500",children:E===e.id?(0,r.jsx)(c.Ucs,{}):(0,r.jsx)(c.Vr3,{})}),(0,r.jsx)("div",{className:"h-10 w-10 flex-shrink-0 mr-3 relative rounded-md overflow-hidden",children:e.image?(0,r.jsx)(d.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover"}):(0,r.jsx)(o,{className:"rounded-md"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),E===e.id&&(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:e.description||"No description"})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.price.toFixed(2)]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Active":"Inactive"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.rebateConfigs.length," levels",E===e.id&&(0,r.jsx)("div",{className:"mt-2 text-xs",children:e.rebateConfigs.sort((e,t)=>e.level-t.level).map(e=>(0,r.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,r.jsxs)("span",{children:["Level ",e.level,":"]}),(0,r.jsxs)("span",{children:[e.percentage,"%"]})]},e.id))})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{J(e),B(!0)},className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,r.jsx)(c.Ny1,{})}),(0,r.jsx)("button",{onClick:()=>er(e),className:"text-green-600 hover:text-green-900",title:"Edit Product",children:(0,r.jsx)(c.uO9,{})}),(0,r.jsx)("button",{onClick:async()=>{try{if(!(await fetch(`/api/products/${e.id}/toggle-status`,{method:"PATCH"})).ok)throw Error("Failed to toggle product status");Q(),L({type:"success",text:`Product ${e.isActive?"deactivated":"activated"} successfully`})}catch(e){console.error("Error toggling product status:",e),L({type:"error",text:"Failed to toggle product status"})}},className:`${e.isActive?"text-orange-600 hover:text-orange-900":"text-green-600 hover:text-green-900"}`,title:e.isActive?"Deactivate Product":"Activate Product",children:e.isActive?(0,r.jsx)(c.RIx,{}):(0,r.jsx)(c.jZj,{})}),(0,r.jsx)("button",{onClick:()=>{K(e),Z(!0)},className:"text-red-600 hover:text-red-900",title:"Delete Product",children:(0,r.jsx)(c.qbC,{})})]})})]},e.id))})]})}):(0,r.jsx)("p",{className:"text-gray-500",children:"No products found."})})]}),V&&z&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Product Details"}),(0,r.jsx)("button",{onClick:()=>B(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)(c.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"md:w-1/3",children:[(0,r.jsx)("div",{className:"relative w-full h-64 rounded-md overflow-hidden border border-gray-200",children:z.image?(0,r.jsx)(d.default,{src:z.image,alt:z.name,fill:!0,className:"object-contain"}):(0,r.jsx)(o,{})}),z.image&&(0,r.jsx)("div",{className:"mt-2 flex justify-center",children:(0,r.jsxs)("a",{href:z.image,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800 flex items-center",children:[(0,r.jsx)(c.Ny1,{className:"mr-1"})," View full image"]})})]}),(0,r.jsxs)("div",{className:"md:w-2/3",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:z.name}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${z.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:z.isActive?"Active":"Inactive"})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Price"}),(0,r.jsxs)("p",{className:"text-lg font-semibold",children:["₱",z.price.toFixed(2)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Description"}),(0,r.jsx)("p",{className:"text-gray-700",children:z.description||"No description provided"})]})]})]}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("h3",{className:"text-md font-semibold mb-3",children:"Rebate Configuration"}),z.rebateConfigs.length>0?(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-2",children:"Level"}),(0,r.jsx)("th",{className:"text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-2",children:"Percentage"}),(0,r.jsx)("th",{className:"text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-2",children:"Rebate Amount"})]})}),(0,r.jsx)("tbody",{children:z.rebateConfigs.sort((e,t)=>e.level-t.level).map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"py-2 text-sm",children:["Level ",e.level]}),(0,r.jsxs)("td",{className:"py-2 text-sm",children:[e.percentage,"%"]}),(0,r.jsxs)("td",{className:"py-2 text-sm",children:["₱",(z.price*e.percentage/100).toFixed(2)]})]},e.id))})]})}):(0,r.jsx)("p",{className:"text-gray-500",children:"No rebate configuration"})]}),(0,r.jsxs)("div",{className:"flex justify-end mt-6 space-x-3",children:[(0,r.jsx)("button",{onClick:()=>{B(!1),er(z)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Edit Product"}),(0,r.jsx)("button",{onClick:()=>B(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Close"})]})]})]})}),W&&H&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-md w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Delete"}),(0,r.jsxs)("p",{className:"text-gray-700 mb-6",children:['Are you sure you want to delete the product "',H.name,'"? This action cannot be undone.']}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:async()=>{try{if(!(await fetch(`/api/products/${H.id}`,{method:"DELETE"})).ok)throw Error("Failed to delete product");Q(),L({type:"success",text:"Product deleted successfully"}),Z(!1),K(null)}catch(e){console.error("Error deleting product:",e),L({type:"error",text:"Failed to delete product"})}},className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Delete"}),(0,r.jsx)("button",{onClick:()=>{Z(!1),K(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"})]})]})})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(46242));module.exports=r})();
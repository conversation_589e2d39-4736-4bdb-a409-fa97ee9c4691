(()=>{var e={};e.id=4895,e.ids=[4895],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29822:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["shop",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34771)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\shop\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\shop\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/shop/page",pathname:"/shop",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},34771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\shop\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\shop\\page.tsx","default")},36658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(60687),n=r(43210),i=r(82136),a=r(68367),o=r(85814),l=r.n(o),d=r(23877),c=r(28253),u=r(30474),p=Object.defineProperty,m=(e,t,r)=>t in e?p(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,h=new Map,x=new WeakMap,g=0,f=void 0;n.Component;let b=({src:e,alt:t,width:r,height:i,fallbackSrc:a="/images/placeholder.jpg",lowQualitySrc:o,loadingColor:l="#f3f4f6",threshold:d=.1,className:c="",...p})=>{let[m,b]=(0,n.useState)(!1),[v,j]=(0,n.useState)(!1),[y,w]=(0,n.useState)(!1),{ref:N,inView:C}=function({threshold:e,delay:t,trackVisibility:r,rootMargin:s,root:i,triggerOnce:a,skip:o,initialInView:l,fallbackInView:d,onChange:c}={}){var u;let[p,m]=n.useState(null),b=n.useRef(c),[v,j]=n.useState({inView:!!l,entry:void 0});b.current=c,n.useEffect(()=>{let n;if(!o&&p)return n=function(e,t,r={},s=f){if(void 0===window.IntersectionObserver&&void 0!==s){let n=e.getBoundingClientRect();return t(s,{isIntersecting:s,target:e,intersectionRatio:"number"==typeof r.threshold?r.threshold:0,time:0,boundingClientRect:n,intersectionRect:n,rootBounds:n}),()=>{}}let{id:n,observer:i,elements:a}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var r;return`${t}_${"root"===t?!(r=e.root)?"0":(x.has(r)||(g+=1,x.set(r,g.toString())),x.get(r)):e[t]}`}).toString(),r=h.get(t);if(!r){let s,n=new Map,i=new IntersectionObserver(t=>{t.forEach(t=>{var r;let i=t.isIntersecting&&s.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=i),null==(r=n.get(t.target))||r.forEach(e=>{e(i,t)})})},e);s=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),r={id:t,observer:i,elements:n},h.set(t,r)}return r}(r),o=a.get(e)||[];return a.has(e)||a.set(e,o),o.push(t),i.observe(e),function(){o.splice(o.indexOf(t),1),0===o.length&&(a.delete(e),i.unobserve(e)),0===a.size&&(i.disconnect(),h.delete(n))}}(p,(e,t)=>{j({inView:e,entry:t}),b.current&&b.current(e,t),t.isIntersecting&&a&&n&&(n(),n=void 0)},{root:i,rootMargin:s,threshold:e,trackVisibility:r,delay:t},d),()=>{n&&n()}},[Array.isArray(e)?e.toString():e,p,i,s,a,o,r,d,t]);let y=null==(u=v.entry)?void 0:u.target,w=n.useRef(void 0);p||!y||a||o||w.current===y||(w.current=y,j({inView:!!l,entry:void 0}));let N=[m,v.inView,v.entry];return N.ref=N[0],N.inView=N[1],N.entry=N[2],N}({threshold:d,triggerOnce:!0}),S=(0,n.useRef)(null);(0,n.useEffect)(()=>{C&&w(!0)},[C]);let L=`
    transition-opacity duration-300 ease-in-out
    ${m?"opacity-100":"opacity-0"}
    ${c}
  `;return(0,s.jsxs)("div",{ref:N,className:"relative overflow-hidden",style:{width:r,height:i},children:[!m&&(0,s.jsx)("div",{className:"absolute inset-0 animate-pulse",style:{backgroundColor:l}}),!m&&o&&y&&(0,s.jsx)(u.default,{src:o,alt:t,fill:!0,className:"object-cover blur-sm",priority:!1}),y&&(0,s.jsx)(u.default,{...p,ref:S,src:v?a:e,alt:t,width:r,height:i,className:L,onLoadingComplete:()=>{b(!0)},onError:()=>{j(!0)},priority:!1})]})},v=({product:e})=>{let{data:t}=(0,i.useSession)(),{addItem:r}=(0,c._)(),[a,o]=(0,n.useState)(!1),u=e=>`₱${e.toFixed(2)}`,p=!!t?.user,{displayPrice:m,discount:h,discountPercentage:x}=(0,n.useMemo)(()=>{let t=p?e.price:e.srp,r=e.srp-e.price,s=Math.round(r/e.srp*100);return{displayPrice:t,discount:r,discountPercentage:s}},[p,e.price,e.srp]);return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[(0,s.jsx)(l(),{href:`/shop/product/${e.id}`,children:(0,s.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[e.image?(0,s.jsx)(b,{src:e.image,alt:e.name,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover hover:scale-105 transition-transform duration-300",loadingColor:"#f3f4f6",fallbackSrc:"/images/product-placeholder.jpg",threshold:.2}):(0,s.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,s.jsx)(d.AsH,{className:"text-gray-400 h-8 w-8"})}),p&&h>0&&(0,s.jsxs)("div",{className:"absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded",children:[x,"% OFF"]})]})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)(l(),{href:`/shop/product/${e.id}`,children:(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors duration-200 mb-1 line-clamp-2",children:e.name})}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-3 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-lg font-bold text-green-600",children:u(m)}),p&&h>0&&(0,s.jsx)("div",{className:"text-sm text-gray-500 line-through",children:u(e.srp)})]}),(0,s.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded",children:[e.pv," PV"]})]}),!p&&(0,s.jsxs)("div",{className:"text-xs text-blue-600 mb-3",children:[(0,s.jsx)(l(),{href:"/login",className:"hover:underline",children:"Sign in as a member"})," ","for discounted prices!"]}),(0,s.jsx)("button",{type:"button",onClick:()=>{r({id:e.id,name:e.name,price:e.price,srp:e.srp,image:e.image,quantity:1,pv:e.pv}),o(!0),setTimeout(()=>{o(!1)},3e3)},disabled:a,className:`w-full flex items-center justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${a?"bg-green-600":"bg-blue-600 hover:bg-blue-700"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200`,children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.CMH,{className:"mr-2"}),"Added to Cart"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.AsH,{className:"mr-2"}),"Add to Cart"]})})]})]})};function j(){let{data:e,status:t}=(0,i.useSession)(),[r,o]=(0,n.useState)([]),[c,u]=(0,n.useState)(!0),[p,m]=(0,n.useState)(null),[h,x]=(0,n.useState)({type:"",text:""}),[g,f]=(0,n.useState)("");return"loading"===t||c?(0,s.jsx)(a.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,s.jsx)(a.A,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold mb-4",children:"Shop Products"}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.KSO,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search products...",value:g,onChange:e=>f(e.target.value)})]})}),h.text&&(0,s.jsx)("div",{className:`mb-6 p-4 rounded-md ${"success"===h.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,children:h.text}),!e&&(0,s.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-blue-800 mb-1",children:"Member Benefits"}),(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:["Sign in as a member to enjoy discounted prices and earn rebates on your purchases!",(0,s.jsx)("span",{className:"ml-2",children:(0,s.jsx)(l(),{href:"/login",className:"text-blue-600 hover:underline font-medium",children:"Sign in"})})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase())).map(e=>(0,s.jsx)(v,{product:e},e.id))}),0===r.length&&!c&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No products available at the moment."})}),r.length>0&&0===r.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase())).length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No products match your search."})})]})})}},59724:(e,t,r)=>{Promise.resolve().then(r.bind(r,36658))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},96172:(e,t,r)=>{Promise.resolve().then(r.bind(r,34771))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>r(29822));module.exports=s})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4895],{29493:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(95155),i=s(12115),n=s(12108),a=s(70357),l=s(6874),o=s.n(l),c=s(29911),d=s(5323),u=s(66766),h=Object.defineProperty,m=(e,t,s)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,x=new Map,p=new WeakMap,g=0,f=void 0;i.Component;let b=e=>{let{src:t,alt:s,width:n,height:a,fallbackSrc:l="/images/placeholder.jpg",lowQualitySrc:o,loadingColor:c="#f3f4f6",threshold:d=.1,className:h="",...m}=e,[b,v]=(0,i.useState)(!1),[j,y]=(0,i.useState)(!1),[w,N]=(0,i.useState)(!1),{ref:S,inView:C}=function(){var e;let{threshold:t,delay:s,trackVisibility:r,rootMargin:n,root:a,triggerOnce:l,skip:o,initialInView:c,fallbackInView:d,onChange:u}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[h,m]=i.useState(null),b=i.useRef(u),[v,j]=i.useState({inView:!!c,entry:void 0});b.current=u,i.useEffect(()=>{let e;if(!o&&h)return e=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:f;if(void 0===window.IntersectionObserver&&void 0!==r){let i=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof s.threshold?s.threshold:0,time:0,boundingClientRect:i,intersectionRect:i,rootBounds:i}),()=>{}}let{id:i,observer:n,elements:a}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var s;return"".concat(t,"_").concat("root"===t?(s=e.root)?(p.has(s)||(g+=1,p.set(s,g.toString())),p.get(s)):"0":e[t])}).toString(),s=x.get(t);if(!s){let r,i=new Map,n=new IntersectionObserver(t=>{t.forEach(t=>{var s;let n=t.isIntersecting&&r.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=n),null==(s=i.get(t.target))||s.forEach(e=>{e(n,t)})})},e);r=n.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),s={id:t,observer:n,elements:i},x.set(t,s)}return s}(s),l=a.get(e)||[];return a.has(e)||a.set(e,l),l.push(t),n.observe(e),function(){l.splice(l.indexOf(t),1),0===l.length&&(a.delete(e),n.unobserve(e)),0===a.size&&(n.disconnect(),x.delete(i))}}(h,(t,s)=>{j({inView:t,entry:s}),b.current&&b.current(t,s),s.isIntersecting&&l&&e&&(e(),e=void 0)},{root:a,rootMargin:n,threshold:t,trackVisibility:r,delay:s},d),()=>{e&&e()}},[Array.isArray(t)?t.toString():t,h,a,n,l,o,r,d,s]);let y=null==(e=v.entry)?void 0:e.target,w=i.useRef(void 0);h||!y||l||o||w.current===y||(w.current=y,j({inView:!!c,entry:void 0}));let N=[m,v.inView,v.entry];return N.ref=N[0],N.inView=N[1],N.entry=N[2],N}({threshold:d,triggerOnce:!0}),A=(0,i.useRef)(null);(0,i.useEffect)(()=>{C&&N(!0)},[C]);let k="\n    transition-opacity duration-300 ease-in-out\n    ".concat(b?"opacity-100":"opacity-0","\n    ").concat(h,"\n  ");return(0,r.jsxs)("div",{ref:S,className:"relative overflow-hidden",style:{width:n,height:a},children:[!b&&(0,r.jsx)("div",{className:"absolute inset-0 animate-pulse",style:{backgroundColor:c}}),!b&&o&&w&&(0,r.jsx)(u.default,{src:o,alt:s,fill:!0,className:"object-cover blur-sm",priority:!1}),w&&(0,r.jsx)(u.default,{...m,ref:A,src:j?l:t,alt:s,width:n,height:a,className:k,onLoadingComplete:()=>{v(!0)},onError:()=>{y(!0)},priority:!1})]})},v=e=>{let{product:t}=e,{data:s}=(0,n.useSession)(),{addItem:a}=(0,d._)(),[l,u]=(0,i.useState)(!1),h=e=>"₱".concat(e.toFixed(2)),m=!!(null==s?void 0:s.user),{displayPrice:x,discount:p,discountPercentage:g}=(0,i.useMemo)(()=>{let e=m?t.price:t.srp,s=t.srp-t.price,r=Math.round(s/t.srp*100);return{displayPrice:e,discount:s,discountPercentage:r}},[m,t.price,t.srp]);return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsx)(o(),{href:"/shop/product/".concat(t.id),children:(0,r.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[t.image?(0,r.jsx)(b,{src:t.image,alt:t.name,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover hover:scale-105 transition-transform duration-300",loadingColor:"#f3f4f6",fallbackSrc:"/images/product-placeholder.jpg",threshold:.2}):(0,r.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(c.AsH,{className:"text-gray-400 h-8 w-8"})}),m&&p>0&&(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded",children:[g,"% OFF"]})]})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)(o(),{href:"/shop/product/".concat(t.id),children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors duration-200 mb-1 line-clamp-2",children:t.name})}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-3 line-clamp-2",children:t.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-lg font-bold text-green-600",children:h(x)}),m&&p>0&&(0,r.jsx)("div",{className:"text-sm text-gray-500 line-through",children:h(t.srp)})]}),(0,r.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded",children:[t.pv," PV"]})]}),!m&&(0,r.jsxs)("div",{className:"text-xs text-blue-600 mb-3",children:[(0,r.jsx)(o(),{href:"/login",className:"hover:underline",children:"Sign in as a member"})," ","for discounted prices!"]}),(0,r.jsx)("button",{type:"button",onClick:()=>{a({id:t.id,name:t.name,price:t.price,srp:t.srp,image:t.image,quantity:1,pv:t.pv}),u(!0),setTimeout(()=>{u(!1)},3e3)},disabled:l,className:"w-full flex items-center justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ".concat(l?"bg-green-600":"bg-blue-600 hover:bg-blue-700"," focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"),children:l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.CMH,{className:"mr-2"}),"Added to Cart"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.AsH,{className:"mr-2"}),"Add to Cart"]})})]})]})};function j(){let{data:e,status:t}=(0,n.useSession)(),[s,l]=(0,i.useState)([]),[d,u]=(0,i.useState)(!0),[h,m]=(0,i.useState)(null),[x,p]=(0,i.useState)({type:"",text:""}),[g,f]=(0,i.useState)("");return((0,i.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/products"),t=await e.json();t&&t.products&&Array.isArray(t.products)?l(t.products):Array.isArray(t)?l(t):(console.error("Unexpected products data format:",t),l([])),u(!1)}catch(e){console.error("Error fetching products:",e),u(!1)}})()},[]),"loading"===t||d)?(0,r.jsx)(a.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,r.jsx)(a.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold mb-4",children:"Shop Products"}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search products...",value:g,onChange:e=>f(e.target.value)})]})}),x.text&&(0,r.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("success"===x.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:x.text}),!e&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-800 mb-1",children:"Member Benefits"}),(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:["Sign in as a member to enjoy discounted prices and earn rebates on your purchases!",(0,r.jsx)("span",{className:"ml-2",children:(0,r.jsx)(o(),{href:"/login",className:"text-blue-600 hover:underline font-medium",children:"Sign in"})})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase())).map(e=>(0,r.jsx)(v,{product:e},e.id))}),0===s.length&&!d&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No products available at the moment."})}),s.length>0&&0===s.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase())).length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No products match your search."})})]})})}},42180:(e,t,s)=>{Promise.resolve().then(s.bind(s,29493))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(42180)),_N_E=e.O()}]);
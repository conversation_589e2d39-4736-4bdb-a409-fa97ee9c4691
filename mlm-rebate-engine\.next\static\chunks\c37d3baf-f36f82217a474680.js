"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8702],{74211:(e,t,n)=>{n.d(t,{Cz:()=>ew,Do:()=>l,Gc:()=>nP,Jo:()=>eM,Ln:()=>nv,Mi:()=>z,PI:()=>v,Pj:()=>E,TG:()=>i,VH:()=>tg,Zk:()=>M,ck:()=>n_,fM:()=>n$,h7:()=>eH,yX:()=>s});var o,r,a,l,i,s,d=n(12115),c=n(75694),u=n(33036),g=n(92816),h=n(93219),p=n(82903),m=n(14897),f=n(16672);n(47650);let y=(0,d.createContext)(null),x=y.Provider,S={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${!e?"source":"target"} handle id: "${!e?t.sourceHandle:t.targetHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},b=S.error001();function E(e,t){let n=(0,d.useContext)(y);if(null===n)throw Error(b);return(0,u.n)(n,e,t)}let v=()=>{let e=(0,d.useContext)(y);if(null===e)throw Error(b);return(0,d.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},w=e=>e.userSelectionActive?"none":"all";function M({position:e,children:t,className:n,style:o,...r}){let a=E(w),l=`${e}`.split("-");return d.createElement("div",{className:(0,c.A)(["react-flow__panel",n,...l]),style:{...o,pointerEvents:a},...r},t)}function C({proOptions:e,position:t="bottom-right"}){return e?.hideAttribution?null:d.createElement(M,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},d.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var N=(0,d.memo)(({x:e,y:t,label:n,labelStyle:o={},labelShowBg:r=!0,labelBgStyle:a={},labelBgPadding:l=[2,4],labelBgBorderRadius:i=2,children:s,className:u,...g})=>{let h=(0,d.useRef)(null),[p,m]=(0,d.useState)({x:0,y:0,width:0,height:0}),f=(0,c.A)(["react-flow__edge-textwrapper",u]);return((0,d.useEffect)(()=>{if(h.current){let e=h.current.getBBox();m({x:e.x,y:e.y,width:e.width,height:e.height})}},[n]),void 0!==n&&n)?d.createElement("g",{transform:`translate(${e-p.width/2} ${t-p.height/2})`,className:f,visibility:p.width?"visible":"hidden",...g},r&&d.createElement("rect",{width:p.width+2*l[0],x:-l[0],y:-l[1],height:p.height+2*l[1],className:"react-flow__edge-textbg",style:a,rx:i,ry:i}),d.createElement("text",{className:"react-flow__edge-text",y:p.height/2,dy:"0.3em",ref:h,style:o},n),s):null});let A=e=>({width:e.offsetWidth,height:e.offsetHeight}),k=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),I=(e={x:0,y:0},t)=>({x:k(e.x,t[0][0],t[1][0]),y:k(e.y,t[0][1],t[1][1])}),P=(e,t,n)=>e<t?k(Math.abs(e-t),1,50)/50:e>n?-k(Math.abs(e-n),1,50)/50:0,R=(e,t)=>[20*P(e.x,35,t.width-35),20*P(e.y,35,t.height-35)],_=e=>e.getRootNode?.()||window?.document,$=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),O=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),B=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),D=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),z=(e,t)=>B($(O(e),O(t))),L=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),T=e=>H(e.width)&&H(e.height)&&H(e.x)&&H(e.y),H=e=>!isNaN(e)&&isFinite(e),F=Symbol.for("internals"),V=["Enter"," ","Escape"],K=(e,t)=>{},Y=e=>"nativeEvent"in e;function Z(e){let t=Y(e)?e.nativeEvent:e,n=t.composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n?.nodeName)||n?.hasAttribute("contenteditable")||!!n?.closest(".nokey")}let X=e=>"clientX"in e,W=(e,t)=>{let n=X(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},G=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,j=({id:e,path:t,labelX:n,labelY:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:c,style:u,markerEnd:g,markerStart:h,interactionWidth:p=20})=>d.createElement(d.Fragment,null,d.createElement("path",{id:e,style:u,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:g,markerStart:h}),p&&d.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:p,className:"react-flow__edge-interaction"}),r&&H(n)&&H(o)?d.createElement(N,{x:n,y:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:c}):null);function U(e,t,n){return void 0===n?n:o=>{let r=t().edges.find(t=>t.id===e);r&&n(o,{...r})}}function q({sourceX:e,sourceY:t,targetX:n,targetY:o}){let r=Math.abs(n-e)/2,a=Math.abs(o-t)/2;return[n<e?n+r:n-r,o<t?o+a:o-a,r,a]}function Q({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:a,targetControlX:l,targetControlY:i}){let s=.125*e+.375*r+.375*l+.125*n,d=.125*t+.375*a+.375*i+.125*o,c=Math.abs(s-e),u=Math.abs(d-t);return[s,d,c,u]}function J({pos:e,x1:t,y1:n,x2:o,y2:r}){return e===s.Left||e===s.Right?[.5*(t+o),n]:[t,.5*(n+r)]}function ee({sourceX:e,sourceY:t,sourcePosition:n=s.Bottom,targetX:o,targetY:r,targetPosition:a=s.Top}){let[l,i]=J({pos:n,x1:e,y1:t,x2:o,y2:r}),[d,c]=J({pos:a,x1:o,y1:r,x2:e,y2:t}),[u,g,h,p]=Q({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:l,sourceControlY:i,targetControlX:d,targetControlY:c});return[`M${e},${t} C${l},${i} ${d},${c} ${o},${r}`,u,g,h,p]}j.displayName="BaseEdge",function(e){e.Strict="strict",e.Loose="loose"}(o||(o={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(r||(r={})),function(e){e.Partial="partial",e.Full="full"}(a||(a={})),function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(l||(l={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(i||(i={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(s||(s={}));let et=(0,d.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,sourcePosition:r=s.Bottom,targetPosition:a=s.Top,label:l,labelStyle:i,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:f,interactionWidth:y})=>{let[x,S,b]=ee({sourceX:e,sourceY:t,sourcePosition:r,targetX:n,targetY:o,targetPosition:a});return d.createElement(j,{path:x,labelX:S,labelY:b,label:l,labelStyle:i,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:f,interactionWidth:y})});et.displayName="SimpleBezierEdge";let en={[s.Left]:{x:-1,y:0},[s.Right]:{x:1,y:0},[s.Top]:{x:0,y:-1},[s.Bottom]:{x:0,y:1}},eo=({source:e,sourcePosition:t=s.Bottom,target:n})=>t===s.Left||t===s.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},er=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function ea({sourceX:e,sourceY:t,sourcePosition:n=s.Bottom,targetX:o,targetY:r,targetPosition:a=s.Top,borderRadius:l=5,centerX:i,centerY:d,offset:c=20}){let[u,g,h,p,m]=function({source:e,sourcePosition:t=s.Bottom,target:n,targetPosition:o=s.Top,center:r,offset:a}){let l,i,d=en[t],c=en[o],u={x:e.x+d.x*a,y:e.y+d.y*a},g={x:n.x+c.x*a,y:n.y+c.y*a},h=eo({source:u,sourcePosition:t,target:g}),p=0!==h.x?"x":"y",m=h[p],f=[],y={x:0,y:0},x={x:0,y:0},[S,b,E,v]=q({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(d[p]*c[p]==-1){l=r.x??S,i=r.y??b;let e=[{x:l,y:u.y},{x:l,y:g.y}],t=[{x:u.x,y:i},{x:g.x,y:i}];f=d[p]===m?"x"===p?e:t:"x"===p?t:e}else{let r=[{x:u.x,y:g.y}],s=[{x:g.x,y:u.y}];if(f="x"===p?d.x===m?s:r:d.y===m?r:s,t===o){let t=Math.abs(e[p]-n[p]);if(t<=a){let o=Math.min(a-1,a-t);d[p]===m?y[p]=(u[p]>e[p]?-1:1)*o:x[p]=(g[p]>n[p]?-1:1)*o}}if(t!==o){let e="x"===p?"y":"x",t=d[p]===c[e],n=u[e]>g[e],o=u[e]<g[e];(1===d[p]&&(!t&&n||t&&o)||1!==d[p]&&(!t&&o||t&&n))&&(f="x"===p?r:s)}let h={x:u.x+y.x,y:u.y+y.y},S={x:g.x+x.x,y:g.y+x.y};Math.max(Math.abs(h.x-f[0].x),Math.abs(S.x-f[0].x))>=Math.max(Math.abs(h.y-f[0].y),Math.abs(S.y-f[0].y))?(l=(h.x+S.x)/2,i=f[0].y):(l=f[0].x,i=(h.y+S.y)/2)}return[[e,{x:u.x+y.x,y:u.y+y.y},...f,{x:g.x+x.x,y:g.y+x.y},n],l,i,E,v]}({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:a,center:{x:i,y:d},offset:c});return[u.reduce((e,t,n)=>{let o="";return e+(n>0&&n<u.length-1?function(e,t,n,o){let r=Math.min(er(e,t)/2,er(t,n)/2,o),{x:a,y:l}=t;if(e.x===a&&a===n.x||e.y===l&&l===n.y)return`L${a} ${l}`;if(e.y===l){let t=e.x<n.x?-1:1,o=e.y<n.y?1:-1;return`L ${a+r*t},${l}Q ${a},${l} ${a},${l+r*o}`}let i=e.x<n.x?1:-1,s=e.y<n.y?-1:1;return`L ${a},${l+r*s}Q ${a},${l} ${a+r*i},${l}`}(u[n-1],t,u[n+1],l):`${0===n?"M":"L"}${t.x} ${t.y}`)},""),g,h,p,m]}let el=(0,d.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:c,labelBgBorderRadius:u,style:g,sourcePosition:h=s.Bottom,targetPosition:p=s.Top,markerEnd:m,markerStart:f,pathOptions:y,interactionWidth:x})=>{let[S,b,E]=ea({sourceX:e,sourceY:t,sourcePosition:h,targetX:n,targetY:o,targetPosition:p,borderRadius:y?.borderRadius,offset:y?.offset});return d.createElement(j,{path:S,labelX:b,labelY:E,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:c,labelBgBorderRadius:u,style:g,markerEnd:m,markerStart:f,interactionWidth:x})});el.displayName="SmoothStepEdge";let ei=(0,d.memo)(e=>d.createElement(el,{...e,pathOptions:(0,d.useMemo)(()=>({borderRadius:0,offset:e.pathOptions?.offset}),[e.pathOptions?.offset])}));ei.displayName="StepEdge";let es=(0,d.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:c,style:u,markerEnd:g,markerStart:h,interactionWidth:p})=>{let[m,f,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:o}){let[r,a,l,i]=q({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,a,l,i]}({sourceX:e,sourceY:t,targetX:n,targetY:o});return d.createElement(j,{path:m,labelX:f,labelY:y,label:r,labelStyle:a,labelShowBg:l,labelBgStyle:i,labelBgPadding:s,labelBgBorderRadius:c,style:u,markerEnd:g,markerStart:h,interactionWidth:p})});function ed(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function ec({pos:e,x1:t,y1:n,x2:o,y2:r,c:a}){switch(e){case s.Left:return[t-ed(t-o,a),n];case s.Right:return[t+ed(o-t,a),n];case s.Top:return[t,n-ed(n-r,a)];case s.Bottom:return[t,n+ed(r-n,a)]}}function eu({sourceX:e,sourceY:t,sourcePosition:n=s.Bottom,targetX:o,targetY:r,targetPosition:a=s.Top,curvature:l=.25}){let[i,d]=ec({pos:n,x1:e,y1:t,x2:o,y2:r,c:l}),[c,u]=ec({pos:a,x1:o,y1:r,x2:e,y2:t,c:l}),[g,h,p,m]=Q({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:i,sourceControlY:d,targetControlX:c,targetControlY:u});return[`M${e},${t} C${i},${d} ${c},${u} ${o},${r}`,g,h,p,m]}es.displayName="StraightEdge";let eg=(0,d.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:o,sourcePosition:r=s.Bottom,targetPosition:a=s.Top,label:l,labelStyle:i,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:f,pathOptions:y,interactionWidth:x})=>{let[S,b,E]=eu({sourceX:e,sourceY:t,sourcePosition:r,targetX:n,targetY:o,targetPosition:a,curvature:y?.curvature});return d.createElement(j,{path:S,labelX:b,labelY:E,label:l,labelStyle:i,labelShowBg:c,labelBgStyle:u,labelBgPadding:g,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:f,interactionWidth:x})});eg.displayName="BezierEdge";let eh=(0,d.createContext)(null),ep=eh.Provider;eh.Consumer;let em=()=>(0,d.useContext)(eh),ef=e=>"id"in e&&"source"in e&&"target"in e,ey=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,ex=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`},eS=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),eb=(e,t)=>{let n;return e.source&&e.target?eS(n=ef(e)?{...e}:{...e,id:ey(e)},t)?t:t.concat(n):(K("006",S.error006()),t)},eE=({x:e,y:t},[n,o,r],a,[l,i])=>{let s={x:(e-n)/r,y:(t-o)/r};return a?{x:l*Math.round(s.x/l),y:i*Math.round(s.y/i)}:s},ev=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),ew=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};let n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},eM=(e,t=[0,0])=>0===e.length?{x:0,y:0,width:0,height:0}:B(e.reduce((e,n)=>{let{x:o,y:r}=ew(n,t).positionAbsolute;return $(e,O({x:o,y:r,width:n.width||0,height:n.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),eC=(e,t,[n,o,r]=[0,0,1],a=!1,l=!1,i=[0,0])=>{let s={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},d=[];return e.forEach(e=>{let{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(l&&!o||r)return!1;let{positionAbsolute:c}=ew(e,i),u=L(s,{x:c.x,y:c.y,width:t||0,height:n||0}),g=void 0===t||void 0===n||null===t||null===n,h=(t||0)*(n||0);(g||a&&u>0||u>=h||e.dragging)&&d.push(e)}),d},eN=(e,t)=>{let n=e.map(e=>e.id);return t.filter(e=>n.includes(e.source)||n.includes(e.target))},eA=(e,t,n,o,r,a=.1)=>{let l=k(Math.min(t/(e.width*(1+a)),n/(e.height*(1+a))),o,r),i=e.x+e.width/2;return{x:t/2-i*l,y:n/2-(e.y+e.height/2)*l,zoom:l}},ek=(e,t=0)=>e.transition().duration(t);function eI(e,t,n,o){return(t[n]||[]).reduce((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t),[])}let eP={source:null,target:null,sourceHandle:null,targetHandle:null},eR=()=>({handleDomNode:null,isValid:!1,connection:eP,endHandle:null});function e_(e,t,n,r,a,l,i){let s="target"===a,d=i.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),c={...eR(),handleDomNode:d};if(d){let e=e$(void 0,d),a=d.getAttribute("data-nodeid"),i=d.getAttribute("data-handleid"),u=d.classList.contains("connectable"),g=d.classList.contains("connectableend"),h={source:s?a:n,sourceHandle:s?i:r,target:s?n:a,targetHandle:s?r:i};c.connection=h,u&&g&&(t===o.Strict?s&&"source"===e||!s&&"target"===e:a!==n||i!==r)&&(c.endHandle={nodeId:a,handleId:i,type:e},c.isValid=l(h))}return c}function e$(e,t){return e?e:t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null}function eO(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function eB({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:a,setState:l,isValidConnection:i,edgeUpdaterType:s,onReconnectEnd:d}){let c,u,g=_(e.target),{connectionMode:h,domNode:p,autoPanOnConnect:m,connectionRadius:f,onConnectStart:y,panBy:x,getNodes:S,cancelConnection:b}=a(),E=0,{x:v,y:w}=W(e),M=e$(s,g?.elementFromPoint(v,w)),C=p?.getBoundingClientRect();if(!C||!M)return;let N=W(e,C),A=!1,k=null,I=!1,P=null,$=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce((e,r)=>{if(r[F]){let{handleBounds:a}=r[F],l=[],i=[];a&&(l=eI(r,a,"source",`${t}-${n}-${o}`),i=eI(r,a,"target",`${t}-${n}-${o}`)),e.push(...l,...i)}return e},[])}({nodes:S(),nodeId:n,handleId:t,handleType:M}),O=()=>{if(!m)return;let[e,t]=R(N,C);x({x:e,y:t}),E=requestAnimationFrame(O)};function B(e){var o,s;let d,{transform:p}=a();N=W(e,C);let{handle:m,validHandleResult:y}=function(e,t,n,o,r,a){let{x:l,y:i}=W(e),s=t.elementsFromPoint(l,i).find(e=>e.classList.contains("react-flow__handle"));if(s){let e=s.getAttribute("data-nodeid");if(e){let t=e$(void 0,s),o=s.getAttribute("data-handleid"),l=a({nodeId:e,id:o,type:t});if(l){let a=r.find(n=>n.nodeId===e&&n.type===t&&n.id===o);return{handle:{id:o,type:t,nodeId:e,x:a?.x||n.x,y:a?.y||n.y},validHandleResult:l}}}}let d=[],c=1/0;if(r.forEach(e=>{let t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){let n=a(e);t<=c&&(t<c?d=[{handle:e,validHandleResult:n}]:t===c&&d.push({handle:e,validHandleResult:n}),c=t)}}),!d.length)return{handle:null,validHandleResult:eR()};if(1===d.length)return d[0];let u=d.some(({validHandleResult:e})=>e.isValid),g=d.some(({handle:e})=>"target"===e.type);return d.find(({handle:e,validHandleResult:t})=>g?"target"===e.type:!u||t.isValid)||d[0]}(e,g,eE(N,p,!1,[1,1]),f,$,e=>e_(e,h,n,t,r?"target":"source",i,g));if(c=m,A||(O(),A=!0),P=y.handleDomNode,k=y.connection,I=y.isValid,l({connectionPosition:c&&I?ev({x:c.x,y:c.y},p):N,connectionStatus:(o=!!c,d=null,(s=I)?d="valid":o&&!s&&(d="invalid"),d),connectionEndHandle:y.endHandle}),!c&&!I&&!P)return eO(u);k.source!==k.target&&P&&(eO(u),u=P,P.classList.add("connecting","react-flow__handle-connecting"),P.classList.toggle("valid",I),P.classList.toggle("react-flow__handle-valid",I))}function D(e){(c||P)&&k&&I&&o?.(k),a().onConnectEnd?.(e),s&&d?.(e),eO(u),b(),cancelAnimationFrame(E),A=!1,I=!1,k=null,P=null,g.removeEventListener("mousemove",B),g.removeEventListener("mouseup",D),g.removeEventListener("touchmove",B),g.removeEventListener("touchend",D)}l({connectionPosition:N,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:M,connectionStartHandle:{nodeId:n,handleId:t,type:M},connectionEndHandle:null}),y?.(e,{nodeId:n,handleId:t,handleType:M}),g.addEventListener("mousemove",B),g.addEventListener("mouseup",D),g.addEventListener("touchmove",B),g.addEventListener("touchend",D)}let eD=()=>!0,ez=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),eL=(e,t,n)=>o=>{let{connectionStartHandle:r,connectionEndHandle:a,connectionClickStartHandle:l}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||a?.nodeId===e&&a?.handleId===t&&a?.type===n,clickConnecting:l?.nodeId===e&&l?.handleId===t&&l?.type===n}},eT=(0,d.forwardRef)(({type:e="source",position:t=s.Top,isValidConnection:n,isConnectable:o=!0,isConnectableStart:r=!0,isConnectableEnd:a=!0,id:l,onConnect:i,children:u,className:h,onMouseDown:p,onTouchStart:m,...f},y)=>{let x=l||null,b="target"===e,w=v(),M=em(),{connectOnClick:C,noPanClassName:N}=E(ez,g.x),{connecting:A,clickConnecting:k}=E(eL(M,x,e),g.x);M||w.getState().onError?.("010",S.error010());let I=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=w.getState(),r={...t,...e};if(o){let{edges:e,setEdges:t}=w.getState();t(eb(r,e))}n?.(r),i?.(r)},P=e=>{if(!M)return;let t=X(e);r&&(t&&0===e.button||!t)&&eB({event:e,handleId:x,nodeId:M,onConnect:I,isTarget:b,getState:w.getState,setState:w.setState,isValidConnection:n||w.getState().isValidConnection||eD}),t?p?.(e):m?.(e)};return d.createElement("div",{"data-handleid":x,"data-nodeid":M,"data-handlepos":t,"data-id":`${M}-${x}-${e}`,className:(0,c.A)(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",N,h,{source:!b,target:b,connectable:o,connectablestart:r,connectableend:a,connecting:k,connectionindicator:o&&(r&&!A||a&&A)}]),onMouseDown:P,onTouchStart:P,onClick:C?t=>{let{onClickConnectStart:o,onClickConnectEnd:a,connectionClickStartHandle:l,connectionMode:i,isValidConnection:s}=w.getState();if(!M||!l&&!r)return;if(!l){o?.(t,{nodeId:M,handleId:x,handleType:e}),w.setState({connectionClickStartHandle:{nodeId:M,type:e,handleId:x}});return}let d=_(t.target),c=n||s||eD,{connection:u,isValid:g}=e_({nodeId:M,id:x,type:e},i,l.nodeId,l.handleId||null,l.type,c,d);g&&I(u),a?.(t),w.setState({connectionClickStartHandle:null})}:void 0,ref:y,...f},u)});eT.displayName="Handle";var eH=(0,d.memo)(eT);let eF=({data:e,isConnectable:t,targetPosition:n=s.Top,sourcePosition:o=s.Bottom})=>d.createElement(d.Fragment,null,d.createElement(eH,{type:"target",position:n,isConnectable:t}),e?.label,d.createElement(eH,{type:"source",position:o,isConnectable:t}));eF.displayName="DefaultNode";var eV=(0,d.memo)(eF);let eK=({data:e,isConnectable:t,sourcePosition:n=s.Bottom})=>d.createElement(d.Fragment,null,e?.label,d.createElement(eH,{type:"source",position:n,isConnectable:t}));eK.displayName="InputNode";var eY=(0,d.memo)(eK);let eZ=({data:e,isConnectable:t,targetPosition:n=s.Top})=>d.createElement(d.Fragment,null,d.createElement(eH,{type:"target",position:n,isConnectable:t}),e?.label);eZ.displayName="OutputNode";var eX=(0,d.memo)(eZ);let eW=()=>null;eW.displayName="GroupNode";let eG=e=>({selectedNodes:e.getNodes().filter(e=>e.selected),selectedEdges:e.edges.filter(e=>e.selected).map(e=>({...e}))}),ej=e=>e.id;function eU(e,t){return(0,g.x)(e.selectedNodes.map(ej),t.selectedNodes.map(ej))&&(0,g.x)(e.selectedEdges.map(ej),t.selectedEdges.map(ej))}let eq=(0,d.memo)(({onSelectionChange:e})=>{let t=v(),{selectedNodes:n,selectedEdges:o}=E(eG,eU);return(0,d.useEffect)(()=>{let r={nodes:n,edges:o};e?.(r),t.getState().onSelectionChange.forEach(e=>e(r))},[n,o,e]),null});eq.displayName="SelectionListener";let eQ=e=>!!e.onSelectionChange;function eJ({onSelectionChange:e}){let t=E(eQ);return e||t?d.createElement(eq,{onSelectionChange:e}):null}let e0=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function e1(e,t){(0,d.useEffect)(()=>{void 0!==e&&t(e)},[e])}function e2(e,t,n){(0,d.useEffect)(()=>{void 0!==t&&n({[e]:t})},[t])}let e5=({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,onConnect:r,onConnectStart:a,onConnectEnd:l,onClickConnectStart:i,onClickConnectEnd:s,nodesDraggable:c,nodesConnectable:u,nodesFocusable:h,edgesFocusable:p,edgesUpdatable:m,elevateNodesOnSelect:f,minZoom:y,maxZoom:x,nodeExtent:S,onNodesChange:b,onEdgesChange:w,elementsSelectable:M,connectionMode:C,snapGrid:N,snapToGrid:A,translateExtent:k,connectOnClick:I,defaultEdgeOptions:P,fitView:R,fitViewOptions:_,onNodesDelete:$,onEdgesDelete:O,onNodeDrag:B,onNodeDragStart:D,onNodeDragStop:z,onSelectionDrag:L,onSelectionDragStart:T,onSelectionDragStop:H,noPanClassName:F,nodeOrigin:V,rfId:K,autoPanOnConnect:Y,autoPanOnNodeDrag:Z,onError:X,connectionRadius:W,isValidConnection:G,nodeDragThreshold:j})=>{let{setNodes:U,setEdges:q,setDefaultNodesAndEdges:Q,setMinZoom:J,setMaxZoom:ee,setTranslateExtent:et,setNodeExtent:en,reset:eo}=E(e0,g.x),er=v();return(0,d.useEffect)(()=>(Q(n,o?.map(e=>({...e,...P}))),()=>{eo()}),[]),e2("defaultEdgeOptions",P,er.setState),e2("connectionMode",C,er.setState),e2("onConnect",r,er.setState),e2("onConnectStart",a,er.setState),e2("onConnectEnd",l,er.setState),e2("onClickConnectStart",i,er.setState),e2("onClickConnectEnd",s,er.setState),e2("nodesDraggable",c,er.setState),e2("nodesConnectable",u,er.setState),e2("nodesFocusable",h,er.setState),e2("edgesFocusable",p,er.setState),e2("edgesUpdatable",m,er.setState),e2("elementsSelectable",M,er.setState),e2("elevateNodesOnSelect",f,er.setState),e2("snapToGrid",A,er.setState),e2("snapGrid",N,er.setState),e2("onNodesChange",b,er.setState),e2("onEdgesChange",w,er.setState),e2("connectOnClick",I,er.setState),e2("fitViewOnInit",R,er.setState),e2("fitViewOnInitOptions",_,er.setState),e2("onNodesDelete",$,er.setState),e2("onEdgesDelete",O,er.setState),e2("onNodeDrag",B,er.setState),e2("onNodeDragStart",D,er.setState),e2("onNodeDragStop",z,er.setState),e2("onSelectionDrag",L,er.setState),e2("onSelectionDragStart",T,er.setState),e2("onSelectionDragStop",H,er.setState),e2("noPanClassName",F,er.setState),e2("nodeOrigin",V,er.setState),e2("rfId",K,er.setState),e2("autoPanOnConnect",Y,er.setState),e2("autoPanOnNodeDrag",Z,er.setState),e2("onError",X,er.setState),e2("connectionRadius",W,er.setState),e2("isValidConnection",G,er.setState),e2("nodeDragThreshold",j,er.setState),e1(e,U),e1(t,q),e1(y,J),e1(x,ee),e1(k,et),e1(S,en),null},e3={display:"none"},e4={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},e7="react-flow__node-desc",e6="react-flow__edge-desc",e9=e=>e.ariaLiveMessage;function e8({rfId:e}){let t=E(e9);return d.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:e4},t)}function te({rfId:e,disableKeyboardA11y:t}){return d.createElement(d.Fragment,null,d.createElement("div",{id:`${e7}-${e}`,style:e3},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),d.createElement("div",{id:`${e6}-${e}`,style:e3},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&d.createElement(e8,{rfId:e}))}var tt=(e=null,t={actInsideInputWithModifier:!0})=>{let[n,o]=(0,d.useState)(!1),r=(0,d.useRef)(!1),a=(0,d.useRef)(new Set([])),[l,i]=(0,d.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.split("+")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,d.useEffect)(()=>{let n=t?.target||("undefined"!=typeof document?document:null);if(null!==e){let e=e=>{if(r.current=e.ctrlKey||e.metaKey||e.shiftKey,(!r.current||r.current&&!t.actInsideInputWithModifier)&&Z(e))return!1;let n=to(e.code,i);a.current.add(e[n]),tn(l,a.current,!1)&&(e.preventDefault(),o(!0))},s=e=>{if((!r.current||r.current&&!t.actInsideInputWithModifier)&&Z(e))return!1;let n=to(e.code,i);tn(l,a.current,!0)?(o(!1),a.current.clear()):a.current.delete(e[n]),"Meta"===e.key&&a.current.clear(),r.current=!1},d=()=>{a.current.clear(),o(!1)};return n?.addEventListener("keydown",e),n?.addEventListener("keyup",s),window.addEventListener("blur",d),()=>{n?.removeEventListener("keydown",e),n?.removeEventListener("keyup",s),window.removeEventListener("blur",d)}}},[e,o]),n};function tn(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function to(e,t){return t.includes(e)?"code":"key"}function tr(e,t,n){e.forEach(o=>{let r=o.parentNode||o.parentId;if(r&&!e.has(r))throw Error(`Parent node ${r} not found`);if(r||n?.[o.id]){let{x:r,y:a,z:l}=function e(t,n,o,r){let a=t.parentNode||t.parentId;if(!a)return o;let l=n.get(a),i=ew(l,r);return e(l,n,{x:(o.x??0)+i.x,y:(o.y??0)+i.y,z:(l[F]?.z??0)>(o.z??0)?l[F]?.z??0:o.z??0},r)}(o,e,{...o.position,z:o[F]?.z??0},t);o.positionAbsolute={x:r,y:a},o[F].z=l,n?.[o.id]&&(o[F].isParent=!0)}})}function ta(e,t,n,o){let r=new Map,a={},l=1e3*!!o;return e.forEach(e=>{let n=(H(e.zIndex)?e.zIndex:0)+(e.selected?l:0),o=t.get(e.id),i={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},s=e.parentNode||e.parentId;s&&(a[s]=!0),Object.defineProperty(i,F,{enumerable:!1,value:{handleBounds:o?.type&&o?.type!==e.type?void 0:o?.[F]?.handleBounds,z:n}}),r.set(e.id,i)}),tr(r,n,a),r}function tl(e,t={}){let{getNodes:n,width:o,height:r,minZoom:a,maxZoom:l,d3Zoom:i,d3Selection:s,fitViewOnInitDone:d,fitViewOnInit:c,nodeOrigin:u}=e(),g=t.initial&&!d&&c;if(i&&s&&(g||!t.initial)){let e=n().filter(e=>{let n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some(t=>t.id===e.id):n}),d=e.every(e=>e.width&&e.height);if(e.length>0&&d){let{x:n,y:d,zoom:c}=eA(eM(e,u),o,r,t.minZoom??a,t.maxZoom??l,t.padding??.1),g=h.GS.translate(n,d).scale(c);return"number"==typeof t.duration&&t.duration>0?i.transform(ek(s,t.duration),g):i.transform(s,g),!0}}return!1}function ti({changedNodes:e,changedEdges:t,get:n,set:o}){let{nodeInternals:r,edges:a,onNodesChange:l,onEdgesChange:i,hasDefaultNodes:s,hasDefaultEdges:d}=n();e?.length&&(s&&o({nodeInternals:(e.forEach(e=>{let t=r.get(e.id);t&&r.set(t.id,{...t,[F]:t[F],selected:e.selected})}),new Map(r))}),l?.(e)),t?.length&&(d&&o({edges:a.map(e=>{let n=t.find(t=>t.id===e.id);return n&&(e.selected=n.selected),e})}),i?.(t))}let ts=()=>{},td={zoomIn:ts,zoomOut:ts,zoomTo:ts,getZoom:()=>1,setViewport:ts,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:ts,fitBounds:ts,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},tc=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),tu=()=>{let e=v(),{d3Zoom:t,d3Selection:n}=E(tc,g.x);return(0,d.useMemo)(()=>n&&t?{zoomIn:e=>t.scaleBy(ek(n,e?.duration),1.2),zoomOut:e=>t.scaleBy(ek(n,e?.duration),1/1.2),zoomTo:(e,o)=>t.scaleTo(ek(n,o?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(o,r)=>{let[a,l,i]=e.getState().transform,s=h.GS.translate(o.x??a,o.y??l).scale(o.zoom??i);t.transform(ek(n,r?.duration),s)},getViewport:()=>{let[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>tl(e.getState,t),setCenter:(o,r,a)=>{let{width:l,height:i,maxZoom:s}=e.getState(),d=void 0!==a?.zoom?a.zoom:s,c=l/2-o*d,u=i/2-r*d,g=h.GS.translate(c,u).scale(d);t.transform(ek(n,a?.duration),g)},fitBounds:(o,r)=>{let{width:a,height:l,minZoom:i,maxZoom:s}=e.getState(),{x:d,y:c,zoom:u}=eA(o,a,l,i,s,r?.padding??.1),g=h.GS.translate(d,c).scale(u);t.transform(ek(n,r?.duration),g)},project:t=>{let{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),eE(t,n,o,r)},screenToFlowPosition:t=>{let{transform:n,snapToGrid:o,snapGrid:r,domNode:a}=e.getState();if(!a)return t;let{x:l,y:i}=a.getBoundingClientRect();return eE({x:t.x-l,y:t.y-i},n,o,r)},flowToScreenPosition:t=>{let{transform:n,domNode:o}=e.getState();if(!o)return t;let{x:r,y:a}=o.getBoundingClientRect(),l=ev(t,n);return{x:l.x+r,y:l.y+a}},viewportInitialized:!0}:td,[t,n])};function tg(){let e=tu(),t=v(),n=(0,d.useCallback)(()=>t.getState().getNodes().map(e=>({...e})),[]),o=(0,d.useCallback)(e=>t.getState().nodeInternals.get(e),[]),r=(0,d.useCallback)(()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},[]),a=(0,d.useCallback)(e=>{let{edges:n=[]}=t.getState();return n.find(t=>t.id===e)},[]),l=(0,d.useCallback)(e=>{let{getNodes:n,setNodes:o,hasDefaultNodes:r,onNodesChange:a}=t.getState(),l=n(),i="function"==typeof e?e(l):e;r?o(i):a&&a(0===i.length?l.map(e=>({type:"remove",id:e.id})):i.map(e=>({item:e,type:"reset"})))},[]),i=(0,d.useCallback)(e=>{let{edges:n=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:a}=t.getState(),l="function"==typeof e?e(n):e;r?o(l):a&&a(0===l.length?n.map(e=>({type:"remove",id:e.id})):l.map(e=>({item:e,type:"reset"})))},[]),s=(0,d.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:a,onNodesChange:l}=t.getState();a?r([...o(),...n]):l&&l(n.map(e=>({item:e,type:"add"})))},[]),c=(0,d.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:a,onEdgesChange:l}=t.getState();a?r([...o,...n]):l&&l(n.map(e=>({item:e,type:"add"})))},[]),u=(0,d.useCallback)(()=>{let{getNodes:e,edges:n=[],transform:o}=t.getState(),[r,a,l]=o;return{nodes:e().map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:r,y:a,zoom:l}}},[]),g=(0,d.useCallback)(({nodes:e,edges:n})=>{let{nodeInternals:o,getNodes:r,edges:a,hasDefaultNodes:l,hasDefaultEdges:i,onNodesDelete:s,onEdgesDelete:d,onNodesChange:c,onEdgesChange:u}=t.getState(),g=(e||[]).map(e=>e.id),h=(n||[]).map(e=>e.id),p=r().reduce((e,t)=>{let n=t.parentNode||t.parentId,o=!g.includes(t.id)&&n&&e.find(e=>e.id===n);return("boolean"!=typeof t.deletable||t.deletable)&&(g.includes(t.id)||o)&&e.push(t),e},[]),m=a.filter(e=>"boolean"!=typeof e.deletable||e.deletable),f=m.filter(e=>h.includes(e.id));if(p||f){let e=[...f,...eN(p,m)],n=e.reduce((e,t)=>(e.includes(t.id)||e.push(t.id),e),[]);(i||l)&&(i&&t.setState({edges:a.filter(e=>!n.includes(e.id))}),l&&(p.forEach(e=>{o.delete(e.id)}),t.setState({nodeInternals:new Map(o)}))),n.length>0&&(d?.(e),u&&u(n.map(e=>({id:e,type:"remove"})))),p.length>0&&(s?.(p),c&&c(p.map(e=>({id:e.id,type:"remove"}))))}},[]),h=(0,d.useCallback)(e=>{let n=T(e),o=n?null:t.getState().nodeInternals.get(e.id);return n||o?[n?e:D(o),o,n]:[null,null,n]},[]),p=(0,d.useCallback)((e,n=!0,o)=>{let[r,a,l]=h(e);return r?(o||t.getState().getNodes()).filter(e=>{if(!l&&(e.id===a.id||!e.positionAbsolute))return!1;let t=L(D(e),r);return n&&t>0||t>=r.width*r.height}):[]},[]),m=(0,d.useCallback)((e,t,n=!0)=>{let[o]=h(e);if(!o)return!1;let r=L(o,t);return n&&r>0||r>=o.width*o.height},[]);return(0,d.useMemo)(()=>({...e,getNodes:n,getNode:o,getEdges:r,getEdge:a,setNodes:l,setEdges:i,addNodes:s,addEdges:c,toObject:u,deleteElements:g,getIntersectingNodes:p,isNodeIntersecting:m}),[e,n,o,r,a,l,i,s,c,u,g,p,m])}let th={actInsideInputWithModifier:!1};var tp=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{let n=v(),{deleteElements:o}=tg(),r=tt(e,th),a=tt(t);(0,d.useEffect)(()=>{if(r){let{edges:e,getNodes:t}=n.getState();o({nodes:t().filter(e=>e.selected),edges:e.filter(e=>e.selected)}),n.setState({nodesSelectionActive:!1})}},[r]),(0,d.useEffect)(()=>{n.setState({multiSelectionActive:a})},[a])};let tm={position:"absolute",width:"100%",height:"100%",top:0,left:0},tf=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,ty=e=>({x:e.x,y:e.y,zoom:e.k}),tx=(e,t)=>e.target.closest(`.${t}`),tS=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),tb=e=>{let t=e.ctrlKey&&G()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},tE=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),tv=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:o,zoomOnScroll:a=!0,zoomOnPinch:l=!0,panOnScroll:i=!1,panOnScrollSpeed:s=.5,panOnScrollMode:c=r.Free,zoomOnDoubleClick:u=!0,elementsSelectable:f,panOnDrag:y=!0,defaultViewport:x,translateExtent:b,minZoom:w,maxZoom:M,zoomActivationKeyCode:C,preventScrolling:N=!0,children:I,noWheelClassName:P,noPanClassName:R})=>{let _=(0,d.useRef)(),$=v(),O=(0,d.useRef)(!1),B=(0,d.useRef)(!1),D=(0,d.useRef)(null),z=(0,d.useRef)({x:0,y:0,zoom:0}),{d3Zoom:L,d3Selection:T,d3ZoomHandler:H,userSelectionActive:F}=E(tE,g.x),V=tt(C),K=(0,d.useRef)(0),Y=(0,d.useRef)(!1),Z=(0,d.useRef)();return!function(e){let t=v();(0,d.useEffect)(()=>{let n,o=()=>{if(!e.current)return;let n=A(e.current);(0===n.height||0===n.width)&&t.getState().onError?.("004",S.error004()),t.setState({width:n.width||500,height:n.height||500})};return o(),window.addEventListener("resize",o),e.current&&(n=new ResizeObserver(()=>o())).observe(e.current),()=>{window.removeEventListener("resize",o),n&&e.current&&n.unobserve(e.current)}},[])}(D),(0,d.useEffect)(()=>{if(D.current){let e=D.current.getBoundingClientRect(),t=(0,h.s_)().scaleExtent([w,M]).translateExtent(b),n=(0,p.A)(D.current).call(t),o=h.GS.translate(x.x,x.y).scale(k(x.zoom,w,M)),r=[[0,0],[e.width,e.height]],a=t.constrain()(o,r,b);t.transform(n,a),t.wheelDelta(tb),$.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[a.x,a.y,a.k],domNode:D.current.closest(".react-flow")})}},[]),(0,d.useEffect)(()=>{T&&L&&(!i||V||F?void 0!==H&&T.on("wheel.zoom",function(e,t){if(!N&&"wheel"===e.type&&!e.ctrlKey||tx(e,P))return null;e.preventDefault(),H.call(this,e,t)},{passive:!1}):T.on("wheel.zoom",o=>{if(tx(o,P))return!1;o.preventDefault(),o.stopImmediatePropagation();let a=T.property("__zoom").k||1;if(o.ctrlKey&&l){let e=(0,m.A)(o),t=a*Math.pow(2,tb(o));L.scaleTo(T,t,e,o);return}let i=1===o.deltaMode?20:1,d=c===r.Vertical?0:o.deltaX*i,u=c===r.Horizontal?0:o.deltaY*i;!G()&&o.shiftKey&&c!==r.Vertical&&(d=o.deltaY*i,u=0),L.translateBy(T,-(d/a)*s,-(u/a)*s,{internal:!0});let g=ty(T.property("__zoom")),{onViewportChangeStart:h,onViewportChange:p,onViewportChangeEnd:f}=$.getState();clearTimeout(Z.current),Y.current||(Y.current=!0,t?.(o,g),h?.(g)),Y.current&&(e?.(o,g),p?.(g),Z.current=setTimeout(()=>{n?.(o,g),f?.(g),Y.current=!1},150))},{passive:!1}))},[F,i,c,T,L,H,V,l,N,P,t,e,n]),(0,d.useEffect)(()=>{L&&L.on("start",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;K.current=e.sourceEvent?.button;let{onViewportChangeStart:n}=$.getState(),o=ty(e.transform);O.current=!0,z.current=o,e.sourceEvent?.type==="mousedown"&&$.setState({paneDragging:!0}),n?.(o),t?.(e.sourceEvent,o)})},[L,t]),(0,d.useEffect)(()=>{L&&(F&&!O.current?L.on("zoom",null):F||L.on("zoom",t=>{let{onViewportChange:n}=$.getState();if($.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),B.current=!!(o&&tS(y,K.current??0)),(e||n)&&!t.sourceEvent?.internal){let o=ty(t.transform);n?.(o),e?.(t.sourceEvent,o)}}))},[F,L,e,y,o]),(0,d.useEffect)(()=>{L&&L.on("end",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;let{onViewportChangeEnd:t}=$.getState();if(O.current=!1,$.setState({paneDragging:!1}),o&&tS(y,K.current??0)&&!B.current&&o(e.sourceEvent),B.current=!1,(n||t)&&tf(z.current,e.transform)){let o=ty(e.transform);z.current=o,clearTimeout(_.current),_.current=setTimeout(()=>{t?.(o),n?.(e.sourceEvent,o)},150*!!i)}})},[L,i,y,n,o]),(0,d.useEffect)(()=>{L&&L.filter(e=>{let t=V||a,n=l&&e.ctrlKey;if((!0===y||Array.isArray(y)&&y.includes(1))&&1===e.button&&"mousedown"===e.type&&(tx(e,"react-flow__node")||tx(e,"react-flow__edge")))return!0;if(!y&&!t&&!i&&!u&&!l||F||!u&&"dblclick"===e.type||tx(e,P)&&"wheel"===e.type||tx(e,R)&&("wheel"!==e.type||i&&"wheel"===e.type&&!V)||!l&&e.ctrlKey&&"wheel"===e.type||!t&&!i&&!n&&"wheel"===e.type||!y&&("mousedown"===e.type||"touchstart"===e.type)||Array.isArray(y)&&!y.includes(e.button)&&"mousedown"===e.type)return!1;let o=Array.isArray(y)&&y.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o})},[F,L,a,l,i,u,y,f,V]),d.createElement("div",{className:"react-flow__renderer",ref:D,style:tm},I)},tw=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function tM(){let{userSelectionActive:e,userSelectionRect:t}=E(tw,g.x);return e&&t?d.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function tC(e,t){let n=t.parentNode||t.parentId,o=e.find(e=>e.id===n);if(o){let e=t.position.x+t.width-o.width,n=t.position.y+t.height-o.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(o.style={...o.style},o.style.width=o.style.width??o.width,o.style.height=o.style.height??o.height,e>0&&(o.style.width+=e),n>0&&(o.style.height+=n),t.position.x<0){let e=Math.abs(t.position.x);o.position.x=o.position.x-e,o.style.width+=e,t.position.x=0}if(t.position.y<0){let e=Math.abs(t.position.y);o.position.y=o.position.y-e,o.style.height+=e,t.position.y=0}o.width=o.style.width,o.height=o.style.height}}}function tN(e,t){if(e.some(e=>"reset"===e.type))return e.filter(e=>"reset"===e.type).map(e=>e.item);let n=e.filter(e=>"add"===e.type).map(e=>e.item);return t.reduce((t,n)=>{let o=e.filter(e=>e.id===n.id);if(0===o.length)return t.push(n),t;let r={...n};for(let e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&tC(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&tC(t,r);break;case"remove":return t}return t.push(r),t},n)}function tA(e,t){return tN(e,t)}let tk=(e,t)=>({id:e,type:"select",selected:t});function tI(e,t){return e.reduce((e,n)=>{let o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(tk(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(tk(n.id,!1))),e},[])}let tP=(e,t)=>n=>{n.target===t.current&&e?.(n)},tR=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),t_=(0,d.memo)(({isSelecting:e,selectionMode:t=a.Full,panOnDrag:n,onSelectionStart:o,onSelectionEnd:r,onPaneClick:l,onPaneContextMenu:i,onPaneScroll:s,onPaneMouseEnter:u,onPaneMouseMove:h,onPaneMouseLeave:p,children:m})=>{let f=(0,d.useRef)(null),y=v(),x=(0,d.useRef)(0),S=(0,d.useRef)(0),b=(0,d.useRef)(),{userSelectionActive:w,elementsSelectable:M,dragging:C}=E(tR,g.x),N=()=>{y.setState({userSelectionActive:!1,userSelectionRect:null}),x.current=0,S.current=0},A=e=>{l?.(e),y.getState().resetSelectedElements(),y.setState({nodesSelectionActive:!1})},k=M&&(e||w);return d.createElement("div",{className:(0,c.A)(["react-flow__pane",{dragging:C,selection:e}]),onClick:k?void 0:tP(A,f),onContextMenu:tP(e=>{if(Array.isArray(n)&&n?.includes(2))return void e.preventDefault();i?.(e)},f),onWheel:tP(s?e=>s(e):void 0,f),onMouseEnter:k?void 0:u,onMouseDown:k?t=>{let{resetSelectedElements:n,domNode:r}=y.getState();if(b.current=r?.getBoundingClientRect(),!M||!e||0!==t.button||t.target!==f.current||!b.current)return;let{x:a,y:l}=W(t,b.current);n(),y.setState({userSelectionRect:{width:0,height:0,startX:a,startY:l,x:a,y:l}}),o?.(t)}:void 0,onMouseMove:k?n=>{let{userSelectionRect:o,nodeInternals:r,edges:l,transform:i,onNodesChange:s,onEdgesChange:d,nodeOrigin:c,getNodes:u}=y.getState();if(!e||!b.current||!o)return;y.setState({userSelectionActive:!0,nodesSelectionActive:!1});let g=W(n,b.current),h=o.startX??0,p=o.startY??0,m={...o,x:g.x<h?g.x:h,y:g.y<p?g.y:p,width:Math.abs(g.x-h),height:Math.abs(g.y-p)},f=u(),E=eC(r,m,i,t===a.Partial,!0,c),v=eN(E,l).map(e=>e.id),w=E.map(e=>e.id);if(x.current!==w.length){x.current=w.length;let e=tI(f,w);e.length&&s?.(e)}if(S.current!==v.length){S.current=v.length;let e=tI(l,v);e.length&&d?.(e)}y.setState({userSelectionRect:m})}:h,onMouseUp:k?e=>{if(0!==e.button)return;let{userSelectionRect:t}=y.getState();!w&&t&&e.target===f.current&&A?.(e),y.setState({nodesSelectionActive:x.current>0}),N(),r?.(e)}:void 0,onMouseLeave:k?e=>{w&&(y.setState({nodesSelectionActive:x.current>0}),r?.(e)),N()}:p,ref:f,style:tm},m,d.createElement(tM,null))});function t$(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)break;o=o.parentElement}while(o);return!1}function tO(e,t,n,o,r=[0,0],a){var l;let i=(l=e.extent||o)&&"parent"!==l?[l[0],[l[1][0]-(e.width||0),l[1][1]-(e.height||0)]]:l,s=i,d=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&d&&"parent"!==e.extent){let{x:t,y:o}=ew(n.get(d),r).positionAbsolute;s=[[e.extent[0][0]+t,e.extent[0][1]+o],[e.extent[1][0]+t,e.extent[1][1]+o]]}}else if(d&&e.width&&e.height){let t=n.get(d),{x:o,y:a}=ew(t,r).positionAbsolute;s=t&&H(o)&&H(a)&&H(t.width)&&H(t.height)?[[o+e.width*r[0],a+e.height*r[1]],[o+t.width-e.width+e.width*r[0],a+t.height-e.height+e.height*r[1]]]:s}else a?.("005",S.error005()),s=i;let c={x:0,y:0};d&&(c=ew(n.get(d),r).positionAbsolute);let u=s&&"parent"!==s?I(t,s):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function tB({nodeId:e,dragItems:t,nodeInternals:n}){let o=t.map(e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute}));return[e?o.find(t=>t.id===e):o[0],o]}t_.displayName="Pane";let tD=(e,t,n,o)=>{let r=t.querySelectorAll(e);if(!r||!r.length)return null;let a=Array.from(r),l=t.getBoundingClientRect(),i={x:l.width*o[0],y:l.height*o[1]};return a.map(e=>{let t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-l.left-i.x)/n,y:(t.top-l.top-i.y)/n,...A(e)}})};function tz(e,t,n){return void 0===n?n:o=>{let r=t().nodeInternals.get(e);r&&n(o,{...r})}}function tL({id:e,store:t,unselect:n=!1,nodeRef:o}){let{addSelectedNodes:r,unselectNodesAndEdges:a,multiSelectionActive:l,nodeInternals:i,onError:s}=t.getState(),d=i.get(e);if(!d)return void s?.("012",S.error012(e));t.setState({nodesSelectionActive:!1}),d.selected?(n||d.selected&&l)&&(a({nodes:[d],edges:[]}),requestAnimationFrame(()=>o?.current?.blur())):r([e])}function tT(e){return(t,n,o)=>e?.(t,o)}function tH({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:o,nodeId:r,isSelectable:a,selectNodesOnDrag:l}){let i=v(),[s,c]=(0,d.useState)(!1),u=(0,d.useRef)([]),g=(0,d.useRef)({x:null,y:null}),h=(0,d.useRef)(0),m=(0,d.useRef)(null),y=(0,d.useRef)({x:0,y:0}),x=(0,d.useRef)(null),S=(0,d.useRef)(!1),b=(0,d.useRef)(!1),E=(0,d.useRef)(!1),w=function(){let e=v();return(0,d.useCallback)(({sourceEvent:t})=>{let{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),a=t.touches?t.touches[0].clientX:t.clientX,l=t.touches?t.touches[0].clientY:t.clientY,i={x:(a-n[0])/n[2],y:(l-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(i.x/o[0]):i.x,ySnapped:r?o[1]*Math.round(i.y/o[1]):i.y,...i}},[])}();return(0,d.useEffect)(()=>{if(e?.current){let s=(0,p.A)(e.current),d=({x:e,y:t})=>{let{nodeInternals:n,onNodeDrag:o,onSelectionDrag:a,updateNodePositions:l,nodeExtent:s,snapGrid:d,snapToGrid:h,nodeOrigin:p,onError:m}=i.getState();g.current={x:e,y:t};let f=!1,y={x:0,y:0,x2:0,y2:0};if(u.current.length>1&&s&&(y=O(eM(u.current,p))),u.current=u.current.map(o=>{let r={x:e-o.distance.x,y:t-o.distance.y};h&&(r.x=d[0]*Math.round(r.x/d[0]),r.y=d[1]*Math.round(r.y/d[1]));let a=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];u.current.length>1&&s&&!o.extent&&(a[0][0]=o.positionAbsolute.x-y.x+s[0][0],a[1][0]=o.positionAbsolute.x+(o.width??0)-y.x2+s[1][0],a[0][1]=o.positionAbsolute.y-y.y+s[0][1],a[1][1]=o.positionAbsolute.y+(o.height??0)-y.y2+s[1][1]);let l=tO(o,r,n,a,p,m);return f=f||o.position.x!==l.position.x||o.position.y!==l.position.y,o.position=l.position,o.positionAbsolute=l.positionAbsolute,o}),!f)return;l(u.current,!0,!0),c(!0);let S=r?o:tT(a);if(S&&x.current){let[e,t]=tB({nodeId:r,dragItems:u.current,nodeInternals:n});S(x.current,e,t)}},v=()=>{if(!m.current)return;let[e,t]=R(y.current,m.current);if(0!==e||0!==t){let{transform:n,panBy:o}=i.getState();g.current.x=(g.current.x??0)-e/n[2],g.current.y=(g.current.y??0)-t/n[2],o({x:e,y:t})&&d(g.current)}h.current=requestAnimationFrame(v)},M=t=>{let{nodeInternals:n,multiSelectionActive:o,nodesDraggable:s,unselectNodesAndEdges:d,onNodeDragStart:c,onSelectionDragStart:h}=i.getState();b.current=!0;let p=r?c:tT(h);l&&a||o||!r||n.get(r)?.selected||d(),r&&a&&l&&tL({id:r,store:i,nodeRef:e});let m=w(t);if(g.current=m,u.current=Array.from(n.values()).filter(e=>(e.selected||e.id===r)&&(!e.parentNode||e.parentId||!function e(t,n){let o=t.parentNode||t.parentId;if(!o)return!1;let r=n.get(o);return!!r&&(!!r.selected||e(r,n))}(e,n))&&(e.draggable||s&&void 0===e.draggable)).map(e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:m.x-(e.positionAbsolute?.x??0),y:m.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})),p&&u.current){let[e,o]=tB({nodeId:r,dragItems:u.current,nodeInternals:n});p(t.sourceEvent,e,o)}};if(t)s.on(".drag",null);else{let t=(0,f.A)().on("start",e=>{let{domNode:t,nodeDragThreshold:n}=i.getState();0===n&&M(e),E.current=!1,g.current=w(e),m.current=t?.getBoundingClientRect()||null,y.current=W(e.sourceEvent,m.current)}).on("drag",e=>{let t=w(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=i.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(E.current=!0),!E.current){if(!S.current&&b.current&&n&&(S.current=!0,v()),!b.current){let n=t.xSnapped-(g?.current?.x??0),r=t.ySnapped-(g?.current?.y??0);Math.sqrt(n*n+r*r)>o&&M(e)}(g.current.x!==t.xSnapped||g.current.y!==t.ySnapped)&&u.current&&b.current&&(x.current=e.sourceEvent,y.current=W(e.sourceEvent,m.current),d(t))}}).on("end",e=>{if(b.current&&!E.current&&(c(!1),S.current=!1,b.current=!1,cancelAnimationFrame(h.current),u.current)){let{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:a}=i.getState(),l=r?o:tT(a);if(t(u.current,!1,!1),l){let[t,o]=tB({nodeId:r,dragItems:u.current,nodeInternals:n});l(e.sourceEvent,t,o)}}}).filter(t=>{let r=t.target;return!t.button&&(!n||!t$(r,`.${n}`,e))&&(!o||t$(r,o,e))});return s.call(t),()=>{s.on(".drag",null)}}}},[e,t,n,o,a,i,r,l,w]),s}function tF(){let e=v();return(0,d.useCallback)(t=>{let{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:a,snapToGrid:l,snapGrid:i,onError:s,nodesDraggable:d}=e.getState(),c=a().filter(e=>e.selected&&(e.draggable||d&&void 0===e.draggable)),u=l?i[0]:5,g=l?i[1]:5,h=t.isShiftPressed?4:1,p=t.x*u*h,m=t.y*g*h;r(c.map(e=>{if(e.positionAbsolute){let t={x:e.positionAbsolute.x+p,y:e.positionAbsolute.y+m};l&&(t.x=i[0]*Math.round(t.x/i[0]),t.y=i[1]*Math.round(t.y/i[1]));let{positionAbsolute:r,position:a}=tO(e,t,n,o,void 0,s);e.position=a,e.positionAbsolute=r}return e}),!0,!1)},[])}let tV={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var tK=e=>{let t=({id:t,type:n,data:o,xPos:r,yPos:a,xPosOrigin:l,yPosOrigin:i,selected:s,onClick:u,onMouseEnter:g,onMouseMove:h,onMouseLeave:p,onContextMenu:m,onDoubleClick:f,style:y,className:x,isDraggable:S,isSelectable:b,isConnectable:E,isFocusable:w,selectNodesOnDrag:M,sourcePosition:C,targetPosition:N,hidden:A,resizeObserver:k,dragHandle:I,zIndex:P,isParent:R,noDragClassName:_,noPanClassName:$,initialized:O,disableKeyboardA11y:B,ariaLabel:D,rfId:z,hasHandleBounds:L})=>{let T=v(),H=(0,d.useRef)(null),F=(0,d.useRef)(null),K=(0,d.useRef)(C),Y=(0,d.useRef)(N),X=(0,d.useRef)(n),W=b||S||u||g||h||p,G=tF(),j=tz(t,T.getState,g),U=tz(t,T.getState,h),q=tz(t,T.getState,p),Q=tz(t,T.getState,m),J=tz(t,T.getState,f);(0,d.useEffect)(()=>()=>{F.current&&(k?.unobserve(F.current),F.current=null)},[]),(0,d.useEffect)(()=>{if(H.current&&!A){let e=H.current;O&&L&&F.current===e||(F.current&&k?.unobserve(F.current),k?.observe(e),F.current=e)}},[A,O,L]),(0,d.useEffect)(()=>{let e=X.current!==n,o=K.current!==C,r=Y.current!==N;H.current&&(e||o||r)&&(e&&(X.current=n),o&&(K.current=C),r&&(Y.current=N),T.getState().updateNodeDimensions([{id:t,nodeElement:H.current,forceUpdate:!0}]))},[t,n,C,N]);let ee=tH({nodeRef:H,disabled:A||!S,noDragClassName:_,handleSelector:I,nodeId:t,isSelectable:b,selectNodesOnDrag:M});return A?null:d.createElement("div",{className:(0,c.A)(["react-flow__node",`react-flow__node-${n}`,{[$]:S},x,{selected:s,selectable:b,parent:R,dragging:ee}]),ref:H,style:{zIndex:P,transform:`translate(${l}px,${i}px)`,pointerEvents:W?"all":"none",visibility:O?"visible":"hidden",...y},"data-id":t,"data-testid":`rf__node-${t}`,onMouseEnter:j,onMouseMove:U,onMouseLeave:q,onContextMenu:Q,onClick:e=>{let{nodeDragThreshold:n}=T.getState();if(b&&(!M||!S||n>0)&&tL({id:t,store:T,nodeRef:H}),u){let n=T.getState().nodeInternals.get(t);n&&u(e,{...n})}},onDoubleClick:J,onKeyDown:w?e=>{!Z(e)&&!B&&(V.includes(e.key)&&b?tL({id:t,store:T,unselect:"Escape"===e.key,nodeRef:H}):S&&s&&Object.prototype.hasOwnProperty.call(tV,e.key)&&(T.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~r}, y: ${~~a}`}),G({x:tV[e.key].x,y:tV[e.key].y,isShiftPressed:e.shiftKey})))}:void 0,tabIndex:w?0:void 0,role:w?"button":void 0,"aria-describedby":B?void 0:`${e7}-${z}`,"aria-label":D},d.createElement(ep,{value:t},d.createElement(e,{id:t,data:o,type:n,xPos:r,yPos:a,selected:s,isConnectable:E,sourcePosition:C,targetPosition:N,dragging:ee,dragHandle:I,zIndex:P})))};return t.displayName="NodeWrapper",(0,d.memo)(t)};let tY=e=>({...eM(e.getNodes().filter(e=>e.selected),e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive});var tZ=(0,d.memo)(function({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){let o=v(),{width:r,height:a,x:l,y:i,transformString:s,userSelectionActive:u}=E(tY,g.x),h=tF(),p=(0,d.useRef)(null);if((0,d.useEffect)(()=>{n||p.current?.focus({preventScroll:!0})},[n]),tH({nodeRef:p}),u||!r||!a)return null;let m=e?t=>{e(t,o.getState().getNodes().filter(e=>e.selected))}:void 0;return d.createElement("div",{className:(0,c.A)(["react-flow__nodesselection","react-flow__container",t]),style:{transform:s}},d.createElement("div",{ref:p,className:"react-flow__nodesselection-rect",onContextMenu:m,tabIndex:n?void 0:-1,onKeyDown:n?void 0:e=>{Object.prototype.hasOwnProperty.call(tV,e.key)&&h({x:tV[e.key].x,y:tV[e.key].y,isShiftPressed:e.shiftKey})},style:{width:r,height:a,top:i,left:l}}))});let tX=e=>e.nodesSelectionActive,tW=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:a,onPaneScroll:l,deleteKeyCode:i,onMove:s,onMoveStart:c,onMoveEnd:u,selectionKeyCode:g,selectionOnDrag:h,selectionMode:p,onSelectionStart:m,onSelectionEnd:f,multiSelectionKeyCode:y,panActivationKeyCode:x,zoomActivationKeyCode:S,elementsSelectable:b,zoomOnScroll:v,zoomOnPinch:w,panOnScroll:M,panOnScrollSpeed:C,panOnScrollMode:N,zoomOnDoubleClick:A,panOnDrag:k,defaultViewport:I,translateExtent:P,minZoom:R,maxZoom:_,preventScrolling:$,onSelectionContextMenu:O,noWheelClassName:B,noPanClassName:D,disableKeyboardA11y:z})=>{let L=E(tX),T=tt(g),H=tt(x),F=H||k,V=H||M,K=T||h&&!0!==F;return tp({deleteKeyCode:i,multiSelectionKeyCode:y}),d.createElement(tv,{onMove:s,onMoveStart:c,onMoveEnd:u,onPaneContextMenu:a,elementsSelectable:b,zoomOnScroll:v,zoomOnPinch:w,panOnScroll:V,panOnScrollSpeed:C,panOnScrollMode:N,zoomOnDoubleClick:A,panOnDrag:!T&&F,defaultViewport:I,translateExtent:P,minZoom:R,maxZoom:_,zoomActivationKeyCode:S,preventScrolling:$,noWheelClassName:B,noPanClassName:D},d.createElement(t_,{onSelectionStart:m,onSelectionEnd:f,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:o,onPaneMouseLeave:r,onPaneContextMenu:a,onPaneScroll:l,panOnDrag:F,isSelecting:!!K,selectionMode:p},e,L&&d.createElement(tZ,{onSelectionContextMenu:O,noPanClassName:D,disableKeyboardA11y:z})))};tW.displayName="FlowRenderer";var tG=(0,d.memo)(tW);function tj(e){let t={input:tK(e.input||eY),default:tK(e.default||eV),output:tK(e.output||eX),group:tK(e.group||eW)},n=Object.keys(e).filter(e=>!["input","default","output","group"].includes(e)).reduce((t,n)=>(t[n]=tK(e[n]||eV),t),{});return{...t,...n}}let tU=({x:e,y:t,width:n,height:o,origin:r})=>!n||!o||r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]},tq=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),tQ=e=>{let{nodesDraggable:t,nodesConnectable:n,nodesFocusable:o,elementsSelectable:r,updateNodeDimensions:a,onError:l}=E(tq,g.x),i=function(e){return E((0,d.useCallback)(t=>e?eC(t.nodeInternals,{x:0,y:0,width:t.width,height:t.height},t.transform,!0):t.getNodes(),[e]))}(e.onlyRenderVisibleElements),c=(0,d.useRef)(),u=(0,d.useMemo)(()=>{if("undefined"==typeof ResizeObserver)return null;let e=new ResizeObserver(e=>{a(e.map(e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})))});return c.current=e,e},[]);return(0,d.useEffect)(()=>()=>{c?.current?.disconnect()},[]),d.createElement("div",{className:"react-flow__nodes",style:tm},i.map(a=>{let i=a.type||"default";e.nodeTypes[i]||(l?.("003",S.error003(i)),i="default");let c=e.nodeTypes[i]||e.nodeTypes.default,g=!!(a.draggable||t&&void 0===a.draggable),h=!!(a.selectable||r&&void 0===a.selectable),p=!!(a.connectable||n&&void 0===a.connectable),m=!!(a.focusable||o&&void 0===a.focusable),f=e.nodeExtent?I(a.positionAbsolute,e.nodeExtent):a.positionAbsolute,y=f?.x??0,x=f?.y??0,b=tU({x:y,y:x,width:a.width??0,height:a.height??0,origin:e.nodeOrigin});return d.createElement(c,{key:a.id,id:a.id,className:a.className,style:a.style,type:i,data:a.data,sourcePosition:a.sourcePosition||s.Bottom,targetPosition:a.targetPosition||s.Top,hidden:a.hidden,xPos:y,yPos:x,xPosOrigin:b.x,yPosOrigin:b.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!a.selected,isDraggable:g,isSelectable:h,isConnectable:p,isFocusable:m,resizeObserver:u,dragHandle:a.dragHandle,zIndex:a[F]?.z??0,isParent:!!a[F]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!a.width&&!!a.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:a.ariaLabel,hasHandleBounds:!!a[F]?.handleBounds})}))};tQ.displayName="NodeRenderer";var tJ=(0,d.memo)(tQ);let t0=(e,t,n)=>n===s.Left?e-t:n===s.Right?e+t:e,t1=(e,t,n)=>n===s.Top?e-t:n===s.Bottom?e+t:e,t2="react-flow__edgeupdater",t5=({position:e,centerX:t,centerY:n,radius:o=10,onMouseDown:r,onMouseEnter:a,onMouseOut:l,type:i})=>d.createElement("circle",{onMouseDown:r,onMouseEnter:a,onMouseOut:l,className:(0,c.A)([t2,`${t2}-${i}`]),cx:t0(t,o,e),cy:t1(n,o,e),r:o,stroke:"transparent",fill:"transparent"}),t3=()=>!0;var t4=e=>{let t=({id:t,className:n,type:o,data:r,onClick:a,onEdgeDoubleClick:l,selected:i,animated:s,label:u,labelStyle:g,labelShowBg:h,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:f,style:y,source:x,target:S,sourceX:b,sourceY:E,targetX:w,targetY:M,sourcePosition:C,targetPosition:N,elementsSelectable:A,hidden:k,sourceHandleId:I,targetHandleId:P,onContextMenu:R,onMouseEnter:_,onMouseMove:$,onMouseLeave:O,reconnectRadius:B,onReconnect:D,onReconnectStart:z,onReconnectEnd:L,markerEnd:T,markerStart:H,rfId:F,ariaLabel:K,isFocusable:Y,isReconnectable:Z,pathOptions:X,interactionWidth:W,disableKeyboardA11y:G})=>{let j=(0,d.useRef)(null),[q,Q]=(0,d.useState)(!1),[J,ee]=(0,d.useState)(!1),et=v(),en=(0,d.useMemo)(()=>`url('#${ex(H,F)}')`,[H,F]),eo=(0,d.useMemo)(()=>`url('#${ex(T,F)}')`,[T,F]);if(k)return null;let er=U(t,et.getState,l),ea=U(t,et.getState,R),el=U(t,et.getState,_),ei=U(t,et.getState,$),es=U(t,et.getState,O),ed=(e,n)=>{if(0!==e.button)return;let{edges:o,isValidConnection:r}=et.getState(),a=n?S:x,l=(n?P:I)||null,i=n?"target":"source",s=o.find(e=>e.id===t);ee(!0),z?.(e,s,i),eB({event:e,handleId:l,nodeId:a,onConnect:e=>D?.(s,e),isTarget:n,getState:et.getState,setState:et.setState,isValidConnection:r||t3,edgeUpdaterType:i,onReconnectEnd:e=>{ee(!1),L?.(e,s,i)}})},ec=()=>Q(!0),eu=()=>Q(!1);return d.createElement("g",{className:(0,c.A)(["react-flow__edge",`react-flow__edge-${o}`,n,{selected:i,animated:s,inactive:!A&&!a,updating:q}]),onClick:e=>{let{edges:n,addSelectedEdges:o,unselectNodesAndEdges:r,multiSelectionActive:l}=et.getState(),i=n.find(e=>e.id===t);i&&(A&&(et.setState({nodesSelectionActive:!1}),i.selected&&l?(r({nodes:[],edges:[i]}),j.current?.blur()):o([t])),a&&a(e,i))},onDoubleClick:er,onContextMenu:ea,onMouseEnter:el,onMouseMove:ei,onMouseLeave:es,onKeyDown:Y?e=>{if(!G&&V.includes(e.key)&&A){let{unselectNodesAndEdges:n,addSelectedEdges:o,edges:r}=et.getState();"Escape"===e.key?(j.current?.blur(),n({edges:[r.find(e=>e.id===t)]})):o([t])}}:void 0,tabIndex:Y?0:void 0,role:Y?"button":"img","data-testid":`rf__edge-${t}`,"aria-label":null===K?void 0:K||`Edge from ${x} to ${S}`,"aria-describedby":Y?`${e6}-${F}`:void 0,ref:j},!J&&d.createElement(e,{id:t,source:x,target:S,selected:i,animated:s,label:u,labelStyle:g,labelShowBg:h,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:f,data:r,style:y,sourceX:b,sourceY:E,targetX:w,targetY:M,sourcePosition:C,targetPosition:N,sourceHandleId:I,targetHandleId:P,markerStart:en,markerEnd:eo,pathOptions:X,interactionWidth:W}),Z&&d.createElement(d.Fragment,null,("source"===Z||!0===Z)&&d.createElement(t5,{position:C,centerX:b,centerY:E,radius:B,onMouseDown:e=>ed(e,!0),onMouseEnter:ec,onMouseOut:eu,type:"source"}),("target"===Z||!0===Z)&&d.createElement(t5,{position:N,centerX:w,centerY:M,radius:B,onMouseDown:e=>ed(e,!1),onMouseEnter:ec,onMouseOut:eu,type:"target"})))};return t.displayName="EdgeWrapper",(0,d.memo)(t)};function t7(e){let t={default:t4(e.default||eg),straight:t4(e.bezier||es),step:t4(e.step||ei),smoothstep:t4(e.step||el),simplebezier:t4(e.simplebezier||et)},n=Object.keys(e).filter(e=>!["default","bezier"].includes(e)).reduce((t,n)=>(t[n]=t4(e[n]||eg),t),{});return{...t,...n}}function t6(e,t,n=null){let o=(n?.x||0)+t.x,r=(n?.y||0)+t.y,a=n?.width||t.width,l=n?.height||t.height;switch(e){case s.Top:return{x:o+a/2,y:r};case s.Right:return{x:o+a,y:r+l/2};case s.Bottom:return{x:o+a/2,y:r+l};case s.Left:return{x:o,y:r+l/2}}}function t9(e,t){return e?1!==e.length&&t?t&&e.find(e=>e.id===t)||null:e[0]:null}let t8=(e,t,n,o,r,a)=>{let l=t6(n,e,t),i=t6(a,o,r);return{sourceX:l.x,sourceY:l.y,targetX:i.x,targetY:i.y}};function ne(e){let t=e?.[F]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}let nt=[{level:0,isMaxLevel:!0,edges:[]}],nn={[i.Arrow]:({color:e="none",strokeWidth:t=1})=>d.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[i.ArrowClosed]:({color:e="none",strokeWidth:t=1})=>d.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})},no=({id:e,type:t,color:n,width:o=12.5,height:r=12.5,markerUnits:a="strokeWidth",strokeWidth:l,orient:i="auto-start-reverse"})=>{let s=function(e){let t=v();return(0,d.useMemo)(()=>Object.prototype.hasOwnProperty.call(nn,e)?nn[e]:(t.getState().onError?.("009",S.error009(e)),null),[e])}(t);return s?d.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${o}`,markerHeight:`${r}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:i,refX:"0",refY:"0"},d.createElement(s,{color:n,strokeWidth:l})):null},nr=({defaultColor:e,rfId:t})=>n=>{let o=[];return n.edges.reduce((n,r)=>([r.markerStart,r.markerEnd].forEach(r=>{if(r&&"object"==typeof r){let a=ex(r,t);o.includes(a)||(n.push({id:a,color:r.color||e,...r}),o.push(a))}}),n),[]).sort((e,t)=>e.id.localeCompare(t.id))},na=({defaultColor:e,rfId:t})=>{let n=E((0,d.useCallback)(nr({defaultColor:e,rfId:t}),[e,t]),(e,t)=>!(e.length!==t.length||e.some((e,n)=>e.id!==t[n].id)));return d.createElement("defs",null,n.map(e=>d.createElement(no,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient})))};na.displayName="MarkerDefinitions";var nl=(0,d.memo)(na);let ni=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),ns=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:r,edgeTypes:a,noPanClassName:l,onEdgeContextMenu:i,onEdgeMouseEnter:u,onEdgeMouseMove:h,onEdgeMouseLeave:p,onEdgeClick:m,onEdgeDoubleClick:f,onReconnect:y,onReconnectStart:x,onReconnectEnd:b,reconnectRadius:v,children:w,disableKeyboardA11y:M})=>{let{edgesFocusable:C,edgesUpdatable:N,elementsSelectable:A,width:k,height:I,connectionMode:P,nodeInternals:R,onError:_}=E(ni,g.x),$=function(e,t,n){return function(e,t,n=!1){let o=-1,r=Object.entries(e.reduce((e,r)=>{let a=H(r.zIndex),l=a?r.zIndex:0;if(n){let e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,i=Math.max(n?.[F]?.z||0,e?.[F]?.z||0,1e3);l=(a?r.zIndex:0)+(o?i:0)}return e[l]?e[l].push(r):e[l]=[r],o=l>o?l:o,e},{})).map(([e,t])=>{let n=+e;return{edges:t,level:n,isMaxLevel:n===o}});return 0===r.length?nt:r}(E((0,d.useCallback)(n=>e?n.edges.filter(e=>{let o=t.get(e.source),r=t.get(e.target);return o?.width&&o?.height&&r?.width&&r?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:a,width:l,height:i,transform:s}){let d={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+a)};d.x===d.x2&&(d.x2+=1),d.y===d.y2&&(d.y2+=1);let c=O({x:(0-s[0])/s[2],y:(0-s[1])/s[2],width:l/s[2],height:i/s[2]});return Math.ceil(Math.max(0,Math.min(c.x2,d.x2)-Math.max(c.x,d.x))*Math.max(0,Math.min(c.y2,d.y2)-Math.max(c.y,d.y)))>0}({sourcePos:o.positionAbsolute||{x:0,y:0},targetPos:r.positionAbsolute||{x:0,y:0},sourceWidth:o.width,sourceHeight:o.height,targetWidth:r.width,targetHeight:r.height,width:n.width,height:n.height,transform:n.transform})}):n.edges,[e,t])),t,n)}(t,R,n);return k?d.createElement(d.Fragment,null,$.map(({level:t,edges:n,isMaxLevel:g})=>d.createElement("svg",{key:t,style:{zIndex:t},width:k,height:I,className:"react-flow__edges react-flow__container"},g&&d.createElement(nl,{defaultColor:e,rfId:r}),d.createElement("g",null,n.map(e=>{let[t,n,g]=ne(R.get(e.source)),[E,w,k]=ne(R.get(e.target));if(!g||!k)return null;let I=e.type||"default";a[I]||(_?.("011",S.error011(I)),I="default");let $=a[I]||a.default,O=P===o.Strict?w.target:(w.target??[]).concat(w.source??[]),B=t9(n.source,e.sourceHandle),D=t9(O,e.targetHandle),z=B?.position||s.Bottom,L=D?.position||s.Top,T=!!(e.focusable||C&&void 0===e.focusable),H=e.reconnectable||e.updatable;if(!B||!D)return _?.("008",S.error008(B,e)),null;let{sourceX:F,sourceY:V,targetX:K,targetY:Y}=t8(t,B,z,E,D,L);return d.createElement($,{key:e.id,id:e.id,className:(0,c.A)([e.className,l]),type:I,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:F,sourceY:V,targetX:K,targetY:Y,sourcePosition:z,targetPosition:L,elementsSelectable:A,onContextMenu:i,onMouseEnter:u,onMouseMove:h,onMouseLeave:p,onClick:m,onEdgeDoubleClick:f,onReconnect:y,onReconnectStart:x,onReconnectEnd:b,reconnectRadius:v,rfId:r,ariaLabel:e.ariaLabel,isFocusable:T,isReconnectable:void 0!==y&&(H||N&&void 0===H),pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:M})})))),w):null};ns.displayName="EdgeRenderer";var nd=(0,d.memo)(ns);let nc=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function nu({children:e}){let t=E(nc);return d.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}let ng={[s.Left]:s.Right,[s.Right]:s.Left,[s.Top]:s.Bottom,[s.Bottom]:s.Top},nh=({nodeId:e,handleType:t,style:n,type:r=l.Bezier,CustomComponent:a,connectionStatus:i})=>{let{fromNode:s,handleId:c,toX:u,toY:h,connectionMode:p}=E((0,d.useCallback)(t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode}),[e]),g.x),m=s?.[F]?.handleBounds,f=m?.[t];if(p===o.Loose&&(f=f||m?.["source"===t?"target":"source"]),!s||!f)return null;let y=c?f.find(e=>e.id===c):f[0],x=y?y.x+y.width/2:(s.width??0)/2,S=y?y.y+y.height/2:s.height??0,b=(s.positionAbsolute?.x??0)+x,v=(s.positionAbsolute?.y??0)+S,w=y?.position,M=w?ng[w]:null;if(!w||!M)return null;if(a)return d.createElement(a,{connectionLineType:r,connectionLineStyle:n,fromNode:s,fromHandle:y,fromX:b,fromY:v,toX:u,toY:h,fromPosition:w,toPosition:M,connectionStatus:i});let C="",N={sourceX:b,sourceY:v,sourcePosition:w,targetX:u,targetY:h,targetPosition:M};return r===l.Bezier?[C]=eu(N):r===l.Step?[C]=ea({...N,borderRadius:0}):r===l.SmoothStep?[C]=ea(N):r===l.SimpleBezier?[C]=ee(N):C=`M${b},${v} ${u},${h}`,d.createElement("path",{d:C,fill:"none",className:"react-flow__connection-path",style:n})};nh.displayName="ConnectionLine";let np=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function nm({containerStyle:e,style:t,type:n,component:o}){let{nodeId:r,handleType:a,nodesConnectable:l,width:i,height:s,connectionStatus:u}=E(np,g.x);return r&&a&&i&&l?d.createElement("svg",{style:e,width:i,height:s,className:"react-flow__edges react-flow__connectionline react-flow__container"},d.createElement("g",{className:(0,c.A)(["react-flow__connection",u])},d.createElement(nh,{nodeId:r,handleType:a,style:t,type:n,CustomComponent:o,connectionStatus:u}))):null}function nf(e,t){return(0,d.useRef)(null),v(),(0,d.useMemo)(()=>t(e),[e])}let ny=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:o,onMoveEnd:r,onInit:a,onNodeClick:l,onEdgeClick:i,onNodeDoubleClick:s,onEdgeDoubleClick:c,onNodeMouseEnter:u,onNodeMouseMove:g,onNodeMouseLeave:h,onNodeContextMenu:p,onSelectionContextMenu:m,onSelectionStart:f,onSelectionEnd:y,connectionLineType:x,connectionLineStyle:S,connectionLineComponent:b,connectionLineContainerStyle:E,selectionKeyCode:v,selectionOnDrag:w,selectionMode:M,multiSelectionKeyCode:C,panActivationKeyCode:N,zoomActivationKeyCode:A,deleteKeyCode:k,onlyRenderVisibleElements:I,elementsSelectable:P,selectNodesOnDrag:R,defaultViewport:_,translateExtent:$,minZoom:O,maxZoom:B,preventScrolling:D,defaultMarkerColor:z,zoomOnScroll:L,zoomOnPinch:T,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:V,zoomOnDoubleClick:K,panOnDrag:Y,onPaneClick:Z,onPaneMouseEnter:X,onPaneMouseMove:W,onPaneMouseLeave:G,onPaneScroll:j,onPaneContextMenu:U,onEdgeContextMenu:q,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:eo,reconnectRadius:er,noDragClassName:ea,noWheelClassName:el,noPanClassName:ei,elevateEdgesOnSelect:es,disableKeyboardA11y:ed,nodeOrigin:ec,nodeExtent:eu,rfId:eg})=>{let eh=nf(e,tj),ep=nf(t,t7);return!function(e){let t=tg(),n=(0,d.useRef)(!1);(0,d.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(a),d.createElement(tG,{onPaneClick:Z,onPaneMouseEnter:X,onPaneMouseMove:W,onPaneMouseLeave:G,onPaneContextMenu:U,onPaneScroll:j,deleteKeyCode:k,selectionKeyCode:v,selectionOnDrag:w,selectionMode:M,onSelectionStart:f,onSelectionEnd:y,multiSelectionKeyCode:C,panActivationKeyCode:N,zoomActivationKeyCode:A,elementsSelectable:P,onMove:n,onMoveStart:o,onMoveEnd:r,zoomOnScroll:L,zoomOnPinch:T,zoomOnDoubleClick:K,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:V,panOnDrag:Y,defaultViewport:_,translateExtent:$,minZoom:O,maxZoom:B,onSelectionContextMenu:m,preventScrolling:D,noDragClassName:ea,noWheelClassName:el,noPanClassName:ei,disableKeyboardA11y:ed},d.createElement(nu,null,d.createElement(nd,{edgeTypes:ep,onEdgeClick:i,onEdgeDoubleClick:c,onlyRenderVisibleElements:I,onEdgeContextMenu:q,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:eo,reconnectRadius:er,defaultMarkerColor:z,noPanClassName:ei,elevateEdgesOnSelect:!!es,disableKeyboardA11y:ed,rfId:eg},d.createElement(nm,{style:S,type:x,component:b,containerStyle:E})),d.createElement("div",{className:"react-flow__edgelabel-renderer"}),d.createElement(tJ,{nodeTypes:eh,onNodeClick:l,onNodeDoubleClick:s,onNodeMouseEnter:u,onNodeMouseMove:g,onNodeMouseLeave:h,onNodeContextMenu:p,selectNodesOnDrag:R,onlyRenderVisibleElements:I,noPanClassName:ei,noDragClassName:ea,disableKeyboardA11y:ed,nodeOrigin:ec,nodeExtent:eu,rfId:eg})))};ny.displayName="GraphView";var nx=(0,d.memo)(ny);let nS=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],nb={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:nS,nodeExtent:nS,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:o.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:K,isValidConnection:void 0},nE=()=>(0,u.h)((e,t)=>({...nb,setNodes:n=>{let{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:a}=t();e({nodeInternals:ta(n,o,r,a)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{let{defaultEdgeOptions:o={}}=t();e({edges:n.map(e=>({...o,...e}))})},setDefaultNodesAndEdges:(n,o)=>{let r=void 0!==n,a=void 0!==o;e({nodeInternals:r?ta(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map,edges:a?o:[],hasDefaultNodes:r,hasDefaultEdges:a})},updateNodeDimensions:n=>{let{onNodesChange:o,nodeInternals:r,fitViewOnInit:a,fitViewOnInitDone:l,fitViewOnInitOptions:i,domNode:s,nodeOrigin:d}=t(),c=s?.querySelector(".react-flow__viewport");if(!c)return;let u=window.getComputedStyle(c),{m22:g}=new window.DOMMatrixReadOnly(u.transform),h=n.reduce((e,t)=>{let n=r.get(t.id);if(n?.hidden)r.set(n.id,{...n,[F]:{...n[F],handleBounds:void 0}});else if(n){let o=A(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[F]:{...n[F],handleBounds:{source:tD(".source",t.nodeElement,g,d),target:tD(".target",t.nodeElement,g,d)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e},[]);tr(r,d);let p=l||a&&!l&&tl(t,{initial:!0,...i});e({nodeInternals:new Map(r),fitViewOnInitDone:p}),h?.length>0&&o?.(h)},updateNodePositions:(e,n=!0,o=!1)=>{let{triggerNodeChanges:r}=t();r(e.map(e=>{let t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t}))},triggerNodeChanges:n=>{let{onNodesChange:o,nodeInternals:r,hasDefaultNodes:a,nodeOrigin:l,getNodes:i,elevateNodesOnSelect:s}=t();n?.length&&(a&&e({nodeInternals:ta(tA(n,i()),r,l,s)}),o?.(n))},addSelectedNodes:n=>{let o,{multiSelectionActive:r,edges:a,getNodes:l}=t(),i=null;r?o=n.map(e=>tk(e,!0)):(o=tI(l(),n),i=tI(a,[])),ti({changedNodes:o,changedEdges:i,get:t,set:e})},addSelectedEdges:n=>{let o,{multiSelectionActive:r,edges:a,getNodes:l}=t(),i=null;r?o=n.map(e=>tk(e,!0)):(o=tI(a,n),i=tI(l(),[])),ti({changedNodes:i,changedEdges:o,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{let{edges:r,getNodes:a}=t();ti({changedNodes:(n||a()).map(e=>(e.selected=!1,tk(e.id,!1))),changedEdges:(o||r).map(e=>tk(e.id,!1)),get:t,set:e})},setMinZoom:n=>{let{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{let{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{let{edges:n,getNodes:o}=t();ti({changedNodes:o().filter(e=>e.selected).map(e=>tk(e.id,!1)),changedEdges:n.filter(e=>e.selected).map(e=>tk(e.id,!1)),get:t,set:e})},setNodeExtent:n=>{let{nodeInternals:o}=t();o.forEach(e=>{e.positionAbsolute=I(e.position,n)}),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{let{transform:n,width:o,height:r,d3Zoom:a,d3Selection:l,translateExtent:i}=t();if(!a||!l||!e.x&&!e.y)return!1;let s=h.GS.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),d=a?.constrain()(s,[[0,0],[o,r]],i);return a.transform(l,d),n[0]!==d.x||n[1]!==d.y||n[2]!==d.k},cancelConnection:()=>e({connectionNodeId:nb.connectionNodeId,connectionHandleId:nb.connectionHandleId,connectionHandleType:nb.connectionHandleType,connectionStatus:nb.connectionStatus,connectionStartHandle:nb.connectionStartHandle,connectionEndHandle:nb.connectionEndHandle}),reset:()=>e({...nb})}),Object.is),nv=({children:e})=>{let t=(0,d.useRef)(null);return t.current||(t.current=nE()),d.createElement(x,{value:t.current},e)};nv.displayName="ReactFlowProvider";let nw=({children:e})=>(0,d.useContext)(y)?d.createElement(d.Fragment,null,e):d.createElement(nv,null,e);nw.displayName="ReactFlowWrapper";let nM={input:eY,default:eV,output:eX,group:eW},nC={default:eg,straight:es,step:ei,smoothstep:el,simplebezier:et},nN=[0,0],nA=[15,15],nk={x:0,y:0,zoom:1},nI={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},nP=(0,d.forwardRef)(({nodes:e,edges:t,defaultNodes:n,defaultEdges:i,className:s,nodeTypes:u=nM,edgeTypes:g=nC,onNodeClick:h,onEdgeClick:p,onInit:m,onMove:f,onMoveStart:y,onMoveEnd:x,onConnect:S,onConnectStart:b,onConnectEnd:E,onClickConnectStart:v,onClickConnectEnd:w,onNodeMouseEnter:M,onNodeMouseMove:N,onNodeMouseLeave:A,onNodeContextMenu:k,onNodeDoubleClick:I,onNodeDragStart:P,onNodeDrag:R,onNodeDragStop:_,onNodesDelete:$,onEdgesDelete:O,onSelectionChange:B,onSelectionDragStart:D,onSelectionDrag:z,onSelectionDragStop:L,onSelectionContextMenu:T,onSelectionStart:H,onSelectionEnd:F,connectionMode:V=o.Strict,connectionLineType:K=l.Bezier,connectionLineStyle:Y,connectionLineComponent:Z,connectionLineContainerStyle:X,deleteKeyCode:W="Backspace",selectionKeyCode:j="Shift",selectionOnDrag:U=!1,selectionMode:q=a.Full,panActivationKeyCode:Q="Space",multiSelectionKeyCode:J=G()?"Meta":"Control",zoomActivationKeyCode:ee=G()?"Meta":"Control",snapToGrid:et=!1,snapGrid:en=nA,onlyRenderVisibleElements:eo=!1,selectNodesOnDrag:er=!0,nodesDraggable:ea,nodesConnectable:el,nodesFocusable:ei,nodeOrigin:es=nN,edgesFocusable:ed,edgesUpdatable:ec,elementsSelectable:eu,defaultViewport:eg=nk,minZoom:eh=.5,maxZoom:ep=2,translateExtent:em=nS,preventScrolling:ef=!0,nodeExtent:ey,defaultMarkerColor:ex="#b1b1b7",zoomOnScroll:eS=!0,zoomOnPinch:eb=!0,panOnScroll:eE=!1,panOnScrollSpeed:ev=.5,panOnScrollMode:ew=r.Free,zoomOnDoubleClick:eM=!0,panOnDrag:eC=!0,onPaneClick:eN,onPaneMouseEnter:eA,onPaneMouseMove:ek,onPaneMouseLeave:eI,onPaneScroll:eP,onPaneContextMenu:eR,children:e_,onEdgeContextMenu:e$,onEdgeDoubleClick:eO,onEdgeMouseEnter:eB,onEdgeMouseMove:eD,onEdgeMouseLeave:ez,onEdgeUpdate:eL,onEdgeUpdateStart:eT,onEdgeUpdateEnd:eH,onReconnect:eF,onReconnectStart:eV,onReconnectEnd:eK,reconnectRadius:eY=10,edgeUpdaterRadius:eZ=10,onNodesChange:eX,onEdgesChange:eW,noDragClassName:eG="nodrag",noWheelClassName:ej="nowheel",noPanClassName:eU="nopan",fitView:eq=!1,fitViewOptions:eQ,connectOnClick:e0=!0,attributionPosition:e1,proOptions:e2,defaultEdgeOptions:e3,elevateNodesOnSelect:e4=!0,elevateEdgesOnSelect:e7=!1,disableKeyboardA11y:e6=!1,autoPanOnConnect:e9=!0,autoPanOnNodeDrag:e8=!0,connectionRadius:tt=20,isValidConnection:tn,onError:to,style:tr,id:ta,nodeDragThreshold:tl,...ti},ts)=>{let td=ta||"1";return d.createElement("div",{...ti,style:{...tr,...nI},ref:ts,className:(0,c.A)(["react-flow",s]),"data-testid":"rf__wrapper",id:ta},d.createElement(nw,null,d.createElement(nx,{onInit:m,onMove:f,onMoveStart:y,onMoveEnd:x,onNodeClick:h,onEdgeClick:p,onNodeMouseEnter:M,onNodeMouseMove:N,onNodeMouseLeave:A,onNodeContextMenu:k,onNodeDoubleClick:I,nodeTypes:u,edgeTypes:g,connectionLineType:K,connectionLineStyle:Y,connectionLineComponent:Z,connectionLineContainerStyle:X,selectionKeyCode:j,selectionOnDrag:U,selectionMode:q,deleteKeyCode:W,multiSelectionKeyCode:J,panActivationKeyCode:Q,zoomActivationKeyCode:ee,onlyRenderVisibleElements:eo,selectNodesOnDrag:er,defaultViewport:eg,translateExtent:em,minZoom:eh,maxZoom:ep,preventScrolling:ef,zoomOnScroll:eS,zoomOnPinch:eb,zoomOnDoubleClick:eM,panOnScroll:eE,panOnScrollSpeed:ev,panOnScrollMode:ew,panOnDrag:eC,onPaneClick:eN,onPaneMouseEnter:eA,onPaneMouseMove:ek,onPaneMouseLeave:eI,onPaneScroll:eP,onPaneContextMenu:eR,onSelectionContextMenu:T,onSelectionStart:H,onSelectionEnd:F,onEdgeContextMenu:e$,onEdgeDoubleClick:eO,onEdgeMouseEnter:eB,onEdgeMouseMove:eD,onEdgeMouseLeave:ez,onReconnect:eF??eL,onReconnectStart:eV??eT,onReconnectEnd:eK??eH,reconnectRadius:eY??eZ,defaultMarkerColor:ex,noDragClassName:eG,noWheelClassName:ej,noPanClassName:eU,elevateEdgesOnSelect:e7,rfId:td,disableKeyboardA11y:e6,nodeOrigin:es,nodeExtent:ey}),d.createElement(e5,{nodes:e,edges:t,defaultNodes:n,defaultEdges:i,onConnect:S,onConnectStart:b,onConnectEnd:E,onClickConnectStart:v,onClickConnectEnd:w,nodesDraggable:ea,nodesConnectable:el,nodesFocusable:ei,edgesFocusable:ed,edgesUpdatable:ec,elementsSelectable:eu,elevateNodesOnSelect:e4,minZoom:eh,maxZoom:ep,nodeExtent:ey,onNodesChange:eX,onEdgesChange:eW,snapToGrid:et,snapGrid:en,connectionMode:V,translateExtent:em,connectOnClick:e0,defaultEdgeOptions:e3,fitView:eq,fitViewOptions:eQ,onNodesDelete:$,onEdgesDelete:O,onNodeDragStart:P,onNodeDrag:R,onNodeDragStop:_,onSelectionDrag:z,onSelectionDragStart:D,onSelectionDragStop:L,noPanClassName:eU,nodeOrigin:es,rfId:td,autoPanOnConnect:e9,autoPanOnNodeDrag:e8,onError:to,connectionRadius:tt,isValidConnection:tn,nodeDragThreshold:tl}),d.createElement(eJ,{onSelectionChange:B}),e_,d.createElement(C,{proOptions:e2,position:e1}),d.createElement(te,{rfId:td,disableKeyboardA11y:e6})))});nP.displayName="ReactFlow";function nR(e){return t=>{let[n,o]=(0,d.useState)(t),r=(0,d.useCallback)(t=>o(n=>e(t,n)),[]);return[n,o,r]}}let n_=nR(tA),n$=nR(function(e,t){return tN(e,t)})}}]);
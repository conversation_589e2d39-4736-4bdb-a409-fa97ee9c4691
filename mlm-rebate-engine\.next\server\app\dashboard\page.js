(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={2106:(e,s,t)=>{Promise.resolve().then(t.bind(t,80559))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42274:(e,s,t)=>{Promise.resolve().then(t.bind(t,58061))},58061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(60687),a=t(43210),i=t(82136),l=t(16189),n=t(30474),d=t(59391),c=t(68367),o=t(23877);let m=(0,a.lazy)(()=>Promise.all([t.e(9947),t.e(5316)]).then(t.bind(t,75316))),x=(0,a.lazy)(()=>t.e(4558).then(t.bind(t,64558))),h=(0,a.lazy)(()=>t.e(2537).then(t.bind(t,12537))),u=(0,a.lazy)(()=>t.e(4190).then(t.bind(t,54190))),p=(0,a.lazy)(()=>t.e(9753).then(t.bind(t,9753))),j=(0,a.lazy)(()=>t.e(8450).then(t.bind(t,98450))),g=(0,a.lazy)(()=>t.e(9342).then(t.bind(t,69342))),b=async(e="month")=>{let s=await fetch(`/api/dashboard?timeframe=${e}`);if(!s.ok)throw Error("Failed to fetch dashboard data");return s.json()};function f(){let{data:e,status:s}=(0,i.useSession)(),t=(0,l.useRouter)(),[f,v]=(0,a.useState)("month"),{data:N,isLoading:y,error:w}=(0,d.I)({queryKey:["dashboardData",f],queryFn:()=>b(f),enabled:"authenticated"===s,staleTime:3e5,refetchOnWindowFocus:!1});if("loading"===s||y)return(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})})});if(w)return(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl text-red-500",children:"Error loading dashboard data"})})});let k={id:N?.user?.id.toString()||"current-user",name:N?.user?.name||e?.user?.name||"Current User",rank:N?.user?.rank||"Distributor",image:N?.user?.profileImage||e?.user?.image||void 0},C=N?.recentData?.rebates.slice(0,3).map((e,s)=>({id:`user${s+1}`,name:e.generator.name,rank:"Distributor",position:s%2==0?"left":"right"}))||[],P=e=>{v(e)};return(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"Dashboard"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center bg-gray-100 rounded-md",children:(0,r.jsxs)("select",{value:f,onChange:e=>P(e.target.value),className:"bg-transparent border-none py-2 px-3 focus:ring-0 text-sm",children:[(0,r.jsx)("option",{value:"week",children:"Last 7 Days"}),(0,r.jsx)("option",{value:"month",children:"This Month"}),(0,r.jsx)("option",{value:"year",children:"This Year"})]})}),(0,r.jsxs)("button",{className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none",children:[(0,r.jsx)(o.jNV,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center text-xs text-white",children:"3"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,r.jsx)(o.lcY,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Wallet Balance"}),(0,r.jsxs)("p",{className:"text-2xl font-semibold",children:["₱",N?.stats.walletBalance.toFixed(2)||"0.00"]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,r.jsx)(o.YYR,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,r.jsxs)("p",{className:"text-2xl font-semibold",children:["₱",N?.stats.totalRebates.toFixed(2)||"0.00"]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 rounded-full bg-purple-100 text-purple-500 mr-4",children:(0,r.jsx)(o.YXz,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Downline Members"}),(0,r.jsx)("p",{className:"text-2xl font-semibold",children:N?.stats.downlineCount||0})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 rounded-full bg-orange-100 text-orange-500 mr-4",children:(0,r.jsx)(o.AsH,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Purchases"}),(0,r.jsx)("p",{className:"text-2xl font-semibold",children:N?.stats.purchaseCount||0})]})]})})]}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading performance summary..."}),children:(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(h,{metrics:[]})})}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading charts..."}),children:(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(m,{rebateData:N?.charts.rebates?Object.values(N.charts.rebates):[],salesData:N?.charts.sales?Object.values(N.charts.sales):[],rankDistribution:N?.charts.rankDistribution||{},timeframe:f})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading widget..."}),children:(0,r.jsx)("div",{children:(0,r.jsx)(p,{})})}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading widget..."}),children:(0,r.jsx)("div",{children:(0,r.jsx)(j,{})})}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading widget..."}),children:(0,r.jsx)("div",{children:(0,r.jsx)(g,{})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"h-64 flex items-center justify-center",children:"Loading genealogy..."}),children:(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)(x,{currentUser:k,downlineMembers:C})})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsx)("h3",{className:"font-medium text-gray-700",children:"Recent Rebates"})}),(0,r.jsxs)("div",{className:"p-4",children:[N?.recentData.rebates&&N.recentData.rebates.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:N.recentData.rebates.map(e=>(0,r.jsxs)("div",{className:"py-3 flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.generator.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"text-sm font-medium text-green-600",children:["+₱",e.amount.toFixed(2)]})]},e.id))}):(0,r.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No rebates received yet."}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("button",{onClick:()=>t.push("/rebates"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"View All Rebates"})})]})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(u,{})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md mb-8",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsx)("h3",{className:"font-medium text-gray-700",children:"Quick Actions"})}),(0,r.jsxs)("div",{className:"p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("button",{onClick:()=>t.push("/shop"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(o.AsH,{className:"mr-2"})," Shop Products"]}),(0,r.jsxs)("button",{onClick:()=>t.push("/genealogy"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors",children:[(0,r.jsx)(o.YXz,{className:"mr-2"})," View Genealogy"]}),(0,r.jsxs)("button",{onClick:()=>t.push("/wallet"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 transition-colors",children:[(0,r.jsx)(o.lcY,{className:"mr-2"})," Manage Wallet"]}),(0,r.jsxs)("button",{onClick:()=>t.push("/referrals"),className:"flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:[(0,r.jsx)(o.YXz,{className:"mr-2"})," Invite Members"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,r.jsx)(o.sHz,{className:"text-green-500 mr-2"})," About Extreme Life Herbal"]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)("div",{className:"relative w-24 h-24",children:(0,r.jsx)(n.default,{src:"/images/20250503.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})})}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Extreme Life Herbal Products Trading is a leading provider of high-quality herbal supplements and wellness products in the Philippines."}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Our mission is to promote health and wellness through natural products while providing business opportunities for our distributors."}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("button",{onClick:()=>t.push("/about"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"Learn More"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,r.jsx)(o.AsH,{className:"text-green-500 mr-2"})," Featured Products"]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,r.jsx)(o.sHz,{})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Biogen Extreme Concentrate"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Concentrated organic enzyme formula"})]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,r.jsx)(o.sHz,{})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Veggie Coffee 124 in 1"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Caffeine-free coffee alternative"})]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,r.jsx)(o.sHz,{})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Shield Herbal Care Soap"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Premium herbal soap for skin care"})]})]})]}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("button",{onClick:()=>t.push("/shop"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"View All Products"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,r.jsx)(o.dRU,{className:"text-green-500 mr-2"})," Contact Us"]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("ul",{className:"space-y-4",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,r.jsx)(o.dRU,{})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Phone"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"+63 (2) 8123 4567"})]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,r.jsx)(o.maD,{})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"<EMAIL>"})]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-5 w-5 text-green-500",children:(0,r.jsx)(o.vq8,{})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Address"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"123 Herbal Street, Makati City, Metro Manila, Philippines"})]})]})]}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("button",{onClick:()=>t.push("/contact"),className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"Contact Us"})})]})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\dashboard\\page.tsx","default")},98140:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\dashboard\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,8414,9567,3877,474,4859,9391,3024],()=>t(98140));module.exports=r})();
(()=>{var e={};e.id=9154,e.ids=[9154],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3720:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(43210),i=s(82136),n=s(59391),l=s(23877),o=s(85814),d=s.n(o),c=s(43324),m=s(29947);function h({userId:e,timeRange:t="last30days"}){let[s,i]=(0,a.useState)(null),[n,o]=(0,a.useState)(!0),[d,c]=(0,a.useState)(null),[h,u]=(0,a.useState)(t),x=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),p=e=>new Intl.NumberFormat().format(e),b=e=>{u(e)},g={labels:s?.salesByMonth.map(e=>e.month)||[],datasets:[{label:"Personal Sales",data:s?.salesByMonth.map(e=>e.personalSales)||[],borderColor:"rgb(53, 162, 235)",backgroundColor:"rgba(53, 162, 235, 0.5)"},{label:"Team Sales",data:s?.salesByMonth.map(e=>e.teamSales)||[],borderColor:"rgb(255, 99, 132)",backgroundColor:"rgba(255, 99, 132, 0.5)"}]},v={labels:s?.newMembersByMonth.map(e=>e.month)||[],datasets:[{label:"New Members",data:s?.newMembersByMonth.map(e=>e.count)||[],backgroundColor:"rgba(75, 192, 192, 0.5)"}]},f={labels:s?.rankDistribution.map(e=>e.rankName)||[],datasets:[{label:"Members",data:s?.rankDistribution.map(e=>e.count)||[],backgroundColor:["rgba(255, 99, 132, 0.5)","rgba(54, 162, 235, 0.5)","rgba(255, 206, 86, 0.5)","rgba(75, 192, 192, 0.5)","rgba(153, 102, 255, 0.5)","rgba(255, 159, 64, 0.5)"],borderColor:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(255, 159, 64, 1)"],borderWidth:1}]},j={labels:s?.networkDepth.map(e=>`Level ${e.level}`)||[],datasets:[{label:"Members",data:s?.networkDepth.map(e=>e.count)||[],backgroundColor:"rgba(153, 102, 255, 0.5)"}]},y={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}}};return n&&!s?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading metrics data..."})]}):d?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,r.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,r.jsx)(l.BS8,{className:"mr-2"}),"Error loading metrics data"]}),(0,r.jsx)("p",{className:"text-red-600",children:d})]})}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,r.jsx)(l.YYR,{className:"mr-2 text-blue-500"}),"Genealogy Metrics Dashboard"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l.bfZ,{className:"text-gray-500 mr-2"}),(0,r.jsxs)("select",{value:h,onChange:e=>b(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"last90days",children:"Last 90 Days"}),(0,r.jsx)("option",{value:"last6months",children:"Last 6 Months"}),(0,r.jsx)("option",{value:"last12months",children:"Last 12 Months"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(l.YXz,{className:"text-blue-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Total Members"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:p(s?.totalMembers||0)}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[p(s?.activeMembers||0)," active members"]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(l.NPy,{className:"text-green-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"New Members"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:p(s?.newMembersLast30Days||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"in the last 30 days"})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(l.AsH,{className:"text-yellow-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Total Sales"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:x(s?.totalSales||0)}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Avg: ",x(s?.averageSalesPerMember||0)," per member"]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(l.lcY,{className:"text-purple-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Total Rebates"})]}),(0,r.jsx)("div",{className:"text-2xl font-bold",children:x(s?.totalRebates||0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"distributed to members"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(l.YYR,{className:"text-blue-500 mr-2"}),"Sales by Month"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.N1,{data:g,options:y})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(l.v$b,{className:"text-green-500 mr-2"}),"New Members by Month"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.yP,{data:v,options:y})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(l.qvi,{className:"text-yellow-500 mr-2"}),"Rank Distribution"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.Fq,{data:f,options:y})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(l.v$b,{className:"text-purple-500 mr-2"}),"Network Depth"]}),(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(m.yP,{data:j,options:y})})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"font-medium mb-4 flex items-center",children:[(0,r.jsx)(l.YXz,{className:"text-blue-500 mr-2"}),"Top Performers"]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Member"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Personal Sales"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Team Sales"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Downline Count"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s?.topPerformers.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:x(e.personalSales)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:x(e.teamSales)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:p(e.downlineCount)})})]},e.id))})]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-2 flex items-center",children:[(0,r.jsx)(l.__w,{className:"text-blue-500 mr-2"}),"Insights"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-blue-700",children:[(0,r.jsxs)("li",{children:["Your network has grown by ",p(s?.newMembersLast30Days||0)," members in the last 30 days."]}),(0,r.jsxs)("li",{children:["The most common rank in your network is ",s?.rankDistribution[0]?.rankName||"N/A","."]}),(0,r.jsxs)("li",{children:["Your network extends to ",s?.networkDepth.length||0," levels deep."]}),(0,r.jsxs)("li",{children:["Your top performer generated ",x(s?.topPerformers[0]?.teamSales||0)," in team sales."]})]})]})]})}function u(){let{data:e,status:t}=(0,i.useSession)(),[s,o]=(0,a.useState)(void 0),[c,m]=(0,a.useState)("last30days"),{data:u,isLoading:x}=(0,n.I)({queryKey:["user"],queryFn:async()=>{if(!e?.user?.email)return null;let t=await fetch("/api/users/me");if(!t.ok)throw Error("Failed to fetch user data");return await t.json()},enabled:"authenticated"===t});return(u&&!s&&o(u.id),"loading"===t||x)?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===t?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(l.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view genealogy metrics."}),(0,r.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Metrics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"View key metrics and analytics for your genealogy network"})]}),(0,r.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(l.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[s?(0,r.jsx)(h,{userId:s,timeRange:c}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,r.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading metrics data..."})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center mb-4",children:[(0,r.jsx)(l.__w,{className:"mr-2 text-blue-500"}),"About Genealogy Metrics"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{children:"The Genealogy Metrics Dashboard provides valuable insights into your network's performance and growth. Use these metrics to identify trends, recognize top performers, and make data-driven decisions to grow your business."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Key Metrics"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"Total Members - The total number of distributors in your network"}),(0,r.jsx)("li",{children:"New Members - Recent additions to your network"}),(0,r.jsx)("li",{children:"Total Sales - Combined sales volume across your network"}),(0,r.jsx)("li",{children:"Total Rebates - Commissions and bonuses distributed"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"Performance Charts"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Sales by Month - Track sales trends over time"}),(0,r.jsx)("li",{children:"New Members by Month - Monitor recruitment performance"}),(0,r.jsx)("li",{children:"Rank Distribution - See the composition of your network"}),(0,r.jsx)("li",{children:"Network Depth - Analyze your network's structure"})]})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"How to Use These Metrics"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Identify Growth Opportunities:"})," Look for trends in sales and recruitment to identify seasonal patterns or growth opportunities."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Recognize Top Performers:"})," Identify and reward your top performers to encourage continued success."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Balance Your Network:"})," Use the rank distribution and network depth charts to identify areas that need development."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Set Goals:"})," Use historical data to set realistic goals for future performance."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Track Progress:"})," Monitor your metrics regularly to track progress toward your goals."]})]})]}),(0,r.jsx)("p",{children:"For more detailed analysis, you can export these metrics or view specific reports in the reporting section. The metrics are updated daily to provide you with the most current information about your network."})]})]})]})]})}c.t1.register(c.PP,c.kc,c.FN,c.No,c.E8,c.Bs,c.hE,c.m_,c.s$)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16332:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\metrics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\metrics\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>l,_:()=>n});var r=s(60687),a=s(43210);let i=(0,a.createContext)(void 0),n=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},l=({children:e})=>{let[t,s]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let n=e=>{s(t=>t.filter(t=>t.id!==e))},l=t.reduce((e,t)=>e+t.quantity,0),o=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,r.jsx)(i.Provider,{value:{items:t,addItem:e=>{s(t=>{let s=t.findIndex(t=>t.id===e.id);if(!(s>=0))return[...t,e];{let r=[...t];return r[s]={...r[s],quantity:r[s].quantity+e.quantity},r}})},removeItem:n,updateQuantity:(e,t)=>{if(t<=0)return void n(e);s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{s([])},itemCount:l,subtotal:o,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},45851:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),a=s(25217),i=s(8693),n=s(43210);function l({children:e}){let[t]=(0,n.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(i.Ht,{client:t,children:e})}},61135:()=>{},61184:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["genealogy",{children:["metrics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,16332)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\metrics\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\metrics\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/genealogy/metrics/page",pathname:"/genealogy/metrics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(60687),a=s(43210);let i=()=>"serviceWorker"in navigator,n=async()=>{if(!i())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),l(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},l=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&o()})})},o=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,s]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{n();let e=()=>{s(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,t&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,s)=>{Promise.resolve().then(s.bind(s,37043)),Promise.resolve().then(s.bind(s,23229)),Promise.resolve().then(s.bind(s,82113)),Promise.resolve().then(s.bind(s,41750))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,s)=>{Promise.resolve().then(s.bind(s,28253)),Promise.resolve().then(s.bind(s,97695)),Promise.resolve().then(s.bind(s,45851)),Promise.resolve().then(s.bind(s,63345))},79551:e=>{"use strict";e.exports=require("url")},79692:(e,t,s)=>{Promise.resolve().then(s.bind(s,3720))},82113:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},92844:(e,t,s)=>{Promise.resolve().then(s.bind(s,16332))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>h});var r=s(37413),a=s(22376),i=s.n(a),n=s(68726),l=s.n(n);s(61135);var o=s(23229),d=s(37043),c=s(82113),m=s(41750);let h={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:(0,r.jsx)(o.AuthProvider,{children:(0,r.jsx)(c.default,{children:(0,r.jsx)(d.CartProvider,{children:(0,r.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},97695:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>i});var r=s(60687),a=s(82136);function i({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,9391,9947],()=>s(61184));module.exports=r})();
(()=>{var e={};e.id=5737,e.ids=[5737],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14993:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var i=s(60687),n=s(43210),r=s(82136),l=s(16189),a=s(59391),o=s(68367),d=s(51215);function c(e,t,s){let i,n=s.initialDeps??[];function r(){var r,l,a,o;let d,c;s.key&&(null==(r=s.debug)?void 0:r.call(s))&&(d=Date.now());let h=e();if(!(h.length!==n.length||h.some((e,t)=>n[t]!==e)))return i;if(n=h,s.key&&(null==(l=s.debug)?void 0:l.call(s))&&(c=Date.now()),i=t(...h),s.key&&(null==(a=s.debug)?void 0:a.call(s))){let e=Math.round((Date.now()-d)*100)/100,t=Math.round((Date.now()-c)*100)/100,i=t/16,n=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${n(t,5)} /${n(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*i,120))}deg 100% 31%);`,null==s?void 0:s.key)}return null==(o=null==s?void 0:s.onChange)||o.call(s,i),i}return r.updateDeps=e=>{n=e},r}function h(e,t){if(void 0!==e)return e;throw Error(`Unexpected undefined${t?`: ${t}`:""}`)}let u=(e,t)=>1>=Math.abs(e-t),m=(e,t,s)=>{let i;return function(...n){e.clearTimeout(i),i=e.setTimeout(()=>t.apply(this,n),s)}},x=e=>{let{offsetWidth:t,offsetHeight:s}=e;return{width:t,height:s}},p=e=>e,g=e=>{let t=Math.max(e.startIndex-e.overscan,0),s=Math.min(e.endIndex+e.overscan,e.count-1),i=[];for(let e=t;e<=s;e++)i.push(e);return i},f=(e,t)=>{let s=e.scrollElement;if(!s)return;let i=e.targetWindow;if(!i)return;let n=e=>{let{width:s,height:i}=e;t({width:Math.round(s),height:Math.round(i)})};if(n(x(s)),!i.ResizeObserver)return()=>{};let r=new i.ResizeObserver(t=>{let i=()=>{let e=t[0];if(null==e?void 0:e.borderBoxSize){let t=e.borderBoxSize[0];if(t)return void n({width:t.inlineSize,height:t.blockSize})}n(x(s))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(i):i()});return r.observe(s,{box:"border-box"}),()=>{r.unobserve(s)}},b={passive:!0},v="undefined"==typeof window||"onscrollend"in window,y=(e,t)=>{let s=e.scrollElement;if(!s)return;let i=e.targetWindow;if(!i)return;let n=0,r=e.options.useScrollendEvent&&v?()=>void 0:m(i,()=>{t(n,!1)},e.options.isScrollingResetDelay),l=i=>()=>{let{horizontal:l,isRtl:a}=e.options;n=l?s.scrollLeft*(a&&-1||1):s.scrollTop,r(),t(n,i)},a=l(!0),o=l(!1);o(),s.addEventListener("scroll",a,b);let d=e.options.useScrollendEvent&&v;return d&&s.addEventListener("scrollend",o,b),()=>{s.removeEventListener("scroll",a),d&&s.removeEventListener("scrollend",o)}},j=(e,t,s)=>{if(null==t?void 0:t.borderBoxSize){let e=t.borderBoxSize[0];if(e)return Math.round(e[s.options.horizontal?"inlineSize":"blockSize"])}return e[s.options.horizontal?"offsetWidth":"offsetHeight"]},w=(e,{adjustments:t=0,behavior:s},i)=>{var n,r;null==(r=null==(n=i.scrollElement)?void 0:n.scrollTo)||r.call(n,{[i.options.horizontal?"left":"top"]:e+t,behavior:s})};class N{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null,t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver(e=>{e.forEach(e=>{let t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()})}):null);return{disconnect:()=>{var s;null==(s=t())||s.disconnect(),e=null},observe:e=>{var s;return null==(s=t())?void 0:s.observe(e,{box:"border-box"})},unobserve:e=>{var s;return null==(s=t())?void 0:s.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([t,s])=>{void 0===s&&delete e[t]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:p,rangeExtractor:g,onChange:()=>{},measureElement:j,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...e}},this.notify=e=>{var t,s;null==(s=(t=this.options).onChange)||s.call(t,this,e)},this.maybeNotify=c(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;let t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t)return void this.maybeNotify();this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{let s=new Map,i=new Map;for(let n=t-1;n>=0;n--){let t=e[n];if(s.has(t.lane))continue;let r=i.get(t.lane);if(null==r||t.end>r.end?i.set(t.lane,t):t.end<r.end&&s.set(t.lane,!0),s.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end)[0]:void 0},this.getMeasurementOptions=c(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,t,s,i,n)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:s,getItemKey:i,enabled:n}),{key:!1}),this.getMeasurements=c(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:t,scrollMargin:s,getItemKey:i,enabled:n},r)=>{if(!n)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(e=>{this.itemSizeCache.set(e.key,e.size)}));let l=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let a=this.measurementsCache.slice(0,l);for(let n=l;n<e;n++){let e=i(n),l=1===this.options.lanes?a[n-1]:this.getFurthestMeasurement(a,n),o=l?l.end+this.options.gap:t+s,d=r.get(e),c="number"==typeof d?d:this.options.estimateSize(n),h=o+c,u=l?l.lane:n%this.options.lanes;a[n]={index:n,start:o,size:c,end:h,key:e,lane:u}}return this.measurementsCache=a,a},{key:!1,debug:()=>this.options.debug}),this.calculateRange=c(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(e,t,s,i)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:s,lanes:i}){let n=e.length-1;if(e.length<=i)return{startIndex:0,endIndex:n};let r=S(0,n,t=>e[t].start,s),l=r;if(1===i)for(;l<n&&e[l].end<s+t;)l++;else if(i>1){let a=Array(i).fill(0);for(;l<n&&a.some(e=>e<s+t);){let t=e[l];a[t.lane]=t.end,l++}let o=Array(i).fill(s+t);for(;r>=0&&o.some(e=>e>=s);){let t=e[r];o[t.lane]=t.start,r--}r=Math.max(0,r-r%i),l=Math.min(n,l+(i-1-l%i))}return{startIndex:r,endIndex:l}}({measurements:e,outerSize:t,scrollOffset:s,lanes:i}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=c(()=>{let e=null,t=null,s=this.calculateRange();return s&&(e=s.startIndex,t=s.endIndex),this.maybeNotify.updateDeps([this.isScrolling,e,t]),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]},(e,t,s,i,n)=>null===i||null===n?[]:e({startIndex:i,endIndex:n,overscan:t,count:s}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{let t=this.options.indexAttribute,s=e.getAttribute(t);return s?parseInt(s,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{let s=this.indexFromElement(e),i=this.measurementsCache[s];if(!i)return;let n=i.key,r=this.elementsCache.get(n);r!==e&&(r&&this.observer.unobserve(r),this.observer.observe(e),this.elementsCache.set(n,e)),e.isConnected&&this.resizeItem(s,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{let s=this.measurementsCache[e];if(!s)return;let i=t-(this.itemSizeCache.get(s.key)??s.size);0!==i&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(s,i,this):s.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(s.index),this.itemSizeCache=new Map(this.itemSizeCache.set(s.key,t)),this.notify(!1))},this.measureElement=e=>{if(!e)return void this.elementsCache.forEach((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))});this._measureElement(e,void 0)},this.getVirtualItems=c(()=>[this.getVirtualIndexes(),this.getMeasurements()],(e,t)=>{let s=[];for(let i=0,n=e.length;i<n;i++){let n=t[e[i]];s.push(n)}return s},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{let t=this.getMeasurements();if(0!==t.length)return h(t[S(0,t.length-1,e=>h(t[e]).start,e)])},this.getOffsetForAlignment=(e,t,s=0)=>{let i=this.getSize(),n=this.getScrollOffset();return"auto"===t&&(t=e>=n+i?"end":"start"),"center"===t?e+=(s-i)/2:"end"===t&&(e-=i),Math.max(Math.min(this.getTotalSize()-i,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));let s=this.measurementsCache[e];if(!s)return;let i=this.getSize(),n=this.getScrollOffset();if("auto"===t)if(s.end>=n+i-this.options.scrollPaddingEnd)t="end";else{if(!(s.start<=n+this.options.scrollPaddingStart))return[n,t];t="start"}let r="end"===t?s.end+this.options.scrollPaddingEnd:s.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(r,t,s.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:s}={})=>{this.cancelScrollToIndex(),"smooth"===s&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:s})},this.scrollToIndex=(e,{align:t="auto",behavior:s}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===s&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let i=this.getOffsetForIndex(e,t);if(!i)return;let[n,r]=i;this._scrollToOffset(n,{adjustments:void 0,behavior:s}),"smooth"!==s&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){let[t]=h(this.getOffsetForIndex(e,r));u(t,this.getScrollOffset())||this.scrollToIndex(e,{align:r,behavior:s})}else this.scrollToIndex(e,{align:r,behavior:s})}))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;let t,s=this.getMeasurements();if(0===s.length)t=this.options.paddingStart;else if(1===this.options.lanes)t=(null==(e=s[s.length-1])?void 0:e.end)??0;else{let e=Array(this.options.lanes).fill(null),i=s.length-1;for(;i>=0&&e.some(e=>null===e);){let t=s[i];null===e[t.lane]&&(e[t.lane]=t.end),i--}t=Math.max(...e.filter(e=>null!==e))}return Math.max(t-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:s})=>{this.options.scrollToFn(e,{behavior:s,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}let S=(e,t,s,i)=>{for(;e<=t;){let n=(e+t)/2|0,r=s(n);if(r<i)e=n+1;else{if(!(r>i))return n;t=n-1}}return e>0?e-1:0},C="undefined"!=typeof document?n.useLayoutEffect:n.useEffect,M=function({items:e,height:t=400,width:s="100%",itemHeight:r=50,overscan:l=5,renderItem:a,keyExtractor:o,onEndReached:c,onEndReachedThreshold:h=.5,className:u="",itemClassName:m="",emptyComponent:x,loadingComponent:p,isLoading:g=!1}){let b=(0,n.useRef)(null),[v,j]=(0,n.useState)(!1),S=function(e){let t=n.useReducer(()=>({}),{})[1],s={...e,onChange:(s,i)=>{var n;i?(0,d.flushSync)(t):t(),null==(n=e.onChange)||n.call(e,s,i)}},[i]=n.useState(()=>new N(s));return i.setOptions(s),C(()=>i._didMount(),[]),C(()=>i._willUpdate()),i}({observeElementRect:f,observeElementOffset:y,scrollToFn:w,...{count:e.length,getScrollElement:()=>b.current,estimateSize:()=>r,overscan:l}}),M=(0,n.useCallback)(()=>{if(!c||v)return;let{scrollHeight:e,scrollTop:t,clientHeight:s}=b.current;t+s>=e*h&&(j(!0),c(),setTimeout(()=>{j(!1)},1e3))},[c,v,h]);return g&&p?(0,i.jsx)("div",{style:{height:t,width:s},children:p}):0===e.length&&x?(0,i.jsx)("div",{style:{height:t,width:s},children:x}):(0,i.jsx)("div",{ref:b,className:`overflow-auto ${u}`,style:{height:t,width:s},onScroll:c?M:void 0,children:(0,i.jsx)("div",{style:{height:`${S.getTotalSize()}px`,width:"100%",position:"relative"},children:S.getVirtualItems().map(t=>{let s=e[t.index],n=o(s,t.index);return(0,i.jsx)("div",{className:`absolute top-0 left-0 w-full ${m}`,style:{height:`${t.size}px`,transform:`translateY(${t.start}px)`},children:a(s,t.index)},n)})})})};var I=s(23877);function E(){let{data:e,status:t}=(0,r.useSession)();(0,l.useRouter)();let[s,d]=(0,n.useState)([]),[c,h]=(0,n.useState)(!0),[u,m]=(0,n.useState)("all"),[x,p]=(0,n.useState)({totalRebates:0,totalAmount:0,pendingAmount:0,processedAmount:0,failedAmount:0,pendingCount:0,processedCount:0,failedCount:0}),[g,f]=(0,n.useState)({startDate:"",endDate:""}),[b,v]=(0,n.useState)(!1),[y,j]=(0,n.useState)(1),[w,N]=(0,n.useState)(10),[S,C]=(0,n.useState)(1),[E,z]=(0,n.useState)(0),O=(0,n.useCallback)(async()=>{let e=new URLSearchParams;e.append("page",y.toString()),e.append("pageSize",w.toString()),"all"!==u&&e.append("status",u),g.startDate&&e.append("startDate",g.startDate),g.endDate&&e.append("endDate",g.endDate);let t=await fetch(`/api/rebates?${e.toString()}`);if(!t.ok)throw Error(`Failed to fetch rebates: ${t.statusText}`);let s=await t.json();return s.rebates&&Array.isArray(s.rebates)?{rebates:s.rebates,pagination:{totalPages:s.pagination.totalPages,totalItems:s.pagination.totalItems}}:{rebates:Array.isArray(s)?s:[],pagination:{totalPages:1,totalItems:Array.isArray(s)?s.length:0}}},[u,y,w,g]),D=(0,n.useCallback)(async()=>{let e=await fetch("/api/rebates/stats");if(!e.ok)throw Error(`Failed to fetch rebate stats: ${e.statusText}`);return await e.json()},[]),{data:k,isLoading:A,error:T}=(0,a.I)({queryKey:["rebates",u,y,w,g],queryFn:O,enabled:"authenticated"===t,staleTime:6e4,keepPreviousData:!0}),{data:R,isLoading:F}=(0,a.I)({queryKey:["rebateStats"],queryFn:D,enabled:"authenticated"===t,staleTime:3e5}),P=e=>{let{name:t,value:s}=e.target;f(e=>({...e,[t]:s}))},L=e=>{e>0&&e<=S&&j(e)};s.reduce((e,t)=>e+t.amount,0);let _=s.reduce((e,t)=>(e[t.level]=(e[t.level]||0)+t.amount,e),{});return"loading"===t||c?(0,i.jsx)(o.A,{children:(0,i.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,i.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,i.jsx)(o.A,{children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("h1",{className:"text-2xl font-semibold mb-6 flex items-center",children:[(0,i.jsx)(I.lcY,{className:"mr-2 text-blue-500"})," Rebate Earnings"]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,i.jsx)(I.lcY,{className:"h-5 w-5"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,i.jsxs)("p",{className:"text-xl font-semibold",children:["₱",x.totalAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:[x.totalRebates," transactions"]})]})]})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,i.jsx)(I.CMH,{className:"h-5 w-5"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Processed"}),(0,i.jsxs)("p",{className:"text-xl font-semibold",children:["₱",x.processedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:[x.processedCount," transactions"]})]})]})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4",children:(0,i.jsx)(I.DIg,{className:"h-5 w-5"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Pending"}),(0,i.jsxs)("p",{className:"text-xl font-semibold",children:["₱",x.pendingAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:[x.pendingCount," transactions"]})]})]})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 rounded-full bg-red-100 text-red-500 mr-4",children:(0,i.jsx)(I.QCr,{className:"h-5 w-5"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Failed"}),(0,i.jsxs)("p",{className:"text-xl font-semibold",children:["₱",x.failedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:[x.failedCount," transactions"]})]})]})})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,i.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,i.jsx)(I.YYR,{className:"mr-2 text-blue-500"})," Earnings by Level"]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[Object.entries(_).sort(([e],[t])=>parseInt(e)-parseInt(t)).map(([e,t])=>(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,i.jsxs)("p",{className:"text-sm text-gray-500 font-medium",children:["Level ",e]}),(0,i.jsxs)("p",{className:"text-xl font-semibold",children:["₱",t.toFixed(2)]})]},e)),0===Object.keys(_).length&&(0,i.jsx)("div",{className:"col-span-5 text-center py-4 text-gray-500",children:"No rebate data available for level breakdown"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,i.jsx)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("select",{value:u,onChange:e=>m(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,i.jsx)("option",{value:"all",children:"All Statuses"}),(0,i.jsx)("option",{value:"pending",children:"Pending"}),(0,i.jsx)("option",{value:"processed",children:"Processed"}),(0,i.jsx)("option",{value:"failed",children:"Failed"})]}),(0,i.jsxs)("button",{onClick:()=>v(!b),className:"px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center",children:[(0,i.jsx)(I.YsJ,{className:"mr-2"}),b?"Hide Date Filter":"Date Filter",b?(0,i.jsx)(I.Ucs,{className:"ml-2"}):(0,i.jsx)(I.Vr3,{className:"ml-2"})]})]})}),b&&(0,i.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,i.jsx)(I.bfZ,{className:"text-gray-400"})}),(0,i.jsx)("input",{type:"date",name:"startDate",value:g.startDate,onChange:P,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,i.jsx)(I.bfZ,{className:"text-gray-400"})}),(0,i.jsx)("input",{type:"date",name:"endDate",value:g.endDate,onChange:P,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]})]}),(0,i.jsx)("div",{className:"mt-4 flex justify-end",children:(0,i.jsx)("button",{onClick:()=>{m("all"),f({startDate:"",endDate:""})},className:"px-4 py-2 text-gray-700 hover:text-gray-900",children:"Reset Filters"})})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,i.jsx)("div",{className:"px-6 py-4 border-b",children:(0,i.jsx)("h2",{className:"text-lg font-semibold",children:"Rebate Transactions"})}),(0,i.jsx)("div",{className:"p-6",children:c?(0,i.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,i.jsx)(I.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading rebates..."})]}):s.length>0?(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"min-w-full border-b border-gray-200 mb-2",children:(0,i.jsxs)("div",{className:"grid grid-cols-8 gap-2",children:[(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"From"}),(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,i.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Processed At"})]})}),(0,i.jsx)(M,{items:s,height:500,itemHeight:70,overscan:5,keyExtractor:e=>e.id,renderItem:e=>(0,i.jsxs)("div",{className:"grid grid-cols-8 gap-2 border-b border-gray-200 hover:bg-gray-50",children:[(0,i.jsx)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,i.jsx)("div",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:(0,i.jsx)(I.x$1,{className:"text-gray-500"})}),(0,i.jsx)("div",{children:(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.generator.name})})]})}),(0,i.jsx)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.purchase.product.name}),(0,i.jsxs)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["Level ",e.level]}),(0,i.jsxs)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.percentage,"%"]}),(0,i.jsxs)("div",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600",children:["₱",e.amount.toFixed(2)]}),(0,i.jsx)("div",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"processed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,i.jsx)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.processedAt?new Date(e.processedAt).toLocaleString():"-"})]}),emptyComponent:(0,i.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No rebates found matching your criteria."}),loadingComponent:(0,i.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,i.jsx)(I.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading rebates..."})]}),onEndReached:()=>{y<S&&L(y+1)},onEndReachedThreshold:.8})]}):(0,i.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No rebates found matching your criteria."})}),s.length>0&&(0,i.jsxs)("div",{className:"px-6 py-4 border-t flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-sm text-gray-700 mr-2",children:"Rows per page:"}),(0,i.jsxs)("select",{value:w,onChange:e=>{N(parseInt(e.target.value)),j(1)},className:"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,i.jsx)("option",{value:"10",children:"10"}),(0,i.jsx)("option",{value:"25",children:"25"}),(0,i.jsx)("option",{value:"50",children:"50"}),(0,i.jsx)("option",{value:"100",children:"100"})]})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsxs)("span",{className:"text-sm text-gray-700 mr-4",children:[y," of ",S," pages (",E," total rebates)"]}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("button",{onClick:()=>L(1),disabled:1===y,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"First"}),(0,i.jsx)("button",{onClick:()=>L(y-1),disabled:1===y,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,i.jsx)("button",{onClick:()=>L(y+1),disabled:y===S,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"}),(0,i.jsx)("button",{onClick:()=>L(S),disabled:y===S,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Last"})]})]})]})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28575:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\rebates\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rebates\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47074:(e,t,s)=>{Promise.resolve().then(s.bind(s,28575))},49876:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>d});var i=s(65239),n=s(48088),r=s(88170),l=s.n(r),a=s(30893),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);s.d(t,o);let d={children:["",{children:["rebates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28575)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rebates\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rebates\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/rebates/page",pathname:"/rebates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65226:(e,t,s)=>{Promise.resolve().then(s.bind(s,14993))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[4243,8414,9567,3877,474,4859,9391,3024],()=>s(49876));module.exports=i})();
(()=>{var e={};e.id=8938,e.ids=[8938],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3775:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>G});var r=t(60687),s=t(43210),x=t(82136),d=t(16189),n=t(47081),c=t(23877),i=t(96330);global.prisma||new i.PrismaClient({log:["query"]});var o=t(94719),l=t(55511),f=null;function b(e,a){if("number"!=typeof(e=e||C))throw Error("Illegal arguments: "+typeof e+", "+typeof a);e<4?e=4:e>31&&(e=31);var t=[];return t.push("$2b$"),e<10&&t.push("0"),t.push(e.toString()),t.push("$"),t.push(j(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return l.randomBytes(e)}catch{}if(!f)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return f(e)}(E),E)),t.join("")}function u(e,a,t){if("function"==typeof a&&(t=a,a=void 0),"function"==typeof e&&(t=e,e=void 0),void 0===e)e=C;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function r(a){g(function(){try{a(null,b(e))}catch(e){a(e)}})}if(!t)return new Promise(function(e,a){r(function(t,r){if(t)return void a(t);e(r)})});if("function"!=typeof t)throw Error("Illegal callback: "+typeof t);r(t)}function m(e,a){if(void 0===a&&(a=C),"number"==typeof a&&(a=b(a)),"string"!=typeof e||"string"!=typeof a)throw Error("Illegal arguments: "+typeof e+", "+typeof a);return _(e,a)}function h(e,a,t,r){function s(t){"string"==typeof e&&"number"==typeof a?u(a,function(a,s){_(e,s,t,r)}):"string"==typeof e&&"string"==typeof a?_(e,a,t,r):g(t.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof a)))}if(!t)return new Promise(function(e,a){s(function(t,r){if(t)return void a(t);e(r)})});if("function"!=typeof t)throw Error("Illegal callback: "+typeof t);s(t)}function p(e,a){for(var t=e.length^a.length,r=0;r<e.length;++r)t|=e.charCodeAt(r)^a.charCodeAt(r);return 0===t}var g="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function v(e){for(var a=0,t=0,r=0;r<e.length;++r)(t=e.charCodeAt(r))<128?a+=1:t<2048?a+=2:(64512&t)==55296&&(64512&e.charCodeAt(r+1))==56320?(++r,a+=4):a+=3;return a}var y="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),w=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function j(e,a){var t,r,s=0,x=[];if(a<=0||a>e.length)throw Error("Illegal len: "+a);for(;s<a;){if(t=255&e[s++],x.push(y[t>>2&63]),t=(3&t)<<4,s>=a||(t|=(r=255&e[s++])>>4&15,x.push(y[63&t]),t=(15&r)<<2,s>=a)){x.push(y[63&t]);break}t|=(r=255&e[s++])>>6&3,x.push(y[63&t]),x.push(y[63&r])}return x.join("")}function N(e,a){var t,r,s,x,d,n=0,c=e.length,i=0,o=[];if(a<=0)throw Error("Illegal len: "+a);for(;n<c-1&&i<a&&(t=(d=e.charCodeAt(n++))<w.length?w[d]:-1,r=(d=e.charCodeAt(n++))<w.length?w[d]:-1,-1!=t&&-1!=r)&&(x=t<<2>>>0|(48&r)>>4,o.push(String.fromCharCode(x)),!(++i>=a||n>=c||-1==(s=(d=e.charCodeAt(n++))<w.length?w[d]:-1)||(x=(15&r)<<4>>>0|(60&s)>>2,o.push(String.fromCharCode(x)),++i>=a||n>=c)));){;x=(3&s)<<6>>>0|((d=e.charCodeAt(n++))<w.length?w[d]:-1),o.push(String.fromCharCode(x)),++i}var l=[];for(n=0;n<i;n++)l.push(o[n].charCodeAt(0));return l}var E=16,C=10,P=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],k=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],S=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function A(e,a,t,r){var s,x=e[a],d=e[a+1];return x^=t[0],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[1],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[2],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[3],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[4],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[5],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[6],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[7],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[8],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[9],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[10],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[11],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[12],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[13],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[14],d^=(r[x>>>24]+r[256|x>>16&255]^r[512|x>>8&255])+r[768|255&x]^t[15],x^=(r[d>>>24]+r[256|d>>16&255]^r[512|d>>8&255])+r[768|255&d]^t[16],e[a]=d^t[17],e[a+1]=x,e}function M(e,a){for(var t=0,r=0;t<4;++t)r=r<<8|255&e[a],a=(a+1)%e.length;return{key:r,offp:a}}function I(e,a,t){for(var r,s=0,x=[0,0],d=a.length,n=t.length,c=0;c<d;c++)s=(r=M(e,s)).offp,a[c]=a[c]^r.key;for(c=0;c<d;c+=2)x=A(x,0,a,t),a[c]=x[0],a[c+1]=x[1];for(c=0;c<n;c+=2)x=A(x,0,a,t),t[c]=x[0],t[c+1]=x[1]}function R(e,a,t,r,s){var x,d,n=S.slice(),c=n.length;if(t<4||t>31){if(d=Error("Illegal number of rounds (4-31): "+t),r)return void g(r.bind(this,d));throw d}if(a.length!==E){if(d=Error("Illegal salt length: "+a.length+" != "+E),r)return void g(r.bind(this,d));throw d}t=1<<t>>>0;var i,o,l,f=0;function b(){if(s&&s(f/t),f<t)for(var x=Date.now();f<t&&(f+=1,I(e,i,o),I(a,i,o),!(Date.now()-x>100)););else{for(f=0;f<64;f++)for(l=0;l<c>>1;l++)A(n,l<<1,i,o);var d=[];for(f=0;f<c;f++)d.push((n[f]>>24&255)>>>0),d.push((n[f]>>16&255)>>>0),d.push((n[f]>>8&255)>>>0),d.push((255&n[f])>>>0);return r?void r(null,d):d}r&&g(b)}if("function"==typeof Int32Array?(i=new Int32Array(P),o=new Int32Array(k)):(i=P.slice(),o=k.slice()),!function(e,a,t,r){for(var s,x=0,d=[0,0],n=t.length,c=r.length,i=0;i<n;i++)x=(s=M(a,x)).offp,t[i]=t[i]^s.key;for(i=0,x=0;i<n;i+=2)x=(s=M(e,x)).offp,d[0]^=s.key,x=(s=M(e,x)).offp,d[1]^=s.key,d=A(d,0,t,r),t[i]=d[0],t[i+1]=d[1];for(i=0;i<c;i+=2)x=(s=M(e,x)).offp,d[0]^=s.key,x=(s=M(e,x)).offp,d[1]^=s.key,d=A(d,0,t,r),r[i]=d[0],r[i+1]=d[1]}(a,e,i,o),void 0!==r)b();else for(;;)if(void 0!==(x=b()))return x||[]}function _(e,a,t,r){if("string"!=typeof e||"string"!=typeof a){if(s=Error("Invalid string / salt: Not a string"),t)return void g(t.bind(this,s));throw s}if("$"!==a.charAt(0)||"2"!==a.charAt(1)){if(s=Error("Invalid salt version: "+a.substring(0,2)),t)return void g(t.bind(this,s));throw s}if("$"===a.charAt(2))x="\0",d=3;else{if("a"!==(x=a.charAt(2))&&"b"!==x&&"y"!==x||"$"!==a.charAt(3)){if(s=Error("Invalid salt revision: "+a.substring(2,4)),t)return void g(t.bind(this,s));throw s}d=4}if(a.charAt(d+2)>"$"){if(s=Error("Missing salt rounds"),t)return void g(t.bind(this,s));throw s}var s,x,d,n=10*parseInt(a.substring(d,d+1),10)+parseInt(a.substring(d+1,d+2),10),c=a.substring(d+3,d+25),i=function(e){for(var a,t,r=0,s=Array(v(e)),x=0,d=e.length;x<d;++x)(a=e.charCodeAt(x))<128?s[r++]=a:(a<2048?s[r++]=a>>6|192:((64512&a)==55296&&(64512&(t=e.charCodeAt(x+1)))==56320?(a=65536+((1023&a)<<10)+(1023&t),++x,s[r++]=a>>18|240,s[r++]=a>>12&63|128):s[r++]=a>>12|224,s[r++]=a>>6&63|128),s[r++]=63&a|128);return s}(e+=x>="a"?"\0":""),o=N(c,E);function l(e){var a=[];return a.push("$2"),x>="a"&&a.push(x),a.push("$"),n<10&&a.push("0"),a.push(n.toString()),a.push("$"),a.push(j(o,o.length)),a.push(j(e,4*S.length-1)),a.join("")}if(void 0===t)return l(R(i,o,n));R(i,o,n,function(e,a){e?t(e,null):t(null,l(a))},r)}let L={setRandomFallback:function(e){f=e},genSaltSync:b,genSalt:u,hashSync:m,hash:h,compareSync:function(e,a){if("string"!=typeof e||"string"!=typeof a)throw Error("Illegal arguments: "+typeof e+", "+typeof a);return 60===a.length&&p(m(e,a.substring(0,a.length-31)),a)},compare:function(e,a,t,r){function s(t){return"string"!=typeof e||"string"!=typeof a?void g(t.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof a))):60!==a.length?void g(t.bind(this,null,!1)):void h(e,a.substring(0,29),function(e,r){e?t(e):t(null,p(r,a))},r)}if(!t)return new Promise(function(e,a){s(function(t,r){if(t)return void a(t);e(r)})});if("function"!=typeof t)throw Error("Illegal callback: "+typeof t);s(t)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return v(e)>72},encodeBase64:function(e,a){return j(e,a)},decodeBase64:function(e,a){return N(e,a)}};new i.PrismaClient,(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,a){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let a=new i.PrismaClient,t=await a.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await a.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let r=await L.compare(e.password,t.password);if(console.log("Password valid:",r),!r)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:s,...x}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}}),process.env.NEXTAUTH_SECRET;var T=function(e){return e.NEW_MEMBER="new_member",e.ESTABLISHED_MEMBER="established_member",e.HIGH_PERFORMER="high_performer",e.EDGE_CASES="edge_cases",e}({});function G(){let{data:e,status:a}=(0,x.useSession)(),t=(0,d.useRouter)(),[i,o]=(0,s.useState)(T.NEW_MEMBER),[l,f]=(0,s.useState)(1),[b,u]=(0,s.useState)("test"),[m,h]=(0,s.useState)(`cleanup_${Date.now()}`),[p,g]=(0,s.useState)(!1),[v,y]=(0,s.useState)(null),[w,j]=(0,s.useState)(null),[N,E]=(0,s.useState)([]),[C,P]=(0,s.useState)(null),[k,S]=(0,s.useState)(!1),[A,M]=(0,s.useState)(null),[I,R]=(0,s.useState)(null),[_,L]=(0,s.useState)(0),[G,D]=(0,s.useState)(""),[H,q]=(0,s.useState)(!1),[B,F]=(0,s.useState)(null);if("unauthenticated"===a)return t.push("/login?returnUrl=/admin/test-data"),null;if("loading"===a)return(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})});if(e?.user?.role!=="admin")return(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,r.jsxs)("h1",{className:"text-red-700 text-lg font-semibold flex items-center",children:[(0,r.jsx)(c.BS8,{className:"mr-2"}),"Access Denied"]}),(0,r.jsx)("p",{className:"mt-2",children:"You do not have permission to access this page. This page is restricted to administrators only."})]})});let U=async()=>{try{g(!0),y(null),j(null),E([]),P(null);let e=await fetch("/api/admin/test-data/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({scenario:i,count:l,prefix:b,cleanupToken:m})}),a=await e.json();if(!e.ok)throw Error(a.error||"Failed to generate test data");j(a.message),E(a.users||[]),P(a.stats||null)}catch(e){console.error("Error generating test data:",e),y(e instanceof Error?e.message:String(e))}finally{g(!1)}},W=async()=>{try{S(!0),M(null),R(null),L(0);let e=G||m,a=await fetch("/api/admin/test-data/cleanup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({cleanupToken:e})}),t=await a.json();if(!a.ok)throw Error(t.error||"Failed to clean up test data");R(t.message),L(t.count||0),q(!1),e===m&&(E([]),P(null))}catch(e){console.error("Error cleaning up test data:",e),M(e instanceof Error?e.message:String(e))}finally{S(!1)}},$=(e,a)=>{navigator.clipboard.writeText(e),F(a),setTimeout(()=>F(null),2e3)},O=e=>{switch(e){case T.NEW_MEMBER:return(0,r.jsx)(c.NPy,{className:"text-green-500"});case T.ESTABLISHED_MEMBER:return(0,r.jsx)(c.YXz,{className:"text-blue-500"});case T.HIGH_PERFORMER:return(0,r.jsx)(c.p7b,{className:"text-purple-500"});case T.EDGE_CASES:return(0,r.jsx)(c.XiY,{className:"text-red-500"});default:return(0,r.jsx)(c.YXz,{className:"text-gray-500"})}};return(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Data Generator"}),(0,r.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.BS8,{className:"h-5 w-5 text-yellow-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,r.jsx)("strong",{children:"Warning:"})," This tool is for testing purposes only. Generated data will be marked as test data and can be cleaned up using the cleanup token."]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsx)("h2",{className:"font-medium text-gray-700",children:"Generate Test Data"})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Test Scenario"}),(0,r.jsxs)("select",{className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:i,onChange:e=>o(e.target.value),children:[(0,r.jsx)("option",{value:T.NEW_MEMBER,children:"New Member"}),(0,r.jsx)("option",{value:T.ESTABLISHED_MEMBER,children:"Established Member"}),(0,r.jsx)("option",{value:T.HIGH_PERFORMER,children:"High Performer"}),(0,r.jsx)("option",{value:T.EDGE_CASES,children:"Edge Cases"})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:(e=>{switch(e){case T.NEW_MEMBER:return"New member with no downline, no purchases, and no rebates";case T.ESTABLISHED_MEMBER:return"Established member with moderate downline (3-5), purchases (5-10), and rebates";case T.HIGH_PERFORMER:return"High-performing member with large downline (20-30), many purchases (20-30), and high earnings";case T.EDGE_CASES:return"Edge cases with unusual data patterns (very long names, extreme values, etc.)";default:return""}})(i)})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Number of Users"}),(0,r.jsx)("input",{type:"number",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:l,onChange:e=>f(Math.max(1,parseInt(e.target.value)||1)),min:"1",max:"10"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Maximum 10 users per generation"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Prefix"}),(0,r.jsx)("input",{type:"text",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:b,onChange:e=>u(e.target.value)}),(0,r.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:["Emails will be generated as ",b,"_",i,"<EMAIL>, etc."]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cleanup Token"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",className:"flex-1 border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:m,onChange:e=>h(e.target.value)}),(0,r.jsx)("button",{type:"button",className:"inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100",onClick:()=>h(`cleanup_${Date.now()}`),title:"Generate new token",children:(0,r.jsx)(c.pXu,{})})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Save this token to clean up the generated test data later"})]}),(0,r.jsx)("button",{type:"button",className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:U,disabled:p,children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.hW,{className:"animate-spin mr-2"}),"Generating..."]}):(0,r.jsx)(r.Fragment,{children:"Generate Test Data"})}),v&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm",children:v}),w&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-green-50 text-green-700 rounded-md text-sm",children:w})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsx)("h2",{className:"font-medium text-gray-700",children:"Clean Up Test Data"})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cleanup Token"}),(0,r.jsx)("input",{type:"text",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",value:G,onChange:e=>D(e.target.value),placeholder:"Enter cleanup token"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Enter the cleanup token used when generating the test data"})]}),H?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"mb-2 text-sm text-red-600 font-medium",children:"Are you sure you want to clean up all test data with this token?"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{type:"button",className:"flex-1 flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",onClick:W,disabled:k,children:k?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.hW,{className:"animate-spin mr-2"}),"Cleaning..."]}):(0,r.jsx)(r.Fragment,{children:"Yes, Clean Up"})}),(0,r.jsx)("button",{type:"button",className:"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>q(!1),disabled:k,children:"Cancel"})]})]}):(0,r.jsxs)("button",{type:"button",className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",onClick:()=>q(!0),disabled:k||!G&&!m,children:[(0,r.jsx)(c.qbC,{className:"mr-2"}),"Clean Up Test Data"]}),A&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm",children:A}),I&&(0,r.jsxs)("div",{className:"mt-4 p-3 bg-green-50 text-green-700 rounded-md text-sm",children:[I,_>0&&(0,r.jsxs)("p",{className:"mt-1 font-medium",children:[_," items deleted"]})]})]})]})]}),N.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsx)("h2",{className:"font-medium text-gray-700",children:"Generated Test Users"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Password"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Scenario"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map((e,a)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center",children:O(e.scenario)}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.email}),(0,r.jsx)("button",{className:"text-xs text-blue-600 hover:text-blue-800 flex items-center mt-1",onClick:()=>$(e.email,3*a),children:B===3*a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.aZA,{className:"mr-1"}),"Copied!"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.Y4c,{className:"mr-1"}),"Copy"]})})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.password}),(0,r.jsx)("button",{className:"text-xs text-blue-600 hover:text-blue-800 flex items-center mt-1",onClick:()=>$(e.password,3*a+1),children:B===3*a+1?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.aZA,{className:"mr-1"}),"Copied!"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.Y4c,{className:"mr-1"}),"Copy"]})})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                          ${e.scenario===T.NEW_MEMBER?"bg-green-100 text-green-800":e.scenario===T.ESTABLISHED_MEMBER?"bg-blue-100 text-blue-800":e.scenario===T.HIGH_PERFORMER?"bg-purple-100 text-purple-800":"bg-red-100 text-red-800"}`,children:e.scenario.replace("_"," ")})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsxs)("button",{className:"text-blue-600 hover:text-blue-800 flex items-center",onClick:()=>{let a=`/login?email=${encodeURIComponent(e.email)}&password=${encodeURIComponent(e.password)}`;window.open(a,"_blank")},children:["Login as User",(0,r.jsx)(c.Z0P,{className:"ml-1"})]})})]},e.id))})]})})]}),C&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsx)("h2",{className:"font-medium text-gray-700",children:"Test Data Statistics"})}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-blue-500 text-xl font-semibold",children:C.usersCreated}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Created"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-500 text-xl font-semibold",children:C.downlinesCreated}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Downlines Created"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-purple-500 text-xl font-semibold",children:C.purchasesCreated}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Purchases Created"})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-yellow-600 text-xl font-semibold",children:C.rebatesGenerated}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Rebates Generated"})]}),(0,r.jsxs)("div",{className:"bg-indigo-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-indigo-500 text-xl font-semibold",children:C.referralLinksCreated}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Referral Links Created"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsxs)("h2",{className:"font-medium text-gray-700 flex items-center",children:[(0,r.jsx)(c.__w,{className:"mr-2 text-blue-500"}),"Test Scenarios"]})}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,r.jsx)(c.NPy,{className:"mr-2 text-green-500"}),"New Member"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates a new member with no downline, no purchases, and no rebates. This is useful for testing the onboarding experience and empty states in the dashboard."})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,r.jsx)(c.YXz,{className:"mr-2 text-blue-500"}),"Established Member"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates a member with a moderate downline (3-5 members), purchases (5-10), and rebates. This is useful for testing the typical user experience with some activity."})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,r.jsx)(c.p7b,{className:"mr-2 text-purple-500"}),"High Performer"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates a high-performing member with a large downline (20-30 members), many purchases (20-30), and high earnings. This is useful for testing performance with large data sets and formatting of large numbers."})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,r.jsx)(c.XiY,{className:"mr-2 text-red-500"}),"Edge Cases"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Creates users with unusual data patterns, such as very long names, extreme values, etc. This is useful for testing edge cases and error handling."})]})]})})]})]})})}},5836:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>d.a,__next_app__:()=>l,pages:()=>o,routeModule:()=>f,tree:()=>i});var r=t(65239),s=t(48088),x=t(88170),d=t.n(x),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(a,c);let i={children:["",{children:["admin",{children:["test-data",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,76296)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-data\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-data\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/test-data/page",pathname:"/admin/test-data",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},10192:(e,a,t)=>{Promise.resolve().then(t.bind(t,3775))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,a,t)=>{"use strict";var r=t(65773);t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,a,t)=>{"use strict";t.d(a,{AuthProvider:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,a,t)=>{"use strict";t.d(a,{CartProvider:()=>n,_:()=>d});var r=t(60687),s=t(43210);let x=(0,s.createContext)(void 0),d=()=>{let e=(0,s.useContext)(x);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},n=({children:e})=>{let[a,t]=(0,s.useState)([]);(0,s.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,s.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(a))},[a]);let d=e=>{t(a=>a.filter(a=>a.id!==e))},n=a.reduce((e,a)=>e+a.quantity,0),c=a.reduce((e,a)=>e+a.price*a.quantity,0),i=a.reduce((e,a)=>e+a.pv*a.quantity,0);return(0,r.jsx)(x.Provider,{value:{items:a,addItem:e=>{t(a=>{let t=a.findIndex(a=>a.id===e.id);if(!(t>=0))return[...a,e];{let r=[...a];return r[t]={...r[t],quantity:r[t].quantity+e.quantity},r}})},removeItem:d,updateQuantity:(e,a)=>{if(a<=0)return void d(e);t(t=>t.map(t=>t.id===e?{...t,quantity:a}:t))},clearCart:()=>{t([])},itemCount:n,subtotal:c,totalPV:i},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,a,t)=>{"use strict";t.d(a,{CartProvider:()=>s});var r=t(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,a,t)=>{"use strict";t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},45851:(e,a,t)=>{"use strict";t.d(a,{default:()=>n});var r=t(60687),s=t(25217),x=t(8693),d=t(43210);function n({children:e}){let[a]=(0,d.useState)(()=>new s.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(x.Ht,{client:a,children:e})}},52048:(e,a,t)=>{Promise.resolve().then(t.bind(t,76296))},55511:e=>{"use strict";e.exports=require("crypto")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,a,t)=>{"use strict";t.d(a,{default:()=>i});var r=t(60687),s=t(43210);let x=()=>"serviceWorker"in navigator,d=async()=>{if(!x())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),n(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},n=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let a=e.installing;a&&a.addEventListener("statechange",()=>{"installed"===a.state&&navigator.serviceWorker.controller&&c()})})},c=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},i=({children:e})=>{let[a,t]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{d();let e=()=>{t(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,a&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,a,t)=>{Promise.resolve().then(t.bind(t,37043)),Promise.resolve().then(t.bind(t,23229)),Promise.resolve().then(t.bind(t,82113)),Promise.resolve().then(t.bind(t,41750))},70440:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});var r=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,a,t)=>{Promise.resolve().then(t.bind(t,28253)),Promise.resolve().then(t.bind(t,97695)),Promise.resolve().then(t.bind(t,45851)),Promise.resolve().then(t.bind(t,63345))},76296:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\test-data\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\test-data\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},82113:(e,a,t)=>{"use strict";t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94431:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>b,metadata:()=>f});var r=t(37413),s=t(22376),x=t.n(s),d=t(68726),n=t.n(d);t(61135);var c=t(23229),i=t(37043),o=t(82113),l=t(41750);let f={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function b({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${x().variable} ${n().variable} antialiased`,children:(0,r.jsx)(c.AuthProvider,{children:(0,r.jsx)(o.default,{children:(0,r.jsx)(i.CartProvider,{children:(0,r.jsx)(l.default,{children:e})})})})})})}},94719:(e,a)=>{"use strict";a.A=function(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},96111:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},96330:e=>{"use strict";e.exports=require("@prisma/client")},97695:(e,a,t)=>{"use strict";t.d(a,{AuthProvider:()=>x});var r=t(60687),s=t(82136);function x({children:e}){return(0,r.jsx)(s.SessionProvider,{children:e})}}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[4243,8414,9567,3877,474,7081],()=>t(5836));module.exports=r})();
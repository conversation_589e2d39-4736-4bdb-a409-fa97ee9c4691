"use strict";exports.id=4190,exports.ids=[4190],exports.modules={54190:(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});var s=n(60687),i=n(43210),a=n(85814),r=n.n(a),l=n(23877);let o=({events:e=[]})=>{let[t,n]=(0,i.useState)("all"),a=e.length>0?e:[{id:"1",title:"Product Training Webinar",date:"2023-07-15",time:"2:00 PM - 4:00 PM",type:"training",description:"Learn about our new Biogen Shield Herbal Care Soap and its benefits.",link:"/events/product-training"},{id:"2",title:"Summer Sales Promotion",date:"2023-07-01",time:"12:00 AM",type:"promotion",description:"Special summer promotion with increased PV for all products.",link:"/promotions/summer-sales"},{id:"3",title:"Monthly Distributor Meeting",date:"2023-07-20",time:"6:00 PM - 8:00 PM",location:"Zoom Meeting",type:"meeting",description:"Monthly meeting to discuss business updates and strategies.",link:"/events/monthly-meeting"},{id:"4",title:"Recognition Day",date:"2023-07-30",time:"3:00 PM - 6:00 PM",location:"Grand Ballroom, Manila Hotel",type:"recognition",description:"Celebrating our top performers and rank advancements.",link:"/events/recognition-day"},{id:"5",title:"New Compensation Plan Announcement",date:"2023-07-10",type:"announcement",description:"Important updates to our compensation structure.",link:"/announcements/compensation-update"}],o=[..."all"===t?a:a.filter(e=>e.type===t)].sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()),c=e=>new Date(e).toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"}),d=e=>{switch(e){case"training":return(0,s.jsx)(l.YNd,{className:"text-blue-500"});case"promotion":return(0,s.jsx)(l.Wp,{className:"text-purple-500"});case"meeting":return(0,s.jsx)(l.YXz,{className:"text-green-500"});case"recognition":return(0,s.jsx)(l.SBv,{className:"text-yellow-500"});case"announcement":return(0,s.jsx)(l.sdT,{className:"text-red-500"});default:return(0,s.jsx)(l.bfZ,{className:"text-gray-500"})}};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,s.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-b flex justify-between items-center",children:[(0,s.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,s.jsx)(l.bfZ,{className:"mr-2 text-blue-500"})," Upcoming Events"]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(l.YsJ,{className:"text-gray-400 mr-2"}),(0,s.jsxs)("select",{value:t,onChange:e=>n(e.target.value),className:"text-sm border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",children:[(0,s.jsx)("option",{value:"all",children:"All Events"}),(0,s.jsx)("option",{value:"training",children:"Training"}),(0,s.jsx)("option",{value:"promotion",children:"Promotions"}),(0,s.jsx)("option",{value:"meeting",children:"Meetings"}),(0,s.jsx)("option",{value:"recognition",children:"Recognition"}),(0,s.jsx)("option",{value:"announcement",children:"Announcements"})]})]})]}),(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:o.length>0?o.map(e=>(0,s.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:d(e.type)}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("h4",{className:"text-base font-medium text-gray-900",children:e.title}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:c(e.date)})]}),(e.time||e.location)&&(0,s.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:[e.time&&(0,s.jsx)("span",{children:e.time}),e.time&&e.location&&(0,s.jsx)("span",{children:" • "}),e.location&&(0,s.jsx)("span",{children:e.location})]}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:e.description}),e.link&&(0,s.jsxs)(r(),{href:e.link,className:"mt-2 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800",children:["Learn more ",(0,s.jsx)(l.X6T,{className:"ml-1 h-3 w-3"})]})]})]})},e.id)):(0,s.jsx)("div",{className:"p-6 text-center",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No upcoming events found."})})}),(0,s.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t text-center",children:(0,s.jsx)(r(),{href:"/events",className:"text-sm font-medium text-blue-600 hover:text-blue-800",children:"View All Events"})})]})}}};
(()=>{var e={};e.id=9789,e.ids=[9789],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>l,aP:()=>d});var s=t(96330),o=t(13581),a=t(85663),n=t(55511),i=t.n(n);async function u(e){return await a.Ay.hash(e,10)}function d(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let l={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await a.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:n,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},32602:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>p});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),u=t(35426),d=t(12909),l=t(31183);async function c(e){try{let r=await (0,u.getServerSession)(d.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to view orders"},{status:401});let t=await l.z.user.findUnique({where:{email:r.user.email}});if(!t)return i.NextResponse.json({error:"User not found"},{status:404});let s=new URL(e.url),o=parseInt(s.searchParams.get("page")||"1"),a=parseInt(s.searchParams.get("limit")||"10"),n=s.searchParams.get("status"),c=(o-1)*a,p={userId:t.id,...n?{status:n}:{}},[g,m]=await Promise.all([l.z.order.findMany({where:p,include:{shippingAddress:!0,shippingMethod:!0,purchases:{include:{product:!0}},payments:!0},orderBy:{createdAt:"desc"},skip:c,take:a}),l.z.order.count({where:p})]),h=Math.ceil(m/a);return i.NextResponse.json({orders:g,pagination:{page:o,limit:a,total:m,totalPages:h,hasNextPage:o<h,hasPrevPage:o>1}})}catch(e){return console.error("Error fetching orders:",e),i.NextResponse.json({error:"An error occurred while fetching orders"},{status:500})}}async function p(e){try{let r=await e.json(),{isGuestOrder:t,customerName:s,customerEmail:o,customerPhone:a,guestShippingAddress:n}=r,c=null;if(t){if(!s||!o||!a||!n)return i.NextResponse.json({error:"Guest information is required for guest orders"},{status:400})}else{let e=await (0,u.getServerSession)(d.Nh);if(!e||!e.user)return i.NextResponse.json({error:"You must be logged in to create a member order"},{status:401});let r=await l.z.user.findUnique({where:{email:e.user.email}});if(!r)return i.NextResponse.json({error:"User not found"},{status:404});c=r.id}let{items:p,shippingAddressId:g,shippingMethodId:m,paymentMethod:h,subtotal:w,shippingFee:f,discount:x=0,total:y,notes:q}=r;if(!p||!Array.isArray(p)||0===p.length)return i.NextResponse.json({error:"Order must contain at least one item"},{status:400});if(!t&&!g)return i.NextResponse.json({error:"Shipping address is required"},{status:400});if(!m)return i.NextResponse.json({error:"Shipping method is required"},{status:400});if(!h)return i.NextResponse.json({error:"Payment method is required"},{status:400});if(t&&"wallet"===h)return i.NextResponse.json({error:"Wallet payment is not available for guest orders"},{status:400});let v=function(){let e=new Date,r=e.getFullYear().toString()+(e.getMonth()+1).toString().padStart(2,"0")+e.getDate().toString().padStart(2,"0"),t=Math.floor(1e5*Math.random()).toString().padStart(5,"0");return`ELH-${r}-${t}`}(),b=await l.z.$transaction(async e=>{let r=g;t&&n&&(r=(await e.shippingAddress.create({data:{name:n.name,phone:n.phone,email:n.email,addressLine1:n.addressLine1,addressLine2:n.addressLine2||null,city:n.city,region:n.region,postalCode:n.postalCode,isGuestAddress:!0}})).id);let i=await e.order.create({data:{userId:c,orderNumber:v,customerName:t?s:null,customerEmail:t?o:null,customerPhone:t?a:null,isGuestOrder:t,subtotal:w,shippingFee:f,discount:x,total:y,status:"pending",paymentStatus:"pending",notes:q,shippingAddressId:r,shippingMethodId:m}});for(let r of p){let{productId:t,quantity:s,price:o,priceType:a}=r;if(!await e.product.findUnique({where:{id:t}}))throw Error(`Product with ID ${t} not found`);await e.purchase.create({data:{userId:c,productId:t,quantity:s,totalAmount:o*s,priceType:a||"member",status:"pending",orderId:i.id}})}return await e.payment.create({data:{orderId:i.id,amount:y,paymentMethod:h,status:"pending"}}),i});return i.NextResponse.json({message:"Order created successfully",orderId:b.id,orderNumber:b.orderNumber})}catch(e){return console.error("Error creating order:",e),i.NextResponse.json({error:"An error occurred while creating the order"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\orders\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:w}=g;function f(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(32602));module.exports=s})();
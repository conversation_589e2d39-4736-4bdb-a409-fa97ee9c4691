(()=>{var e={};e.id=9185,e.ids=[9185],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>d,aP:()=>l});var s=t(96330),o=t(13581),n=t(85663),i=t(55511),a=t.n(i);async function u(e){return await n.Ay.hash(e,10)}function l(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let d={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:i,...a}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var a=o?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s.default=e,t&&t.set(e,s),s}(t(35426));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49878:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>b,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>f,GET:()=>c,PUT:()=>p});var o=t(96559),n=t(48088),i=t(37719),a=t(31183),u=t(32190),l=t(19854),d=t(12909);async function c(e,{params:r}){try{let e=await (0,l.getServerSession)(d.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to view rebate configurations"},{status:401});let t=e.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await a.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});if(6!==s.rankId)return u.NextResponse.json({error:"You do not have permission to view rebate configurations"},{status:403});let o=parseInt(r.id),n=await a.z.rebateConfig.findUnique({where:{id:o},include:{product:!0}});if(!n)return u.NextResponse.json({error:`Rebate configuration with ID ${o} not found`},{status:404});return u.NextResponse.json({rebateConfig:n})}catch(e){return console.error("Error fetching rebate configuration:",e),u.NextResponse.json({error:"Failed to fetch rebate configuration"},{status:500})}}async function p(e,{params:r}){try{let t=await (0,l.getServerSession)(d.Nh);if(!t||!t.user)return u.NextResponse.json({error:"You must be logged in to update rebate configurations"},{status:401});let s=t.user.email;if(!s)return u.NextResponse.json({error:"User email not found in session"},{status:400});let o=await a.z.user.findUnique({where:{email:s},select:{id:!0,rankId:!0}});if(!o)return u.NextResponse.json({error:"User not found"},{status:404});if(6!==o.rankId)return u.NextResponse.json({error:"You do not have permission to update rebate configurations"},{status:403});let n=parseInt(r.id),i=await a.z.rebateConfig.findUnique({where:{id:n}});if(!i)return u.NextResponse.json({error:`Rebate configuration with ID ${n} not found`},{status:404});let{productId:c,level:p,rewardType:f,percentage:g,fixedAmount:w}=await e.json();if(!c||!p||!f)return u.NextResponse.json({error:"Missing required fields: productId, level, and rewardType"},{status:400});if("percentage"!==f&&"fixed"!==f)return u.NextResponse.json({error:"Invalid reward type. Must be 'percentage' or 'fixed'"},{status:400});if("percentage"===f&&(void 0===g||g<0||g>100))return u.NextResponse.json({error:"Invalid percentage. Must be between 0 and 100"},{status:400});if("fixed"===f&&(void 0===w||w<0))return u.NextResponse.json({error:"Invalid fixed amount. Must be greater than or equal to 0"},{status:400});if(!await a.z.product.findUnique({where:{id:c}}))return u.NextResponse.json({error:`Product with ID ${c} not found`},{status:404});if(c!==i.productId||p!==i.level){let e=await a.z.rebateConfig.findUnique({where:{productId_level:{productId:c,level:p}}});if(e&&e.id!==n)return u.NextResponse.json({error:`A configuration already exists for product ${c} and level ${p}`},{status:409})}let x=await a.z.rebateConfig.update({where:{id:n},data:{productId:c,level:p,rewardType:f,percentage:"percentage"===f?g:0,fixedAmount:"fixed"===f?w:0},include:{product:!0}});return u.NextResponse.json({rebateConfig:x})}catch(e){return console.error("Error updating rebate configuration:",e),u.NextResponse.json({error:"Failed to update rebate configuration"},{status:500})}}async function f(e,{params:r}){try{let e=await (0,l.getServerSession)(d.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to delete rebate configurations"},{status:401});let t=e.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await a.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});if(6!==s.rankId)return u.NextResponse.json({error:"You do not have permission to delete rebate configurations"},{status:403});let o=parseInt(r.id);if(!await a.z.rebateConfig.findUnique({where:{id:o}}))return u.NextResponse.json({error:`Rebate configuration with ID ${o} not found`},{status:404});return await a.z.rebateConfig.delete({where:{id:o}}),u.NextResponse.json({success:!0})}catch(e){return console.error("Error deleting rebate configuration:",e),u.NextResponse.json({error:"Failed to delete rebate configuration"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/rebate-configs/[id]/route",pathname:"/api/admin/rebate-configs/[id]",filename:"route",bundlePath:"app/api/admin/rebate-configs/[id]/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rebate-configs\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:x,serverHooks:b}=g;function h(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:x})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(49878));module.exports=s})();
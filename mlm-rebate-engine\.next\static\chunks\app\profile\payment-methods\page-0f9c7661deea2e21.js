(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2006],{28584:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(95155),r=s(12115),n=s(12108),l=s(35695),d=s(70357),i=s(29911);function o(){let{data:e,status:t}=(0,n.useSession)(),s=(0,l.useRouter)(),[o,c]=(0,r.useState)(!0),[m,u]=(0,r.useState)([]),[h,x]=(0,r.useState)([]),[y,p]=(0,r.useState)({type:"",text:""}),[f,b]=(0,r.useState)(!1),[g,j]=(0,r.useState)(null),[v,N]=(0,r.useState)({}),[w,S]=(0,r.useState)(!1);(0,r.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,r.useEffect)(()=>{"authenticated"===t&&E()},[t]);let E=async()=>{c(!0),p({type:"",text:""});try{let e=await fetch("/api/payment-methods?includeUserMethods=true");if(!e.ok)throw Error("Failed to fetch payment methods: ".concat(e.statusText));let t=await e.json();u(t.paymentMethods||[]),x(t.userPaymentMethods||[])}catch(e){console.error("Error fetching payment methods:",e),p({type:"error",text:"Failed to load payment methods. Please try again."})}finally{c(!1)}},k=()=>{b(!0),j(null),N({})},C=async()=>{if(!g)return void p({type:"error",text:"Please select a payment method"});S(!0),p({type:"",text:""});try{let e=await fetch("/api/payment-methods/user",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:g,details:v,isDefault:0===h.length})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to add payment method: ".concat(e.statusText))}await E(),b(!1),p({type:"success",text:"Payment method added successfully"})}catch(e){console.error("Error adding payment method:",e),p({type:"error",text:e instanceof Error?e.message:"Failed to add payment method"})}finally{S(!1)}},M=async e=>{if(confirm("Are you sure you want to delete this payment method?")){c(!0),p({type:"",text:""});try{let t=await fetch("/api/payment-methods/user?id=".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete payment method: ".concat(t.statusText));await E(),p({type:"success",text:"Payment method deleted successfully"})}catch(e){console.error("Error deleting payment method:",e),p({type:"error",text:"Failed to delete payment method"})}finally{c(!1)}}},D=async e=>{c(!0),p({type:"",text:""});try{let t=await fetch("/api/payment-methods/user",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:e,isDefault:!0})});if(!t.ok)throw Error("Failed to set default payment method: ".concat(t.statusText));await E(),p({type:"success",text:"Default payment method updated successfully"})}catch(e){console.error("Error setting default payment method:",e),p({type:"error",text:"Failed to set default payment method"})}finally{c(!1)}};return"loading"===t||o?(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(i.hW,{className:"animate-spin text-green-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})}):(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Payment Methods"}),(0,a.jsxs)("button",{type:"button",onClick:k,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[(0,a.jsx)(i.OiG,{className:"inline mr-2"}),"Add Payment Method"]})]}),y.text&&(0,a.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("error"===y.type?"bg-red-100 text-red-700":"success"===y.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:y.text}),f&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Add Payment Method"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Method"}),(0,a.jsxs)("select",{value:g||"",onChange:e=>j(e.target.value?parseInt(e.target.value):null),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select a payment method"}),m.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),g&&(e=>{if(!e.requiresDetails||!e.detailsSchema)return null;try{let t=JSON.parse(e.detailsSchema);if(!t.properties)return null;return(0,a.jsx)("div",{className:"mt-4 space-y-4",children:Object.entries(t.properties).map(e=>{var s,r;let[n,l]=e;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[l.description||n,(null==(s=t.required)?void 0:s.includes(n))&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",value:v[n]||"",onChange:e=>N({...v,[n]:e.target.value}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",required:null==(r=t.required)?void 0:r.includes(n)})]},n)})})}catch(e){return console.error("Error parsing schema:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing schema"})}})(m.find(e=>e.id===g)),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{b(!1),j(null),N({})},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:C,disabled:w||!g,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):"Save"})]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:(0,a.jsx)("div",{className:"p-6",children:0===h.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{children:"You don't have any payment methods yet."}),(0,a.jsxs)("button",{type:"button",onClick:k,className:"mt-4 text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(i.OiG,{className:"inline mr-2"}),"Add your first payment method"]})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:h.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 ".concat(e.isDefault?"border-green-500 bg-green-50":"border-gray-200"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"mr-3",children:["gcash"===e.paymentMethod.code&&(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold",children:"G"}),"maya"===e.paymentMethod.code&&(0,a.jsx)("div",{className:"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold",children:"M"}),"cash"===e.paymentMethod.code&&(0,a.jsx)("div",{className:"w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold",children:"₱"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:e.paymentMethod.name}),e.isDefault&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,a.jsx)(i.CMH,{className:"mr-1"}),"Default"]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[!e.isDefault&&(0,a.jsx)("button",{type:"button",onClick:()=>D(e.id),className:"text-blue-600 hover:text-blue-800",title:"Set as default",children:"Set Default"}),(0,a.jsx)("button",{type:"button",onClick:()=>M(e.id),className:"text-red-600 hover:text-red-800",title:"Delete",children:(0,a.jsx)(i.qbC,{})})]})]}),e.paymentMethod.requiresDetails&&(0,a.jsx)("div",{className:"mt-3 text-sm text-gray-600",children:(()=>{try{let t=JSON.parse(e.details);return(0,a.jsx)("div",{className:"space-y-1",children:Object.entries(t).map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-medium",children:[t,":"]})," ",s]},t)})})}catch(e){return"Invalid details"}})()}),(0,a.jsxs)("div",{className:"mt-3 text-xs text-gray-500",children:["Added on ",new Date(e.createdAt).toLocaleDateString()]})]},e.id))})})})]})})}},55828:(e,t,s)=>{Promise.resolve().then(s.bind(s,28584))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(55828)),_N_E=e.O()}]);
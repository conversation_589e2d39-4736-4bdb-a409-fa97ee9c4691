(()=>{var e={};e.id=2454,e.ids=[2454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8548:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),a=t(43210),n=t(16189),o=t(85814),l=t.n(o),d=t(30474),i=t(23877);function c(){let e=(0,n.useRouter)(),r={PERSONAL_INFO:0,CONTACT_INFO:1,SECURITY:2,PAYMENT_INFO:3,REFERRAL:4,REVIEW:5},[t,o]=(0,a.useState)(r.PERSONAL_INFO),[c,m]=(0,a.useState)({name:"",email:"",password:"",confirmPassword:"",phone:"",uplineId:"",address:"",city:"",region:"",postalCode:"",birthdate:"",preferredPaymentMethod:"",bankName:"",bankAccountNumber:"",bankAccountName:"",gcashNumber:"",payMayaNumber:"",agreeToTerms:!1,receiveUpdates:!1}),[u,h]=(0,a.useState)({}),[p,x]=(0,a.useState)({}),[b,g]=(0,a.useState)(!1),[y,f]=(0,a.useState)(!1),[N,v]=(0,a.useState)(!1),[j,w]=(0,a.useState)(!1),[P,k]=(0,a.useState)(""),C=e=>/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(String(e).toLowerCase()),M=e=>""===e||e.length>=10,A=e=>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(e),I=(e,r)=>{let t="";switch(e){case"name":r?r.length<2&&(t="Name must be at least 2 characters"):t="Name is required";break;case"email":r?C(r)||(t="Please enter a valid email address"):t="Email is required";break;case"phone":r&&!M(r)&&(t="Please enter a valid phone number");break;case"password":r?A(r)||(t="Password must be at least 8 characters with 1 uppercase letter, 1 lowercase letter, and 1 number"):t="Password is required";break;case"confirmPassword":r?r!==c.password&&(t="Passwords do not match"):t="Please confirm your password";break;case"preferredPaymentMethod":"bank"===r?c.bankName?c.bankAccountNumber?c.bankAccountName||(t="Bank account name is required"):t="Bank account number is required":t="Bank name is required":"gcash"===r?c.gcashNumber||(t="GCash number is required"):"paymaya"!==r||c.payMayaNumber||(t="PayMaya number is required");break;case"bankName":case"bankAccountNumber":case"bankAccountName":"bank"!==c.preferredPaymentMethod||r||(t=`${e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())} is required`);break;case"gcashNumber":"gcash"!==c.preferredPaymentMethod||r?r&&!/^\d{11}$/.test(r)&&(t="Please enter a valid 11-digit GCash number"):t="GCash number is required";break;case"payMayaNumber":"paymaya"!==c.preferredPaymentMethod||r?r&&!/^\d{11}$/.test(r)&&(t="Please enter a valid 11-digit PayMaya number"):t="PayMaya number is required";break;case"agreeToTerms":r||(t="You must agree to the terms and conditions")}return t},E=e=>{let{name:r,value:t,type:s,checked:a}=e.target,n="checkbox"===s?a:t;m(e=>({...e,[r]:n})),x(e=>({...e,[r]:!0}));let o=I(r,n);h(e=>({...e,[r]:o})),"uplineId"===r&&(v(!1),k(""))},T=e=>{let{name:r,value:t,type:s,checked:a}=e.target;x(e=>({...e,[r]:!0}));let n=I(r,"checkbox"===s?a:t);h(e=>({...e,[r]:n}))},R=()=>{let e=!0,s={},a={[r.PERSONAL_INFO]:["name"],[r.CONTACT_INFO]:["email","phone"],[r.SECURITY]:["password","confirmPassword"],[r.PAYMENT_INFO]:["preferredPaymentMethod"],[r.REFERRAL]:["agreeToTerms"],[r.REVIEW]:[]};return t===r.PAYMENT_INFO&&("bank"===c.preferredPaymentMethod?a[r.PAYMENT_INFO].push("bankName","bankAccountNumber","bankAccountName"):"gcash"===c.preferredPaymentMethod?a[r.PAYMENT_INFO].push("gcashNumber"):"paymaya"===c.preferredPaymentMethod&&a[r.PAYMENT_INFO].push("payMayaNumber")),a[t].forEach(r=>{let t=I(r,c[r]);t&&(e=!1,s[r]=t)}),h(s),g(e),e},S=async()=>{if(c.uplineId){w(!0);try{await new Promise(e=>setTimeout(e,1e3));let e=["Maria Santos","Juan Dela Cruz","Angelica Reyes","Roberto Tan"],r=e[Math.floor(Math.random()*e.length)];k(r),v(!0)}catch(e){h(e=>({...e,uplineId:"Failed to verify upline ID"})),v(!1)}finally{w(!1)}}},F=async r=>{r.preventDefault();let t={},s=!0,a=["name","email","password","confirmPassword","agreeToTerms"];if(c.preferredPaymentMethod&&(a.push("preferredPaymentMethod"),"bank"===c.preferredPaymentMethod?a.push("bankName","bankAccountNumber","bankAccountName"):"gcash"===c.preferredPaymentMethod?a.push("gcashNumber"):"paymaya"===c.preferredPaymentMethod&&a.push("payMayaNumber")),a.forEach(e=>{let r=I(e,c[e]);r&&(s=!1,t[e]=r)}),!s)return void h(t);f(!0),console.log("Registration form submitted:",{...c,password:"***"});try{console.log("Sending registration request to API");let r={name:c.name,email:c.email,password:c.password,confirmPassword:c.confirmPassword,phone:c.phone,uplineId:c.uplineId||void 0,address:c.address,city:c.city,region:c.region,postalCode:c.postalCode,birthdate:c.birthdate,preferredPaymentMethod:c.preferredPaymentMethod||void 0,bankName:"bank"===c.preferredPaymentMethod?c.bankName:void 0,bankAccountNumber:"bank"===c.preferredPaymentMethod?c.bankAccountNumber:void 0,bankAccountName:"bank"===c.preferredPaymentMethod?c.bankAccountName:void 0,gcashNumber:"gcash"===c.preferredPaymentMethod?c.gcashNumber:void 0,payMayaNumber:"paymaya"===c.preferredPaymentMethod?c.payMayaNumber:void 0,agreeToTerms:c.agreeToTerms,receiveUpdates:c.receiveUpdates};console.log("Request body:",{...r,password:"***",confirmPassword:"***"});let t=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});console.log("Registration response status:",t.status);let s=await t.json();if(console.log("Registration response data:",s),!t.ok){if(s.errors){let e=Object.values(s.errors).join(", ");throw Error(e||"Validation failed")}throw Error(s.error||"Failed to register")}console.log("Registration successful, redirecting to login page"),e.push("/login?registered=true")}catch(e){console.error("Registration error:",e),h(r=>({...r,form:e.message||"An error occurred during registration"})),f(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-green-50 to-white py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"relative w-20 h-20",children:(0,s.jsx)(d.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})})}),(0,s.jsx)("h2",{className:"mt-2 text-center text-2xl font-bold text-gray-900",children:"Extreme Life Herbal"}),(0,s.jsx)("h3",{className:"text-center text-lg text-green-700 font-medium",children:"Create Your Account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/login",className:"font-medium text-green-600 hover:text-green-500 transition-colors",children:"Sign in"})]})]}),(0,s.jsx)("div",{className:"flex justify-between items-center w-full mb-8",children:["Personal Information","Contact Details","Security","Payment Details","Referral","Review & Submit"].map((e,r)=>(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${t===r?"bg-green-600 text-white":t>r?"bg-green-100 text-green-600 border-2 border-green-600":"bg-gray-100 text-gray-500"}`,children:t>r?(0,s.jsx)(i.CMH,{className:"h-4 w-4"}):r+1}),(0,s.jsx)("span",{className:`mt-2 text-xs ${t===r?"text-green-600 font-medium":"text-gray-500"} hidden sm:block`,children:e})]},r))}),(0,s.jsxs)("form",{onSubmit:F,children:[(()=>{switch(t){case r.PERSONAL_INFO:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["Full Name ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.x$1,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${u.name&&p.name?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Juan Dela Cruz",value:c.name,onChange:E,onBlur:T}),u.name&&p.name&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.name})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"birthdate",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date of Birth"}),(0,s.jsx)("input",{id:"birthdate",name:"birthdate",type:"date",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",value:c.birthdate,onChange:E}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Your birth date is used to verify your identity and for birthday promotions"})]})]});case r.CONTACT_INFO:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["Email Address ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.maD,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${u.email&&p.email?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"<EMAIL>",value:c.email,onChange:E,onBlur:T}),u.email&&p.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.email})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.Cab,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",autoComplete:"tel",className:`appearance-none block w-full pl-10 pr-3 py-2 border ${u.phone&&p.phone?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"+63 XXX XXX XXXX",value:c.phone,onChange:E,onBlur:T}),u.phone&&p.phone&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.phone})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-1",children:"Address"}),(0,s.jsx)("input",{id:"address",name:"address",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Street Address",value:c.address,onChange:E})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700 mb-1",children:"City"}),(0,s.jsx)("input",{id:"city",name:"city",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"City",value:c.city,onChange:E})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"region",className:"block text-sm font-medium text-gray-700 mb-1",children:"Region/Province"}),(0,s.jsx)("input",{id:"region",name:"region",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Region/Province",value:c.region,onChange:E})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code"}),(0,s.jsx)("input",{id:"postalCode",name:"postalCode",type:"text",className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm",placeholder:"Postal Code",value:c.postalCode,onChange:E})]})]});case r.SECURITY:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:["Password ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${u.password&&p.password?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"••••••••",value:c.password,onChange:E,onBlur:T}),u.password&&p.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.password})]}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Password must be at least 8 characters with 1 uppercase letter, 1 lowercase letter, and 1 number"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["Confirm Password ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${u.confirmPassword&&p.confirmPassword?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"••••••••",value:c.confirmPassword,onChange:E,onBlur:T}),u.confirmPassword&&p.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.confirmPassword})]})]})]});case r.PAYMENT_INFO:return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:["Payment Details for Rebates ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-4",children:"Please provide your preferred payment method for receiving rebates and commissions."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Payment Method"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("label",{className:`relative block p-3 border rounded-lg ${"bank"===c.preferredPaymentMethod?"border-green-500 bg-green-50":"border-gray-300"} cursor-pointer`,children:[(0,s.jsx)("input",{type:"radio",name:"preferredPaymentMethod",value:"bank",className:"sr-only",checked:"bank"===c.preferredPaymentMethod,onChange:E}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Bank Transfer"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("label",{className:`relative block p-3 border rounded-lg ${"gcash"===c.preferredPaymentMethod?"border-green-500 bg-green-50":"border-gray-300"} cursor-pointer`,children:[(0,s.jsx)("input",{type:"radio",name:"preferredPaymentMethod",value:"gcash",className:"sr-only",checked:"gcash"===c.preferredPaymentMethod,onChange:E}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"GCash"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("label",{className:`relative block p-3 border rounded-lg ${"paymaya"===c.preferredPaymentMethod?"border-green-500 bg-green-50":"border-gray-300"} cursor-pointer`,children:[(0,s.jsx)("input",{type:"radio",name:"preferredPaymentMethod",value:"paymaya",className:"sr-only",checked:"paymaya"===c.preferredPaymentMethod,onChange:E}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"PayMaya"})]})})]}),u.preferredPaymentMethod&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.preferredPaymentMethod})]}),"bank"===c.preferredPaymentMethod&&(0,s.jsxs)("div",{className:"space-y-4 p-4 border border-gray-200 rounded-md bg-gray-50",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"bankName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Bank Name ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"bankName",name:"bankName",type:"text",className:`appearance-none block w-full px-3 py-2 border ${u.bankName&&p.bankName?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"BDO, BPI, Metrobank, etc.",value:c.bankName,onChange:E,onBlur:T}),u.bankName&&p.bankName&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.bankName})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"bankAccountNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["Account Number ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"bankAccountNumber",name:"bankAccountNumber",type:"text",className:`appearance-none block w-full px-3 py-2 border ${u.bankAccountNumber&&p.bankAccountNumber?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Your bank account number",value:c.bankAccountNumber,onChange:E,onBlur:T}),u.bankAccountNumber&&p.bankAccountNumber&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.bankAccountNumber})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"bankAccountName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Account Name ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"bankAccountName",name:"bankAccountName",type:"text",className:`appearance-none block w-full px-3 py-2 border ${u.bankAccountName&&p.bankAccountName?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Name on your bank account",value:c.bankAccountName,onChange:E,onBlur:T}),u.bankAccountName&&p.bankAccountName&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.bankAccountName})]})]}),"gcash"===c.preferredPaymentMethod&&(0,s.jsx)("div",{className:"space-y-4 p-4 border border-gray-200 rounded-md bg-gray-50",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"gcashNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["GCash Number ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"gcashNumber",name:"gcashNumber",type:"text",className:`appearance-none block w-full px-3 py-2 border ${u.gcashNumber&&p.gcashNumber?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"09XX XXX XXXX",value:c.gcashNumber,onChange:E,onBlur:T}),u.gcashNumber&&p.gcashNumber&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.gcashNumber}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Please ensure this is the same number registered with your GCash account"})]})}),"paymaya"===c.preferredPaymentMethod&&(0,s.jsx)("div",{className:"space-y-4 p-4 border border-gray-200 rounded-md bg-gray-50",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"payMayaNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:["PayMaya Number ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"payMayaNumber",name:"payMayaNumber",type:"text",className:`appearance-none block w-full px-3 py-2 border ${u.payMayaNumber&&p.payMayaNumber?"border-red-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"09XX XXX XXXX",value:c.payMayaNumber,onChange:E,onBlur:T}),u.payMayaNumber&&p.payMayaNumber&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.payMayaNumber}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Please ensure this is the same number registered with your PayMaya account"})]})})]})]}),(0,s.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(i.__w,{className:"h-5 w-5 text-yellow-400"})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm text-yellow-700",children:"Your payment details are used to send your rebates and commissions. Make sure they are accurate."})})]})})]});case r.REFERRAL:return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"uplineId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Upline ID (Referrer)"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.NPy,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"uplineId",name:"uplineId",type:"text",className:`appearance-none block w-full pl-10 pr-3 py-2 border ${u.uplineId?"border-red-300":N?"border-green-300":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm`,placeholder:"Enter upline ID if you were referred",value:c.uplineId,onChange:E}),u.uplineId&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.uplineId}),N&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-green-600 flex items-center",children:[(0,s.jsx)(i.CMH,{className:"mr-1"})," Verified: ",P]})]}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("button",{type:"button",onClick:S,disabled:!c.uplineId||j,className:`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md ${!c.uplineId||j?"bg-gray-200 text-gray-500 cursor-not-allowed":"bg-green-100 text-green-700 hover:bg-green-200"}`,children:j?"Verifying...":"Verify ID"})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",className:`h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded ${u.agreeToTerms&&p.agreeToTerms?"border-red-300":""}`,checked:c.agreeToTerms,onChange:E})}),(0,s.jsxs)("div",{className:"ml-3 text-sm",children:[(0,s.jsxs)("label",{htmlFor:"agreeToTerms",className:"font-medium text-gray-700",children:["I agree to the ",(0,s.jsx)(l(),{href:"/terms",className:"text-green-600 hover:text-green-500",children:"Terms and Conditions"})," and ",(0,s.jsx)(l(),{href:"/privacy",className:"text-green-600 hover:text-green-500",children:"Privacy Policy"})," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),u.agreeToTerms&&p.agreeToTerms&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.agreeToTerms})]})]})}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"receiveUpdates",name:"receiveUpdates",type:"checkbox",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:c.receiveUpdates,onChange:E})}),(0,s.jsx)("div",{className:"ml-3 text-sm",children:(0,s.jsx)("label",{htmlFor:"receiveUpdates",className:"font-medium text-gray-700",children:"I want to receive updates about products, promotions, and events"})})]})})]});case r.REVIEW:return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Review Your Information"}),(0,s.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Personal Information"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c.name}),c.birthdate&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Born: ",c.birthdate]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Contact Information"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c.email}),c.phone&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c.phone}),c.address&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[c.address,", ",c.city,", ",c.region," ",c.postalCode]})]}),c.preferredPaymentMethod&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Payment Information"}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Payment Method: ","bank"===c.preferredPaymentMethod?"Bank Transfer":"gcash"===c.preferredPaymentMethod?"GCash":"PayMaya"]}),"bank"===c.preferredPaymentMethod&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Bank: ",c.bankName]}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Account: ",c.bankAccountNumber]}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Account Name: ",c.bankAccountName]})]}),"gcash"===c.preferredPaymentMethod&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["GCash Number: ",c.gcashNumber]}),"paymaya"===c.preferredPaymentMethod&&(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["PayMaya Number: ",c.payMayaNumber]})]}),c.uplineId&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Referral Information"}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:["Upline ID: ",c.uplineId,N&&` (${P})`]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Preferences"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:c.receiveUpdates?"Will receive updates":"Will not receive updates"})]})]})}),(0,s.jsx)("p",{className:"mt-4 text-sm text-gray-500",children:"Please review your information carefully. You can go back to previous steps to make changes if needed."})]}),u.form&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:u.form})]});default:return null}})(),(0,s.jsxs)("div",{className:"flex justify-between mt-8",children:[t>0?(0,s.jsxs)("button",{type:"button",onClick:()=>{o(e=>e-1)},className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,s.jsx)(i.QVr,{className:"mr-2 h-4 w-4"}),"Back"]}):(0,s.jsx)("div",{}),t<r.REVIEW?(0,s.jsxs)("button",{type:"button",onClick:()=>{R()&&o(e=>e+1)},disabled:!b,className:`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${b?"bg-green-600 hover:bg-green-700":"bg-gray-300 cursor-not-allowed"} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500`,children:["Next",(0,s.jsx)(i.Z0P,{className:"ml-2 h-4 w-4"})]}):(0,s.jsx)("button",{type:"submit",disabled:y,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating account..."]}):(0,s.jsxs)(s.Fragment,{children:["Create Account",(0,s.jsx)(i.CMH,{className:"ml-2 h-4 w-4"})]})})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12431:(e,r,t)=>{Promise.resolve().then(t.bind(t,94530))},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,r,t)=>{"use strict";t.d(r,{CartProvider:()=>l,_:()=>o});var s=t(60687),a=t(43210);let n=(0,a.createContext)(void 0),o=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},l=({children:e})=>{let[r,t]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let o=e=>{t(r=>r.filter(r=>r.id!==e))},l=r.reduce((e,r)=>e+r.quantity,0),d=r.reduce((e,r)=>e+r.price*r.quantity,0),i=r.reduce((e,r)=>e+r.pv*r.quantity,0);return(0,s.jsx)(n.Provider,{value:{items:r,addItem:e=>{t(r=>{let t=r.findIndex(r=>r.id===e.id);if(!(t>=0))return[...r,e];{let s=[...r];return s[t]={...s[t],quantity:s[t].quantity+e.quantity},s}})},removeItem:o,updateQuantity:(e,r)=>{if(r<=0)return void o(e);t(t=>t.map(t=>t.id===e?{...t,quantity:r}:t))},clearCart:()=>{t([])},itemCount:l,subtotal:d,totalPV:i},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,r,t)=>{"use strict";t.d(r,{CartProvider:()=>a});var s=t(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},44898:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>i});var s=t(65239),a=t(48088),n=t(88170),o=t.n(n),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let i={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94530)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\register\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},45851:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(60687),a=t(25217),n=t(8693),o=t(43210);function l({children:e}){let[r]=(0,o.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,s.jsx)(n.Ht,{client:r,children:e})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(60687),a=t(43210);let n=()=>"serviceWorker"in navigator,o=async()=>{if(!n())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),l(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},l=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let r=e.installing;r&&r.addEventListener("statechange",()=>{"installed"===r.state&&navigator.serviceWorker.controller&&d()})})},d=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},i=({children:e})=>{let[r,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{o();let e=()=>{t(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,s.jsxs)(s.Fragment,{children:[e,r&&(0,s.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,s.jsxs)("div",{className:"mr-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,s.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,s.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,r,t)=>{Promise.resolve().then(t.bind(t,37043)),Promise.resolve().then(t.bind(t,23229)),Promise.resolve().then(t.bind(t,82113)),Promise.resolve().then(t.bind(t,41750))},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72263:(e,r,t)=>{Promise.resolve().then(t.bind(t,8548))},74104:(e,r,t)=>{Promise.resolve().then(t.bind(t,28253)),Promise.resolve().then(t.bind(t,97695)),Promise.resolve().then(t.bind(t,45851)),Promise.resolve().then(t.bind(t,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h,metadata:()=>u});var s=t(37413),a=t(22376),n=t.n(a),o=t(68726),l=t.n(o);t(61135);var d=t(23229),i=t(37043),c=t(82113),m=t(41750);let u={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/********.svg",apple:"/images/********.svg"},themeColor:"#4CAF50"};function h({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${l().variable} antialiased`,children:(0,s.jsx)(d.AuthProvider,{children:(0,s.jsx)(c.default,{children:(0,s.jsx)(i.CartProvider,{children:(0,s.jsx)(m.default,{children:e})})})})})})}},94530:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\register\\page.tsx","default")},96111:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},97695:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>n});var s=t(60687),a=t(82136);function n({children:e}){return(0,s.jsx)(a.SessionProvider,{children:e})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,8414,9567,3877,474],()=>t(44898));module.exports=s})();
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1407],{81407:(e,s,l)=>{l.r(s),l.d(s,{default:()=>d});var r=l(95155),t=l(12115),a=l(29911);let n={Starter:{color:"bg-gray-100 text-gray-800",borderColor:"border-gray-300",icon:(0,r.jsx)(a.gt3,{className:"text-gray-400"}),benefits:["Basic commission rates","Access to product catalog","Personal dashboard"]},Bronze:{color:"bg-yellow-100 text-yellow-800",borderColor:"border-yellow-300",icon:(0,r.jsx)(a.gt3,{className:"text-yellow-600"}),benefits:["5% commission on direct referrals","Access to training materials","Monthly team reports"]},Silver:{color:"bg-gray-200 text-gray-800",borderColor:"border-gray-400",icon:(0,r.jsxs)(r.Frag<PERSON>,{children:[(0,r.jsx)(a.gt3,{className:"text-gray-500"}),(0,r.jsx)(a.gt3,{className:"text-gray-500 ml-0.5"})]}),benefits:["7% commission on direct referrals","3% on level 2","Quarterly bonus eligibility"]},Gold:{color:"bg-yellow-200 text-yellow-800",borderColor:"border-yellow-400",icon:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.gt3,{className:"text-yellow-600"}),(0,r.jsx)(a.gt3,{className:"text-yellow-600 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-yellow-600 ml-0.5"})]}),benefits:["10% commission on direct referrals","5% on level 2","3% on level 3","Leadership training access"]},Platinum:{color:"bg-blue-100 text-blue-800",borderColor:"border-blue-300",icon:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.gt3,{className:"text-blue-500"}),(0,r.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-blue-500 ml-0.5"})]}),benefits:["12% commission on direct referrals","7% on level 2","5% on level 3","3% on levels 4-5","Annual retreat invitation"]},Diamond:{color:"bg-purple-100 text-purple-800",borderColor:"border-purple-300",icon:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.gt3,{className:"text-purple-500"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"}),(0,r.jsx)(a.gt3,{className:"text-purple-500 ml-0.5"})]}),benefits:["15% commission on direct referrals","10% on level 2","7% on level 3","5% on levels 4-5","3% on levels 6-10","Car bonus program","Executive leadership council"]}},i=e=>n[e]||n.Starter,c=e=>{let{user:s,onClose:l}=e;if(!s)return null;let t=i(s.rank.name);return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:"p-6 ".concat(t.color," rounded-t-lg border-b ").concat(t.borderColor),children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-white flex items-center justify-center text-2xl shadow-md",children:s.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold",children:s.name}),(0,r.jsx)("div",{className:"flex items-center mt-1",children:(0,r.jsxs)("span",{className:"flex items-center text-sm",children:[t.icon,(0,r.jsx)("span",{className:"ml-1",children:s.rank.name})]})})]})]}),(0,r.jsx)("button",{onClick:l,className:"text-gray-500 hover:text-gray-700 focus:outline-none",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"User Information"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.AWX,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"User ID"}),(0,r.jsx)("div",{children:s.id})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.maD,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Email"}),(0,r.jsx)("div",{children:s.email})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.M5n,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Downline Members"}),(0,r.jsx)("div",{children:s._count.downline})]})]}),void 0!==s.walletBalance&&(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.lcY,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Wallet Balance"}),(0,r.jsxs)("div",{children:["₱",s.walletBalance.toFixed(2)]})]})]}),s.level>0&&(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.YXz,{className:"mt-1 mr-3 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Network Level"}),(0,r.jsxs)("div",{children:["Level ",s.level]})]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Rank Benefits"}),(0,r.jsx)("ul",{className:"space-y-2",children:t.benefits.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-2 mt-0.5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),(0,r.jsx)("span",{children:e})]},s))})]})]}),(0,r.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Actions"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"View Full Profile"}),(0,r.jsx)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:"Send Message"}),0===s.level&&(0,r.jsx)("button",{className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",children:"Generate Referral Link"})]})]})]})]})})},o=e=>{let{user:s,isRoot:l=!1,depth:n,maxDepth:c,initialExpandedLevels:d,onUserSelect:x,viewMode:m,highlightTerm:h}=e,[u,g]=(0,t.useState)(n<d),j=s.children&&s.children.length>0,f=j&&n<c,p=i(s.rank.name),b=h&&(s.name.toLowerCase().includes(h.toLowerCase())||s.email.toLowerCase().includes(h.toLowerCase())||s.id.toString().includes(h));return(0,r.jsxs)("div",{className:"mb-2 ".concat(l?"":"ml-6"),children:[(0,r.jsxs)("div",{className:"\n          flex items-center p-3 rounded-md\n          ".concat(l?"bg-blue-50 border border-blue-200":"bg-white border border-gray-200","\n          ").concat(b?"ring-2 ring-yellow-400 shadow-md":"","\n          hover:shadow-md transition-shadow duration-200\n        "),children:[f&&(0,r.jsx)("button",{onClick:()=>g(!u),className:"mr-2 text-gray-500 hover:text-gray-700 focus:outline-none","aria-label":u?"Collapse":"Expand",children:u?(0,r.jsx)(a.Vr3,{}):(0,r.jsx)(a.X6T,{})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[l?(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2",children:(0,r.jsx)(a.x$1,{className:"text-blue-500"})}):(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2",children:s.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:s.name}),"detailed"===m&&(0,r.jsx)("div",{className:"text-xs text-gray-500",children:s.email})]}),(0,r.jsxs)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full flex items-center ".concat(p.color),children:[p.icon,(0,r.jsx)("span",{className:"ml-1",children:s.rank.name})]})]}),"detailed"===m&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1 flex flex-wrap gap-x-3",children:[(0,r.jsxs)("span",{children:["ID: ",s.id]}),(0,r.jsxs)("span",{children:["Downline: ",s._count.downline]}),!l&&(0,r.jsxs)("span",{children:["Level ",s.level]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[!l&&"compact"===m&&(0,r.jsxs)("div",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:["Lvl ",s.level]}),(0,r.jsx)("button",{onClick:()=>x(s),className:"p-1.5 text-blue-500 hover:bg-blue-50 rounded-full","aria-label":"View details",children:(0,r.jsx)(a.__w,{})})]})]}),j&&u&&(0,r.jsx)("div",{className:"mt-2 border-l-2 border-gray-200 pl-2",children:s.children.map(e=>(0,r.jsx)(o,{user:e,depth:n+1,maxDepth:c,initialExpandedLevels:d,onUserSelect:x,viewMode:m,highlightTerm:h},e.id))})]})},d=e=>{let{data:s,maxDepth:l=10,initialExpandedLevels:d=2,onUserSelect:x}=e,[m,h]=(0,t.useState)(""),[u,g]=(0,t.useState)(!1),[j,f]=(0,t.useState)("detailed"),[p,b]=(0,t.useState)(null),[v,N]=(0,t.useState)(null),[y,w]=(0,t.useState)(""),k=(0,t.useRef)(null);(0,t.useEffect)(()=>{let e=setTimeout(()=>{w(m)},300);return()=>clearTimeout(e)},[m]);let C=(e,s,l)=>{let r=!s||e.name.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase())||e.id.toString().includes(s),t=!l||e.rank.name===l;if(r&&t)return e;if(e.children&&e.children.length>0){let r=e.children.map(e=>C(e,s,l)).filter(Boolean);if(r.length>0)return{...e,children:r}}return null},L=(m||v)&&C(s,m,v)||s;return(0,r.jsxs)("div",{className:"genealogy-tree",children:[(0,r.jsx)("div",{className:"mb-6 bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{ref:k,type:"text",placeholder:"Search by name, email, or ID...",value:m,onChange:e=>h(e.target.value),className:"w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"}),(0,r.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,r.jsx)(a.KSO,{className:"h-5 w-5"})}),m&&(0,r.jsx)("button",{onClick:()=>{h(""),w(""),k.current&&k.current.focus()},className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>g(!u),className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:u?"Collapse All":"Expand All"}),(0,r.jsxs)("button",{onClick:()=>f("compact"===j?"detailed":"compact"),className:"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center",children:["compact"===j?(0,r.jsx)(a.Ny1,{className:"mr-1"}):(0,r.jsx)(a.mx3,{className:"mr-1"}),"compact"===j?"Detailed":"Compact"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,r.jsx)(a.YsJ,{className:"mr-1"})," Filter by rank:"]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("button",{onClick:()=>N(null),className:"text-xs px-3 py-1 rounded-full ".concat(null===v?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"All"}),Object.keys(n).map(e=>(0,r.jsxs)("button",{onClick:()=>N(e===v?null:e),className:"text-xs px-3 py-1 rounded-full flex items-center ".concat(v===e?"bg-green-600 text-white":"".concat(i(e).color," hover:bg-opacity-80")),children:[i(e).icon,(0,r.jsx)("span",{className:"ml-1",children:e})]},e))]})]})]})}),(0,r.jsxs)("div",{className:"mb-6 bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-semibold mb-2 text-gray-500",children:"Rank Legend"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-3",children:Object.entries(n).map(e=>{let[s,l]=e;return(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full ".concat(l.color.split(" ")[0]," mr-1")}),(0,r.jsxs)("span",{className:"text-sm flex items-center",children:[l.icon,(0,r.jsx)("span",{className:"ml-1",children:s})]})]},s)})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6 overflow-x-auto",children:(0,r.jsx)(o,{user:L,isRoot:!0,depth:0,maxDepth:l,initialExpandedLevels:u?l:d,onUserSelect:e=>{b(e),x&&x(e)},viewMode:j,highlightTerm:y})}),p&&(0,r.jsx)(c,{user:p,onClose:()=>b(null)})]})}}}]);
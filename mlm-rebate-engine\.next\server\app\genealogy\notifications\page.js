(()=>{var e={};e.id=387,e.ids=[387],e.modules={647:(e,t,s)=>{Promise.resolve().then(s.bind(s,94362))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4868:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(65239),i=s(48088),n=s(88170),a=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["genealogy",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,59833)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\notifications\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\notifications\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/genealogy/notifications/page",pathname:"/genealogy/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>o,_:()=>a});var r=s(60687),i=s(43210);let n=(0,i.createContext)(void 0),a=()=>{let e=(0,i.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},o=({children:e})=>{let[t,s]=(0,i.useState)([]);(0,i.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,i.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let a=e=>{s(t=>t.filter(t=>t.id!==e))},o=t.reduce((e,t)=>e+t.quantity,0),l=t.reduce((e,t)=>e+t.price*t.quantity,0),c=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,r.jsx)(n.Provider,{value:{items:t,addItem:e=>{s(t=>{let s=t.findIndex(t=>t.id===e.id);if(!(s>=0))return[...t,e];{let r=[...t];return r[s]={...r[s],quantity:r[s].quantity+e.quantity},r}})},removeItem:a,updateQuantity:(e,t)=>{if(t<=0)return void a(e);s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{s([])},itemCount:o,subtotal:l,totalPV:c},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>i});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},40815:(e,t,s)=>{Promise.resolve().then(s.bind(s,59833))},41750:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},45851:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(60687),i=s(25217),n=s(8693),a=s(43210);function o({children:e}){let[t]=(0,a.useState)(()=>new i.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(n.Ht,{client:t,children:e})}},59833:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\notifications\\page.tsx","default")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687),i=s(43210);let n=()=>"serviceWorker"in navigator,a=async()=>{if(!n())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),o(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},o=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},c=({children:e})=>{let[t,s]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{a();let e=()=>{s(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,t&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,s)=>{Promise.resolve().then(s.bind(s,37043)),Promise.resolve().then(s.bind(s,23229)),Promise.resolve().then(s.bind(s,82113)),Promise.resolve().then(s.bind(s,41750))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,s)=>{Promise.resolve().then(s.bind(s,28253)),Promise.resolve().then(s.bind(s,97695)),Promise.resolve().then(s.bind(s,45851)),Promise.resolve().then(s.bind(s,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94362:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),i=s(43210),n=s(82136),a=s(59391),o=s(23877),l=s(85814),c=s.n(l);function d({userId:e,limit:t=5,showControls:s=!0,onNotificationClick:n}){let[a,l]=(0,i.useState)([]),[d,m]=(0,i.useState)(!0),[h,u]=(0,i.useState)(null),[x,p]=(0,i.useState)(!1),[b,f]=(0,i.useState)(null),[v,g]=(0,i.useState)(!1),[y,j]=(0,i.useState)({newMembers:!0,purchases:!0,rankAdvancements:!0,rebates:!0,system:!0,emailNotifications:!1,pushNotifications:!1}),N=async e=>{try{if(!(await fetch(`/api/genealogy/notifications/${e}/read`,{method:"POST"})).ok)throw Error("Failed to mark notification as read");l(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))}catch(e){console.error("Error marking notification as read:",e)}},w=async()=>{try{if(!(await fetch("/api/genealogy/notifications/read-all",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e})})).ok)throw Error("Failed to mark all notifications as read");l(e=>e.map(e=>({...e,isRead:!0})))}catch(e){console.error("Error marking all notifications as read:",e)}},k=e=>{e.isRead||N(e.id),n&&n(e)},C=async()=>{try{if(!(await fetch("/api/genealogy/notifications/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,settings:y})})).ok)throw Error("Failed to save notification settings");g(!1)}catch(e){console.error("Error saving notification settings:",e)}},P=e=>{switch(e){case"new_member":return(0,r.jsx)(o.NPy,{className:"text-green-500"});case"purchase":return(0,r.jsx)(o.AsH,{className:"text-blue-500"});case"rank_advancement":return(0,r.jsx)(o.SBv,{className:"text-yellow-500"});case"rebate":return(0,r.jsx)(FaWallet,{className:"text-purple-500"});case"system":return(0,r.jsx)(o.Pcn,{className:"text-gray-500"});default:return(0,r.jsx)(o.jNV,{className:"text-gray-500"})}},M=e=>{let t=new Date(e),s=Math.floor(Math.floor((new Date().getTime()-t.getTime())/1e3)/60),r=Math.floor(s/60),i=Math.floor(r/24);return i>0?`${i} day${i>1?"s":""} ago`:r>0?`${r} hour${r>1?"s":""} ago`:s>0?`${s} minute${s>1?"s":""} ago`:"Just now"},S=a.filter(e=>!e.isRead).length;return d&&0===a.length?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4 flex items-center justify-center h-32",children:[(0,r.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading notifications..."})]}):h?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-4",children:(0,r.jsxs)("div",{className:"bg-red-50 p-3 rounded-md",children:[(0,r.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,r.jsx)(o.BS8,{className:"mr-2"}),"Error loading notifications"]}),(0,r.jsx)("p",{className:"text-red-600",children:h})]})}):v?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,r.jsx)(o.Pcn,{className:"mr-2 text-blue-500"}),"Notification Settings"]}),(0,r.jsx)("button",{onClick:()=>g(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)(o.QCr,{})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border-b pb-3",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Notification Types"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"newMembers",checked:y.newMembers,onChange:e=>j(t=>({...t,newMembers:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"newMembers",className:"ml-2 block text-sm text-gray-700",children:"New Members"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"purchases",checked:y.purchases,onChange:e=>j(t=>({...t,purchases:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"purchases",className:"ml-2 block text-sm text-gray-700",children:"Purchases"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"rankAdvancements",checked:y.rankAdvancements,onChange:e=>j(t=>({...t,rankAdvancements:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"rankAdvancements",className:"ml-2 block text-sm text-gray-700",children:"Rank Advancements"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"rebates",checked:y.rebates,onChange:e=>j(t=>({...t,rebates:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"rebates",className:"ml-2 block text-sm text-gray-700",children:"Rebates"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"system",checked:y.system,onChange:e=>j(t=>({...t,system:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"system",className:"ml-2 block text-sm text-gray-700",children:"System Notifications"})]})]})]}),(0,r.jsxs)("div",{className:"border-b pb-3",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Delivery Methods"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"emailNotifications",checked:y.emailNotifications,onChange:e=>j(t=>({...t,emailNotifications:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"emailNotifications",className:"ml-2 block text-sm text-gray-700",children:"Email Notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"pushNotifications",checked:y.pushNotifications,onChange:e=>j(t=>({...t,pushNotifications:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"pushNotifications",className:"ml-2 block text-sm text-gray-700",children:"Push Notifications"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:()=>g(!1),className:"px-3 py-1.5 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{onClick:C,className:"px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700",children:"Save Settings"})]})]})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b flex justify-between items-center",children:[(0,r.jsxs)("h3",{className:"font-medium flex items-center",children:[(0,r.jsx)(o.jNV,{className:"mr-2 text-blue-500"}),"Notifications",S>0&&(0,r.jsxs)("span",{className:"ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full",children:[S," new"]})]}),s&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>f(b?null:"new_member"),className:`p-1 rounded-md ${"new_member"===b?"bg-green-100 text-green-800":"text-gray-500 hover:text-gray-700"}`,title:"Filter by New Members",children:(0,r.jsx)(o.NPy,{})}),(0,r.jsx)("button",{onClick:()=>f(b?null:"purchase"),className:`p-1 rounded-md ${"purchase"===b?"bg-blue-100 text-blue-800":"text-gray-500 hover:text-gray-700"}`,title:"Filter by Purchases",children:(0,r.jsx)(o.AsH,{})}),(0,r.jsx)("button",{onClick:()=>f(b?null:"rank_advancement"),className:`p-1 rounded-md ${"rank_advancement"===b?"bg-yellow-100 text-yellow-800":"text-gray-500 hover:text-gray-700"}`,title:"Filter by Rank Advancements",children:(0,r.jsx)(o.SBv,{})}),(0,r.jsx)("button",{onClick:()=>g(!0),className:"p-1 text-gray-500 hover:text-gray-700",title:"Notification Settings",children:(0,r.jsx)(o.Pcn,{})})]})]}),0===a.length?(0,r.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,r.jsx)("p",{children:"No notifications found."}),b&&(0,r.jsx)("button",{onClick:()=>f(null),className:"mt-2 text-blue-600 text-sm",children:"Clear filter"})]}):(0,r.jsx)("div",{className:"divide-y",children:a.map(e=>(0,r.jsx)("div",{className:`p-4 ${e.isRead?"bg-white":"bg-blue-50"}`,children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"mr-3 mt-1",children:P(e.type)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"cursor-pointer",onClick:()=>k(e),children:[(0,r.jsx)("div",{className:"font-medium",children:e.title}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e.message}),(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:M(e.createdAt)})]}),(0,r.jsxs)("div",{className:"mt-2 flex justify-end",children:[!e.isRead&&(0,r.jsx)("button",{onClick:()=>N(e.id),className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark as read"}),"new_member"===e.type&&e.data?.userId&&(0,r.jsx)(c(),{href:`/users/${e.data.userId}`,className:"ml-3 text-xs text-blue-600 hover:text-blue-800",children:"View Profile"}),"purchase"===e.type&&e.data?.purchaseId&&(0,r.jsx)(c(),{href:`/purchases/${e.data.purchaseId}`,className:"ml-3 text-xs text-blue-600 hover:text-blue-800",children:"View Purchase"})]})]})]})},e.id))}),s&&(0,r.jsxs)("div",{className:"px-4 py-3 border-t bg-gray-50 flex justify-between items-center",children:[(0,r.jsx)("button",{onClick:w,className:"text-sm text-blue-600 hover:text-blue-800",disabled:0===S,children:"Mark all as read"}),(0,r.jsx)("button",{onClick:()=>p(!x),className:"text-sm text-blue-600 hover:text-blue-800",children:x?"Show less":"View all"})]})]})}function m(){let{data:e,status:t}=(0,n.useSession)(),[s,l]=(0,i.useState)(void 0),{data:m,isLoading:h}=(0,a.I)({queryKey:["user"],queryFn:async()=>{if(!e?.user?.email)return null;let t=await fetch("/api/users/me");if(!t.ok)throw Error("Failed to fetch user data");return await t.json()},enabled:"authenticated"===t});return(m&&!s&&l(m.id),"loading"===t||h)?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===t?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(o.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view genealogy notifications."}),(0,r.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Notifications"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Stay updated on your network's activities and achievements"})]}),(0,r.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(o.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:s?(0,r.jsx)(d,{userId:s,limit:20,showControls:!0,onNotificationClick:e=>{switch(console.log("Notification clicked:",e),e.type){case"new_member":case"rank_advancement":e.data?.userId&&(window.location.href=`/users/${e.data.userId}`);break;case"purchase":e.data?.purchaseId&&(window.location.href=`/purchases/${e.data.purchaseId}`);break;case"rebate":e.data?.rebateId&&(window.location.href=`/rebates/${e.data.rebateId}`)}}}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,r.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading notifications..."})]})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center mb-4",children:[(0,r.jsx)(o.__w,{className:"mr-2 text-blue-500"}),"About Notifications"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{children:"The Genealogy Notifications system keeps you informed about important events and activities in your network. Stay updated on new members, purchases, rank advancements, and more."}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Notification Types"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"New Members"})," - When someone joins your network"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Purchases"})," - When members in your network make purchases"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Rank Advancements"})," - When members achieve new ranks"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Rebates"})," - When you earn rebates from your network's activities"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"System"})," - Important system announcements and updates"]})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"Notification Settings"}),(0,r.jsx)("p",{className:"text-green-700 mb-2",children:"You can customize your notification preferences to receive only the types of notifications that matter to you."}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Choose which notification types to receive"}),(0,r.jsx)("li",{children:"Enable or disable email notifications"}),(0,r.jsx)("li",{children:"Enable or disable push notifications"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"Tips for Managing Notifications"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsx)("li",{children:"Use filters to focus on specific notification types"}),(0,r.jsx)("li",{children:"Mark notifications as read to keep your inbox organized"}),(0,r.jsx)("li",{children:"Click on notifications to view more details or take action"}),(0,r.jsx)("li",{children:"Check notifications regularly to stay informed about your network"})]})]}),(0,r.jsx)("p",{children:"Notifications are an essential tool for managing your network effectively. They help you identify opportunities, recognize achievements, and stay connected with your team."})]})]})})]})]})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>h});var r=s(37413),i=s(22376),n=s.n(i),a=s(68726),o=s.n(a);s(61135);var l=s(23229),c=s(37043),d=s(82113),m=s(41750);let h={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:(0,r.jsx)(l.AuthProvider,{children:(0,r.jsx)(d.default,{children:(0,r.jsx)(c.CartProvider,{children:(0,r.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},97695:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n});var r=s(60687),i=s(82136);function n({children:e}){return(0,r.jsx)(i.SessionProvider,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,9391],()=>s(4868));module.exports=r})();
"use strict";(()=>{var e={};e.id=1086,e.ids=[1086],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},62769:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>q});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>l});var a=t(96559),o=t(48088),n=t(37719),i=t(31183),u=(t(92168),t(32190)),p=t(19854),d=t(12909);async function c(e){try{let r=await (0,p.getServerSession)(d.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view rebates"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let s=await i.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let a=s.id,o=6===s.rankId,n=e.nextUrl.searchParams,c=parseInt(n.get("page")||"1"),l=parseInt(n.get("pageSize")||"10"),x=n.get("userId"),g=n.get("status"),q=n.get("startDate"),m=n.get("endDate"),h={};if(o&&x?h.receiverId=parseInt(x):h.receiverId=a,g&&(h.status=g),q&&(h.createdAt={...h.createdAt||{},gte:new Date(q)}),m){let e=new Date(m);e.setHours(23,59,59,999),h.createdAt={...h.createdAt||{},lte:e}}let f=(c-1)*l,b=await i.z.rebate.findMany({where:h,include:{purchase:{include:{product:!0}},generator:{select:{id:!0,name:!0,email:!0}},receiver:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:f,take:l}),v=await i.z.rebate.count({where:h}),w=Math.ceil(v/l);return u.NextResponse.json({rebates:b,pagination:{page:c,pageSize:l,totalItems:v,totalPages:w}})}catch(e){return console.error("Error fetching rebates:",e),u.NextResponse.json({error:"Failed to fetch rebates"},{status:500})}}async function l(e){try{let e=await (0,p.getServerSession)(d.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to process rebates"},{status:401});return u.NextResponse.json({error:"Only administrators can process rebates"},{status:403})}catch(e){return console.error("Error processing rebates:",e),u.NextResponse.json({error:"Failed to process rebates"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/rebates/route",pathname:"/api/rebates",filename:"route",bundlePath:"app/api/rebates/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rebates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:q,serverHooks:m}=x;function h(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:q})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,4079,2896],()=>t(62769));module.exports=s})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7828],{45872:(e,s,a)=>{Promise.resolve().then(a.bind(a,48342))},48342:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var l=a(95155),i=a(12115),t=a(12108),n=a(87747),r=a(29911),d=a(6874),c=a.n(d),o=a(55028);let x=(0,o.default)(()=>a.e(9465).then(a.bind(a,39465)),{loadableGenerated:{webpack:()=>[39465]},ssr:!1,loading:()=>(0,l.jsxs)("div",{className:"flex items-center justify-center h-40",children:[(0,l.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading statistics..."})]})}),m=(0,o.default)(()=>Promise.all([a.e(1294),a.e(8702),a.e(6113),a.e(6808),a.e(7402)]).then(a.bind(a,27402)),{loadableGenerated:{webpack:()=>[27402]},ssr:!1,loading:()=>(0,l.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,l.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading genealogy visualization..."})]})});function h(){let{data:e,status:s}=(0,t.useSession)(),[a,d]=(0,i.useState)(void 0),{data:o,isLoading:h}=(0,n.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let a=await fetch("/api/users/me");if(!a.ok)throw Error("Failed to fetch user data");return await a.json()},enabled:"authenticated"===s});return(o&&!a&&d(o.id),"loading"===s||h)?(0,l.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,l.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,l.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,l.jsx)(r.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,l.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,l.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,l.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,l.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Basic Genealogy Flow"}),(0,l.jsx)("p",{className:"text-gray-600",children:"A simple implementation of the genealogy tree using React Flow"})]}),(0,l.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,l.jsx)(r.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:[(0,l.jsx)("div",{className:"lg:col-span-3",children:(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:a?(0,l.jsx)(m,{userId:a,maxLevel:3}):(0,l.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,l.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading genealogy data..."})]})})}),(0,l.jsx)("div",{className:"lg:col-span-1",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,l.jsxs)("div",{className:"bg-blue-50 p-3 border-b border-blue-100 flex items-center",children:[(0,l.jsx)(r.v$b,{className:"text-blue-500 mr-2"}),(0,l.jsx)("h3",{className:"font-medium",children:"Network Statistics"})]}),a?(0,l.jsx)(x,{userId:a}):(0,l.jsxs)("div",{className:"flex items-center justify-center h-40",children:[(0,l.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading statistics..."})]})]})})]}),(0,l.jsxs)("div",{className:"mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,l.jsx)("h2",{className:"text-lg font-medium text-blue-800 mb-2",children:"About Basic Genealogy Flow"}),(0,l.jsx)("p",{className:"text-blue-700 mb-2",children:"This is a simple implementation of the genealogy tree using React Flow. It provides:"}),(0,l.jsxs)("ul",{className:"list-disc list-inside text-blue-700 space-y-1",children:[(0,l.jsx)("li",{children:"Interactive node-based visualization"}),(0,l.jsx)("li",{children:"Expand/collapse functionality for nodes"}),(0,l.jsx)("li",{children:"Basic user details panel"}),(0,l.jsx)("li",{children:"Minimap for navigation"}),(0,l.jsx)("li",{children:"Zoom and pan controls"})]}),(0,l.jsx)("p",{className:"text-blue-700 mt-2",children:"This is the first phase of our enhanced genealogy visualization. Future phases will add more features and optimizations."})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,5557,6967,7747,8441,1684,7358],()=>s(45872)),_N_E=e.O()}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[186],{15567:(e,s,t)=>{t.d(s,{A:()=>n});var a=t(95155),r=t(12115),l=t(29911);function n(e){let{onRefresh:s,onSearch:t,onToggleFilters:n,showFilters:c,isLoading:i}=e,[o,d]=(0,r.useState)("");return(0,a.jsxs)("div",{className:"bg-white p-3 border-b flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:n,className:"flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:[(0,a.jsx)(l.YsJ,{className:"mr-1"}),"Filters",c?(0,a.jsx)(l.<PERSON><PERSON>,{className:"ml-1"}):(0,a.jsx)(l.Vr3,{className:"ml-1"})]}),(0,a.jsxs)("button",{onClick:s,className:"flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200",disabled:i,children:[(0,a.jsx)(l.Swo,{className:"mr-1 ".concat(i?"animate-spin":"")}),i?"Refreshing...":"Refresh"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(o)},className:"flex items-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search users...",value:o,onChange:e=>d(e.target.value),className:"pl-9 pr-4 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-48 md:w-64"}),(0,a.jsx)("div",{className:"absolute left-3 top-2 text-gray-400",children:(0,a.jsx)(l.KSO,{})})]}),(0,a.jsx)("button",{type:"submit",className:"ml-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Search"})]})]})}},20186:(e,s,t)=>{t.r(s),t.d(s,{default:()=>p});var a=t(95155),r=t(12115),l=t(74211),n=t(93306),c=t(77581),i=t(57916);t(11687);var o=t(29911);let d={Starter:{color:"bg-gray-100 text-gray-800",borderColor:"border-gray-300",icon:(0,a.jsx)(o.gt3,{className:"text-gray-400"})},Bronze:{color:"bg-yellow-100 text-yellow-800",borderColor:"border-yellow-300",icon:(0,a.jsx)(o.gt3,{className:"text-yellow-600"})},Silver:{color:"bg-gray-200 text-gray-800",borderColor:"border-gray-400",icon:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.gt3,{className:"text-gray-500"}),(0,a.jsx)(o.gt3,{className:"text-gray-500 ml-0.5"})]})},Gold:{color:"bg-yellow-200 text-yellow-800",borderColor:"border-yellow-400",icon:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.gt3,{className:"text-yellow-600"}),(0,a.jsx)(o.gt3,{className:"text-yellow-600 ml-0.5"}),(0,a.jsx)(o.gt3,{className:"text-yellow-600 ml-0.5"})]})},Platinum:{color:"bg-blue-100 text-blue-800",borderColor:"border-blue-300",icon:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.gt3,{className:"text-blue-500"}),(0,a.jsx)(o.gt3,{className:"text-blue-500 ml-0.5"}),(0,a.jsx)(o.gt3,{className:"text-blue-500 ml-0.5"}),(0,a.jsx)(o.gt3,{className:"text-blue-500 ml-0.5"})]})},Diamond:{color:"bg-purple-100 text-purple-800",borderColor:"border-purple-300",icon:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.gt3,{className:"text-purple-500"}),(0,a.jsx)(o.gt3,{className:"text-purple-500 ml-0.5"}),(0,a.jsx)(o.gt3,{className:"text-purple-500 ml-0.5"}),(0,a.jsx)(o.gt3,{className:"text-purple-500 ml-0.5"}),(0,a.jsx)(o.gt3,{className:"text-purple-500 ml-0.5"})]})}},m=e=>d[e]||d.Starter,x=(0,r.memo)(function(e){let{data:s,isConnectable:t}=e,{user:n,onExpand:c,onSelect:i,isExpanded:d,hasChildren:x}=s,u=m(n.rankName),[h,g]=(0,r.useState)(!1),b=0===n.level,p=e=>void 0===e?"":new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);return(0,a.jsxs)("div",{className:"\n        p-3 rounded-md shadow-md w-[200px] transition-all duration-200\n        ".concat(h?"shadow-lg transform scale-105":"","\n        ").concat(b?"bg-blue-50 border-2 border-blue-300":"bg-white border ".concat(u.borderColor),"\n      "),onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:[(0,a.jsx)(l.h7,{type:"source",position:l.yX.Bottom,isConnectable:t,className:"w-3 h-3 bg-gray-400"}),!b&&(0,a.jsx)(l.h7,{type:"target",position:l.yX.Top,isConnectable:t,className:"w-3 h-3 bg-gray-400"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-2 ".concat(b?"bg-blue-100":u.color),children:b?(0,a.jsx)(o.x$1,{className:"text-blue-500"}):(0,a.jsx)("span",{className:"text-sm font-medium",children:n.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 truncate",children:[(0,a.jsx)("div",{className:"font-medium text-sm truncate",title:n.name,children:n.name}),(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate",title:n.email,children:n.email})]})]}),(0,a.jsxs)("div",{className:"text-xs px-2 py-1 rounded-full flex items-center justify-center mb-2 ".concat(u.color),children:[u.icon,(0,a.jsx)("span",{className:"ml-1",children:n.rankName})]}),n.performanceMetrics&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,a.jsxs)("div",{className:"bg-green-50 p-1 rounded text-xs flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"flex items-center text-green-600 mb-0.5",children:[(0,a.jsx)(o.AsH,{className:"mr-1",size:10}),(0,a.jsx)("span",{children:"Personal"})]}),(0,a.jsx)("span",{className:"font-medium",children:p(n.performanceMetrics.personalSales)})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-1 rounded text-xs flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"flex items-center text-blue-600 mb-0.5",children:[(0,a.jsx)(o.YXz,{className:"mr-1",size:10}),(0,a.jsx)("span",{children:"Team"})]}),(0,a.jsx)("span",{className:"font-medium",children:p(n.performanceMetrics.teamSales)})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"ID:"}),(0,a.jsx)("span",{className:"font-medium",children:n.id})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Downline:"}),(0,a.jsx)("span",{className:"font-medium",children:n.downlineCount})]}),void 0!==n.walletBalance&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(o.lcY,{className:"mr-1",size:10}),"Balance:"]}),(0,a.jsx)("span",{className:"font-medium",children:p(n.walletBalance)})]}),n.level>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Level:"}),(0,a.jsx)("span",{className:"font-medium",children:n.level})]})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-3 pt-2 border-t border-gray-200",children:[x?(0,a.jsx)("button",{onClick:c,className:"text-xs px-2 py-1 rounded flex items-center ".concat(d?"bg-blue-100 text-blue-700 hover:bg-blue-200":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Vr3,{className:"mr-1"})," Collapse"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.X6T,{className:"mr-1"})," Expand"]})}):(0,a.jsx)("div",{className:"text-xs px-2 py-1 text-gray-400",children:"No children"}),(0,a.jsxs)("button",{onClick:i,className:"text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded flex items-center hover:bg-blue-200",children:[(0,a.jsx)(o.__w,{className:"mr-1"})," Details"]})]})]})]})});var u=t(10396),h=t(15567),g=t(98369);let b={userNode:x};function p(e){let{userId:s,maxLevel:t=3,initialLayout:d="vertical"}=e,[m,x,p]=(0,l.ck)([]),[j,f,y]=(0,l.fM)([]),[N,v]=(0,r.useState)(!0),[w,k]=(0,r.useState)(null),[C,S]=(0,r.useState)(null),[D,F]=(0,r.useState)(new Set),[A,B]=(0,r.useState)(d),[E,z]=(0,r.useState)(!1),[M,L]=(0,r.useState)({sortBy:"createdAt",sortDirection:"desc"}),[V,I]=(0,r.useState)(""),[P,R]=(0,r.useState)(!1),T=(0,l.VH)(),_=(0,r.useCallback)(async()=>{v(!0),k(null);try{let e=new URLSearchParams({maxLevel:t.toString(),userId:s.toString(),includePerformanceMetrics:"true"});M.rankId&&e.append("rankId",M.rankId.toString()),M.sortBy&&e.append("sortBy",M.sortBy),M.sortDirection&&e.append("sortDirection",M.sortDirection),V&&e.append("search",V);let a=await fetch("/api/genealogy?".concat(e.toString()));if(!a.ok)throw Error("Failed to fetch genealogy data");let r=await a.json(),{nodes:l,edges:n}=H(r);R(!0),x(l),f(n),setTimeout(()=>{T.fitView({padding:.2}),R(!1)},300)}catch(e){k(e instanceof Error?e.message:"An unknown error occurred"),R(!1)}finally{v(!1)}},[s,t,M,V,x,f,T]);(0,r.useEffect)(()=>{_()},[_]);let H=(0,r.useCallback)(e=>{let s=[],t=[],a={id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:0,downlineCount:e._count.downline,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics};return s.push({id:a.id.toString(),type:"userNode",position:{x:0,y:0},data:{user:a,onExpand:()=>J(a.id.toString()),onSelect:()=>S(a),isExpanded:!0,hasChildren:e.children&&e.children.length>0},className:"animate-fade-in"}),e.children&&e.children.length>0&&G(e.children,a.id.toString(),0,0,1,s,t),{nodes:s,edges:t}},[]),G=(0,r.useCallback)((e,s,t,a,r,l,n)=>{let c,i,o,d,m=200*e.length+(e.length-1)*40;"vertical"===A?(c=t-m/2+100,d=a+150,e.forEach((e,t)=>{X(e,s,o=c+240*t,d,r,l,n)})):(o=t+250,i=a-m/2+100,e.forEach((e,t)=>{X(e,s,o,d=i+240*t,r,l,n)}))},[A]),X=(0,r.useCallback)((e,s,t,a,r,n,c)=>{let i={id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:r,downlineCount:e._count.downline,children:e.children,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics},o=i.id.toString();n.push({id:o,type:"userNode",position:{x:t,y:a},data:{user:i,onExpand:()=>J(o),onSelect:()=>S(i),isExpanded:D.has(o),hasChildren:e.children&&e.children.length>0},className:"animate-fade-in"}),c.push({id:"e-".concat(s,"-").concat(o),source:s,target:o,type:"vertical"===A?"smoothstep":"straight",animated:!1,style:{stroke:"#888",strokeWidth:1.5},markerEnd:{type:l.TG.ArrowClosed,width:15,height:15,color:"#888"}}),e.children&&e.children.length>0&&D.has(o)&&G(e.children,o,t,a,r+1,n,c)},[D,A,G]),J=(0,r.useCallback)(e=>{F(s=>{let t=new Set(s);return t.has(e)?t.delete(e):t.add(e),t})},[]),U=(0,r.useCallback)((e,s)=>{S(s.data.user)},[]),W=(0,r.useCallback)(e=>{I(e)},[]),Y=(0,r.useCallback)(()=>{z(e=>!e)},[]),Z=(0,r.useCallback)(e=>{L(e)},[]),O=(0,r.useCallback)(()=>{B(e=>"vertical"===e?"horizontal":"vertical")},[]);return N&&0===m.length?(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading genealogy data..."})]}):w?(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,a.jsx)("h3",{className:"text-red-800 font-medium",children:"Error loading genealogy data"}),(0,a.jsx)("p",{className:"text-red-600",children:w})]}):(0,a.jsxs)("div",{className:"flex flex-col border border-gray-200 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white border-b",children:[(0,a.jsx)(h.A,{onRefresh:_,onSearch:W,onToggleFilters:Y,showFilters:E,isLoading:N}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:O,className:"flex items-center px-3 py-1.5 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200",title:"Switch to ".concat("vertical"===A?"horizontal":"vertical"," layout"),children:[(0,a.jsx)(o.aQJ,{className:"mr-1"}),"vertical"===A?"Horizontal":"Vertical"," Layout"]}),(0,a.jsxs)("button",{onClick:()=>T.fitView({padding:.2}),className:"flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",title:"Fit view",children:[(0,a.jsx)(o.Ny1,{className:"mr-1"}),"Fit View"]})]})]}),E&&(0,a.jsx)(g.A,{onApplyFilters:Z}),(0,a.jsxs)("div",{className:"h-[600px] relative",children:[P&&(0,a.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10",children:(0,a.jsx)(o.hW,{className:"animate-spin text-blue-500 text-2xl"})}),(0,a.jsxs)(l.Gc,{nodes:m,edges:j,onNodesChange:p,onEdgesChange:y,onNodeClick:U,nodeTypes:b,fitView:!0,attributionPosition:"bottom-right",connectionLineType:"vertical"===A?l.Do.SmoothStep:l.Do.Straight,minZoom:.1,maxZoom:1.5,defaultViewport:{x:0,y:0,zoom:.8},children:[(0,a.jsx)(n.H,{}),(0,a.jsx)(c.o,{nodeColor:e=>{var s;let t=null==(s=e.data)?void 0:s.user;if(!t)return"#eee";if(0===t.level)return"#93c5fd";switch(t.rankName){case"Starter":return"#f3f4f6";case"Bronze":return"#fef3c7";case"Silver":return"#e5e7eb";case"Gold":return"#fef08a";case"Platinum":return"#dbeafe";case"Diamond":return"#f3e8ff";default:return"#eee"}},maskColor:"#ffffff50"}),(0,a.jsx)(i.V,{}),C&&(0,a.jsx)(l.Zk,{position:"top-right",className:"p-0 w-80",children:(0,a.jsx)(u.A,{user:C,onClose:()=>S(null),className:"max-h-[80vh]"})})]})]})]})}},98369:(e,s,t)=>{t.d(s,{A:()=>n});var a=t(95155),r=t(12115),l=t(29911);function n(e){let{onApplyFilters:s,className:t=""}=e,[n,c]=(0,r.useState)({sortBy:"createdAt",sortDirection:"desc"}),[i,o]=(0,r.useState)([]),[d,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{m(!0);try{let e=await fetch("/api/ranks");if(!e.ok)throw Error("Failed to fetch ranks");let s=await e.json();o(s)}catch(e){console.error("Error fetching ranks:",e)}finally{m(!1)}})()},[]);let x=(e,s)=>{c(t=>({...t,[e]:s}))};return(0,a.jsxs)("div",{className:"bg-gray-50 p-4 border-b ".concat(t),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsx)("h3",{className:"font-medium",children:"Filter & Sort Options"}),(0,a.jsx)("button",{onClick:()=>{c({sortBy:"createdAt",sortDirection:"desc"}),s({sortBy:"createdAt",sortDirection:"desc"})},className:"text-sm text-blue-600 hover:text-blue-800",children:"Reset Filters"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Rank"}),(0,a.jsxs)("select",{value:n.rankId||"",onChange:e=>x("rankId",e.target.value?parseInt(e.target.value):void 0),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Ranks"}),d?(0,a.jsx)("option",{disabled:!0,children:"Loading ranks..."}):i.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort By"}),(0,a.jsxs)("select",{value:n.sortBy||"createdAt",onChange:e=>x("sortBy",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"name",children:"Name"}),(0,a.jsx)("option",{value:"createdAt",children:"Join Date"}),(0,a.jsx)("option",{value:"rank",children:"Rank"}),(0,a.jsx)("option",{value:"downlineCount",children:"Downline Count"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort Direction"}),(0,a.jsx)("button",{onClick:()=>{x("sortDirection","asc"===n.sortDirection?"desc":"asc")},className:"w-full flex items-center justify-center border border-gray-300 rounded-md px-3 py-2 hover:bg-gray-50",children:"asc"===n.sortDirection?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.HL0,{className:"mr-2"})," Ascending"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.EDF,{className:"mr-2"})," Descending"]})})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{s(n)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Apply Filters"})})]})}}}]);
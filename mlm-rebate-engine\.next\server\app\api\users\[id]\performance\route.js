(()=>{var e={};e.id=3027,e.ids=[3027],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>c,aP:()=>l});var s=t(96330),o=t(13581),a=t(85663),n=t(55511),i=t.n(n);async function u(e){return await a.Ay.hash(e,10)}function l(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await a.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:n,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(s,a,i):s[a]=e[a]}return s.default=e,t&&t.set(e,s),s}(t(35426));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50180:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),u=t(19854),l=t(12909),c=t(31183);async function d(e,{params:r}){try{let e=await (0,u.getServerSession)(l.Nh);if(!e||!e.user)return i.NextResponse.json({error:"You must be logged in to view performance metrics"},{status:401});let t=parseInt(r.id);if(isNaN(t))return i.NextResponse.json({error:"Invalid user ID"},{status:400});let s=await c.z.user.findUnique({where:{id:t},include:{_count:{select:{downline:!0}}}});if(!s)return i.NextResponse.json({error:"User not found"},{status:404});let o=(await c.z.user.findMany({where:{uplineId:t},select:{id:!0}})).map(e=>e.id),a=await c.z.purchase.aggregate({where:{userId:t,status:"completed"},_sum:{totalAmount:!0}}),n=await c.z.purchase.aggregate({where:{userId:{in:o},status:"completed"},_sum:{totalAmount:!0}}),d=await c.z.rebate.aggregate({where:{receiverId:t,status:"completed"},_sum:{amount:!0}}),p=await c.z.user.count({where:{uplineId:t,createdAt:{gte:new Date(Date.now()-2592e6)}}}),m=await c.z.purchase.count({where:{userId:t,createdAt:{gte:new Date(Date.now()-7776e6)}}}),w=await c.z.user.count({where:{uplineId:t,createdAt:{gte:new Date(Date.now()-7776e6)}}}),f=Math.min(100,10*m+20*w);return i.NextResponse.json({personalSales:a._sum.totalAmount||0,teamSales:n._sum.totalAmount||0,totalSales:(a._sum.totalAmount||0)+(n._sum.totalAmount||0),rebatesEarned:d._sum.amount||0,teamSize:s._count.downline,newTeamMembers:p,activityScore:f,lastUpdated:new Date})}catch(e){return console.error("Error fetching performance metrics:",e),i.NextResponse.json({error:"Failed to fetch performance metrics"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/users/[id]/performance/route",pathname:"/api/users/[id]/performance",filename:"route",bundlePath:"app/api/users/[id]/performance/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\performance\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:f}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(50180));module.exports=s})();
(()=>{var e={};e.id=3302,e.ids=[3302],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12266:(e,s,t)=>{Promise.resolve().then(t.bind(t,84030))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["referrals",{children:["commissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81236)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\commissions\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\commissions\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/referrals/commissions/page",pathname:"/referrals/commissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},81236:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\referrals\\\\commissions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\commissions\\page.tsx","default")},84030:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(60687),a=t(43210),i=t(82136),n=t(16189),l=t(68367),d=t(23877),c=t(30474),o=t(85814),x=t.n(o);function m(){let{data:e,status:s}=(0,i.useSession)();(0,n.useRouter)();let[t,o]=(0,a.useState)(!0),[m,p]=(0,a.useState)([]),[u,h]=(0,a.useState)({type:"",text:""}),[g,f]=(0,a.useState)("all"),j=e=>new Date(e).toLocaleDateString(),y=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),b=e=>{switch(e){case"pending":return(0,r.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(d.w_X,{className:"mr-1"})," Pending"]});case"approved":return(0,r.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:[(0,r.jsx)(d.CMH,{className:"mr-1"})," Approved"]});case"paid":return(0,r.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:[(0,r.jsx)(d.MxO,{className:"mr-1"})," Paid"]});case"rejected":return(0,r.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:[(0,r.jsx)(d.QCr,{className:"mr-1"})," Rejected"]});default:return(0,r.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800",children:e})}};return"loading"===s||t?(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,r.jsx)(d.hW,{className:"animate-spin text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Referral Commissions"}),u.text&&(0,r.jsx)("div",{className:`mb-6 p-4 rounded-md ${"error"===u.type?"bg-red-100 text-red-700":"success"===u.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:u.text}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,r.jsx)(d.MxO,{className:"text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Commissions"}),(0,r.jsx)("div",{className:"text-xl font-bold",children:y(m.reduce((e,s)=>e+s.amount,0))})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full mr-4",children:(0,r.jsx)(d.w_X,{className:"text-yellow-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Pending Commissions"}),(0,r.jsx)("div",{className:"text-xl font-bold",children:y(m.filter(e=>"pending"===e.status||"approved"===e.status).reduce((e,s)=>e+s.amount,0))})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-green-100 p-3 rounded-full mr-4",children:(0,r.jsx)(d.CMH,{className:"text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Paid Commissions"}),(0,r.jsx)("div",{className:"text-xl font-bold",children:y(m.filter(e=>"paid"===e.status).reduce((e,s)=>e+s.amount,0))})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center",children:[(0,r.jsx)("div",{className:"mr-4 mb-2",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Filter by Status:"})}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("button",{type:"button",onClick:()=>f("all"),className:`px-3 py-1 rounded-md text-sm ${"all"===g?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"All"}),(0,r.jsx)("button",{type:"button",onClick:()=>f("pending"),className:`px-3 py-1 rounded-md text-sm ${"pending"===g?"bg-yellow-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"Pending"}),(0,r.jsx)("button",{type:"button",onClick:()=>f("approved"),className:`px-3 py-1 rounded-md text-sm ${"approved"===g?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"Approved"}),(0,r.jsx)("button",{type:"button",onClick:()=>f("paid"),className:`px-3 py-1 rounded-md text-sm ${"paid"===g?"bg-green-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"Paid"}),(0,r.jsx)("button",{type:"button",onClick:()=>f("rejected"),className:`px-3 py-1 rounded-md text-sm ${"rejected"===g?"bg-red-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"Rejected"})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Commission History"})}),(0,r.jsx)("div",{className:"p-4",children:0===m.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("p",{children:"No referral commissions found."}),(0,r.jsxs)("p",{className:"mt-2",children:["Start sharing products to earn commissions! Visit the ",(0,r.jsx)(x(),{href:"/referrals",className:"text-blue-600 hover:underline",children:"Referrals"})," page."]})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Buyer"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Purchase Details"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Commission"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[e.product.image?(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10 mr-3",children:(0,r.jsx)(c.default,{src:e.product.image,alt:e.product.name,width:40,height:40,className:"rounded-md object-cover"})}):(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md mr-3 flex items-center justify-center",children:(0,r.jsx)(d.AsH,{className:"text-gray-500"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.product.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:y(e.product.price)})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-8 w-8 bg-gray-200 rounded-full mr-3 flex items-center justify-center",children:(0,r.jsx)(d.x$1,{className:"text-gray-500"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.buyer.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.buyer.email})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.AsH,{className:"text-gray-500 mr-1"}),(0,r.jsxs)("span",{children:["Quantity: ",e.purchase.quantity]})]}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)(d.MxO,{className:"text-gray-500 mr-1"}),(0,r.jsxs)("span",{children:["Total: ",y(e.purchase.totalAmount)]})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsx)("div",{className:"font-medium text-green-600",children:y(e.amount)}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.percentage.toFixed(2),"% commission"]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[b(e.status),"paid"===e.status&&e.paidAt&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Paid on ",j(e.paidAt)]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.bfZ,{className:"text-gray-400 mr-1"}),j(e.createdAt)]})})]},e.id))})]})})})]}),(0,r.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6 text-center",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Earn More Commissions!"}),(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:"Share more products with your network to increase your earnings."}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,r.jsx)(x(),{href:"/shop",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Browse Products"}),(0,r.jsx)(x(),{href:"/referrals",className:"inline-block bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:"Manage Referral Links"})]})]})]})})}},94114:(e,s,t)=>{Promise.resolve().then(t.bind(t,81236))}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>t(30132));module.exports=r})();
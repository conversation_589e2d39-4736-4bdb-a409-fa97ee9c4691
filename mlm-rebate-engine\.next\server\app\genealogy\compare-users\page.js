(()=>{var e={};e.id=2489,e.ids=[2489],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,s,t)=>{"use strict";t.d(s,{CartProvider:()=>l,_:()=>i});var r=t(60687),a=t(43210);let n=(0,a.createContext)(void 0),i=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},l=({children:e})=>{let[s,t]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(s))},[s]);let i=e=>{t(s=>s.filter(s=>s.id!==e))},l=s.reduce((e,s)=>e+s.quantity,0),d=s.reduce((e,s)=>e+s.price*s.quantity,0),o=s.reduce((e,s)=>e+s.pv*s.quantity,0);return(0,r.jsx)(n.Provider,{value:{items:s,addItem:e=>{t(s=>{let t=s.findIndex(s=>s.id===e.id);if(!(t>=0))return[...s,e];{let r=[...s];return r[t]={...r[t],quantity:r[t].quantity+e.quantity},r}})},removeItem:i,updateQuantity:(e,s)=>{if(s<=0)return void i(e);t(t=>t.map(t=>t.id===e?{...t,quantity:s}:t))},clearCart:()=>{t([])},itemCount:l,subtotal:d,totalPV:o},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,s,t)=>{"use strict";t.d(s,{CartProvider:()=>a});var r=t(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},45851:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var r=t(60687),a=t(25217),n=t(8693),i=t(43210);function l({children:e}){let[s]=(0,i.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(n.Ht,{client:s,children:e})}},60053:(e,s,t)=>{Promise.resolve().then(t.bind(t,86119))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var r=t(60687),a=t(43210);let n=()=>"serviceWorker"in navigator,i=async()=>{if(!n())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),l(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},l=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let s=e.installing;s&&s.addEventListener("statechange",()=>{"installed"===s.state&&navigator.serviceWorker.controller&&d()})})},d=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},o=({children:e})=>{let[s,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{i();let e=()=>{t(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,s&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,s,t)=>{Promise.resolve().then(t.bind(t,37043)),Promise.resolve().then(t.bind(t,23229)),Promise.resolve().then(t.bind(t,82113)),Promise.resolve().then(t.bind(t,41750))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73621:(e,s,t)=>{Promise.resolve().then(t.bind(t,96109))},74104:(e,s,t)=>{Promise.resolve().then(t.bind(t,28253)),Promise.resolve().then(t.bind(t,97695)),Promise.resolve().then(t.bind(t,45851)),Promise.resolve().then(t.bind(t,63345))},76260:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["genealogy",{children:["compare-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,86119)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare-users\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare-users\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/genealogy/compare-users/page",pathname:"/genealogy/compare-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},82113:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},86119:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\compare-users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare-users\\page.tsx","default")},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u,metadata:()=>x});var r=t(37413),a=t(22376),n=t.n(a),i=t(68726),l=t.n(i);t(61135);var d=t(23229),o=t(37043),c=t(82113),m=t(41750);let x={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${l().variable} antialiased`,children:(0,r.jsx)(d.AuthProvider,{children:(0,r.jsx)(c.default,{children:(0,r.jsx)(o.CartProvider,{children:(0,r.jsx)(m.default,{children:e})})})})})})}},96109:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(60687),a=t(43210),n=t(82136),i=t(59391),l=t(16189),d=t(23877),o=t(85814),c=t.n(o);function m({userId1:e,userId2:s,timeRange:t="last30days"}){let[n,i]=(0,a.useState)(null),[o,c]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null),[u,h]=(0,a.useState)(t),p=(0,l.useRouter)(),f=e=>{h(e)},g=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),b=e=>new Intl.NumberFormat().format(e),v=e=>`${e>0?"+":""}${e.toFixed(2)}%`,j=e=>e>0?(0,r.jsx)(d.uCC,{className:"text-green-500"}):e<0?(0,r.jsx)(d.$TP,{className:"text-red-500"}):(0,r.jsx)(d.VsL,{className:"text-gray-500"}),y=e=>{p.push(`/users/${e}`)},N=e=>{p.push(`/genealogy?userId=${e}`)};return o&&!n?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,r.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading comparison data..."})]}):m?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,r.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,r.jsx)(d.BS8,{className:"mr-2"}),"Error loading comparison data"]}),(0,r.jsx)("p",{className:"text-red-600",children:m})]})}):n?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,r.jsx)(d.yk7,{className:"mr-2 text-blue-500"}),"Genealogy Comparison"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.bfZ,{className:"text-gray-500 mr-2"}),(0,r.jsxs)("select",{value:u,onChange:e=>f(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"last90days",children:"Last 90 Days"}),(0,r.jsx)("option",{value:"last6months",children:"Last 6 Months"}),(0,r.jsx)("option",{value:"last12months",children:"Last 12 Months"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-lg",children:n.user1.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:n.user1.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",n.user1.id]})]}),(0,r.jsx)("div",{className:"text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded-full",children:n.user1.rankName})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Downline"}),(0,r.jsx)("div",{className:"font-medium",children:b(n.user1.downlineCount)})]}),n.user1.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,r.jsx)("div",{className:"font-medium",children:g(n.user1.performanceMetrics.personalSales)})]}),n.user1.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,r.jsx)("div",{className:"font-medium",children:g(n.user1.performanceMetrics.teamSales)})]}),n.user1.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Rebates Earned"}),(0,r.jsx)("div",{className:"font-medium",children:g(n.user1.performanceMetrics.rebatesEarned)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>y(n.user1.id),className:"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700",children:"View Profile"}),(0,r.jsx)("button",{onClick:()=>N(n.user1.id),className:"px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700",children:"View Genealogy"})]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-lg",children:n.user2.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:n.user2.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",n.user2.id]})]}),(0,r.jsx)("div",{className:"text-sm px-2 py-1 bg-purple-100 text-purple-800 rounded-full",children:n.user2.rankName})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Downline"}),(0,r.jsx)("div",{className:"font-medium",children:b(n.user2.downlineCount)})]}),n.user2.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,r.jsx)("div",{className:"font-medium",children:g(n.user2.performanceMetrics.personalSales)})]}),n.user2.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,r.jsx)("div",{className:"font-medium",children:g(n.user2.performanceMetrics.teamSales)})]}),n.user2.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Rebates Earned"}),(0,r.jsx)("div",{className:"font-medium",children:g(n.user2.performanceMetrics.rebatesEarned)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>y(n.user2.id),className:"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700",children:"View Profile"}),(0,r.jsx)("button",{onClick:()=>N(n.user2.id),className:"px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700",children:"View Genealogy"})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"font-medium mb-3 flex items-center",children:[(0,r.jsx)(d.YYR,{className:"text-blue-500 mr-2"}),"Performance Comparison"]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metric"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:n.user1.name}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:n.user2.name}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Difference"})]})}),(0,r.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(FaUsers,{className:"text-blue-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Downline Count"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:b(n.user1.downlineCount)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:b(n.user2.downlineCount)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[j(n.differences.downlineCount),(0,r.jsxs)("span",{className:`ml-2 text-sm ${n.differences.downlineCount>0?"text-green-600":n.differences.downlineCount<0?"text-red-600":"text-gray-500"}`,children:[b(Math.abs(n.differences.downlineCount))," (",v(n.differences.downlineCountPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.AsH,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Personal Sales"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(n.user1.performanceMetrics?.personalSales||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(n.user2.performanceMetrics?.personalSales||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[j(n.differences.personalSales),(0,r.jsxs)("span",{className:`ml-2 text-sm ${n.differences.personalSales>0?"text-green-600":n.differences.personalSales<0?"text-red-600":"text-gray-500"}`,children:[g(Math.abs(n.differences.personalSales))," (",v(n.differences.personalSalesPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.AsH,{className:"text-blue-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Team Sales"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(n.user1.performanceMetrics?.teamSales||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(n.user2.performanceMetrics?.teamSales||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[j(n.differences.teamSales),(0,r.jsxs)("span",{className:`ml-2 text-sm ${n.differences.teamSales>0?"text-green-600":n.differences.teamSales<0?"text-red-600":"text-gray-500"}`,children:[g(Math.abs(n.differences.teamSales))," (",v(n.differences.teamSalesPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.lcY,{className:"text-purple-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Rebates Earned"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(n.user1.performanceMetrics?.rebatesEarned||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(n.user2.performanceMetrics?.rebatesEarned||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[j(n.differences.rebatesEarned),(0,r.jsxs)("span",{className:`ml-2 text-sm ${n.differences.rebatesEarned>0?"text-green-600":n.differences.rebatesEarned<0?"text-red-600":"text-gray-500"}`,children:[g(Math.abs(n.differences.rebatesEarned))," (",v(n.differences.rebatesEarnedPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.NPy,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"New Members"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:b(n.user1.performanceMetrics?.newTeamMembers||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:b(n.user2.performanceMetrics?.newTeamMembers||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[j(n.differences.newMembers),(0,r.jsxs)("span",{className:`ml-2 text-sm ${n.differences.newMembers>0?"text-green-600":n.differences.newMembers<0?"text-red-600":"text-gray-500"}`,children:[b(Math.abs(n.differences.newMembers))," (",v(n.differences.newMembersPercentage),")"]})]})})]})]})]})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-3 flex items-center",children:[(0,r.jsx)(FaUsers,{className:"text-blue-500 mr-2"}),"Network Overlap"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-center",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-1",children:["Unique to ",n.user1.name]}),(0,r.jsx)("div",{className:"text-xl font-semibold text-blue-700",children:b(n.differences.uniqueMembers1)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"members"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-3 rounded-md text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Common Members"}),(0,r.jsx)("div",{className:"text-xl font-semibold text-purple-700",children:b(n.differences.commonMembers)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"members"})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md text-center",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-1",children:["Unique to ",n.user2.name]}),(0,r.jsx)("div",{className:"text-xl font-semibold text-green-700",children:b(n.differences.uniqueMembers2)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"members"})]})]})]})]}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.yk7,{className:"text-gray-400 text-4xl mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Comparison Data"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Unable to load comparison data for the selected users."})]})})}function x(){let{data:e,status:s}=(0,n.useSession)(),t=(0,l.useSearchParams)(),o=t.get("userId1"),x=t.get("userId2"),u=t.get("timeRange")||"last30days",[h,p]=(0,a.useState)(o?parseInt(o):void 0),[f,g]=(0,a.useState)(x?parseInt(x):void 0),[b,v]=(0,a.useState)(u||"last30days"),[j,y]=(0,a.useState)(""),[N,w]=(0,a.useState)(""),[C,P]=(0,a.useState)([]),[S,M]=(0,a.useState)([]),[k,L]=(0,a.useState)(!1),[E,I]=(0,a.useState)(!1),{data:A,isLoading:R}=(0,i.I)({queryKey:["user"],queryFn:async()=>{if(!e?.user?.email)return null;let s=await fetch("/api/users/me");if(!s.ok)throw Error("Failed to fetch user data");return await s.json()},enabled:"authenticated"===s});!A||h||o||p(A.id);let q=async()=>{if(j.trim()){L(!0);try{let e=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:j,page:1,pageSize:5})});if(!e.ok)throw Error("Failed to search users");let s=await e.json();P(s.users)}catch(e){console.error("Error searching users:",e)}finally{L(!1)}}},D=async()=>{if(N.trim()){I(!0);try{let e=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:N,page:1,pageSize:5})});if(!e.ok)throw Error("Failed to search users");let s=await e.json();M(s.users)}catch(e){console.error("Error searching users:",e)}finally{I(!1)}}},U=e=>{p(e.id),P([]),y("")},T=e=>{g(e.id),M([]),w("")},_=e=>{v(e)};return"loading"===s||R?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(d.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to compare genealogy users."}),(0,r.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy User Comparison"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Compare two users to analyze differences and similarities"})]}),(0,r.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(d.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,r.jsx)(d.NPy,{className:"mr-2 text-blue-500"}),"Select Users to Compare"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"User 1"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID...",value:j,onChange:e=>y(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("button",{onClick:q,disabled:k,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:k?(0,r.jsx)(d.hW,{className:"animate-spin"}):(0,r.jsx)(d.KSO,{})})]}),C.length>0&&(0,r.jsx)("div",{className:"mt-2 border rounded-md divide-y max-h-60 overflow-y-auto",children:C.map(e=>(0,r.jsxs)("div",{className:"p-2 hover:bg-gray-50 cursor-pointer",onClick:()=>U(e),children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]},e.id))}),h&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-blue-50 rounded-md",children:[(0,r.jsx)("div",{className:"font-medium",children:"Selected User 1"}),(0,r.jsxs)("div",{className:"text-sm",children:["ID: ",h]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"User 2"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID...",value:N,onChange:e=>w(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("button",{onClick:D,disabled:E,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:E?(0,r.jsx)(d.hW,{className:"animate-spin"}):(0,r.jsx)(d.KSO,{})})]}),S.length>0&&(0,r.jsx)("div",{className:"mt-2 border rounded-md divide-y max-h-60 overflow-y-auto",children:S.map(e=>(0,r.jsxs)("div",{className:"p-2 hover:bg-gray-50 cursor-pointer",onClick:()=>T(e),children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]},e.id))}),f&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-purple-50 rounded-md",children:[(0,r.jsx)("div",{className:"font-medium",children:"Selected User 2"}),(0,r.jsxs)("div",{className:"text-sm",children:["ID: ",f]})]})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time Range"}),(0,r.jsxs)("select",{value:b,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"last90days",children:"Last 90 Days"}),(0,r.jsx)("option",{value:"last6months",children:"Last 6 Months"}),(0,r.jsx)("option",{value:"last12months",children:"Last 12 Months"})]})]})]}),h&&f?(0,r.jsx)(m,{userId1:h,userId2:f,timeRange:b}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.yk7,{className:"text-gray-400 text-4xl mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"Select Users to Compare"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Please select two users to compare their genealogy trees."})]})}),(0,r.jsxs)("div",{className:"mt-6 bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,r.jsx)(d.__w,{className:"mr-2 text-blue-500"}),"About Genealogy User Comparison"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{children:"The Genealogy User Comparison tool allows you to compare two users to identify differences and similarities in their networks. This can be useful for analyzing performance, identifying growth opportunities, and understanding network structures."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Key Metrics Compared"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"Downline Count - Total number of members in each network"}),(0,r.jsx)("li",{children:"Personal Sales - Direct sales made by each user"}),(0,r.jsx)("li",{children:"Team Sales - Sales made by the entire downline"}),(0,r.jsx)("li",{children:"Rebates Earned - Commissions and bonuses earned"}),(0,r.jsx)("li",{children:"New Members - Recent additions to each network"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"Network Overlap Analysis"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Unique Members - Members exclusive to each network"}),(0,r.jsx)("li",{children:"Common Members - Members present in both networks"}),(0,r.jsx)("li",{children:"Percentage Differences - Relative performance metrics"}),(0,r.jsx)("li",{children:"Growth Patterns - Differences in network expansion"})]})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"How to Use Comparison Data"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Identify Strengths and Weaknesses:"})," Compare performance metrics to identify areas where each user excels or needs improvement."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Learn from Success:"})," Analyze the structure and strategies of the more successful user to apply those lessons to the other."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Find Collaboration Opportunities:"})," Identify common members who could serve as bridges between networks for collaborative efforts."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Set Realistic Goals:"})," Use the comparison data to set achievable growth targets based on proven performance."]})]})]}),(0,r.jsx)("p",{children:"For more detailed analysis, you can adjust the time range to focus on different periods or export the comparison data for further study."})]})]})]})}},96111:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},97695:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>n});var r=t(60687),a=t(82136);function n({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,8414,9567,3877,9391],()=>t(76260));module.exports=r})();
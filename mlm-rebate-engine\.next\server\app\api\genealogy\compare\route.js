(()=>{var e={};e.id=1377,e.ids=[1377],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>l,Nh:()=>c,aP:()=>u});var a=r(96330),s=r(13581),n=r(85663),o=r(55511),i=r.n(o);async function l(e){return await n.Ay.hash(e,10)}function u(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new a.PrismaClient;let c={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,s.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new a.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let s=await n.Ay.compare(e.password,r.password);if(console.log("Password valid:",s),!s)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:o,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var s=r(12269);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=s?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(a,n,i):a[n]=e[n]}return a.default=e,r&&r.set(e,a),a}(r(35426));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52620:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>f,serverHooks:()=>b,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>m});var s=r(96559),n=r(48088),o=r(37719),i=r(32190),l=r(19854),u=r(12909),c=r(31183),d=r(70762);let p=d.z.object({userId1:d.z.string().transform(e=>parseInt(e)),userId2:d.z.string().transform(e=>parseInt(e)),timeRange:d.z.enum(["last30days","last90days","last6months","last12months"]).default("last30days")});async function m(e){try{let t,r=await (0,l.getServerSession)(u.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to compare genealogy trees"},{status:401});let a=new URL(e.url),s=a.searchParams.get("userId1"),n=a.searchParams.get("userId2"),o=a.searchParams.get("timeRange")||"last30days",d=p.safeParse({userId1:s,userId2:n,timeRange:o});if(!d.success)return i.NextResponse.json({error:"Invalid parameters",details:d.error.format()},{status:400});let{userId1:m,userId2:f,timeRange:h}=d.data,y=new Date;switch(h){case"last30days":default:t=new Date(y.getTime()-2592e6);break;case"last90days":t=new Date(y.getTime()-7776e6);break;case"last6months":t=new Date(y.getFullYear(),y.getMonth()-6,y.getDate());break;case"last12months":t=new Date(y.getFullYear()-1,y.getMonth(),y.getDate())}let b=await c.z.user.findUnique({where:{id:m},include:{rank:!0,_count:{select:{downline:!0}}}});if(!b)return i.NextResponse.json({error:"User 1 not found"},{status:404});let x=await c.z.user.findUnique({where:{id:f},include:{rank:!0,_count:{select:{downline:!0}}}});if(!x)return i.NextResponse.json({error:"User 2 not found"},{status:404});let v=(await g(m)).map(e=>e.id),k=(await g(f)).map(e=>e.id),_=v.filter(e=>!k.includes(e)).length,j=k.filter(e=>!v.includes(e)).length,P=v.filter(e=>k.includes(e)).length,A=await w(m,v,t),M=await w(f,k,t),S=b._count.downline-x._count.downline,E=0!==x._count.downline?S/x._count.downline*100:0,q=A.personalSales-M.personalSales,O=0!==M.personalSales?q/M.personalSales*100:0,I=A.teamSales-M.teamSales,z=0!==M.teamSales?I/M.teamSales*100:0,R=A.rebatesEarned-M.rebatesEarned,N=0!==M.rebatesEarned?R/M.rebatesEarned*100:0,T=A.newTeamMembers-M.newTeamMembers,C=0!==M.newTeamMembers?T/M.newTeamMembers*100:0,U={user1:{id:b.id,name:b.name,email:b.email,rankName:b.rank.name,downlineCount:b._count.downline,walletBalance:b.walletBalance,createdAt:b.createdAt,performanceMetrics:A},user2:{id:x.id,name:x.name,email:x.email,rankName:x.rank.name,downlineCount:x._count.downline,walletBalance:x.walletBalance,createdAt:x.createdAt,performanceMetrics:M},differences:{downlineCount:S,downlineCountPercentage:E,personalSales:q,personalSalesPercentage:O,teamSales:I,teamSalesPercentage:z,rebatesEarned:R,rebatesEarnedPercentage:N,newMembers:T,newMembersPercentage:C,uniqueMembers1:_,uniqueMembers2:j,commonMembers:P},timeRange:h};return i.NextResponse.json(U)}catch(e){return console.error("Error comparing genealogy trees:",e),i.NextResponse.json({error:"Failed to compare genealogy trees"},{status:500})}}async function g(e){let t=await c.z.user.findMany({select:{id:!0,uplineId:!0}}),r=new Map;t.forEach(e=>{e.uplineId&&(r.has(e.uplineId)||r.set(e.uplineId,[]),r.get(e.uplineId)?.push(e.id))});let a=[];return!function e(t){(r.get(t)||[]).forEach(t=>{a.push({id:t}),e(t)})}(e),a}async function w(e,t,r){let a=(await c.z.purchase.aggregate({where:{userId:e,createdAt:{gte:r},status:"completed"},_sum:{totalAmount:!0}}))._sum.totalAmount||0,s=(await c.z.purchase.aggregate({where:{userId:{in:t},createdAt:{gte:r},status:"completed"},_sum:{totalAmount:!0}}))._sum.totalAmount||0,n=(await c.z.rebate.aggregate({where:{receiverId:e,createdAt:{gte:r},status:"completed"},_sum:{amount:!0}}))._sum.amount||0,o=await c.z.user.count({where:{id:{in:t},createdAt:{gte:r}}});return{personalSales:a,teamSales:s,totalSales:a+s,rebatesEarned:n,teamSize:t.length,newTeamMembers:o}}let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/genealogy/compare/route",pathname:"/api/genealogy/compare",filename:"route",bundlePath:"app/api/genealogy/compare/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\compare\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:b}=f;function x(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8044,3112,8381],()=>r(52620));module.exports=a})();
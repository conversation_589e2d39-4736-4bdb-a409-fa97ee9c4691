(()=>{var e={};e.id=2992,e.ids=[2992],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["genealogy",{children:["compare",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58786)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/genealogy/compare/page",pathname:"/genealogy/compare",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},15690:(e,t,r)=>{Promise.resolve().then(r.bind(r,58786))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>o,_:()=>i});var s=r(60687),a=r(43210);let n=(0,a.createContext)(void 0),i=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},o=({children:e})=>{let[t,r]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{r(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let i=e=>{r(t=>t.filter(t=>t.id!==e))},o=t.reduce((e,t)=>e+t.quantity,0),l=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,s.jsx)(n.Provider,{value:{items:t,addItem:e=>{r(t=>{let r=t.findIndex(t=>t.id===e.id);if(!(r>=0))return[...t,e];{let s=[...t];return s[r]={...s[r],quantity:s[r].quantity+e.quantity},s}})},removeItem:i,updateQuantity:(e,t)=>{if(t<=0)return void i(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},itemCount:o,subtotal:l,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35368:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),a=r(43210),n=r(82136),i=r(59391),o=r(23877),l=r(85814),d=r.n(l),c=r(30036),m=r(4934);let p=(0,c.default)(async()=>{},{loadableGenerated:{modules:["app\\genealogy\\compare\\page.tsx -> @/components/genealogy/BasicGenealogyFlow"]},ssr:!1,loading:()=>(0,s.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,s.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("span",{children:"Loading visualization..."})]})}),x=(0,c.default)(async()=>{},{loadableGenerated:{modules:["app\\genealogy\\compare\\page.tsx -> @/components/genealogy/EnhancedGenealogyFlow"]},ssr:!1,loading:()=>(0,s.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,s.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("span",{children:"Loading visualization..."})]})}),h=(0,c.default)(async()=>{},{loadableGenerated:{modules:["app\\genealogy\\compare\\page.tsx -> @/components/genealogy/GenealogyTree"]},ssr:!1,loading:()=>(0,s.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,s.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("span",{children:"Loading visualization..."})]})});function u(){let{data:e,status:t}=(0,n.useSession)(),[r,l]=(0,a.useState)(void 0),[c,u]=(0,a.useState)("original"),[g,v]=(0,a.useState)("enhanced"),{data:f,isLoading:y}=(0,i.I)({queryKey:["user"],queryFn:async()=>{if(!e?.user?.email)return null;let t=await fetch("/api/users/me");if(!t.ok)throw Error("Failed to fetch user data");return await t.json()},enabled:"authenticated"===t});f&&!r&&l(f.id);let b=e=>{if(!r)return(0,s.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,s.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("span",{children:"Loading user data..."})]});switch(e){case"original":return(0,s.jsx)(h,{userId:r,maxLevel:3});case"basic":return(0,s.jsx)(p,{userId:r,maxLevel:3});case"enhanced":return(0,s.jsx)(m.Ln,{children:(0,s.jsx)(x,{userId:r,maxLevel:3})});default:return null}},w=e=>{switch(e){case"original":return"Original Tree";case"basic":return"Basic Flow";case"enhanced":return"Enhanced Flow";default:return""}};return"loading"===t||y?(0,s.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,s.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===t?(0,s.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,s.jsx)(o.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,s.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,s.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Visualization Comparison"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Compare different genealogy visualization methods side by side"})]}),(0,s.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,s.jsx)(o.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,s.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-lg mb-4 flex flex-wrap items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Left Visualization"}),(0,s.jsxs)("select",{value:c,onChange:e=>u(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"original",children:"Original Tree"}),(0,s.jsx)("option",{value:"basic",children:"Basic Flow"}),(0,s.jsx)("option",{value:"enhanced",children:"Enhanced Flow"})]})]}),(0,s.jsx)("button",{onClick:()=>{u(g),v(c)},className:"mt-6 p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200",title:"Swap visualizations",children:(0,s.jsx)(o.yk7,{})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Right Visualization"}),(0,s.jsxs)("select",{value:g,onChange:e=>v(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"original",children:"Original Tree"}),(0,s.jsx)("option",{value:"basic",children:"Basic Flow"}),(0,s.jsx)("option",{value:"enhanced",children:"Enhanced Flow"})]})]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 mt-4 md:mt-0",children:[(0,s.jsx)("p",{children:"Compare different visualization methods to see which one works best for your needs."}),(0,s.jsx)("p",{children:"Each method has its own strengths and features."})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-blue-50 p-3 border-b border-blue-100",children:(0,s.jsx)("h3",{className:"font-medium",children:w(c)})}),(0,s.jsx)("div",{className:"h-[600px] overflow-hidden",children:b(c)})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-blue-50 p-3 border-b border-blue-100",children:(0,s.jsx)("h3",{className:"font-medium",children:w(g)})}),(0,s.jsx)("div",{className:"h-[600px] overflow-hidden",children:b(g)})]})]}),(0,s.jsxs)("div",{className:"mt-6 bg-white p-4 rounded-lg shadow-lg",children:[(0,s.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Feature Comparison"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Feature"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Original Tree"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Basic Flow"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Enhanced Flow"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Interactive Navigation"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Good"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Excellent"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Performance with Large Trees"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Limited"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Good"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Excellent"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Visual Appeal"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Good"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Excellent"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Information Density"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Low"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Medium"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"High"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Layout Options"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Fixed"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Fixed"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Multiple"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Animation"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"None"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Advanced"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Performance Metrics"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Limited"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Comprehensive"})]})]})]})})]})]})}},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>a});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},45851:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),a=r(25217),n=r(8693),i=r(43210);function o({children:e}){let[t]=(0,i.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,s.jsx)(n.Ht,{client:t,children:e})}},55442:(e,t,r)=>{Promise.resolve().then(r.bind(r,35368))},58786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\compare\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\compare\\page.tsx","default")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(60687),a=r(43210);let n=()=>"serviceWorker"in navigator,i=async()=>{if(!n())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),o(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},o=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,r]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{i();let e=()=>{r(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,s.jsxs)(s.Fragment,{children:[e,t&&(0,s.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,s.jsxs)("div",{className:"mr-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,s.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,s.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,r)=>{Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,23229)),Promise.resolve().then(r.bind(r,82113)),Promise.resolve().then(r.bind(r,41750))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,r)=>{Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,97695)),Promise.resolve().then(r.bind(r,45851)),Promise.resolve().then(r.bind(r,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x,metadata:()=>p});var s=r(37413),a=r(22376),n=r.n(a),i=r(68726),o=r.n(i);r(61135);var l=r(23229),d=r(37043),c=r(82113),m=r(41750);let p={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function x({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:(0,s.jsx)(l.AuthProvider,{children:(0,s.jsx)(c.default,{children:(0,s.jsx)(d.CartProvider,{children:(0,s.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},97695:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(60687),a=r(82136);function n({children:e}){return(0,s.jsx)(a.SessionProvider,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,8414,9567,3877,9391,5833],()=>r(11200));module.exports=s})();
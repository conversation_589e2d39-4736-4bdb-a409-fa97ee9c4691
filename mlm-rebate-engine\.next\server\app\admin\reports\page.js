(()=>{var e={};e.id=7618,e.ids=[7618],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16404:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\reports\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40524:(e,s,t)=>{Promise.resolve().then(t.bind(t,16404))},42738:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(60687),r=t(43210),l=t(82136),i=t(16189),n=t(68367),d=t(47081),c=t(23877),o=t(43324),x=t(29947);function m(){let{data:e,status:s}=(0,l.useSession)();(0,i.useRouter)();let[t,o]=(0,r.useState)(!0),[m,p]=(0,r.useState)(null),[h,g]=(0,r.useState)({startDate:new Date(new Date().setDate(new Date().getDate()-30)).toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0]}),[u,b]=(0,r.useState)(!1),j=e=>{let{name:s,value:t}=e.target;g(e=>({...e,[s]:t}))},N=e=>{alert(`Exporting ${e} report to CSV...`)};return"loading"===s||t?(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}):u?(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold",children:"Reports & Analytics"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{htmlFor:"startDate",className:"text-sm text-gray-600",children:"From:"}),(0,a.jsx)("input",{type:"date",id:"startDate",name:"startDate",value:h.startDate,onChange:j,className:"px-2 py-1 border border-gray-300 rounded-md text-sm"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{htmlFor:"endDate",className:"text-sm text-gray-600",children:"To:"}),(0,a.jsx)("input",{type:"date",id:"endDate",name:"endDate",value:h.endDate,onChange:j,className:"px-2 py-1 border border-gray-300 rounded-md text-sm"})]})]})]}),m&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,a.jsx)(c.YXz,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Users"}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:m.summary.totalUsers})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,a.jsx)(c.AsH,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Sales"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",m.summary.totalSales.toLocaleString()]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-purple-100 text-purple-500 mr-4",children:(0,a.jsx)(c.lcY,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",m.summary.totalRebates.toLocaleString()]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4",children:(0,a.jsx)(c.AsH,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Avg. Order Value"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",m.summary.averageOrderValue.toLocaleString()]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-red-100 text-red-500 mr-4",children:(0,a.jsx)(c.qvi,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Conversion Rate"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:[(100*m.summary.conversionRate).toFixed(1),"%"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-indigo-100 text-indigo-500 mr-4",children:(0,a.jsx)(c.bfZ,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Date Range"}),(0,a.jsxs)("p",{className:"text-sm font-semibold",children:[h.startDate," to ",h.endDate]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(c.v$b,{className:"mr-2 text-blue-500"})," Sales by Product"]}),(0,a.jsxs)("button",{onClick:()=>N("sales-by-product"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,a.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(x.yP,{data:{labels:m.salesByProduct.labels,datasets:[{label:"Sales (₱)",data:m.salesByProduct.data,backgroundColor:"rgba(59, 130, 246, 0.5)",borderColor:"rgb(59, 130, 246)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!1}}}})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(c.YYR,{className:"mr-2 text-green-500"})," Sales Trend"]}),(0,a.jsxs)("button",{onClick:()=>N("sales-trend"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,a.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(x.N1,{data:{labels:m.salesByDate.labels,datasets:[{label:"Sales (₱)",data:m.salesByDate.data,borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.5)",tension:.3}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!1}}}})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(c.v$b,{className:"mr-2 text-purple-500"})," Rebates by Level"]}),(0,a.jsxs)("button",{onClick:()=>N("rebates-by-level"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,a.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(x.yP,{data:{labels:m.rebatesByLevel.labels,datasets:[{label:"Rebates (₱)",data:m.rebatesByLevel.data,backgroundColor:"rgba(139, 92, 246, 0.5)",borderColor:"rgb(139, 92, 246)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},title:{display:!1}}}})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(c.qvi,{className:"mr-2 text-yellow-500"})," Users by Rank"]}),(0,a.jsxs)("button",{onClick:()=>N("users-by-rank"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,a.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,a.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-64 h-64",children:(0,a.jsx)(x.Fq,{data:{labels:m.usersByRank.labels,datasets:[{label:"Users",data:m.usersByRank.data,backgroundColor:["rgba(209, 213, 219, 0.8)","rgba(251, 191, 36, 0.8)","rgba(156, 163, 175, 0.8)","rgba(234, 179, 8, 0.8)","rgba(59, 130, 246, 0.8)","rgba(139, 92, 246, 0.8)"],borderColor:["rgb(209, 213, 219)","rgb(251, 191, 36)","rgb(156, 163, 175)","rgb(234, 179, 8)","rgb(59, 130, 246)","rgb(139, 92, 246)"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"},title:{display:!1}}}})})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(c.lcY,{className:"mr-2 text-green-500"})," Top Earners"]}),(0,a.jsxs)("button",{onClick:()=>N("top-earners"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,a.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Rebates"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.topEarners.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"Diamond"===e.rank?"bg-purple-100 text-purple-800":"Platinum"===e.rank?"bg-blue-100 text-blue-800":"Gold"===e.rank?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:e.rank})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["₱",e.totalRebates.toLocaleString()]})]},e.id))})]})})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(c.YXz,{className:"mr-2 text-blue-500"})," Top Recruiters"]}),(0,a.jsxs)("button",{onClick:()=>N("top-recruiters"),className:"text-sm flex items-center text-gray-600 hover:text-gray-900",children:[(0,a.jsx)(c.WCW,{className:"mr-1"})," Export"]})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Direct Downline"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.topRecruiters.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"Diamond"===e.rank?"bg-purple-100 text-purple-800":"Platinum"===e.rank?"bg-blue-100 text-blue-800":"Gold"===e.rank?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:e.rank})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.directDownlineCount})]},e.id))})]})})})]})]})]})]})}):(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}o.t1.register(o.PP,o.kc,o.E8,o.FN,o.No,o.Bs,o.hE,o.m_,o.s$)},46604:(e,s,t)=>{Promise.resolve().then(t.bind(t,42738))},54224:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["admin",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,16404)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\reports\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\reports\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/reports/page",pathname:"/admin/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,8414,9567,3877,474,4859,9947,3024,7081],()=>t(54224));module.exports=a})();
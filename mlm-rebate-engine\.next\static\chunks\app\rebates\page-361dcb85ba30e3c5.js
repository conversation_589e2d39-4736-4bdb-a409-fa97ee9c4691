(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5737],{39734:(e,t,s)=>{Promise.resolve().then(s.bind(s,43842))},43842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(95155),r=s(12115),l=s(12108),i=s(35695),n=s(87747),d=s(70357),c=s(51250);let o=function(e){let{items:t,height:s=400,width:l="100%",itemHeight:i=50,overscan:n=5,renderItem:d,keyExtractor:o,onEndReached:x,onEndReachedThreshold:m=.5,className:u="",itemClassName:p="",emptyComponent:h,loadingComponent:g,isLoading:j=!1}=e,b=(0,r.useRef)(null),[y,f]=(0,r.useState)(!1),N=(0,c.Te)({count:t.length,getScrollElement:()=>b.current,estimateSize:()=>i,overscan:n}),v=(0,r.useCallback)(()=>{if(!x||y)return;let{scrollHeight:e,scrollTop:t,clientHeight:s}=b.current;t+s>=e*m&&(f(!0),x(),setTimeout(()=>{f(!1)},1e3))},[x,y,m]);return j&&g?(0,a.jsx)("div",{style:{height:s,width:l},children:g}):0===t.length&&h?(0,a.jsx)("div",{style:{height:s,width:l},children:h}):(0,a.jsx)("div",{ref:b,className:"overflow-auto ".concat(u),style:{height:s,width:l},onScroll:x?v:void 0,children:(0,a.jsx)("div",{style:{height:"".concat(N.getTotalSize(),"px"),width:"100%",position:"relative"},children:N.getVirtualItems().map(e=>{let s=t[e.index],r=o(s,e.index);return(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full ".concat(p),style:{height:"".concat(e.size,"px"),transform:"translateY(".concat(e.start,"px)")},children:d(s,e.index)},r)})})})};var x=s(29911);function m(){let{data:e,status:t}=(0,l.useSession)(),s=(0,i.useRouter)(),[c,m]=(0,r.useState)([]),[u,p]=(0,r.useState)(!0),[h,g]=(0,r.useState)("all"),[j,b]=(0,r.useState)({totalRebates:0,totalAmount:0,pendingAmount:0,processedAmount:0,failedAmount:0,pendingCount:0,processedCount:0,failedCount:0}),[y,f]=(0,r.useState)({startDate:"",endDate:""}),[N,v]=(0,r.useState)(!1),[w,D]=(0,r.useState)(1),[S,k]=(0,r.useState)(10),[C,A]=(0,r.useState)(1),[F,L]=(0,r.useState)(0);(0,r.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]);let E=(0,r.useCallback)(async()=>{let e=new URLSearchParams;e.append("page",w.toString()),e.append("pageSize",S.toString()),"all"!==h&&e.append("status",h),y.startDate&&e.append("startDate",y.startDate),y.endDate&&e.append("endDate",y.endDate);let t=await fetch("/api/rebates?".concat(e.toString()));if(!t.ok)throw Error("Failed to fetch rebates: ".concat(t.statusText));let s=await t.json();return s.rebates&&Array.isArray(s.rebates)?{rebates:s.rebates,pagination:{totalPages:s.pagination.totalPages,totalItems:s.pagination.totalItems}}:{rebates:Array.isArray(s)?s:[],pagination:{totalPages:1,totalItems:Array.isArray(s)?s.length:0}}},[h,w,S,y]),P=(0,r.useCallback)(async()=>{let e=await fetch("/api/rebates/stats");if(!e.ok)throw Error("Failed to fetch rebate stats: ".concat(e.statusText));return await e.json()},[]),{data:R,isLoading:I,error:T}=(0,n.I)({queryKey:["rebates",h,w,S,y],queryFn:E,enabled:"authenticated"===t,staleTime:6e4,keepPreviousData:!0}),{data:Y,isLoading:_}=(0,n.I)({queryKey:["rebateStats"],queryFn:P,enabled:"authenticated"===t,staleTime:3e5});(0,r.useEffect)(()=>{R&&(m(R.rebates),A(R.pagination.totalPages),L(R.pagination.totalItems))},[R]),(0,r.useEffect)(()=>{Y&&b(Y)},[Y]),(0,r.useEffect)(()=>{p(I||_)},[I,_]);let q=e=>{let{name:t,value:s}=e.target;f(e=>({...e,[t]:s}))},z=e=>{e>0&&e<=C&&D(e)};c.reduce((e,t)=>e+t.amount,0);let O=c.reduce((e,t)=>(e[t.level]=(e[t.level]||0)+t.amount,e),{});return"loading"===t||u?(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-semibold mb-6 flex items-center",children:[(0,a.jsx)(x.lcY,{className:"mr-2 text-blue-500"})," Rebate Earnings"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,a.jsx)(x.lcY,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",j.totalAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[j.totalRebates," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,a.jsx)(x.CMH,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Processed"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",j.processedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[j.processedCount," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4",children:(0,a.jsx)(x.DIg,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Pending"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",j.pendingAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[j.pendingCount," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-red-100 text-red-500 mr-4",children:(0,a.jsx)(x.QCr,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Failed"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",j.failedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[j.failedCount," transactions"]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,a.jsx)(x.YYR,{className:"mr-2 text-blue-500"})," Earnings by Level"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[Object.entries(O).sort((e,t)=>{let[s]=e,[a]=t;return parseInt(s)-parseInt(a)}).map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500 font-medium",children:["Level ",t]}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",s.toFixed(2)]})]},t)}),0===Object.keys(O).length&&(0,a.jsx)("div",{className:"col-span-5 text-center py-4 text-gray-500",children:"No rebate data available for level breakdown"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,a.jsx)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:h,onChange:e=>g(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Statuses"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"processed",children:"Processed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"})]}),(0,a.jsxs)("button",{onClick:()=>v(!N),className:"px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center",children:[(0,a.jsx)(x.YsJ,{className:"mr-2"}),N?"Hide Date Filter":"Date Filter",N?(0,a.jsx)(x.Ucs,{className:"ml-2"}):(0,a.jsx)(x.Vr3,{className:"ml-2"})]})]})}),N&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,a.jsx)(x.bfZ,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"date",name:"startDate",value:y.startDate,onChange:q,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,a.jsx)(x.bfZ,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"date",name:"endDate",value:y.endDate,onChange:q,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]})]}),(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{g("all"),f({startDate:"",endDate:""})},className:"px-4 py-2 text-gray-700 hover:text-gray-900",children:"Reset Filters"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Rebate Transactions"})}),(0,a.jsx)("div",{className:"p-6",children:u?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(x.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading rebates..."})]}):c.length>0?(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"min-w-full border-b border-gray-200 mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-8 gap-2",children:[(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"From"}),(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("div",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Processed At"})]})}),(0,a.jsx)(o,{items:c,height:500,itemHeight:70,overscan:5,keyExtractor:e=>e.id,renderItem:e=>(0,a.jsxs)("div",{className:"grid grid-cols-8 gap-2 border-b border-gray-200 hover:bg-gray-50",children:[(0,a.jsx)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("div",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:(0,a.jsx)(x.x$1,{className:"text-gray-500"})}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.generator.name})})]})}),(0,a.jsx)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.purchase.product.name}),(0,a.jsxs)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["Level ",e.level]}),(0,a.jsxs)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.percentage,"%"]}),(0,a.jsxs)("div",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600",children:["₱",e.amount.toFixed(2)]}),(0,a.jsx)("div",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("processed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsx)("div",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.processedAt?new Date(e.processedAt).toLocaleString():"-"})]}),emptyComponent:(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No rebates found matching your criteria."}),loadingComponent:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(x.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading rebates..."})]}),onEndReached:()=>{w<C&&z(w+1)},onEndReachedThreshold:.8})]}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No rebates found matching your criteria."})}),c.length>0&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-700 mr-2",children:"Rows per page:"}),(0,a.jsxs)("select",{value:S,onChange:e=>{k(parseInt(e.target.value)),D(1)},className:"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"10",children:"10"}),(0,a.jsx)("option",{value:"25",children:"25"}),(0,a.jsx)("option",{value:"50",children:"50"}),(0,a.jsx)("option",{value:"100",children:"100"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700 mr-4",children:[w," of ",C," pages (",F," total rebates)"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>z(1),disabled:1===w,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"First"}),(0,a.jsx)("button",{onClick:()=>z(w-1),disabled:1===w,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>z(w+1),disabled:w===C,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"}),(0,a.jsx)("button",{onClick:()=>z(C),disabled:w===C,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Last"})]})]})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,6967,7747,1250,357,8441,1684,7358],()=>t(39734)),_N_E=e.O()}]);
(()=>{var e={};e.id=3853,e.ids=[3853],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},94552:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),l=t(29021),c=t(33873);async function p(e){try{let e=`
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="90" fill="#4CAF50" />
      <circle cx="100" cy="100" r="70" fill="#388E3C" />
      <path d="M100,30 Q130,60 100,90 Q70,60 100,30" fill="#8BC34A" />
      <path d="M60,70 Q90,100 60,130 Q30,100 60,70" fill="#8BC34A" />
      <path d="M140,70 Q170,100 140,130 Q110,100 140,70" fill="#8BC34A" />
      <path d="M100,110 Q130,140 100,170 Q70,140 100,110" fill="#8BC34A" />
      <circle cx="100" cy="100" r="25" fill="#FFEB3B" />
      <text x="100" y="190" font-family="Arial" font-size="16" text-anchor="middle" fill="white">EXTREME LIFE</text>
    </svg>
    `,r=c.join(process.cwd(),"public");return l.writeFileSync(c.join(r,"logo.svg"),e),a.NextResponse.json({success:!0,message:"Logo generated successfully"})}catch(e){return console.error("Error generating logo:",e),a.NextResponse.json({error:"Failed to generate logo"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/generate-logo/route",pathname:"/api/generate-logo",filename:"route",bundlePath:"app/api/generate-logo/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\generate-logo\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:x}=u;function f(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580],()=>t(94552));module.exports=s})();
"use strict";(()=>{var e={};e.id=8898,e.ids=[8898],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94429:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>A,routeModule:()=>j,serverHooks:()=>q,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>R});var s={};t.r(s),t.d(s,{DELETE:()=>P,GET:()=>y,POST:()=>N,PUT:()=>w});var n=t(96559),a=t(48088),i=t(37719),o=t(31183),u=t(32190),p=t(19854),l=t(12909),m=t(95613),c=t(81437),d=t(70762);let g=d.z.object({name:d.z.string().min(1,"Name is required"),sku:d.z.string().min(1,"SKU is required"),description:d.z.string().nullable().optional(),price:d.z.number().positive("Price must be positive"),pv:d.z.number().min(0,"PV must be non-negative"),binaryValue:d.z.number().min(0,"Binary Value must be non-negative").optional(),inventory:d.z.number().min(0,"Inventory must be non-negative").optional(),tags:d.z.string().nullable().optional(),image:d.z.string().nullable().optional(),isActive:d.z.boolean().optional(),referralCommissionType:d.z.enum(["percentage","fixed"]).nullable().optional(),referralCommissionValue:d.z.number().min(0).nullable().optional()}),v=d.z.object({id:d.z.number().int().positive(),name:d.z.string().min(1,"Name is required").optional(),sku:d.z.string().min(1,"SKU is required").optional(),description:d.z.string().nullable().optional(),price:d.z.number().positive("Price must be positive").optional(),pv:d.z.number().min(0,"PV must be non-negative").optional(),binaryValue:d.z.number().min(0,"Binary Value must be non-negative").optional(),inventory:d.z.number().min(0,"Inventory must be non-negative").optional(),tags:d.z.string().nullable().optional(),image:d.z.string().nullable().optional(),isActive:d.z.boolean().optional(),referralCommissionType:d.z.enum(["percentage","fixed"]).nullable().optional(),referralCommissionValue:d.z.number().min(0).nullable().optional()}),f=d.z.object({updates:d.z.array(v)}),h=d.z.object({id:d.z.number().int().positive(),newSku:d.z.string().min(1,"New SKU is required")}),b=d.z.object({id:d.z.number().int().positive(),isActive:d.z.boolean()});async function x(e){if(!e||!e.user||!e.user.email)return!1;let r=await o.z.user.findUnique({where:{email:e.user.email},select:{id:!0,rankId:!0}});return r?.rankId>=6}async function y(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!await x(r))return u.NextResponse.json({error:"You do not have permission to access this resource"},{status:403});let t=new URL(e.url);switch(t.searchParams.get("action")){case"template":let s=function(){let e=c.Wp.book_new(),r=c.Wp.json_to_sheet([{name:"Sample Product",sku:"PROD-001",description:"This is a sample product",price:100,pv:50,binaryValue:25,inventory:100,tags:"sample,product,template",image:"https://example.com/image.jpg",isActive:!0,referralCommissionType:"percentage",referralCommissionValue:10}]);return c.Wp.sheet_add_aoa(r,[["name*","sku*","description","price*","pv*","binaryValue","inventory","tags","image","isActive","referralCommissionType","referralCommissionValue"]],{origin:"A1"}),c.Wp.book_append_sheet(e,r,"Products"),c.M9(e,{type:"buffer",bookType:"xlsx"})}();return new u.NextResponse(s,{headers:{"Content-Type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","Content-Disposition":"attachment; filename=product_template.xlsx"}});case"export":let n=await o.z.product.findMany({orderBy:{name:"asc"}}),a=function(e){let r=c.Wp.book_new(),t=e.map(e=>({id:e.id,name:e.name,sku:e.sku,description:e.description,price:e.price,pv:e.pv,binaryValue:e.binaryValue,inventory:e.inventory,tags:e.tags,image:e.image,isActive:e.isActive,referralCommissionType:e.referralCommissionType,referralCommissionValue:e.referralCommissionValue,createdAt:e.createdAt?new Date(e.createdAt).toISOString():null,updatedAt:e.updatedAt?new Date(e.updatedAt).toISOString():null})),s=c.Wp.json_to_sheet(t);return c.Wp.book_append_sheet(r,s,"Products"),c.M9(r,{type:"buffer",bookType:"xlsx"})}(n);return new u.NextResponse(a,{headers:{"Content-Type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","Content-Disposition":"attachment; filename=products_export.xlsx"}});case"tags":let i=await (0,m.UH)();return u.NextResponse.json({tags:i});default:let d=t.searchParams.get("search")||void 0,g=t.searchParams.get("tags"),v=t.searchParams.get("isActive"),f=t.searchParams.get("minPrice"),h=t.searchParams.get("maxPrice"),b=t.searchParams.get("minPv"),y=t.searchParams.get("maxPv"),N=t.searchParams.get("minInventory"),w=t.searchParams.get("maxInventory"),P=t.searchParams.get("sortBy")||void 0,j=t.searchParams.get("sortOrder"),z=t.searchParams.get("page"),R=t.searchParams.get("pageSize"),q=g?g.split(","):void 0,A=f?parseFloat(f):void 0,S=h?parseFloat(h):void 0,k=b?parseFloat(b):void 0,V=y?parseFloat(y):void 0,C=N?parseInt(N):void 0,I=w?parseInt(w):void 0,_=z?parseInt(z):1,T=R?parseInt(R):10,U=await (0,m.d$)({search:d,tags:q,isActive:v?"true"===v:void 0,minPrice:A,maxPrice:S,minPv:k,maxPv:V,minInventory:C,maxInventory:I,sortBy:P,sortOrder:"desc"===j?"desc":"asc",page:_,pageSize:T});return u.NextResponse.json(U)}}catch(e){return console.error("Error in products API:",e),u.NextResponse.json({error:"An error occurred while processing your request"},{status:500})}}async function N(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!await x(r))return u.NextResponse.json({error:"You do not have permission to access this resource"},{status:403});let t=r.user.email,s=await o.z.user.findUnique({where:{email:t},select:{id:!0,name:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});if((e.headers.get("content-type")||"").includes("multipart/form-data")){let r=(await e.formData()).get("file");if(!r)return u.NextResponse.json({error:"No file provided"},{status:400});let t=Buffer.from(await r.arrayBuffer()),n=function(e){let r=c.LF(e,{type:"buffer"}),t=r.SheetNames[0],s=r.Sheets[t],n=c.Wp.sheet_to_json(s),a=[];for(let e of n){let r=function(e){let r=[];return(e.name||r.push("Name is required"),e.sku||r.push("SKU is required"),(void 0===e.price||isNaN(Number(e.price))||0>=Number(e.price))&&r.push("Price must be a positive number"),(void 0===e.pv||isNaN(Number(e.pv))||0>Number(e.pv))&&r.push("PV must be a non-negative number"),r.length>0)?{isValid:!1,errors:r}:{isValid:!0,errors:[],data:{name:String(e.name),sku:String(e.sku),description:e.description?String(e.description):null,price:Number(e.price),pv:Number(e.pv),binaryValue:void 0!==e.binaryValue?Number(e.binaryValue):0,inventory:void 0!==e.inventory?Number(e.inventory):0,tags:e.tags?String(e.tags):null,image:e.image?String(e.image):null,isActive:void 0===e.isActive||!!e.isActive,referralCommissionType:e.referralCommissionType?String(e.referralCommissionType):null,referralCommissionValue:void 0!==e.referralCommissionValue?Number(e.referralCommissionValue):null,userId:0,userName:""}}}(e);r.isValid&&r.data?a.push(r.data):console.error("Invalid row:",e,"Errors:",r.errors)}return a}(t);n.forEach(e=>{e.userId=s.id,e.userName=s.name});let a=await (0,m.MB)(n,s.id,s.name);return u.NextResponse.json(a)}{let r=await e.json();switch(r.action||"create"){case"create":let t=g.safeParse(r);if(!t.success)return u.NextResponse.json({error:t.error.errors},{status:400});let n=await (0,m.WY)({...t.data,userId:s.id,userName:s.name});return u.NextResponse.json(n);case"bulk_update":let a=f.safeParse(r);if(!a.success)return u.NextResponse.json({error:a.error.errors},{status:400});let i=await (0,m.Kc)(a.data.updates.map(e=>({id:e.id,data:e})),s.id,s.name);return u.NextResponse.json({updatedCount:i});case"clone":let o=h.safeParse(r);if(!o.success)return u.NextResponse.json({error:o.error.errors},{status:400});let p=await (0,m.p_)(o.data.id,o.data.newSku,s.id,s.name);return u.NextResponse.json(p);case"toggle_status":let l=b.safeParse(r);if(!l.success)return u.NextResponse.json({error:l.error.errors},{status:400});let c=await (0,m._T)(l.data.id,l.data.isActive,s.id,s.name);return u.NextResponse.json(c);default:return u.NextResponse.json({error:"Invalid action"},{status:400})}}}catch(e){return console.error("Error in products API:",e),u.NextResponse.json({error:e instanceof Error?e.message:"An error occurred while processing your request"},{status:500})}}async function w(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!await x(r))return u.NextResponse.json({error:"You do not have permission to access this resource"},{status:403});let t=r.user.email,s=await o.z.user.findUnique({where:{email:t},select:{id:!0,name:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let n=await e.json(),a=v.safeParse(n);if(!a.success)return u.NextResponse.json({error:a.error.errors},{status:400});let{id:i,...c}=a.data,d=await (0,m.vc)(i,{...c,userId:s.id,userName:s.name});return u.NextResponse.json(d)}catch(e){return console.error("Error in products API:",e),u.NextResponse.json({error:e instanceof Error?e.message:"An error occurred while processing your request"},{status:500})}}async function P(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!await x(r))return u.NextResponse.json({error:"You do not have permission to access this resource"},{status:403});let t=r.user.email,s=await o.z.user.findUnique({where:{email:t},select:{id:!0,name:!0}});if(!s)return u.NextResponse.json({error:"User not found"},{status:404});let n=new URL(e.url).searchParams.get("id");if(!n)return u.NextResponse.json({error:"Product ID is required"},{status:400});let a=parseInt(n),i=await (0,m.DD)(a,s.id,s.name);return u.NextResponse.json(i)}catch(e){return console.error("Error in products API:",e),u.NextResponse.json({error:e instanceof Error?e.message:"An error occurred while processing your request"},{status:500})}}let j=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/products/route",pathname:"/api/admin/products",filename:"route",bundlePath:"app/api/admin/products/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:z,workUnitAsyncStorage:R,serverHooks:q}=j;function A(){return(0,i.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:R})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,8381,632,373],()=>t(94429));module.exports=s})();
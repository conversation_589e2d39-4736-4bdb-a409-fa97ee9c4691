"use strict";(()=>{var e={};e.id=8616,e.ids=[8616],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12269:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{e.exports=require("assert")},19854:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var n={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var s=t(12269);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var u=s?Object.getOwnPropertyDescriptor(e,a):null;u&&(u.get||u.set)?Object.defineProperty(n,a,u):n[a]=e[a]}return n.default=e,t&&t.set(e,n),n}(t(35426));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42214:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>y});var n={};t.r(n),t.d(n,{GET:()=>f});var s=t(96559),a=t(48088),o=t(37719),u=t(31183),i=t(32190),p=t(19854),c=t(12909),l=t(95613);async function d(e){if(!e||!e.user||!e.user.email)return!1;let r=await u.z.user.findUnique({where:{email:e.user.email},select:{id:!0,rankId:!0}});return r?.rankId>=6}async function f(e,{params:r}){try{let t=await (0,p.getServerSession)(c.Nh);if(!await d(t))return i.NextResponse.json({error:"You do not have permission to access this resource"},{status:403});let n=parseInt(r.id);if(isNaN(n))return i.NextResponse.json({error:"Invalid product ID"},{status:400});let s=new URL(e.url);switch(s.searchParams.get("action")){case"audit":let a=s.searchParams.get("limit"),o=s.searchParams.get("offset"),u=a?parseInt(a):10,f=o?parseInt(o):0,x=await (0,l.qn)(n,u,f);return i.NextResponse.json(x);case"sales":let m=s.searchParams.get("months"),y=m?parseInt(m):12,h=await (0,l.EC)(n,y);return i.NextResponse.json({salesHistory:h});case"simulate":let j=s.searchParams.get("quantity"),v=s.searchParams.get("maxLevel"),g=j?parseInt(j):1,P=v?parseInt(v):6,w=await (0,l.AG)(n,g,P);return i.NextResponse.json({simulation:w});default:let b=await (0,l.y9)(n);if(!b)return i.NextResponse.json({error:"Product not found"},{status:404});return i.NextResponse.json(b)}}catch(e){return console.error("Error in product details API:",e),i.NextResponse.json({error:"An error occurred while processing your request"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/products/[id]/route",pathname:"/api/admin/products/[id]",filename:"route",bundlePath:"app/api/admin/products/[id]/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:m,workUnitAsyncStorage:y,serverHooks:h}=x;function j(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:y})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4243,580,8044,3112,373],()=>t(42214));module.exports=n})();
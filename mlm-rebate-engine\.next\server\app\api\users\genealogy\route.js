(()=>{var e={};e.id=1380,e.ids=[1380],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3383:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),o=t(48088),a=t(37719),i=t(31183),l=t(32190),u=t(19854),c=t(12909);async function d(e,r=1,t=10){if(r>t)return[];let s=await i.z.user.findMany({where:{uplineId:e},select:{id:!0,name:!0,email:!0,rankId:!0,walletBalance:!0,createdAt:!0,rank:{select:{name:!0,level:!0}}}});return await Promise.all(s.map(async e=>{let s=await d(e.id,r+1,t);return{...e,children:s,level:r}}))}async function p(e){try{let r,t=await (0,u.getServerSession)(c.Nh);if(!t||!t.user)return l.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let s=new URL(e.url),n=s.searchParams.get("userId"),o=parseInt(s.searchParams.get("maxDepth")||"10"),a="true"===s.searchParams.get("includeStats");r=n?parseInt(n):parseInt(t.user.id);let p=await i.z.user.findUnique({where:{id:r},select:{id:!0,name:!0,email:!0,rankId:!0,walletBalance:!0,createdAt:!0,rank:{select:{name:!0,level:!0}}}});if(!p)return l.NextResponse.json({error:"User not found"},{status:404});let g=await d(r,1,o),f={...p,children:g,level:0},h=null;if(a){let e={},r=(t,s)=>{e[s]=(e[s]||0)+1,t.children&&t.children.length>0&&t.children.forEach(e=>{r(e,s+1)})};r(f,0);let t=Object.values(e).reduce((e,r)=>e+r,0),s=0,n=e=>{e.children&&e.children.length>0&&e.children.forEach(e=>{s+=e.walletBalance||0,n(e)})};n(f),h={totalUsers:t,levelCounts:e,totalDownlineBalance:s,directDownlineCount:g.length}}return l.NextResponse.json({genealogy:f,statistics:h})}catch(e){return console.error("Error fetching genealogy:",e),l.NextResponse.json({error:"Failed to fetch genealogy"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/users/genealogy/route",pathname:"/api/users/genealogy",filename:"route",bundlePath:"app/api/users/genealogy/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\genealogy\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:w}=g;function m(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>l,Nh:()=>c,aP:()=>u});var s=t(96330),n=t(13581),o=t(85663),a=t(55511),i=t.n(a);async function l(e){return await o.Ay.hash(e,10)}function u(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await o.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:a,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var n=t(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(s,o,i):s[o]=e[o]}return s.default=e,t&&t.set(e,s),s}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(3383));module.exports=s})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2431],{31168:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(95155),n=r(12115),s=r(12108),l=r(35695),c=r(29911),o=r(6874),i=r.n(o),d=r(3925);function m(e){let{userId:t,userName:r}=e,[s,l]=(0,n.useState)(!1),[o,i]=(0,n.useState)("excel"),[m,u]=(0,n.useState)(!0),[x,h]=(0,n.useState)(3),[p,b]=(0,n.useState)(null),[f,g]=(0,n.useState)(null),y=async()=>{l(!0),b(null),g(null);try{let e=await fetch("/api/genealogy?userId=".concat(t,"&maxLevel=").concat(x,"&includePerformanceMetrics=").concat(m));if(!e.ok)throw Error("Failed to fetch genealogy data");let a=await e.json(),n=j(a,m);switch(o){case"excel":v(n,"".concat(r,"_genealogy"));break;case"csv":N(n,"".concat(r,"_genealogy"));break;case"pdf":await w(n,"".concat(r,"_genealogy"))}b(!0)}catch(e){console.error("Export error:",e),g(e instanceof Error?e.message:"An unknown error occurred"),b(!1)}finally{l(!1)}},j=(e,t)=>{let r=[],a=function(e,n){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",l={ID:e.id,Name:e.name,Email:e.email,Rank:e.rank.name,Level:n,"Downline Count":e._count.downline,"Parent Name":s,"Joined Date":e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"};void 0!==e.walletBalance&&(l["Wallet Balance"]=e.walletBalance),t&&e.performanceMetrics&&(l["Personal Sales"]=e.performanceMetrics.personalSales,l["Team Sales"]=e.performanceMetrics.teamSales,l["Total Sales"]=e.performanceMetrics.totalSales,l["Rebates Earned"]=e.performanceMetrics.rebatesEarned,l["Team Size"]=e.performanceMetrics.teamSize,l["New Team Members (30d)"]=e.performanceMetrics.newTeamMembers),r.push(l),e.children&&e.children.length>0&&e.children.forEach(t=>{a(t,n+1,e.name)})};return a(e,0),r},v=(e,t)=>{let r=d.Wp.json_to_sheet(e),a=d.Wp.book_new();d.Wp.book_append_sheet(a,r,"Genealogy");let n=e.reduce((e,t)=>(Object.keys(t).forEach(r=>{let a=String(t[r]);e[r]=Math.max(e[r]||0,a.length)}),e),{});r["!cols"]=Object.keys(n).map(e=>({wch:n[e]+2})),d._h(a,"".concat(t,".xlsx"))},N=(e,t)=>{let r=d.Wp.json_to_sheet(e),a=new Blob([d.Wp.sheet_to_csv(r)],{type:"text/csv;charset=utf-8;"}),n=URL.createObjectURL(a),s=document.createElement("a");s.href=n,s.setAttribute("download","".concat(t,".csv")),document.body.appendChild(s),s.click(),document.body.removeChild(s)},w=async(e,t)=>{let a='<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';e.length>0&&(a+='<thead><tr style="background-color: #f2f2f2;">',Object.keys(e[0]).forEach(e=>{a+="<th>".concat(e,"</th>")}),a+="</tr></thead>"),a+="<tbody>",e.forEach(e=>{a+="<tr>",Object.values(e).forEach(e=>{a+="<td>".concat(e,"</td>")}),a+="</tr>"}),a+="</tbody></table>";let n=window.open("","_blank");if(!n)throw Error("Failed to open print window. Please check your popup blocker settings.");n.document.write("\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>".concat(t,"</title>\n          <style>\n            body { font-family: Arial, sans-serif; }\n            h1 { text-align: center; }\n            table { width: 100%; border-collapse: collapse; }\n            th { background-color: #f2f2f2; }\n            th, td { padding: 8px; border: 1px solid #ddd; }\n            tr:nth-child(even) { background-color: #f9f9f9; }\n          </style>\n        </head>\n        <body>\n          <h1>Genealogy Export: ").concat(r,"</h1>\n          <p>Generated on: ").concat(new Date().toLocaleString(),"</p>\n          ").concat(a,"\n        </body>\n      </html>\n    ")),n.document.close(),setTimeout(()=>{n.print()},500)};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,a.jsx)(c.Mbn,{className:"mr-2 text-blue-500"}),"Export Genealogy Data"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Export Format"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>i("excel"),className:"flex-1 flex items-center justify-center px-4 py-2 border rounded-md ".concat("excel"===o?"bg-green-50 border-green-500 text-green-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)(c.Ru,{className:"mr-2"}),"Excel"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>i("csv"),className:"flex-1 flex items-center justify-center px-4 py-2 border rounded-md ".concat("csv"===o?"bg-blue-50 border-blue-500 text-blue-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)(c.QaY,{className:"mr-2"}),"CSV"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>i("pdf"),className:"flex-1 flex items-center justify-center px-4 py-2 border rounded-md ".concat("pdf"===o?"bg-red-50 border-red-500 text-red-700":"border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)(c.kl1,{className:"mr-2"}),"PDF"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Maximum Levels to Export"}),(0,a.jsxs)("select",{value:x,onChange:e=>h(parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:1,children:"1 Level (Direct Downline Only)"}),(0,a.jsx)("option",{value:2,children:"2 Levels"}),(0,a.jsx)("option",{value:3,children:"3 Levels"}),(0,a.jsx)("option",{value:4,children:"4 Levels"}),(0,a.jsx)("option",{value:5,children:"5 Levels"}),(0,a.jsx)("option",{value:6,children:"6 Levels (Maximum)"})]})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"includePerformanceMetrics",checked:m,onChange:e=>u(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"includePerformanceMetrics",className:"ml-2 block text-sm text-gray-700",children:"Include Performance Metrics (sales, rebates, etc.)"})]})}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{type:"button",onClick:y,disabled:s,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed flex items-center",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin mr-2"}),"Exporting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.Mbn,{className:"mr-2"}),"Export Genealogy"]})})}),null!==p&&(0,a.jsx)("div",{className:"mt-4 p-3 rounded-md ".concat(p?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:(0,a.jsx)("div",{className:"flex items-center",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.CMH,{className:"mr-2"}),(0,a.jsx)("span",{children:"Export completed successfully!"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.QCr,{className:"mr-2"}),(0,a.jsxs)("span",{children:["Export failed: ",f]})]})})})]})}function u(){let{data:e,status:t}=(0,s.useSession)(),r=(0,l.useSearchParams)().get("userId"),[o,d]=(0,n.useState)(void 0),[u,x]=(0,n.useState)(""),[h,p]=(0,n.useState)(!0),[b,f]=(0,n.useState)(null);return((0,n.useEffect)(()=>{(async()=>{if("authenticated"===t){p(!0),f(null);try{var a;let t=r||(null==e||null==(a=e.user)?void 0:a.id);if(!t)throw Error("No user ID available");let n=await fetch("/api/users/".concat(t));if(!n.ok)throw Error("Failed to fetch user data");let s=await n.json();d(s.id),x(s.name)}catch(e){f(e instanceof Error?e.message:"An unknown error occurred")}finally{p(!1)}}})()},[t,r,e]),"loading"===t||h)?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})}):"unauthenticated"===t?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,a.jsx)(c.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to export genealogy data."}),(0,a.jsx)(i(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):b?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:[(0,a.jsx)("p",{className:"font-medium",children:"Error"}),(0,a.jsx)("p",{children:b})]})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Export Genealogy Data"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Export your genealogy data in various formats"})]}),(0,a.jsxs)(i(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(c.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:o&&(0,a.jsx)(m,{userId:o,userName:u})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,a.jsx)(c.__w,{className:"mr-2 text-blue-500"}),"Export Information"]}),(0,a.jsxs)("div",{className:"space-y-4 text-sm",children:[(0,a.jsx)("p",{children:"Export your genealogy data to analyze it in external tools or share it with your team."}),(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-700 mb-2",children:"Available Export Formats"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Excel (.xlsx)"})," - Best for detailed analysis and formatting"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"CSV"})," - Compatible with most spreadsheet and database applications"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"PDF"})," - Best for printing and sharing"]})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"font-medium text-yellow-700 mb-2",children:"Export Tips"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,a.jsx)("li",{children:"Exporting more levels will result in larger files"}),(0,a.jsx)("li",{children:"Including performance metrics provides more detailed data"}),(0,a.jsx)("li",{children:"PDF export opens a print dialog for saving or printing"})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,a.jsx)("h4",{className:"font-medium text-green-700 mb-2",children:"Data Privacy"}),(0,a.jsx)("p",{className:"text-green-700",children:"Exported data may contain sensitive information. Please handle it securely and in accordance with your organization's data privacy policies."})]})]})]})})]})]})}},32383:()=>{},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},46491:(e,t,r)=>{Promise.resolve().then(r.bind(r,31168))},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var a=r(12115),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=a.createContext&&a.createContext(n),l=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var a,n,s;a=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in a?Object.defineProperty(a,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>a.createElement(m,c({attr:i({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,i({key:r},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var r,{attr:n,size:s,title:o}=e,d=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,l),m=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,d,{className:r,style:i(i({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),o&&a.createElement("title",null,o),e.children)};return void 0!==s?a.createElement(s.Consumer,null,e=>t(e)):t(n)}},83686:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3524,6874,2108,8441,1684,7358],()=>t(46491)),_N_E=e.O()}]);
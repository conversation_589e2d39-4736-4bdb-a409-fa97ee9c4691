(()=>{var e={};e.id=1689,e.ids=[1689],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>a,_:()=>i});var r=s(60687),n=s(43210);let l=(0,n.createContext)(void 0),i=()=>{let e=(0,n.useContext)(l);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},a=({children:e})=>{let[t,s]=(0,n.useState)([]);(0,n.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,n.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let i=e=>{s(t=>t.filter(t=>t.id!==e))},a=t.reduce((e,t)=>e+t.quantity,0),o=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,r.jsx)(l.Provider,{value:{items:t,addItem:e=>{s(t=>{let s=t.findIndex(t=>t.id===e.id);if(!(s>=0))return[...t,e];{let r=[...t];return r[s]={...r[s],quantity:r[s].quantity+e.quantity},r}})},removeItem:i,updateQuantity:(e,t)=>{if(t<=0)return void i(e);s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{s([])},itemCount:a,subtotal:o,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>n});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},43841:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(60687),n=s(43210),l=s(82136),i=s(23877),a=s(85814),o=s.n(a),d=s(33103);function c({onImportComplete:e}){let[t,s]=(0,n.useState)(null),[l,a]=(0,n.useState)("excel"),[o,c]=(0,n.useState)(!1),[m,x]=(0,n.useState)(null),[u,h]=(0,n.useState)(null),[p,g]=(0,n.useState)(null),[b,f]=(0,n.useState)(!1),[y,j]=(0,n.useState)({}),[v,N]=(0,n.useState)([]),[w]=(0,n.useState)(["id","name","email","uplineId","rankName"]),S=(0,n.useRef)(null),C=async()=>{if(t){c(!0),x(null),h(null);try{let e=[];if("excel"===l||"csv"===l?e=await P(t):"json"===l&&(e=await k(t)),!e||0===e.length)throw Error("No data found in the file.");g(e),e.length>0&&N(Object.keys(e[0])),f(!0),x(!0)}catch(e){console.error("Upload error:",e),h(e instanceof Error?e.message:"An unknown error occurred"),x(!1)}finally{c(!1)}}},P=e=>new Promise((t,s)=>{let r=new FileReader;r.onload=e=>{try{let r=e.target?.result;if(!r)return void s(Error("Failed to read file."));let n=d.LF(r,{type:"binary"}),l=n.SheetNames[0],i=n.Sheets[l],a=d.Wp.sheet_to_json(i);t(a)}catch(e){s(e)}},r.onerror=()=>{s(Error("Failed to read file."))},r.readAsBinaryString(e)}),k=e=>new Promise((t,s)=>{let r=new FileReader;r.onload=e=>{try{let r=e.target?.result;if(!r)return void s(Error("Failed to read file."));let n=JSON.parse(r);if(!Array.isArray(n))return void s(Error("JSON file must contain an array of objects."));t(n)}catch(e){s(e)}},r.onerror=()=>{s(Error("Failed to read file."))},r.readAsText(e)}),I=(e,t)=>{j(s=>({...s,[e]:t}))},E=async()=>{if(p){c(!0);try{let t=p.map(e=>{let t={};return Object.entries(y).forEach(([s,r])=>{r&&void 0!==e[r]&&(t[s]=e[r])}),t}),r=[];if(w.forEach(e=>{y[e]||r.push(e)}),r.length>0)throw Error(`Missing required fields: ${r.join(", ")}`);e&&e(t),s(null),g(null),f(!1),j({}),x(!0),S.current&&(S.current.value="")}catch(e){console.error("Import error:",e),h(e instanceof Error?e.message:"An unknown error occurred"),x(!1)}finally{c(!1)}}};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,r.jsx)(i.PiR,{className:"mr-2 text-blue-500"}),"Import Genealogy Data"]}),b?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("h4",{className:"font-medium mb-2 flex items-center",children:[(0,r.jsx)(i.BS8,{className:"mr-2 text-yellow-500"}),"Map Fields"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Please map the fields from your file to the required fields in our system."}),(0,r.jsx)("div",{className:"space-y-3",children:w.map(e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("div",{className:"w-1/3 font-medium text-sm",children:[e.charAt(0).toUpperCase()+e.slice(1),":"]}),(0,r.jsx)("div",{className:"w-2/3",children:(0,r.jsxs)("select",{value:y[e]||"",onChange:t=>I(e,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"-- Select Field --"}),v.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})})]},e))})]}),p&&p.length>0&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Data Preview"}),(0,r.jsxs)("div",{className:"overflow-x-auto",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsx)("tr",{children:Object.keys(p[0]).slice(0,5).map(e=>(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e},e))})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.slice(0,3).map((e,t)=>(0,r.jsx)("tr",{children:Object.keys(e).slice(0,5).map(t=>(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:String(e[t])},t))},t))})]}),p.length>3&&(0,r.jsxs)("div",{className:"text-center text-sm text-gray-500 mt-2",children:["Showing 3 of ",p.length," rows"]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{type:"button",onClick:()=>{s(null),g(null),f(!1),j({}),x(null),h(null),S.current&&(S.current.value="")},className:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"button",onClick:E,disabled:o,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:o?(0,r.jsx)(i.hW,{className:"animate-spin"}):"Import Data"})]}),!1===m&&(0,r.jsx)("div",{className:"mt-4 p-3 rounded-md bg-red-50 text-red-700",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.QCr,{className:"mr-2"}),(0,r.jsxs)("span",{children:["Import failed: ",u]})]})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select File to Import"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"file",ref:S,onChange:e=>{let t=e.target.files?.[0];if(!t)return;s(t),x(null),h(null),g(null);let r=t.name.split(".").pop()?.toLowerCase();"xlsx"===r||"xls"===r?a("excel"):"csv"===r?a("csv"):"json"===r?a("json"):(h("Unsupported file type. Please upload an Excel, CSV, or JSON file."),s(null))},accept:".xlsx,.xls,.csv,.json",className:"hidden",id:"file-upload"}),(0,r.jsx)("label",{htmlFor:"file-upload",className:"flex-1 px-4 py-2 border border-gray-300 rounded-l-md bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer truncate",children:t?t.name:"Choose file..."}),(0,r.jsx)("button",{type:"button",onClick:C,disabled:!t||o,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:o?(0,r.jsx)(i.hW,{className:"animate-spin"}):"Upload"})]}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Supported file types: Excel (.xlsx, .xls), CSV (.csv), JSON (.json)"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"p-3 border rounded-md flex flex-col items-center",children:[(0,r.jsx)(i.Ru,{className:"text-green-500 text-2xl mb-2"}),(0,r.jsx)("div",{className:"text-sm font-medium",children:"Excel"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:".xlsx, .xls"})]}),(0,r.jsxs)("div",{className:"p-3 border rounded-md flex flex-col items-center",children:[(0,r.jsx)(i.QaY,{className:"text-blue-500 text-2xl mb-2"}),(0,r.jsx)("div",{className:"text-sm font-medium",children:"CSV"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:".csv"})]}),(0,r.jsxs)("div",{className:"p-3 border rounded-md flex flex-col items-center",children:[(0,r.jsx)(i.reA,{className:"text-yellow-500 text-2xl mb-2"}),(0,r.jsx)("div",{className:"text-sm font-medium",children:"JSON"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:".json"})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md mb-4",children:[(0,r.jsxs)("h4",{className:"font-medium text-blue-700 mb-2 flex items-center",children:[(0,r.jsx)(i.__w,{className:"mr-1"}),"Import Instructions"]}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700 text-sm",children:[(0,r.jsx)("li",{children:"Prepare your data in Excel, CSV, or JSON format"}),(0,r.jsx)("li",{children:"Ensure your file includes required fields: ID, Name, Email, Upline ID, and Rank"}),(0,r.jsx)("li",{children:"Upload the file and map the fields to the required fields"}),(0,r.jsx)("li",{children:"Review the data before importing"}),(0,r.jsx)("li",{children:"Click Import to add the data to your genealogy"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Import from External Systems"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("button",{type:"button",className:"p-3 border rounded-md flex items-center justify-center hover:bg-gray-50",onClick:()=>alert("CRM integration would be implemented here"),children:[(0,r.jsx)(i.nBS,{className:"mr-2 text-purple-500"}),(0,r.jsx)("span",{children:"Import from CRM"})]}),(0,r.jsxs)("button",{type:"button",className:"p-3 border rounded-md flex items-center justify-center hover:bg-gray-50",onClick:()=>alert("Cloud integration would be implemented here"),children:[(0,r.jsx)(i.BzO,{className:"mr-2 text-blue-500"}),(0,r.jsx)("span",{children:"Import from Cloud"})]})]})]}),null!==m&&(0,r.jsx)("div",{className:`mt-4 p-3 rounded-md ${m?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,children:(0,r.jsx)("div",{className:"flex items-center",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.CMH,{className:"mr-2"}),(0,r.jsx)("span",{children:"File uploaded successfully! Please map the fields."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.QCr,{className:"mr-2"}),(0,r.jsxs)("span",{children:["Upload failed: ",u]})]})})})]})]})}function m({onSyncComplete:e}){let[t,s]=(0,n.useState)([{id:"crm1",name:"Sales CRM",type:"crm",status:"connected",lastSync:"2023-10-15T14:30:00Z",icon:(0,r.jsx)(i.nBS,{className:"text-purple-500"})},{id:"cloud1",name:"Cloud Storage",type:"cloud",status:"connected",lastSync:"2023-10-14T09:15:00Z",icon:(0,r.jsx)(i.tmj,{className:"text-blue-500"})},{id:"api1",name:"External API",type:"api",status:"disconnected",icon:(0,r.jsx)(i.Pcn,{className:"text-gray-500"})}]),[l,a]=(0,n.useState)(null),[o,d]=(0,n.useState)(null),[c,m]=(0,n.useState)(null),[x,u]=(0,n.useState)([{systemId:"crm1",timestamp:"2023-10-15T14:30:00Z",success:!0,message:"Successfully synchronized 156 records"},{systemId:"cloud1",timestamp:"2023-10-14T09:15:00Z",success:!0,message:"Successfully synchronized 142 records"},{systemId:"api1",timestamp:"2023-10-10T11:45:00Z",success:!1,message:"Connection timeout"}]),[h,p]=(0,n.useState)(!1),g=async r=>{a(r.id),d(null),m(null);try{if(await new Promise(e=>setTimeout(e,2e3)),!(Math.random()>.2))throw Error("Failed to synchronize with external system.");let n=t.map(e=>e.id===r.id?{...e,lastSync:new Date().toISOString()}:e);s(n),u(e=>[{systemId:r.id,timestamp:new Date().toISOString(),success:!0,message:"Successfully synchronized records"},...e]),d(!0),e&&e(r)}catch(e){console.error("Sync error:",e),u(t=>[{systemId:r.id,timestamp:new Date().toISOString(),success:!1,message:e instanceof Error?e.message:"An unknown error occurred"},...t]),m(e instanceof Error?e.message:"An unknown error occurred"),d(!1)}finally{a(null)}},b=async e=>{a(e.id),d(null),m(null);try{await new Promise(e=>setTimeout(e,2e3));let r=t.map(t=>t.id===e.id?{...t,status:"connected"}:t);s(r),u(t=>[{systemId:e.id,timestamp:new Date().toISOString(),success:!0,message:`Successfully connected to ${e.name}`},...t]),d(!0)}catch(t){console.error("Connect error:",t),u(s=>[{systemId:e.id,timestamp:new Date().toISOString(),success:!1,message:t instanceof Error?t.message:"An unknown error occurred"},...s]),m(t instanceof Error?t.message:"An unknown error occurred"),d(!1)}finally{a(null)}},f=e=>new Date(e).toLocaleString(),y=e=>{if(!e)return"Never";let t=new Date(e),s=Math.floor(Math.floor((new Date().getTime()-t.getTime())/1e3)/60),r=Math.floor(s/60),n=Math.floor(r/24);return n>0?`${n} day${n>1?"s":""} ago`:r>0?`${r} hour${r>1?"s":""} ago`:s>0?`${s} minute${s>1?"s":""} ago`:"Just now"};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.DIg,{className:"mr-2 text-blue-500"}),"Sync with External Systems"]}),(0,r.jsxs)("button",{onClick:()=>p(!h),className:"text-sm text-blue-600 flex items-center",children:[(0,r.jsx)(i.OKX,{className:"mr-1"}),h?"Hide History":"Show History"]})]}),h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("h4",{className:"font-medium mb-2 flex items-center",children:[(0,r.jsx)(i.OKX,{className:"mr-2 text-blue-500"}),"Synchronization History"]}),0===x.length?(0,r.jsx)("div",{className:"text-center text-gray-500 p-4 border rounded-md",children:"No synchronization history available."}):(0,r.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"System"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Message"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:x.map((e,s)=>{let n=t.find(t=>t.id===e.systemId);return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-2",children:n?.icon}),(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:n?.name||e.systemId})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:f(e.timestamp)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.success?(0,r.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"Success"}):(0,r.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:"Failed"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.message})]},s)})})]})})]}),(0,r.jsx)("button",{onClick:()=>p(!1),className:"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:"Back to Systems"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"space-y-4 mb-4",children:t.map(e=>(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-3 text-xl",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Last sync: ",e.lastSync?y(e.lastSync):"Never"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-3",children:"connected"===e.status?(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"Connected"}):"error"===e.status?(0,r.jsx)("span",{className:"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full",children:"Error"}):(0,r.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full",children:"Disconnected"})}),"connected"===e.status?(0,r.jsx)("button",{onClick:()=>g(e),disabled:l===e.id,className:"px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed text-sm flex items-center",children:l===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.hW,{className:"animate-spin mr-1"}),"Syncing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.DIg,{className:"mr-1"}),"Sync Now"]})}):(0,r.jsx)("button",{onClick:()=>b(e),disabled:l===e.id,className:"px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-green-300 disabled:cursor-not-allowed text-sm flex items-center",children:l===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.hW,{className:"animate-spin mr-1"}),"Connecting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.CMH,{className:"mr-1"}),"Connect"]})})]})]}),"connected"===e.status&&(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t grid grid-cols-2 gap-2",children:[(0,r.jsxs)("button",{onClick:()=>alert(`Import from ${e.name} would be implemented here`),className:"p-2 border rounded-md flex items-center justify-center hover:bg-gray-50 text-sm",children:[(0,r.jsx)(i.mSE,{className:"mr-2 text-blue-500"}),(0,r.jsxs)("span",{children:["Import from ",e.name]})]}),(0,r.jsxs)("button",{onClick:()=>alert(`Export to ${e.name} would be implemented here`),className:"p-2 border rounded-md flex items-center justify-center hover:bg-gray-50 text-sm",children:[(0,r.jsx)(i.BzO,{className:"mr-2 text-green-500"}),(0,r.jsxs)("span",{children:["Export to ",e.name]})]})]})]},e.id))}),(0,r.jsx)("div",{className:"border rounded-md p-4 border-dashed flex items-center justify-center",children:(0,r.jsxs)("button",{onClick:()=>alert("Add new external system would be implemented here"),className:"p-2 text-blue-600 flex items-center",children:[(0,r.jsx)(FaPlus,{className:"mr-2"}),"Add New External System"]})}),(0,r.jsxs)("div",{className:"mt-4 bg-blue-50 p-3 rounded-md",children:[(0,r.jsxs)("h4",{className:"font-medium text-blue-700 mb-2 flex items-center",children:[(0,r.jsx)(i.__w,{className:"mr-1"}),"About Synchronization"]}),(0,r.jsx)("p",{className:"text-sm text-blue-700 mb-2",children:"Synchronizing with external systems allows you to keep your genealogy data up-to-date across multiple platforms."}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700 text-sm",children:[(0,r.jsx)("li",{children:"Connect to your CRM, cloud storage, or other systems"}),(0,r.jsx)("li",{children:"Import new members and updates from external systems"}),(0,r.jsx)("li",{children:"Export your genealogy data to external systems"}),(0,r.jsx)("li",{children:"Schedule automatic synchronization"})]})]}),null!==o&&(0,r.jsx)("div",{className:`mt-4 p-3 rounded-md ${o?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,children:(0,r.jsx)("div",{className:"flex items-center",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.CMH,{className:"mr-2"}),(0,r.jsx)("span",{children:"Synchronization completed successfully!"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.QCr,{className:"mr-2"}),(0,r.jsxs)("span",{children:["Synchronization failed: ",c]})]})})})]})]})}function x(){let{data:e,status:t}=(0,l.useSession)(),[s,a]=(0,n.useState)("import");return"loading"===t?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(i.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})}):"unauthenticated"===t?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(i.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to access genealogy integration features."}),(0,r.jsx)(o(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Integration"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Import, export, and synchronize your genealogy data with external systems"})]}),(0,r.jsxs)(o(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(i.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsxs)("button",{onClick:()=>a("import"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"import"===s?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,r.jsx)(i.PiR,{className:"inline mr-2"}),"Import Data"]}),(0,r.jsxs)("button",{onClick:()=>a("sync"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"sync"===s?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,r.jsx)(i.DIg,{className:"inline mr-2"}),"Sync External Systems"]})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:"import"===s?(0,r.jsx)(c,{onImportComplete:e=>{console.log("Import complete:",e),alert(`Import completed successfully! ${e.length} records imported.`)}}):(0,r.jsx)(m,{onSyncComplete:e=>{console.log("Sync complete:",e),alert(`Synchronization with ${e.name} completed successfully!`)}})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,r.jsx)(i.O2x,{className:"mr-2 text-blue-500"}),"Integration Information"]}),(0,r.jsx)("div",{className:"space-y-4",children:"import"===s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"Import your genealogy data from external sources to keep your network up-to-date."}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-700 mb-2",children:"Supported Import Formats"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"Excel (.xlsx, .xls) - Spreadsheet format"}),(0,r.jsx)("li",{children:"CSV (.csv) - Comma-separated values"}),(0,r.jsx)("li",{children:"JSON (.json) - JavaScript Object Notation"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-700 mb-2",children:"Required Fields"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsx)("li",{children:"ID - Unique identifier for each member"}),(0,r.jsx)("li",{children:"Name - Member's full name"}),(0,r.jsx)("li",{children:"Email - Member's email address"}),(0,r.jsx)("li",{children:"Upline ID - ID of the member's upline"}),(0,r.jsx)("li",{children:"Rank Name - Member's rank in the organization"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-green-700 mb-2",children:"Optional Fields"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Wallet Balance - Member's current wallet balance"}),(0,r.jsx)("li",{children:"Join Date - Date when the member joined"}),(0,r.jsx)("li",{children:"Phone - Member's phone number"}),(0,r.jsx)("li",{children:"Address - Member's address"}),(0,r.jsx)("li",{children:"Performance Metrics - Sales, rebates, etc."})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"Synchronize your genealogy data with external systems to maintain consistency across platforms."}),(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-700 mb-2",children:"Supported Systems"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"CRM Systems - Salesforce, HubSpot, etc."}),(0,r.jsx)("li",{children:"Cloud Storage - Google Drive, Dropbox, etc."}),(0,r.jsx)("li",{children:"External APIs - Custom integrations"}),(0,r.jsx)("li",{children:"Other MLM Platforms - Data migration"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-700 mb-2",children:"Sync Options"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsx)("li",{children:"One-way Import - Import data from external system"}),(0,r.jsx)("li",{children:"One-way Export - Export data to external system"}),(0,r.jsx)("li",{children:"Two-way Sync - Keep both systems in sync"}),(0,r.jsx)("li",{children:"Scheduled Sync - Automate synchronization"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,r.jsx)("h4",{className:"font-medium text-green-700 mb-2",children:"Benefits"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Data Consistency - Same data across all systems"}),(0,r.jsx)("li",{children:"Time Saving - Avoid manual data entry"}),(0,r.jsx)("li",{children:"Error Reduction - Minimize human errors"}),(0,r.jsx)("li",{children:"Real-time Updates - Keep data current"})]})]})]})})]})})]})]})}},45851:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687),n=s(25217),l=s(8693),i=s(43210);function a({children:e}){let[t]=(0,i.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(l.Ht,{client:t,children:e})}},49275:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\integration\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\integration\\page.tsx","default")},55104:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),n=s(48088),l=s(88170),i=s.n(l),a=s(30893),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);s.d(t,o);let d={children:["",{children:["genealogy",{children:["integration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49275)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\integration\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\integration\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/genealogy/integration/page",pathname:"/genealogy/integration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55929:(e,t,s)=>{Promise.resolve().then(s.bind(s,43841))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(60687),n=s(43210);let l=()=>"serviceWorker"in navigator,i=async()=>{if(!l())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),a(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},a=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&o()})})},o=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,s]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{i();let e=()=>{s(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,t&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,s)=>{Promise.resolve().then(s.bind(s,37043)),Promise.resolve().then(s.bind(s,23229)),Promise.resolve().then(s.bind(s,82113)),Promise.resolve().then(s.bind(s,41750))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,s)=>{Promise.resolve().then(s.bind(s,28253)),Promise.resolve().then(s.bind(s,97695)),Promise.resolve().then(s.bind(s,45851)),Promise.resolve().then(s.bind(s,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},85305:(e,t,s)=>{Promise.resolve().then(s.bind(s,49275))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>x});var r=s(37413),n=s(22376),l=s.n(n),i=s(68726),a=s.n(i);s(61135);var o=s(23229),d=s(37043),c=s(82113),m=s(41750);let x={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${l().variable} ${a().variable} antialiased`,children:(0,r.jsx)(o.AuthProvider,{children:(0,r.jsx)(c.default,{children:(0,r.jsx)(d.CartProvider,{children:(0,r.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},97695:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>l});var r=s(60687),n=s(82136);function l({children:e}){return(0,r.jsx)(n.SessionProvider,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,3103],()=>s(55104));module.exports=r})();
exports.id=5626,exports.ids=[5626],exports.modules={12909:(e,a,t)=>{"use strict";t.d(a,{Er:()=>l,Nh:()=>d,aP:()=>c});var r=t(96330),n=t(13581),i=t(85663),s=t(55511),o=t.n(s);async function l(e){return await i.Ay.hash(e,10)}function c(){let e=o().randomBytes(32).toString("hex"),a=new Date;return a.setHours(a.getHours()+1),{token:e,expiresAt:a}}new r.PrismaClient;let d={debug:!0,logger:{error:(e,a)=>{console.error(`NextAuth error: ${e}`,a)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,a)=>{console.log(`NextAuth debug: ${e}`,a)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,a){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let a=new r.PrismaClient,t=await a.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await a.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await i.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:s,...o}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:a})=>(console.log("Session callback called",{session:e,token:a}),a&&e.user&&(e.user.id=a.sub),e),jwt:async({token:e,user:a})=>(console.log("JWT callback called",{token:e,user:a}),a&&(e.sub=a.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28990:(e,a,t)=>{"use strict";t.d(a,{Rt:()=>s,SF:()=>o,T:()=>m});var r=t(31183),n=t(12909),i=t(11742),s=function(e){return e.NEW_MEMBER="new_member",e.ESTABLISHED_MEMBER="established_member",e.HIGH_PERFORMER="high_performer",e.EDGE_CASES="edge_cases",e}({});async function o(e){let{scenario:a,count:t=1,prefix:s="test",cleanupToken:o=""}=e;try{let e=o||`cleanup_${Date.now()}`,m=[],w=0,h=0,p=0,_=0;for(let o=0;o<t;o++){let t=`${s}_${a}_${o+1}@test.com`,g=i.a.person.fullName(),f="Test@123",b=await (0,n.Er)(f),E="new_member"===a?1:"established_member"===a?3:"high_performer"===a?5:Math.floor(6*Math.random())+1,T=`${g}__TEST_DATA__${e}`,A=await r.z.user.create({data:{email:t,name:T,password:b,rankId:E}});switch(m.push({id:A.id,email:t,name:g,password:f,scenario:a}),a){case"new_member":await r.z.user.update({where:{id:A.id},data:{walletBalance:0}});break;case"established_member":await r.z.user.update({where:{id:A.id},data:{walletBalance:i.a.number.float({min:1e3,max:5e3,precision:.01})}});let y=i.a.number.int({min:3,max:5}),$=await l(A.id,y,2,e);w+=$.length;let x=i.a.number.int({min:5,max:10}),S=await c(A.id,x,e);h+=S.length;let z=await d(A.id,$,e);p+=z.length;let k=i.a.number.int({min:3,max:5}),I=await u(A.id,k,e);_+=I.length;break;case"high_performer":await r.z.user.update({where:{id:A.id},data:{walletBalance:i.a.number.float({min:5e4,max:2e5,precision:.01})}});let M=i.a.number.int({min:20,max:30}),v=await l(A.id,M,6,e);w+=v.length;let D=i.a.number.int({min:20,max:30}),C=await c(A.id,D,e);h+=C.length;let R=await d(A.id,v,e);p+=R.length;let B=i.a.number.int({min:10,max:15}),N=await u(A.id,B,e);_+=N.length;break;case"edge_cases":await r.z.user.update({where:{id:A.id},data:{walletBalance:i.a.number.float({min:999999,max:9999999,precision:.01})}}),await r.z.user.update({where:{id:A.id},data:{name:`${i.a.lorem.words(10)}__TEST_DATA__${e}`}});let P=await l(A.id,5,1,e,!0);w+=P.length;let H=await c(A.id,3,e,!0);h+=H.length;let O=await d(A.id,P,e,!0);p+=O.length}}return{success:!0,message:`Successfully generated test data for ${a} scenario`,users:m,stats:{usersCreated:m.length,downlinesCreated:w,purchasesCreated:h,rebatesGenerated:p,referralLinksCreated:_}}}catch(e){return console.error("Error generating test data:",e),{success:!1,message:`Failed to generate test data: ${e instanceof Error?e.message:String(e)}`}}}async function l(e,a,t,s,o=!1){let c=[],d=await r.z.user.findUnique({where:{id:e},select:{rankId:!0}});if(!d)return[];for(let u=0;u<a;u++){let m=Math.min(u%t+1,t),w=i.a.person.fullName(),h=Math.max(1,d.rankId-m);o&&u%3==0&&(w=u%2==0?i.a.lorem.words(15):i.a.lorem.word(2),h=u%5==0?6:1);let p=`${w}__TEST_DATA__${s}`,_=await r.z.user.create({data:{email:`downline_${e}_${u}@test.com`,name:p,password:await (0,n.Er)("Test@123"),rankId:h,uplineId:e}});if(await r.z.user.update({where:{id:_.id},data:{walletBalance:o&&u%4==0?i.a.number.float({min:5e5,max:1e6,precision:.01}):i.a.number.float({min:100,max:2e3,precision:.01})}}),c.push(_),m<t&&u%3==0){let e=Math.floor(a/3);if(e>0){let a=await l(_.id,e,t-1,s,o);c.push(...a)}}}return c}async function c(e,a,t,n=!1){let s=[],o=await r.z.product.findMany({where:{isActive:!0},take:10});if(0===o.length)return[];for(let l=0;l<a;l++){let a=o[l%o.length],c=n&&l%3==0?i.a.number.int({min:50,max:100}):i.a.number.int({min:1,max:5}),d=a.price*c,u=`completed__TEST_DATA__${t}`,m=await r.z.purchase.create({data:{userId:e,productId:a.id,quantity:c,totalAmount:d,status:u}});s.push(m)}return s}async function d(e,a,t,n=!1){let s=[],o=await r.z.product.findMany({where:{isActive:!0},take:10});if(0===o.length||0===a.length)return[];for(let l=0;l<a.length;l++){let c=a[l],d=o[l%o.length],u=n&&l%3==0?i.a.number.int({min:50,max:100}):i.a.number.int({min:1,max:5}),m=d.price*u,w=`completed__TEST_DATA__${t}`,h=await r.z.purchase.create({data:{userId:c.id,productId:d.id,quantity:u,totalAmount:m,status:w}}),p=n&&l%4==0?.5*m:.1*m,_=`completed__TEST_DATA__${t}`,g=await r.z.rebate.create({data:{purchaseId:h.id,receiverId:e,generatorId:c.id,level:1,percentage:n&&l%4==0?50:10,amount:p,status:_}});s.push(g),await r.z.user.update({where:{id:e},data:{walletBalance:{increment:p}}})}return s}async function u(e,a,t){let n=[];try{let s=await r.z.product.findMany({where:{isActive:!0},take:10});if(0===s.length)return[];for(let o=0;o<a;o++){let a=s[o%s.length],l=`ref_${e}_${o}_${Date.now().toString(36)}`,c=`${l}__TEST_DATA__${t}`,d=await r.z.shareableLink.create({data:{userId:e,productId:a.id,code:c,type:"product",title:a.name,description:a.description,customImage:a.image,isActive:!0,clickCount:i.a.number.int({min:10,max:100}),conversionCount:i.a.number.int({min:1,max:10}),totalRevenue:i.a.number.float({min:1e3,max:1e4,precision:.01}),totalCommission:i.a.number.float({min:100,max:1e3,precision:.01})}});n.push(d)}}catch(e){console.error("Error creating referral links:",e)}return n}async function m(e){try{if(!e)return{success:!1,message:"Cleanup token is required",count:0};let a=0,t=(await r.z.user.findMany({where:{name:{contains:`__TEST_DATA__${e}`}}})).map(e=>e.id);try{let n=await r.z.rebate.deleteMany({where:{OR:[{receiverId:{in:t}},{generatorId:{in:t}},{status:{contains:`__TEST_DATA__${e}`}}]}});a+=n.count}catch(e){console.error("Error deleting rebates:",e)}try{let n=await r.z.purchase.deleteMany({where:{OR:[{userId:{in:t}},{status:{contains:`__TEST_DATA__${e}`}}]}});a+=n.count}catch(e){console.error("Error deleting purchases:",e)}try{let n=await r.z.shareableLink.deleteMany({where:{OR:[{userId:{in:t}},{code:{contains:`__TEST_DATA__${e}`}}]}});a+=n.count}catch(e){console.error("Error deleting shareable links:",e)}let n=await r.z.user.deleteMany({where:{name:{contains:`__TEST_DATA__${e}`}}});return a+=n.count,{success:!0,message:`Successfully cleaned up test data with token: ${e}`,count:a}}catch(e){return console.error("Error cleaning up test data:",e),{success:!1,message:`Failed to clean up test data: ${e instanceof Error?e.message:String(e)}`,count:0}}}},31183:(e,a,t)=>{"use strict";t.d(a,{z:()=>n});var r=t(96330);let n=global.prisma||new r.PrismaClient({log:["query"]})},78335:()=>{},96487:()=>{}};
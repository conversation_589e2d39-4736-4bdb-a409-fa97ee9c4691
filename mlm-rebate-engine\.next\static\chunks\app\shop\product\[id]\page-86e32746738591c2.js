(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1319],{588:(e,t,s)=>{Promise.resolve().then(s.bind(s,32096))},32096:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(95155),r=s(12115),n=s(12108),i=s(35695),l=s(70357),o=s(62177),d=s(90221),c=s(55594);c.z.object({email:c.z.string().email("Invalid email address"),password:c.z.string().min(8,"Password must be at least 8 characters"),csrfToken:c.z.string().optional()}),c.z.object({name:c.z.string().min(2,"Name must be at least 2 characters"),email:c.z.string().email("Invalid email address"),password:c.z.string().min(8,"Password must be at least 8 characters"),confirmPassword:c.z.string().min(8,"Confirm password must be at least 8 characters"),phone:c.z.string().optional(),birthdate:c.z.string().optional(),address:c.z.string().optional(),city:c.z.string().optional(),region:c.z.string().optional(),postalCode:c.z.string().optional(),uplineId:c.z.string().optional(),profileImage:c.z.string().optional(),preferredPaymentMethod:c.z.string().optional(),bankName:c.z.string().optional(),bankAccountNumber:c.z.string().optional(),bankAccountName:c.z.string().optional(),gcashNumber:c.z.string().optional(),payMayaNumber:c.z.string().optional(),receiveUpdates:c.z.boolean().optional().default(!1),agreeToTerms:c.z.boolean()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>!0===e.agreeToTerms,{message:"You must agree to the terms and conditions",path:["agreeToTerms"]}).refine(e=>"bank"===e.preferredPaymentMethod?!!e.bankName&&!!e.bankAccountNumber&&!!e.bankAccountName:"gcash"===e.preferredPaymentMethod?!!e.gcashNumber:"paymaya"!==e.preferredPaymentMethod||!!e.payMayaNumber,{message:"Payment details are required for the selected payment method",path:["preferredPaymentMethod"]}),c.z.object({name:c.z.string().min(2,"Name must be at least 2 characters").optional(),phone:c.z.string().optional(),currentPassword:c.z.string().optional(),newPassword:c.z.string().min(8,"New password must be at least 8 characters").optional(),confirmNewPassword:c.z.string().optional(),profileImage:c.z.string().optional()}).refine(e=>!e.newPassword||e.newPassword===e.confirmNewPassword,{message:"New passwords don't match",path:["confirmNewPassword"]}).refine(e=>!e.newPassword||!!e.currentPassword,{message:"Current password is required to set a new password",path:["currentPassword"]}),c.z.object({name:c.z.string().min(2,"Product name must be at least 2 characters"),description:c.z.string().optional(),price:c.z.number().positive("Price must be positive"),image:c.z.string().optional(),rebateConfigs:c.z.array(c.z.object({level:c.z.number().int().positive("Level must be a positive integer"),percentage:c.z.number().positive("Percentage must be positive").max(100,"Percentage cannot exceed 100%")})).min(1,"At least one rebate configuration is required")});let m=c.z.object({productId:c.z.number().int().positive("Product ID must be a positive integer"),quantity:c.z.number().int().positive("Quantity must be a positive integer"),paymentMethodId:c.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:c.z.record(c.z.any()).optional(),referenceNumber:c.z.string().optional(),shippingMethodId:c.z.number().int().positive("Shipping method ID must be a positive integer").optional(),shippingDetails:c.z.record(c.z.any()).optional(),shippingAddress:c.z.string().optional(),shippingFee:c.z.number().nonnegative("Shipping fee must be a non-negative number").optional(),referralCode:c.z.string().optional()});c.z.object({amount:c.z.number().positive("Amount must be positive"),type:c.z.enum(["withdrawal","deposit"],{errorMap:()=>({message:"Invalid transaction type"})}),description:c.z.string().optional(),paymentMethodId:c.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:c.z.record(c.z.any()).optional(),referenceNumber:c.z.string().optional()}),c.z.object({checkAll:c.z.boolean().optional()});var u=s(29911);function h(e){let{onSelect:t,selectedMethodId:s,showAddNew:n=!0}=e,[i,l]=(0,r.useState)(!0),[o,d]=(0,r.useState)([]),[c,m]=(0,r.useState)([]),[h,p]=(0,r.useState)(null),[x,b]=(0,r.useState)(null),[f,g]=(0,r.useState)(!1),[y,j]=(0,r.useState)(null),[v,N]=(0,r.useState)({}),[w,k]=(0,r.useState)(!1);(0,r.useEffect)(()=>{S()},[]),(0,r.useEffect)(()=>{if(s&&c.length>0){let e=c.find(e=>e.id===s)||c.find(e=>e.paymentMethodId===s);e&&p(e)}},[s,c]);let S=async()=>{l(!0),b(null);try{var e,s,a;let r=await fetch("/api/payment-methods?includeUserMethods=true");if(!r.ok)throw Error("Failed to fetch payment methods: ".concat(r.statusText));let n=await r.json();d(n.paymentMethods||[]),m(n.userPaymentMethods||[]);let i=null==(e=n.userPaymentMethods)?void 0:e.find(e=>e.isDefault);i?(p(i),t(i)):(null==(s=n.userPaymentMethods)?void 0:s.length)>0?(p(n.userPaymentMethods[0]),t(n.userPaymentMethods[0])):(null==(a=n.paymentMethods)?void 0:a.length)>0&&(p(n.paymentMethods[0]),t(n.paymentMethods[0]))}catch(e){console.error("Error fetching payment methods:",e),b("Failed to load payment methods. Please try again.")}finally{l(!1)}},P=e=>{p(e),t(e)},z=async()=>{if(!y)return void b("Please select a payment method");k(!0),b(null);try{let e=await fetch("/api/payment-methods/user",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:y,details:v,isDefault:0===c.length})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to add payment method: ".concat(e.statusText))}let s=await e.json();await S(),p(s.userPaymentMethod),t(s.userPaymentMethod),g(!1)}catch(e){console.error("Error adding payment method:",e),b(e instanceof Error?e.message:"Failed to add payment method")}finally{k(!1)}},C=async e=>{if(confirm("Are you sure you want to delete this payment method?")){l(!0),b(null);try{let t=await fetch("/api/payment-methods/user?id=".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete payment method: ".concat(t.statusText));await S()}catch(e){console.error("Error deleting payment method:",e),b("Failed to delete payment method")}finally{l(!1)}}},M=async e=>{l(!0),b(null);try{let t=await fetch("/api/payment-methods/user",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:e,isDefault:!0})});if(!t.ok)throw Error("Failed to set default payment method: ".concat(t.statusText));await S()}catch(e){console.error("Error setting default payment method:",e),b("Failed to set default payment method")}finally{l(!1)}};return i?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(u.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading payment methods..."})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[x&&(0,a.jsx)("div",{className:"bg-red-100 text-red-700 p-3 rounded-md",children:x}),c.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Your Payment Methods"}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3",children:c.map(e=>(0,a.jsx)("div",{className:"border rounded-md p-3 cursor-pointer ".concat(h&&"id"in h&&h.id===e.id?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-300"),onClick:()=>P(e),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"mr-3",children:["gcash"===e.paymentMethod.code&&(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold",children:"G"}),"maya"===e.paymentMethod.code&&(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold",children:"M"}),"cash"===e.paymentMethod.code&&(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold",children:"₱"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.paymentMethod.name}),e.paymentMethod.requiresDetails&&(0,a.jsx)("div",{className:"text-sm text-gray-600",children:(()=>{try{let t=JSON.parse(e.details);return t.accountNumber||t.accountName||"No details"}catch(e){return"Invalid details"}})()})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isDefault&&(0,a.jsxs)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center",children:[(0,a.jsx)(u.CMH,{className:"mr-1"}),"Default"]}),!e.isDefault&&(0,a.jsx)("button",{type:"button",onClick:t=>{t.stopPropagation(),M(e.id)},className:"text-xs text-blue-600 hover:text-blue-800",children:"Set Default"}),(0,a.jsx)("button",{type:"button",onClick:t=>{t.stopPropagation(),C(e.id)},className:"text-red-600 hover:text-red-800",children:(0,a.jsx)(u.qbC,{})})]})]})},e.id))})]}),n&&!f&&(0,a.jsxs)("button",{type:"button",onClick:()=>{g(!0),j(null),N({})},className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(u.OiG,{className:"mr-1"}),"Add Payment Method"]}),f&&(0,a.jsxs)("div",{className:"border rounded-md p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Add Payment Method"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Method"}),(0,a.jsxs)("select",{value:y||"",onChange:e=>j(e.target.value?parseInt(e.target.value):null),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select a payment method"}),o.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),y&&(e=>{if(!e.requiresDetails||!e.detailsSchema)return null;try{let t=JSON.parse(e.detailsSchema);if(!t.properties)return null;return(0,a.jsx)("div",{className:"mt-4 space-y-4",children:Object.entries(t.properties).map(e=>{var s,r;let[n,i]=e;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[i.description||n,(null==(s=t.required)?void 0:s.includes(n))&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",value:v[n]||"",onChange:e=>N({...v,[n]:e.target.value}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",required:null==(r=t.required)?void 0:r.includes(n)})]},n)})})}catch(e){return console.error("Error parsing schema:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing schema"})}})(o.find(e=>e.id===y)),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{g(!1),j(null),N({})},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"button",onClick:z,disabled:w||!y,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):"Save"})]})]})]}),0===c.length&&!f&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Available Payment Methods"}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3",children:o.map(e=>(0,a.jsx)("div",{className:"border rounded-md p-3 cursor-pointer ".concat(h&&"code"in h&&h.code===e.code?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-300"),onClick:()=>P(e),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"mr-3",children:["gcash"===e.code&&(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold",children:"G"}),"maya"===e.code&&(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold",children:"M"}),"cash"===e.code&&(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold",children:"₱"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),e.description&&(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})]})},e.id))})]})]})}function p(e){let{onSelect:t,selectedMethodId:s,initialAddress:n=""}=e,[i,l]=(0,r.useState)(!0),[o,d]=(0,r.useState)([]),[c,m]=(0,r.useState)(null),[h,p]=(0,r.useState)({}),[x,b]=(0,r.useState)(n),[f,g]=(0,r.useState)(null);(0,r.useEffect)(()=>{y()},[]),(0,r.useEffect)(()=>{if(s&&o.length>0){let e=o.find(e=>e.id===s);e&&j(e)}},[s,o]);let y=async()=>{l(!0),g(null);try{let e=await fetch("/api/shipping-methods");if(!e.ok)throw Error("Failed to fetch shipping methods: ".concat(e.statusText));let t=await e.json();d(t),1===t.length&&j(t[0])}catch(e){console.error("Error fetching shipping methods:",e),g("Failed to load shipping methods")}finally{l(!1)}},j=e=>{m(e),p({}),t(e,{},x)},v=(e,s)=>{let a={...h,[e]:s};p(a),c&&t(c,a,x)},N=e=>{b(e),c&&t(c,h,e)},w=e=>{switch(e){case"pickup":return(0,a.jsx)(u.Tvt,{className:"text-blue-500"});case"lalamove":return(0,a.jsx)(u.N8c,{className:"text-orange-500"});case"jnt":return(0,a.jsx)(u.dv1,{className:"text-red-500"});default:return(0,a.jsx)(u.dv1,{className:"text-gray-500"})}};return i?(0,a.jsxs)("div",{className:"flex items-center justify-center p-4",children:[(0,a.jsx)(u.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading shipping methods..."})]}):f?(0,a.jsx)("div",{className:"p-4 bg-red-100 text-red-700 rounded-md",children:f}):0===o.length?(0,a.jsx)("div",{className:"p-4 bg-yellow-100 text-yellow-700 rounded-md",children:"No shipping methods available."}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3",children:o.map(e=>(0,a.jsx)("div",{className:"border rounded-md p-3 cursor-pointer ".concat((null==c?void 0:c.id)===e.id?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-300"),onClick:()=>j(e),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"mr-3 w-8 h-8 flex items-center justify-center",children:w(e.code)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),e.description&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]}),(0,a.jsx)("div",{className:"text-right",children:e.baseFee>0?(0,a.jsxs)("div",{className:"font-medium",children:["₱",e.baseFee.toFixed(2)]}):(0,a.jsx)("div",{className:"text-green-600 font-medium",children:"Free"})})]})},e.id))}),c&&(0,a.jsxs)("div",{className:"mt-4",children:["pickup"!==c.code&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Shipping Address",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("textarea",{value:x,onChange:e=>N(e.target.value),rows:3,className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter your complete shipping address",required:!0})]}),(e=>{if(!e.requiresDetails||!e.detailsSchema)return null;try{let t=JSON.parse(e.detailsSchema);if(!t.properties)return null;return(0,a.jsx)("div",{className:"mt-4 space-y-4",children:Object.entries(t.properties).map(e=>{var s,r;let[n,i]=e;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[i.description||n,(null==(s=t.required)?void 0:s.includes(n))&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"date"===i.format?"date":"text",value:h[n]||"",onChange:e=>v(n,e.target.value),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",required:null==(r=t.required)?void 0:r.includes(n)})]},n)})})}catch(e){return console.error("Error parsing schema:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing schema"})}})(c),"pickup"===c.code&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 text-blue-700 rounded-md flex items-start",children:[(0,a.jsx)(u.__w,{className:"mt-1 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Store Pickup Information"}),(0,a.jsxs)("p",{className:"text-sm",children:["You can pick up your order at our store located at:",(0,a.jsx)("strong",{children:" 123 Main Street, Makati City, Metro Manila"})]}),(0,a.jsx)("p",{className:"text-sm mt-1",children:"Store Hours: Monday to Saturday, 9:00 AM to 6:00 PM"})]})]}),"lalamove"===c.code&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-orange-50 text-orange-700 rounded-md flex items-start",children:[(0,a.jsx)(u.__w,{className:"mt-1 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Lalamove Delivery Information"}),(0,a.jsx)("p",{className:"text-sm",children:"Lalamove provides same-day delivery within Metro Manila. Delivery time is typically 1-3 hours after order confirmation."})]})]}),"jnt"===c.code&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-red-50 text-red-700 rounded-md flex items-start",children:[(0,a.jsx)(u.__w,{className:"mt-1 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"J&T Express Delivery Information"}),(0,a.jsx)("p",{className:"text-sm",children:"J&T Express delivers nationwide. Delivery time is typically 2-3 business days for Metro Manila and 3-7 business days for provincial areas."})]})]})]})]})}function x(e){let{product:t,onSuccess:s,referralCode:n}=e,[i,l]=(0,r.useState)(!1),[c,x]=(0,r.useState)(null),[b,f]=(0,r.useState)(null),[g,y]=(0,r.useState)({}),[j,v]=(0,r.useState)(""),[N,w]=(0,r.useState)(null),[k,S]=(0,r.useState)({}),[P,z]=(0,r.useState)(""),{register:C,handleSubmit:M,watch:E,formState:{errors:I}}=(0,o.mN)({resolver:(0,d.u)(m),defaultValues:{productId:t.id,quantity:1}}),F=E("quantity"),T=t.price*F,D=async e=>{l(!0),x(null);try{if(N&&"pickup"!==N.code&&!P){x("Shipping address is required"),l(!1);return}if(b)if("paymentMethodId"in b){e.paymentMethodId=b.paymentMethodId;try{e.paymentDetails=JSON.parse(b.details)}catch(t){e.paymentDetails={}}}else e.paymentMethodId=b.id,e.paymentDetails=g;N&&(e.shippingMethodId=N.id,e.shippingDetails=k,e.shippingAddress=P,e.shippingFee=N.baseFee),j&&(e.referenceNumber=j),n&&(e.referralCode=n);let t=await fetch("/api/purchases",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to make purchase")}s()}catch(e){console.error("Error making purchase:",e),x(e instanceof Error?e.message:"Failed to make purchase")}finally{l(!1)}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold mb-4",children:["Purchase ",t.name]}),c&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:c}),(0,a.jsxs)("form",{onSubmit:M(D),children:[(0,a.jsx)("input",{type:"hidden",...C("productId")}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quantity"}),(0,a.jsx)("input",{type:"number",...C("quantity"),min:"1",className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"}),I.quantity&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:I.quantity.message})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm font-medium text-gray-700 mb-1",children:[(0,a.jsx)("span",{children:"Price per unit:"}),(0,a.jsxs)("span",{children:["₱",t.price.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm font-medium text-gray-700 mb-1",children:[(0,a.jsx)("span",{children:"PV per unit:"}),(0,a.jsx)("span",{children:t.pv})]}),(0,a.jsxs)("div",{className:"flex justify-between text-lg font-semibold text-gray-900 mb-1",children:[(0,a.jsx)("span",{children:"Total Amount:"}),(0,a.jsxs)("span",{children:["₱",T.toFixed(2)]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(u.CE5,{className:"inline mr-1"})," Shipping Method"]}),(0,a.jsx)(p,{onSelect:(e,t,s)=>{w(e),S(t),z(s)}})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,a.jsx)(h,{onSelect:e=>{f(e),y({})},showAddNew:!0}),(()=>{if(!b||"paymentMethodId"in b||!b.requiresDetails||!b.detailsSchema)return null;try{let e=JSON.parse(b.detailsSchema);if(!e.properties)return null;return(0,a.jsx)("div",{className:"mt-4 space-y-4",children:Object.entries(e.properties).map(t=>{var s,r;let[n,i]=t;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[i.description||n,(null==(s=e.required)?void 0:s.includes(n))&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",value:g[n]||"",onChange:e=>y({...g,[n]:e.target.value}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",required:null==(r=e.required)?void 0:r.includes(n)})]},n)})})}catch(e){return console.error("Error parsing schema:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing schema"})}})(),b&&"cash"!==b.code&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reference Number (Optional)"}),(0,a.jsx)("input",{type:"text",value:j,onChange:e=>v(e.target.value),placeholder:"Enter payment reference number",className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsx)("button",{type:"submit",disabled:i||!b||!N,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.hW,{className:"inline animate-spin mr-2"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.AsH,{className:"inline mr-2"}),"Complete Purchase"]})})]})]})}function b(e){let{product:t,className:s=""}=e,[n,i]=(0,r.useState)(!1),[l,o]=(0,r.useState)(!1),[d,c]=(0,r.useState)(null),[m,h]=(0,r.useState)(!1),[p,x]=(0,r.useState)(null),b=async()=>{if(d)return void i(!0);o(!0),x(null);try{let e=await fetch("/api/shareable-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:t.id,title:t.name,description:t.description,customImage:t.image})});if(!e.ok)throw Error("Failed to generate shareable link: ".concat(e.statusText));let s=await e.json(),a=window.location.origin,r="".concat(a,"/s/").concat(s.link.code);c(r),i(!0)}catch(e){console.error("Error generating shareable link:",e),x("Failed to generate shareable link. Please try again.")}finally{o(!1)}},f=async()=>{if(d)try{await navigator.clipboard.writeText(d),h(!0),setTimeout(()=>{h(!1)},2e3)}catch(e){console.error("Error copying to clipboard:",e)}};return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{type:"button",onClick:b,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ".concat(s),disabled:l,children:l?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.hW,{className:"animate-spin mr-2"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.Zzu,{className:"mr-2"}),"Share"]})}),n&&(0,a.jsx)("div",{className:"absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Share Product"}),(0,a.jsx)("button",{type:"button",onClick:()=>i(!1),className:"text-gray-400 hover:text-gray-500",children:"\xd7"})]}),p&&(0,a.jsx)("div",{className:"mb-3 p-2 bg-red-100 text-red-700 rounded-md text-sm",children:p}),d&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(u.AnD,{className:"text-gray-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Shareable Link"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("input",{type:"text",value:d,readOnly:!0,className:"flex-1 border rounded-l-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("button",{type:"button",onClick:f,className:"bg-gray-100 border border-l-0 rounded-r-md px-3 py-2 hover:bg-gray-200",title:"Copy to clipboard",children:m?(0,a.jsx)(u.CMH,{className:"text-green-600"}):(0,a.jsx)(u.paH,{})})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium mb-2",children:"Share on"}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{if(!d)return;let e="https://www.facebook.com/sharer/sharer.php?u=".concat(encodeURIComponent(d));window.open(e,"_blank")},className:"flex flex-col items-center justify-center w-12 h-12 rounded-full bg-blue-600 text-white hover:bg-blue-700",title:"Share on Facebook",children:(0,a.jsx)(u.iYk,{size:20})}),(0,a.jsx)("button",{type:"button",onClick:()=>{if(!d)return;let e="Check out ".concat(t.name,"!"),s="https://twitter.com/intent/tweet?text=".concat(encodeURIComponent(e),"&url=").concat(encodeURIComponent(d));window.open(s,"_blank")},className:"flex flex-col items-center justify-center w-12 h-12 rounded-full bg-blue-400 text-white hover:bg-blue-500",title:"Share on Twitter",children:(0,a.jsx)(u.feZ,{size:20})}),(0,a.jsx)("button",{type:"button",onClick:()=>{if(!d)return;let e="Check out ".concat(t.name,"! ").concat(d),s="https://wa.me/?text=".concat(encodeURIComponent(e));window.open(s,"_blank")},className:"flex flex-col items-center justify-center w-12 h-12 rounded-full bg-green-500 text-white hover:bg-green-600",title:"Share on WhatsApp",children:(0,a.jsx)(u.EcP,{size:20})}),(0,a.jsx)("button",{type:"button",onClick:()=>{if(!d)return;let e="Check out ".concat(t.name,"!"),s="I thought you might be interested in this product:\n\n".concat(t.name,"\n\n").concat(d),a="mailto:?subject=".concat(encodeURIComponent(e),"&body=").concat(encodeURIComponent(s));window.open(a)},className:"flex flex-col items-center justify-center w-12 h-12 rounded-full bg-gray-600 text-white hover:bg-gray-700",title:"Share via Email",children:(0,a.jsx)(u.maD,{size:20})})]})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Earn commissions when someone purchases this product through your link!"})]})]})})]})}var f=s(66766),g=s(6874),y=s.n(g),j=s(5323);function v(e){let{params:t}=e,{data:s,status:o}=(0,n.useSession)(),d=(0,i.useRouter)(),c=(0,i.useSearchParams)(),[m,h]=(0,r.useState)(!0),[p,g]=(0,r.useState)(null),[v,N]=(0,r.useState)(null),[w,k]=(0,r.useState)(!1),[S,P]=(0,r.useState)(1),[z,C]=(0,r.useState)(!1),{addItem:M}=(0,j._)(),E=c.get("ref");(0,r.useEffect)(()=>{I()},[t.id]);let I=async()=>{h(!0),N(null);try{let e=await fetch("/api/products/".concat(t.id));if(!e.ok)throw Error("Failed to fetch product: ".concat(e.statusText));let s=await e.json();g(s)}catch(e){console.error("Error fetching product:",e),N("Failed to load product. Please try again.")}finally{h(!1)}},F=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return m?(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(u.hW,{className:"animate-spin text-green-500 mr-2"}),(0,a.jsx)("span",{children:"Loading product..."})]})}):v||!p?(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"bg-red-100 text-red-700 p-4 rounded-md mb-4",children:v||"Product not found"}),(0,a.jsxs)(y(),{href:"/shop",className:"flex items-center text-blue-600 hover:underline",children:[(0,a.jsx)(u.QVr,{className:"mr-2"}),"Back to Shop"]})]})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)(y(),{href:"/shop",className:"flex items-center text-blue-600 hover:underline mb-6",children:[(0,a.jsx)(u.QVr,{className:"mr-2"}),"Back to Shop"]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:(0,a.jsxs)("div",{className:"md:flex",children:[(0,a.jsx)("div",{className:"md:w-1/2",children:p.image?(0,a.jsx)("div",{className:"relative h-80 md:h-full",children:(0,a.jsx)(f.default,{src:p.image,alt:p.name,fill:!0,className:"object-cover"})}):(0,a.jsx)("div",{className:"bg-gray-200 h-80 md:h-full flex items-center justify-center",children:(0,a.jsx)(u.AsH,{className:"text-gray-400 text-6xl"})})}),(0,a.jsxs)("div",{className:"md:w-1/2 p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-2",children:p.name}),"authenticated"===o&&(0,a.jsx)(b,{product:p})]}),(0,a.jsxs)("div",{className:"flex flex-col mb-4",children:[(0,a.jsx)("div",{className:"flex items-center mb-2",children:"authenticated"===o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mr-4",children:F(p.price)}),p.srp>p.price&&(0,a.jsx)("div",{className:"text-lg text-gray-500 line-through",children:F(p.srp)}),(0,a.jsxs)("div",{className:"ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-md",children:[p.pv," PV"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-800 mr-4",children:F(p.srp)}),(0,a.jsxs)("div",{className:"ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-md",children:[p.pv," PV"]})]})}),"authenticated"!==o&&(0,a.jsxs)("div",{className:"text-sm text-blue-600",children:[(0,a.jsx)(y(),{href:"/login",className:"hover:underline",children:"Sign in as a member"})," ","to get discounted prices and earn rebates!"]})]}),E&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-yellow-100 text-yellow-800 rounded-md",children:"You were referred to this product by a member. They will earn a commission if you make a purchase."}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Description"}),(0,a.jsx)("p",{className:"text-gray-700",children:p.description})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{htmlFor:"quantity",className:"block text-sm font-medium text-gray-700 mb-2",children:"Quantity"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{P(e=>e>1?e-1:1)},className:"p-2 border border-gray-300 rounded-l-md bg-gray-50 hover:bg-gray-100",children:(0,a.jsx)(u.iu5,{className:"h-4 w-4 text-gray-600"})}),(0,a.jsx)("input",{type:"number",id:"quantity",name:"quantity",min:"1",value:S,onChange:e=>P(Math.max(1,parseInt(e.target.value)||1)),className:"p-2 w-16 text-center border-t border-b border-gray-300 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("button",{type:"button",onClick:()=>{P(e=>e+1)},className:"p-2 border border-gray-300 rounded-r-md bg-gray-50 hover:bg-gray-100",children:(0,a.jsx)(u.OiG,{className:"h-4 w-4 text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{p&&(M({id:p.id,name:p.name,price:p.price,srp:p.srp,image:p.image,quantity:S,pv:p.pv}),C(!0),setTimeout(()=>{C(!1)},3e3))},className:"flex-1 flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ".concat(z?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"," focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"),disabled:z,children:z?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(FaCheck,{className:"mr-2"}),"Added to Cart"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.AsH,{className:"mr-2"}),"Add to Cart"]})}),w?(0,a.jsx)(x,{product:p,onSuccess:()=>{if(k(!1),alert("Purchase successful!"),E)try{fetch("/api/shareable-links/click",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:E})})}catch(e){console.error("Error recording referral click:",e)}d.push("/purchases")},referralCode:E||void 0}):(0,a.jsxs)("button",{type:"button",onClick:()=>{if("unauthenticated"===o)return void d.push("/login?returnUrl=".concat(encodeURIComponent(window.location.pathname)));k(!0)},className:"flex-1 flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,a.jsx)(u.AsH,{className:"mr-2"}),"Buy Now"]})]})]})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,1342,357,8441,1684,7358],()=>t(588)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7341],{36347:(e,t,s)=>{Promise.resolve().then(s.bind(s,85083))},85083:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(95155),r=s(12115),l=s(12108),n=s(35695),i=s(70357),d=s(29911);function c(){let{data:e,status:t}=(0,l.useSession)(),s=(0,n.useRouter)(),[c,o]=(0,r.useState)(!0),[x,m]=(0,r.useState)(!1),[p,u]=(0,r.useState)([]),[h,g]=(0,r.useState)({totalRebates:0,totalAmount:0,pendingAmount:0,processedAmount:0,failedAmount:0,pendingCount:0,processedCount:0,failedCount:0}),[j,b]=(0,r.useState)(""),[f,y]=(0,r.useState)(""),[N,v]=(0,r.useState)({startDate:"",endDate:""}),[w,S]=(0,r.useState)(!1),[D,k]=(0,r.useState)(!1),[C,A]=(0,r.useState)(""),[F,L]=(0,r.useState)(""),[E,P]=(0,r.useState)(1),[R,T]=(0,r.useState)(10),[I,O]=(0,r.useState)(1),[U,_]=(0,r.useState)(0);(0,r.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,r.useEffect)(()=>{"authenticated"===t&&(async()=>{try{let e=await fetch("/api/users/me"),t=await e.json();m(6===t.rankId),6===t.rankId?W():o(!1)}catch(e){console.error("Error checking admin status:",e),o(!1)}})()},[t]);let W=async()=>{o(!0);try{let e=new URLSearchParams;e.append("page",E.toString()),e.append("pageSize",R.toString()),j&&e.append("status",j),f&&e.append("search",f),N.startDate&&e.append("startDate",N.startDate),N.endDate&&e.append("endDate",N.endDate);let t=await fetch("/api/admin/rebates?".concat(e.toString()));if(!t.ok)throw Error("Failed to fetch rebates: ".concat(t.statusText));let s=await t.json();u(s.rebates),O(s.pagination.totalPages),_(s.pagination.totalItems);let a=await fetch("/api/admin/rebates/stats");if(!a.ok)throw Error("Failed to fetch rebate stats: ".concat(a.statusText));let r=await a.json();g(r)}catch(e){console.error("Error fetching rebates:",e)}finally{o(!1)}};(0,r.useEffect)(()=>{x&&W()},[x,E,R,j,f,N]);let Y=async()=>{k(!0),A("Processing pending rebates..."),L("");try{let e=await fetch("/api/admin/rebates/process",{method:"POST"});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to process rebates")}let t=await e.json();A("Successfully processed ".concat(t.processed," rebates")),W()}catch(e){L(e.message||"An error occurred while processing rebates")}finally{setTimeout(()=>{k(!1),A("")},3e3)}},M=e=>{let{name:t,value:s}=e.target;v(e=>({...e,[t]:s}))},H=e=>{e>0&&e<=I&&P(e)},Z=async()=>{try{let e=new URLSearchParams;j&&e.append("status",j),f&&e.append("search",f),N.startDate&&e.append("startDate",N.startDate),N.endDate&&e.append("endDate",N.endDate),e.append("export","true");let t=await fetch("/api/admin/rebates/export?".concat(e.toString()));if(!t.ok)throw Error("Failed to export rebates: ".concat(t.statusText));let s=await t.blob(),a=window.URL.createObjectURL(s),r=document.createElement("a");r.style.display="none",r.href=a,r.download="rebates-export-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(a),document.body.removeChild(r)}catch(e){console.error("Error exporting rebates:",e),alert("Failed to export rebates. Please try again.")}};return"loading"===t||c?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}):x?(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,a.jsx)(d.lcY,{className:"mr-2 text-blue-500"})," Rebate Management"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:Z,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center",children:[(0,a.jsx)(d.WCW,{className:"mr-2"})," Export"]}),(0,a.jsxs)("button",{onClick:Y,disabled:D||0===h.pendingCount,className:"px-4 py-2 text-white rounded-md flex items-center ".concat(D||0===h.pendingCount?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"),children:[D?(0,a.jsx)(d.hW,{className:"animate-spin mr-2"}):(0,a.jsx)(d.DIg,{className:"mr-2"}),"Process Rebates"]})]})]}),C&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-green-100 text-green-700 rounded-md",children:C}),F&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-100 text-red-700 rounded-md",children:F}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,a.jsx)(d.lcY,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Total Rebates"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",h.totalAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[h.totalRebates," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-500 mr-4",children:(0,a.jsx)(d.CMH,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Processed"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",h.processedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[h.processedCount," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4",children:(0,a.jsx)(d.DIg,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Pending"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",h.pendingAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[h.pendingCount," transactions"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-red-100 text-red-500 mr-4",children:(0,a.jsx)(d.BS8,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:"Failed"}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:["₱",h.failedAmount.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[h.failedCount," transactions"]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search by user name or email",value:f,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(d.KSO,{className:"text-gray-400"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:j,onChange:e=>b(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"processed",children:"Processed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"})]}),(0,a.jsxs)("button",{onClick:()=>S(!w),className:"px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center",children:[(0,a.jsx)(d.YsJ,{className:"mr-2"}),w?"Hide Filters":"More Filters"]})]})]}),w&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,a.jsx)(d.bfZ,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"date",name:"startDate",value:N.startDate,onChange:M,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"absolute pl-3 pointer-events-none",children:(0,a.jsx)(d.bfZ,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"date",name:"endDate",value:N.endDate,onChange:M,className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 w-full"})]})]})]}),(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{y(""),b(""),v({startDate:"",endDate:""})},className:"px-4 py-2 text-gray-700 hover:text-gray-900",children:"Reset Filters"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Rebate Transactions"})}),(0,a.jsx)("div",{className:"p-6",children:c?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading rebates..."})]}):p.length>0?(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Generator"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Receiver"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Processed At"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:(0,a.jsx)(d.x$1,{className:"text-gray-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.generator.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.generator.email})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-3",children:(0,a.jsx)(d.x$1,{className:"text-gray-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.receiver.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.receiver.email})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.purchase.product.name}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["Level ",e.level]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600",children:["₱",e.amount.toFixed(2)]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("processed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.processedAt?new Date(e.processedAt).toLocaleString():"-"})]},e.id))})]})}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No rebates found matching your criteria."})}),p.length>0&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-700 mr-2",children:"Rows per page:"}),(0,a.jsxs)("select",{value:R,onChange:e=>{T(parseInt(e.target.value)),P(1)},className:"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"10",children:"10"}),(0,a.jsx)("option",{value:"25",children:"25"}),(0,a.jsx)("option",{value:"50",children:"50"}),(0,a.jsx)("option",{value:"100",children:"100"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700 mr-4",children:[E," of ",I," pages (",U," total rebates)"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>H(1),disabled:1===E,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"First"}),(0,a.jsx)("button",{onClick:()=>H(E-1),disabled:1===E,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>H(E+1),disabled:E===I,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"}),(0,a.jsx)("button",{onClick:()=>H(I),disabled:E===I,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Last"})]})]})]})]})]})}):(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(36347)),_N_E=e.O()}]);
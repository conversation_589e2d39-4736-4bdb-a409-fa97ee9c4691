"use strict";exports.id=5833,exports.ids=[5833],exports.modules={4934:(e,t,n)=>{n.d(t,{h7:()=>ro,yX:()=>u,Ln:()=>oU});var r,o,i,a,l,s,u,c=n(43210);function d(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let n=0,r;n<e.length;n++)""!==(r=d(e[n]))&&(t+=(t&&" ")+r);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}var f=n(39733);let h=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},p=e=>e?h(e):h,{useDebugValue:g}=c,{useSyncExternalStoreWithSelector:m}=f,y=e=>e;function v(e,t=y,n){let r=m(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return g(r),r}let x=(e,t)=>{let n=p(e),r=(e,r=t)=>v(n,e,r);return Object.assign(r,n),r},b=(e,t)=>e?x(e,t):x;function w(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var _={value:()=>{}};function S(){for(var e,t=0,n=arguments.length,r={};t<n;++t){if(!(e=arguments[t]+"")||e in r||/[\s.]/.test(e))throw Error("illegal type: "+e);r[e]=[]}return new E(r)}function E(e){this._=e}function M(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=_,e=e.slice(0,r).concat(e.slice(r+1));break}return null!=n&&e.push({name:t,value:n}),e}function N(){}function k(e){return null==e?N:function(){return this.querySelector(e)}}function C(){return[]}function A(e){return null==e?C:function(){return this.querySelectorAll(e)}}function P(e){return function(){return this.matches(e)}}function $(e){return function(t){return t.matches(e)}}E.prototype=S.prototype={constructor:E,on:function(e,t){var n,r=this._,o=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!r.hasOwnProperty(e))throw Error("unknown type: "+e);return{type:e,name:t}}),i=-1,a=o.length;if(arguments.length<2){for(;++i<a;)if((n=(e=o[i]).type)&&(n=function(e,t){for(var n,r=0,o=e.length;r<o;++r)if((n=e[r]).name===t)return n.value}(r[n],e.name)))return n;return}if(null!=t&&"function"!=typeof t)throw Error("invalid callback: "+t);for(;++i<a;)if(n=(e=o[i]).type)r[n]=M(r[n],e.name,t);else if(null==t)for(n in r)r[n]=M(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new E(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,r,o=Array(n),i=0;i<n;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(r=this._[e],i=0,n=r.length;i<n;++i)r[i].value.apply(t,o)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};var I=Array.prototype.find;function O(){return this.firstElementChild}var z=Array.prototype.filter;function R(){return Array.from(this.children)}function T(e){return Array(e.length)}function D(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function B(e,t,n,r,o,i){for(var a,l=0,s=t.length,u=i.length;l<u;++l)(a=t[l])?(a.__data__=i[l],r[l]=a):n[l]=new D(e,i[l]);for(;l<s;++l)(a=t[l])&&(o[l]=a)}function L(e,t,n,r,o,i,a){var l,s,u,c=new Map,d=t.length,f=i.length,h=Array(d);for(l=0;l<d;++l)(s=t[l])&&(h[l]=u=a.call(s,s.__data__,l,t)+"",c.has(u)?o[l]=s:c.set(u,s));for(l=0;l<f;++l)u=a.call(e,i[l],l,i)+"",(s=c.get(u))?(r[l]=s,s.__data__=i[l],c.delete(u)):n[l]=new D(e,i[l]);for(l=0;l<d;++l)(s=t[l])&&c.get(h[l])===s&&(o[l]=s)}function H(e){return e.__data__}function j(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}D.prototype={constructor:D,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var V="http://www.w3.org/1999/xhtml";let X={svg:"http://www.w3.org/2000/svg",xhtml:V,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Y(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),X.hasOwnProperty(t)?{space:X[t],local:e}:e}function F(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function K(e,t){return e.style.getPropertyValue(t)||F(e).getComputedStyle(e,null).getPropertyValue(t)}function q(e){return e.trim().split(/^|\s+/)}function W(e){return e.classList||new Z(e)}function Z(e){this._node=e,this._names=q(e.getAttribute("class")||"")}function U(e,t){for(var n=W(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function G(e,t){for(var n=W(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function Q(){this.textContent=""}function J(){this.innerHTML=""}function ee(){this.nextSibling&&this.parentNode.appendChild(this)}function et(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function en(e){var t=Y(e);return(t.local?function(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}:function(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===V&&t.documentElement.namespaceURI===V?t.createElement(e):t.createElementNS(n,e)}})(t)}function er(){return null}function eo(){var e=this.parentNode;e&&e.removeChild(this)}function ei(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ea(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function el(e){return function(){var t=this.__on;if(t){for(var n,r=0,o=-1,i=t.length;r<i;++r)(n=t[r],e.type&&n.type!==e.type||n.name!==e.name)?t[++o]=n:this.removeEventListener(n.type,n.listener,n.options);++o?t.length=o:delete this.__on}}}function es(e,t,n){return function(){var r,o=this.__on,i=function(e){t.call(this,e,this.__data__)};if(o){for(var a=0,l=o.length;a<l;++a)if((r=o[a]).type===e.type&&r.name===e.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=n),r.value=t;return}}this.addEventListener(e.type,i,n),r={type:e.type,name:e.name,value:t,listener:i,options:n},o?o.push(r):this.__on=[r]}}function eu(e,t,n){var r=F(e),o=r.CustomEvent;"function"==typeof o?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}Z.prototype={add:function(e){0>this._names.indexOf(e)&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var ec=[null];function ed(e,t){this._groups=e,this._parents=t}function ef(){return new ed([[document.documentElement]],ec)}function eh(e){return"string"==typeof e?new ed([[document.querySelector(e)]],[document.documentElement]):new ed([[e]],ec)}ed.prototype=ef.prototype={constructor:ed,select:function(e){"function"!=typeof e&&(e=k(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a,l=t[o],s=l.length,u=r[o]=Array(s),c=0;c<s;++c)(i=l[c])&&(a=e.call(i,i.__data__,c,l))&&("__data__"in i&&(a.__data__=i.__data__),u[c]=a);return new ed(r,this._parents)},selectAll:function(e){if("function"==typeof e){var t;t=e,e=function(){var e;return e=t.apply(this,arguments),null==e?[]:Array.isArray(e)?e:Array.from(e)}}else e=A(e);for(var n=this._groups,r=n.length,o=[],i=[],a=0;a<r;++a)for(var l,s=n[a],u=s.length,c=0;c<u;++c)(l=s[c])&&(o.push(e.call(l,l.__data__,c,s)),i.push(l));return new ed(o,i)},selectChild:function(e){var t;return this.select(null==e?O:(t="function"==typeof e?e:$(e),function(){return I.call(this.children,t)}))},selectChildren:function(e){var t;return this.selectAll(null==e?R:(t="function"==typeof e?e:$(e),function(){return z.call(this.children,t)}))},filter:function(e){"function"!=typeof e&&(e=P(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],l=a.length,s=r[o]=[],u=0;u<l;++u)(i=a[u])&&e.call(i,i.__data__,u,a)&&s.push(i);return new ed(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,H);var n=t?L:B,r=this._parents,o=this._groups;"function"!=typeof e&&(v=e,e=function(){return v});for(var i=o.length,a=Array(i),l=Array(i),s=Array(i),u=0;u<i;++u){var c=r[u],d=o[u],f=d.length,h="object"==typeof(y=e.call(c,c&&c.__data__,u,r))&&"length"in y?y:Array.from(y),p=h.length,g=l[u]=Array(p),m=a[u]=Array(p);n(c,d,g,m,s[u]=Array(f),h,t);for(var y,v,x,b,w=0,_=0;w<p;++w)if(x=g[w]){for(w>=_&&(_=w+1);!(b=m[_])&&++_<p;);x._next=b||null}}return(a=new ed(a,r))._enter=l,a._exit=s,a},enter:function(){return new ed(this._enter||this._groups.map(T),this._parents)},exit:function(){return new ed(this._exit||this._groups.map(T),this._parents)},join:function(e,t,n){var r=this.enter(),o=this,i=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(o=t(o))&&(o=o.selection()),null==n?i.remove():n(i),r&&o?r.merge(o).order():o},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,a=Math.min(o,i),l=Array(o),s=0;s<a;++s)for(var u,c=n[s],d=r[s],f=c.length,h=l[s]=Array(f),p=0;p<f;++p)(u=c[p]||d[p])&&(h[p]=u);for(;s<o;++s)l[s]=n[s];return new ed(l,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,o=e[t],i=o.length-1,a=o[i];--i>=0;)(r=o[i])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=j);for(var n=this._groups,r=n.length,o=Array(r),i=0;i<r;++i){for(var a,l=n[i],s=l.length,u=o[i]=Array(s),c=0;c<s;++c)(a=l[c])&&(u[c]=a);u.sort(t)}return new ed(o,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var a=r[o];if(a)return a}return null},size:function(){let e=0;for(let t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o,i=t[n],a=0,l=i.length;a<l;++a)(o=i[a])&&e.call(o,o.__data__,a,i);return this},attr:function(e,t){var n=Y(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}}:"function"==typeof t?n.local?function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}:function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}:n.local?function(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}:function(e,t){return function(){this.setAttribute(e,t)}})(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?function(e){return function(){this.style.removeProperty(e)}}:"function"==typeof t?function(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}:function(e,t,n){return function(){this.style.setProperty(e,t,n)}})(e,t,null==n?"":n)):K(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?function(e){return function(){delete this[e]}}:"function"==typeof t?function(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}:function(e,t){return function(){this[e]=t}})(e,t)):this.node()[e]},classed:function(e,t){var n=q(e+"");if(arguments.length<2){for(var r=W(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each(("function"==typeof t?function(e,t){return function(){(t.apply(this,arguments)?U:G)(this,e)}}:t?function(e){return function(){U(this,e)}}:function(e){return function(){G(this,e)}})(n,t))},text:function(e){return arguments.length?this.each(null==e?Q:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}:function(e){return function(){this.textContent=e}})(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?J:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(e){return function(){this.innerHTML=e}})(e)):this.node().innerHTML},raise:function(){return this.each(ee)},lower:function(){return this.each(et)},append:function(e){var t="function"==typeof e?e:en(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var n="function"==typeof e?e:en(e),r=null==t?er:"function"==typeof t?t:k(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(eo)},clone:function(e){return this.select(e?ea:ei)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,o,i=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}),a=i.length;if(arguments.length<2){var l=this.node().__on;if(l){for(var s,u=0,c=l.length;u<c;++u)for(r=0,s=l[u];r<a;++r)if((o=i[r]).type===s.type&&o.name===s.name)return s.value}return}for(r=0,l=t?es:el;r<a;++r)this.each(l(i[r],t,n));return this},dispatch:function(e,t){return this.each(("function"==typeof t?function(e,t){return function(){return eu(this,e,t.apply(this,arguments))}}:function(e,t){return function(){return eu(this,e,t)}})(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,o=e[t],i=0,a=o.length;i<a;++i)(r=o[i])&&(yield r)}};let ep={passive:!1},eg={capture:!0,passive:!1};function em(e){e.stopImmediatePropagation()}function ey(e){e.preventDefault(),e.stopImmediatePropagation()}function ev(e){var t=e.document.documentElement,n=eh(e).on("dragstart.drag",ey,eg);"onselectstart"in t?n.on("selectstart.drag",ey,eg):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function ex(e,t){var n=e.document.documentElement,r=eh(e).on("dragstart.drag",null);t&&(r.on("click.drag",ey,eg),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}function eb(e){return((e=Math.exp(e))+1/e)/2}let ew=function e(t,n,r){function o(e,o){var i,a,l=e[0],s=e[1],u=e[2],c=o[0],d=o[1],f=o[2],h=c-l,p=d-s,g=h*h+p*p;if(g<1e-12)a=Math.log(f/u)/t,i=function(e){return[l+e*h,s+e*p,u*Math.exp(t*e*a)]};else{var m=Math.sqrt(g),y=(f*f-u*u+r*g)/(2*u*n*m),v=(f*f-u*u-r*g)/(2*f*n*m),x=Math.log(Math.sqrt(y*y+1)-y);a=(Math.log(Math.sqrt(v*v+1)-v)-x)/t,i=function(e){var r,o,i=e*a,c=eb(x),d=u/(n*m)*(c*(((r=Math.exp(2*(r=t*i+x)))-1)/(r+1))-((o=Math.exp(o=x))-1/o)/2);return[l+d*h,s+d*p,u*c/eb(t*i+x)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return o.rho=function(t){var n=Math.max(.001,+t),r=n*n;return e(n,r,r*r)},o}(Math.SQRT2,2,4);function e_(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}var eS,eE,eM=0,eN=0,ek=0,eC=0,eA=0,eP=0,e$="object"==typeof performance&&performance.now?performance:Date,eI="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function eO(){return eA||(eI(ez),eA=e$.now()+eP)}function ez(){eA=0}function eR(){this._call=this._time=this._next=null}function eT(e,t,n){var r=new eR;return r.restart(e,t,n),r}function eD(){eA=(eC=e$.now())+eP,eM=eN=0;try{eO(),++eM;for(var e,t=eS;t;)(e=eA-t._time)>=0&&t._call.call(void 0,e),t=t._next;--eM}finally{eM=0,function(){for(var e,t,n=eS,r=1/0;n;)n._call?(r>n._time&&(r=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:eS=t);eE=e,eL(r)}(),eA=0}}function eB(){var e=e$.now(),t=e-eC;t>1e3&&(eP-=t,eC=e)}function eL(e){!eM&&(eN&&(eN=clearTimeout(eN)),e-eA>24?(e<1/0&&(eN=setTimeout(eD,e-e$.now()-eP)),ek&&(ek=clearInterval(ek))):(ek||(eC=e$.now(),ek=setInterval(eB,1e3)),eM=1,eI(eD)))}function eH(e,t,n){var r=new eR;return t=null==t?0:+t,r.restart(n=>{r.stop(),e(n+t)},t,n),r}eR.prototype=eT.prototype={constructor:eR,restart:function(e,t,n){if("function"!=typeof e)throw TypeError("callback is not a function");n=(null==n?eO():+n)+(null==t?0:+t),this._next||eE===this||(eE?eE._next=this:eS=this,eE=this),this._call=e,this._time=n,eL()},stop:function(){this._call&&(this._call=null,this._time=1/0,eL())}};var ej=S("start","end","cancel","interrupt"),eV=[];function eX(e,t,n,r,o,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var r,o=e.__transition;function i(s){var u,c,d,f;if(1!==n.state)return l();for(u in o)if((f=o[u]).name===n.name){if(3===f.state)return eH(i);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",e,e.__data__,f.index,f.group),delete o[u]):+u<t&&(f.state=6,f.timer.stop(),f.on.call("cancel",e,e.__data__,f.index,f.group),delete o[u])}if(eH(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(s))}),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(u=0,n.state=3,r=Array(d=n.tween.length),c=-1;u<d;++u)(f=n.tween[u].value.call(e,e.__data__,n.index,n.group))&&(r[++c]=f);r.length=c+1}}function a(t){for(var o=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=r.length;++i<a;)r[i].call(e,o);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var r in n.state=6,n.timer.stop(),delete o[t],o)return;delete e.__transition}o[t]=n,n.timer=eT(function(e){n.state=1,n.timer.restart(i,n.delay,n.time),n.delay<=e&&i(e-n.delay)},0,n.time)}(e,n,{name:t,index:r,group:o,on:ej,tween:eV,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function eY(e,t){var n=eK(e,t);if(n.state>0)throw Error("too late; already scheduled");return n}function eF(e,t){var n=eK(e,t);if(n.state>3)throw Error("too late; already running");return n}function eK(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw Error("transition not found");return n}function eq(e,t){var n,r,o,i=e.__transition,a=!0;if(i){for(o in t=null==t?null:t+"",i){if((n=i[o]).name!==t){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[o]}a&&delete e.__transition}}function eW(e,t){return e*=1,t*=1,function(n){return e*(1-n)+t*n}}var eZ=180/Math.PI,eU={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function eG(e,t,n,r,o,i){var a,l,s;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(s=e*n+t*r)&&(n-=e*s,r-=t*s),(l=Math.sqrt(n*n+r*r))&&(n/=l,r/=l,s/=l),e*r<t*n&&(e=-e,t=-t,s=-s,a=-a),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*eZ,skewX:Math.atan(s)*eZ,scaleX:a,scaleY:l}}function eQ(e,t,n,r){function o(e){return e.length?e.pop()+" ":""}return function(i,a){var l,s,u,c,d=[],f=[];return i=e(i),a=e(a),!function(e,r,o,i,a,l){if(e!==o||r!==i){var s=a.push("translate(",null,t,null,n);l.push({i:s-4,x:eW(e,o)},{i:s-2,x:eW(r,i)})}else(o||i)&&a.push("translate("+o+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,d,f),l=i.rotate,s=a.rotate,l!==s?(l-s>180?s+=360:s-l>180&&(l+=360),f.push({i:d.push(o(d)+"rotate(",null,r)-2,x:eW(l,s)})):s&&d.push(o(d)+"rotate("+s+r),u=i.skewX,c=a.skewX,u!==c?f.push({i:d.push(o(d)+"skewX(",null,r)-2,x:eW(u,c)}):c&&d.push(o(d)+"skewX("+c+r),!function(e,t,n,r,i,a){if(e!==n||t!==r){var l=i.push(o(i)+"scale(",null,",",null,")");a.push({i:l-4,x:eW(e,n)},{i:l-2,x:eW(t,r)})}else(1!==n||1!==r)&&i.push(o(i)+"scale("+n+","+r+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,d,f),i=a=null,function(e){for(var t,n=-1,r=f.length;++n<r;)d[(t=f[n]).i]=t.x(e);return d.join("")}}}var eJ=eQ(function(e){let t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?eU:eG(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),e0=eQ(function(e){return null==e?eU:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",e),e=r.transform.baseVal.consolidate())?eG((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):eU},", ",")",")");function e1(e,t,n){var r=e._id;return e.each(function(){var e=eF(this,r);(e.value||(e.value={}))[t]=n.apply(this,arguments)}),function(e){return eK(e,r).value[t]}}function e2(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function e5(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function e3(){}var e4="\\s*([+-]?\\d+)\\s*",e8="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",e9="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",e6=/^#([0-9a-f]{3,8})$/,e7=RegExp(`^rgb\\(${e4},${e4},${e4}\\)$`),te=RegExp(`^rgb\\(${e9},${e9},${e9}\\)$`),tt=RegExp(`^rgba\\(${e4},${e4},${e4},${e8}\\)$`),tn=RegExp(`^rgba\\(${e9},${e9},${e9},${e8}\\)$`),tr=RegExp(`^hsl\\(${e8},${e9},${e9}\\)$`),to=RegExp(`^hsla\\(${e8},${e9},${e9},${e8}\\)$`),ti={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function ta(){return this.rgb().formatHex()}function tl(){return this.rgb().formatRgb()}function ts(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=e6.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?tu(t):3===n?new tf(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?tc(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?tc(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=e7.exec(e))?new tf(t[1],t[2],t[3],1):(t=te.exec(e))?new tf(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=tt.exec(e))?tc(t[1],t[2],t[3],t[4]):(t=tn.exec(e))?tc(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=tr.exec(e))?tv(t[1],t[2]/100,t[3]/100,1):(t=to.exec(e))?tv(t[1],t[2]/100,t[3]/100,t[4]):ti.hasOwnProperty(e)?tu(ti[e]):"transparent"===e?new tf(NaN,NaN,NaN,0):null}function tu(e){return new tf(e>>16&255,e>>8&255,255&e,1)}function tc(e,t,n,r){return r<=0&&(e=t=n=NaN),new tf(e,t,n,r)}function td(e,t,n,r){var o;return 1==arguments.length?((o=e)instanceof e3||(o=ts(o)),o)?new tf((o=o.rgb()).r,o.g,o.b,o.opacity):new tf:new tf(e,t,n,null==r?1:r)}function tf(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function th(){return`#${ty(this.r)}${ty(this.g)}${ty(this.b)}`}function tp(){let e=tg(this.opacity);return`${1===e?"rgb(":"rgba("}${tm(this.r)}, ${tm(this.g)}, ${tm(this.b)}${1===e?")":`, ${e})`}`}function tg(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function tm(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ty(e){return((e=tm(e))<16?"0":"")+e.toString(16)}function tv(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new tb(e,t,n,r)}function tx(e){if(e instanceof tb)return new tb(e.h,e.s,e.l,e.opacity);if(e instanceof e3||(e=ts(e)),!e)return new tb;if(e instanceof tb)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),a=NaN,l=i-o,s=(i+o)/2;return l?(a=t===i?(n-r)/l+(n<r)*6:n===i?(r-t)/l+2:(t-n)/l+4,l/=s<.5?i+o:2-i-o,a*=60):l=s>0&&s<1?0:a,new tb(a,l,s,e.opacity)}function tb(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function tw(e){return(e=(e||0)%360)<0?e+360:e}function t_(e){return Math.max(0,Math.min(1,e||0))}function tS(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}function tE(e,t,n,r,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*n+(1+3*e+3*i-3*a)*r+a*o)/6}e2(e3,ts,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:ta,formatHex:ta,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return tx(this).formatHsl()},formatRgb:tl,toString:tl}),e2(tf,td,e5(e3,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new tf(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new tf(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new tf(tm(this.r),tm(this.g),tm(this.b),tg(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:th,formatHex:th,formatHex8:function(){return`#${ty(this.r)}${ty(this.g)}${ty(this.b)}${ty((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:tp,toString:tp})),e2(tb,function(e,t,n,r){return 1==arguments.length?tx(e):new tb(e,t,n,null==r?1:r)},e5(e3,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new tb(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new tb(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new tf(tS(e>=240?e-240:e+120,o,r),tS(e,o,r),tS(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new tb(tw(this.h),t_(this.s),t_(this.l),tg(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=tg(this.opacity);return`${1===e?"hsl(":"hsla("}${tw(this.h)}, ${100*t_(this.s)}%, ${100*t_(this.l)}%${1===e?")":`, ${e})`}`}}));let tM=e=>()=>e;function tN(e,t){var n,r,o=t-e;return o?(n=e,r=o,function(e){return n+e*r}):tM(isNaN(e)?t:e)}let tk=function e(t){var n,r=1==(n=+t)?tN:function(e,t){var r,o,i;return t-e?(r=e,o=t,r=Math.pow(r,i=n),o=Math.pow(o,i)-r,i=1/i,function(e){return Math.pow(r+e*o,i)}):tM(isNaN(e)?t:e)};function o(e,t){var n=r((e=td(e)).r,(t=td(t)).r),o=r(e.g,t.g),i=r(e.b,t.b),a=tN(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function tC(e){return function(t){var n,r,o=t.length,i=Array(o),a=Array(o),l=Array(o);for(n=0;n<o;++n)r=td(t[n]),i[n]=r.r||0,a[n]=r.g||0,l[n]=r.b||0;return i=e(i),a=e(a),l=e(l),r.opacity=1,function(e){return r.r=i(e),r.g=a(e),r.b=l(e),r+""}}}tC(function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),o=e[r],i=e[r+1],a=r>0?e[r-1]:2*o-i,l=r<t-1?e[r+2]:2*i-o;return tE((n-r/t)*t,a,o,i,l)}}),tC(function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),o=e[(r+t-1)%t],i=e[r%t],a=e[(r+1)%t],l=e[(r+2)%t];return tE((n-r/t)*t,o,i,a,l)}});var tA=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tP=RegExp(tA.source,"g");function t$(e,t){var n;return("number"==typeof t?eW:t instanceof ts?tk:(n=ts(t))?(t=n,tk):function(e,t){var n,r,o,i,a,l=tA.lastIndex=tP.lastIndex=0,s=-1,u=[],c=[];for(e+="",t+="";(o=tA.exec(e))&&(i=tP.exec(t));)(a=i.index)>l&&(a=t.slice(l,a),u[s]?u[s]+=a:u[++s]=a),(o=o[0])===(i=i[0])?u[s]?u[s]+=i:u[++s]=i:(u[++s]=null,c.push({i:s,x:eW(o,i)})),l=tP.lastIndex;return l<t.length&&(a=t.slice(l),u[s]?u[s]+=a:u[++s]=a),u.length<2?c[0]?(n=c[0].x,function(e){return n(e)+""}):(r=t,function(){return r}):(t=c.length,function(e){for(var n,r=0;r<t;++r)u[(n=c[r]).i]=n.x(e);return u.join("")})})(e,t)}var tI=ef.prototype.constructor;function tO(e){return function(){this.style.removeProperty(e)}}var tz=0;function tR(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}var tT=ef.prototype;tR.prototype=(function(e){return ef().transition(e)}).prototype={constructor:tR,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=k(e));for(var r=this._groups,o=r.length,i=Array(o),a=0;a<o;++a)for(var l,s,u=r[a],c=u.length,d=i[a]=Array(c),f=0;f<c;++f)(l=u[f])&&(s=e.call(l,l.__data__,f,u))&&("__data__"in l&&(s.__data__=l.__data__),d[f]=s,eX(d[f],t,n,f,d,eK(l,n)));return new tR(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=A(e));for(var r=this._groups,o=r.length,i=[],a=[],l=0;l<o;++l)for(var s,u=r[l],c=u.length,d=0;d<c;++d)if(s=u[d]){for(var f,h=e.call(s,s.__data__,d,u),p=eK(s,n),g=0,m=h.length;g<m;++g)(f=h[g])&&eX(f,t,n,g,h,p);i.push(h),a.push(s)}return new tR(i,a,t,n)},selectChild:tT.selectChild,selectChildren:tT.selectChildren,filter:function(e){"function"!=typeof e&&(e=P(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],l=a.length,s=r[o]=[],u=0;u<l;++u)(i=a[u])&&e.call(i,i.__data__,u,a)&&s.push(i);return new tR(r,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw Error();for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),a=Array(r),l=0;l<i;++l)for(var s,u=t[l],c=n[l],d=u.length,f=a[l]=Array(d),h=0;h<d;++h)(s=u[h]||c[h])&&(f[h]=s);for(;l<r;++l)a[l]=t[l];return new tR(a,this._parents,this._name,this._id)},selection:function(){return new tI(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=++tz,r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],s=l.length,u=0;u<s;++u)if(a=l[u]){var c=eK(a,t);eX(a,e,n,u,l,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new tR(r,this._parents,e,n)},call:tT.call,nodes:tT.nodes,node:tT.node,size:tT.size,empty:tT.empty,each:tT.each,on:function(e,t){var n,r,o,i,a,l,s=this._id;return arguments.length<2?eK(this.node(),s).on.on(e):this.each((n=s,r=e,o=t,l=(r+"").trim().split(/^|\s+/).every(function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e})?eY:eF,function(){var e=l(this,n),t=e.on;t!==i&&(a=(i=t).copy()).on(r,o),e.on=a}))},attr:function(e,t){var n=Y(e),r="transform"===n?e0:t$;return this.attrTween(e,"function"==typeof t?(n.local?function(e,t,n){var r,o,i;return function(){var a,l,s=n(this);return null==s?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local))===(l=s+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,s))}}:function(e,t,n){var r,o,i;return function(){var a,l,s=n(this);return null==s?void this.removeAttribute(e):(a=this.getAttribute(e))===(l=s+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,s))}})(n,r,e1(this,"attr."+e,t)):null==t?(n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}})(n):(n.local?function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===r?o:o=t(r=a,n)}}:function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===r?o:o=t(r=a,n)}})(n,r,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();var r=Y(e);return this.tween(n,(r.local?function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttributeNS(e.space,e.local,o.call(this,t))}),n}return o._value=t,o}:function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttribute(e,o.call(this,t))}),n}return o._value=t,o})(r,t))},style:function(e,t,n){var r,o,i,a,l,s,u,c,d,f,h,p,g,m,y,v,x,b,w,_,S,E="transform"==(e+="")?eJ:t$;return null==t?this.styleTween(e,(r=e,function(){var e=K(this,r),t=(this.style.removeProperty(r),K(this,r));return e===t?null:e===o&&t===i?a:a=E(o=e,i=t)})).on("end.style."+e,tO(e)):"function"==typeof t?this.styleTween(e,(l=e,s=e1(this,"style."+e,t),function(){var e=K(this,l),t=s(this),n=t+"";return null==t&&(this.style.removeProperty(l),n=t=K(this,l)),e===n?null:e===u&&n===c?d:(c=n,d=E(u=e,t))})).each((f=this._id,x="end."+(v="style."+(h=e)),function(){var e=eF(this,f),t=e.on,n=null==e.value[v]?y||(y=tO(h)):void 0;(t!==p||m!==n)&&(g=(p=t).copy()).on(x,m=n),e.on=g})):this.styleTween(e,(b=e,S=t+"",function(){var e=K(this,b);return e===S?null:e===w?_:_=E(w=e,t)}),n).on("end.style."+e,null)},styleTween:function(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==t)return this.tween(r,null);if("function"!=typeof t)throw Error();return this.tween(r,function(e,t,n){var r,o;function i(){var i=t.apply(this,arguments);return i!==o&&(r=(o=i)&&function(t){this.style.setProperty(e,i.call(this,t),n)}),r}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){var t,n;return this.tween("text","function"==typeof e?(t=e1(this,"text",e),function(){var e=t(this);this.textContent=null==e?"":e}):(n=null==e?"":e+"",function(){this.textContent=n}))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw Error();return this.tween(t,function(e){var t,n;function r(){var r=e.apply(this,arguments);return r!==n&&(t=(n=r)&&function(e){this.textContent=r.call(this,e)}),t}return r._value=e,r}(e))},remove:function(){var e;return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r,o=eK(this.node(),n).tween,i=0,a=o.length;i<a;++i)if((r=o[i]).name===e)return r.value;return null}return this.each((null==t?function(e,t){var n,r;return function(){var o=eF(this,e),i=o.tween;if(i!==n){r=n=i;for(var a=0,l=r.length;a<l;++a)if(r[a].name===t){(r=r.slice()).splice(a,1);break}}o.tween=r}}:function(e,t,n){var r,o;if("function"!=typeof n)throw Error();return function(){var i=eF(this,e),a=i.tween;if(a!==r){o=(r=a).slice();for(var l={name:t,value:n},s=0,u=o.length;s<u;++s)if(o[s].name===t){o[s]=l;break}s===u&&o.push(l)}i.tween=o}})(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){eY(this,e).delay=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){eY(this,e).delay=t}})(t,e)):eK(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){eF(this,e).duration=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){eF(this,e).duration=t}})(t,e)):eK(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw Error();return function(){eF(this,e).ease=t}}(t,e)):eK(this.node(),t).ease},easeVarying:function(e){var t;if("function"!=typeof e)throw Error();return this.each((t=this._id,function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw Error();eF(this,t).ease=n}))},end:function(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,a){var l={value:a},s={value:function(){0==--o&&i()}};n.each(function(){var n=eF(this,r),o=n.on;o!==e&&((t=(e=o).copy())._.cancel.push(l),t._.interrupt.push(l),t._.end.push(s)),n.on=t}),0===o&&i()})},[Symbol.iterator]:tT[Symbol.iterator]};var tD={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};ef.prototype.interrupt=function(e){return this.each(function(){eq(this,e)})},ef.prototype.transition=function(e){var t,n;e instanceof tR?(t=e._id,e=e._name):(t=++tz,(n=tD).time=eO(),e=null==e?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],s=l.length,u=0;u<s;++u)(a=l[u])&&eX(a,e,t,u,l,n||function(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw Error(`transition ${t} not found`);return n}(a,t));return new tR(r,this._parents,e,t)};let tB=e=>()=>e;function tL(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function tH(e,t,n){this.k=e,this.x=t,this.y=n}tH.prototype={constructor:tH,scale:function(e){return 1===e?this:new tH(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new tH(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var tj=new tH(1,0,0);function tV(e){e.stopImmediatePropagation()}function tX(e){e.preventDefault(),e.stopImmediatePropagation()}function tY(e){return(!e.ctrlKey||"wheel"===e.type)&&!e.button}function tF(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function tK(){return this.__zoom||tj}function tq(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function tW(){return navigator.maxTouchPoints||"ontouchstart"in this}function tZ(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}tH.prototype;let tU=e=>()=>e;function tG(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:a,y:l,dx:s,dy:u,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:l,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:c}})}function tQ(e){return!e.ctrlKey&&!e.button}function tJ(){return this.parentNode}function t0(e,t){return null==t?{x:e.x,y:e.y}:t}function t1(){return navigator.maxTouchPoints||"ontouchstart"in this}tG.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e},n(51215);let t2=(0,c.createContext)(null),t5=t2.Provider,t3={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${!e?"source":"target"} handle id: "${!e?t.sourceHandle:t.targetHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},t4=t3.error001();function t8(e,t){let n=(0,c.useContext)(t2);if(null===n)throw Error(t4);return v(n,e,t)}let t9=()=>{let e=(0,c.useContext)(t2);if(null===e)throw Error(t4);return(0,c.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},t6=e=>e.userSelectionActive?"none":"all";function t7({position:e,children:t,className:n,style:r,...o}){let i=t8(t6),a=`${e}`.split("-");return c.createElement("div",{className:d(["react-flow__panel",n,...a]),style:{...r,pointerEvents:i},...o},t)}function ne({proOptions:e,position:t="bottom-right"}){return e?.hideAttribution?null:c.createElement(t7,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},c.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var nt=(0,c.memo)(({x:e,y:t,label:n,labelStyle:r={},labelShowBg:o=!0,labelBgStyle:i={},labelBgPadding:a=[2,4],labelBgBorderRadius:l=2,children:s,className:u,...f})=>{let h=(0,c.useRef)(null),[p,g]=(0,c.useState)({x:0,y:0,width:0,height:0}),m=d(["react-flow__edge-textwrapper",u]);return((0,c.useEffect)(()=>{if(h.current){let e=h.current.getBBox();g({x:e.x,y:e.y,width:e.width,height:e.height})}},[n]),void 0!==n&&n)?c.createElement("g",{transform:`translate(${e-p.width/2} ${t-p.height/2})`,className:m,visibility:p.width?"visible":"hidden",...f},o&&c.createElement("rect",{width:p.width+2*a[0],x:-a[0],y:-a[1],height:p.height+2*a[1],className:"react-flow__edge-textbg",style:i,rx:l,ry:l}),c.createElement("text",{className:"react-flow__edge-text",y:p.height/2,dy:"0.3em",ref:h,style:r},n),s):null});let nn=e=>({width:e.offsetWidth,height:e.offsetHeight}),nr=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),no=(e={x:0,y:0},t)=>({x:nr(e.x,t[0][0],t[1][0]),y:nr(e.y,t[0][1],t[1][1])}),ni=(e,t,n)=>e<t?nr(Math.abs(e-t),1,50)/50:e>n?-nr(Math.abs(e-n),1,50)/50:0,na=(e,t)=>[20*ni(e.x,35,t.width-35),20*ni(e.y,35,t.height-35)],nl=e=>e.getRootNode?.()||window?.document,ns=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),nu=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),nc=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),nd=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),nf=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),nh=e=>np(e.width)&&np(e.height)&&np(e.x)&&np(e.y),np=e=>!isNaN(e)&&isFinite(e),ng=Symbol.for("internals"),nm=["Enter"," ","Escape"],ny=(e,t)=>{},nv=e=>"nativeEvent"in e;function nx(e){let t=nv(e)?e.nativeEvent:e,n=t.composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n?.nodeName)||n?.hasAttribute("contenteditable")||!!n?.closest(".nokey")}let nb=e=>"clientX"in e,nw=(e,t)=>{let n=nb(e),r=n?e.clientX:e.touches?.[0].clientX,o=n?e.clientY:e.touches?.[0].clientY;return{x:r-(t?.left??0),y:o-(t?.top??0)}},n_=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,nS=({id:e,path:t,labelX:n,labelY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:u,style:d,markerEnd:f,markerStart:h,interactionWidth:p=20})=>c.createElement(c.Fragment,null,c.createElement("path",{id:e,style:d,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:f,markerStart:h}),p&&c.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:p,className:"react-flow__edge-interaction"}),o&&np(n)&&np(r)?c.createElement(nt,{x:n,y:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:u}):null);function nE(e,t,n){return void 0===n?n:r=>{let o=t().edges.find(t=>t.id===e);o&&n(r,{...o})}}function nM({sourceX:e,sourceY:t,targetX:n,targetY:r}){let o=Math.abs(n-e)/2,i=Math.abs(r-t)/2;return[n<e?n+o:n-o,r<t?r+i:r-i,o,i]}function nN({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:i,targetControlX:a,targetControlY:l}){let s=.125*e+.375*o+.375*a+.125*n,u=.125*t+.375*i+.375*l+.125*r,c=Math.abs(s-e),d=Math.abs(u-t);return[s,u,c,d]}function nk({pos:e,x1:t,y1:n,x2:r,y2:o}){return e===u.Left||e===u.Right?[.5*(t+r),n]:[t,.5*(n+o)]}function nC({sourceX:e,sourceY:t,sourcePosition:n=u.Bottom,targetX:r,targetY:o,targetPosition:i=u.Top}){let[a,l]=nk({pos:n,x1:e,y1:t,x2:r,y2:o}),[s,c]=nk({pos:i,x1:r,y1:o,x2:e,y2:t}),[d,f,h,p]=nN({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:a,sourceControlY:l,targetControlX:s,targetControlY:c});return[`M${e},${t} C${a},${l} ${s},${c} ${r},${o}`,d,f,h,p]}nS.displayName="BaseEdge",function(e){e.Strict="strict",e.Loose="loose"}(o||(o={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(i||(i={})),function(e){e.Partial="partial",e.Full="full"}(a||(a={})),function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(l||(l={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(s||(s={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(u||(u={}));let nA=(0,c.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=u.Bottom,targetPosition:i=u.Top,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:d,labelBgPadding:f,labelBgBorderRadius:h,style:p,markerEnd:g,markerStart:m,interactionWidth:y})=>{let[v,x,b]=nC({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:i});return c.createElement(nS,{path:v,labelX:x,labelY:b,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:d,labelBgPadding:f,labelBgBorderRadius:h,style:p,markerEnd:g,markerStart:m,interactionWidth:y})});nA.displayName="SimpleBezierEdge";let nP={[u.Left]:{x:-1,y:0},[u.Right]:{x:1,y:0},[u.Top]:{x:0,y:-1},[u.Bottom]:{x:0,y:1}},n$=({source:e,sourcePosition:t=u.Bottom,target:n})=>t===u.Left||t===u.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},nI=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function nO({sourceX:e,sourceY:t,sourcePosition:n=u.Bottom,targetX:r,targetY:o,targetPosition:i=u.Top,borderRadius:a=5,centerX:l,centerY:s,offset:c=20}){let[d,f,h,p,g]=function({source:e,sourcePosition:t=u.Bottom,target:n,targetPosition:r=u.Top,center:o,offset:i}){let a,l,s=nP[t],c=nP[r],d={x:e.x+s.x*i,y:e.y+s.y*i},f={x:n.x+c.x*i,y:n.y+c.y*i},h=n$({source:d,sourcePosition:t,target:f}),p=0!==h.x?"x":"y",g=h[p],m=[],y={x:0,y:0},v={x:0,y:0},[x,b,w,_]=nM({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(s[p]*c[p]==-1){a=o.x??x,l=o.y??b;let e=[{x:a,y:d.y},{x:a,y:f.y}],t=[{x:d.x,y:l},{x:f.x,y:l}];m=s[p]===g?"x"===p?e:t:"x"===p?t:e}else{let o=[{x:d.x,y:f.y}],u=[{x:f.x,y:d.y}];if(m="x"===p?s.x===g?u:o:s.y===g?o:u,t===r){let t=Math.abs(e[p]-n[p]);if(t<=i){let r=Math.min(i-1,i-t);s[p]===g?y[p]=(d[p]>e[p]?-1:1)*r:v[p]=(f[p]>n[p]?-1:1)*r}}if(t!==r){let e="x"===p?"y":"x",t=s[p]===c[e],n=d[e]>f[e],r=d[e]<f[e];(1===s[p]&&(!t&&n||t&&r)||1!==s[p]&&(!t&&r||t&&n))&&(m="x"===p?o:u)}let h={x:d.x+y.x,y:d.y+y.y},x={x:f.x+v.x,y:f.y+v.y};Math.max(Math.abs(h.x-m[0].x),Math.abs(x.x-m[0].x))>=Math.max(Math.abs(h.y-m[0].y),Math.abs(x.y-m[0].y))?(a=(h.x+x.x)/2,l=m[0].y):(a=m[0].x,l=(h.y+x.y)/2)}return[[e,{x:d.x+y.x,y:d.y+y.y},...m,{x:f.x+v.x,y:f.y+v.y},n],a,l,w,_]}({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:i,center:{x:l,y:s},offset:c});return[d.reduce((e,t,n)=>{let r="";return e+(n>0&&n<d.length-1?function(e,t,n,r){let o=Math.min(nI(e,t)/2,nI(t,n)/2,r),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a){let t=e.x<n.x?-1:1,r=e.y<n.y?1:-1;return`L ${i+o*t},${a}Q ${i},${a} ${i},${a+o*r}`}let l=e.x<n.x?1:-1,s=e.y<n.y?-1:1;return`L ${i},${a+o*s}Q ${i},${a} ${i+o*l},${a}`}(d[n-1],t,d[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`)},""),f,h,p,g]}let nz=(0,c.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:d,style:f,sourcePosition:h=u.Bottom,targetPosition:p=u.Top,markerEnd:g,markerStart:m,pathOptions:y,interactionWidth:v})=>{let[x,b,w]=nO({sourceX:e,sourceY:t,sourcePosition:h,targetX:n,targetY:r,targetPosition:p,borderRadius:y?.borderRadius,offset:y?.offset});return c.createElement(nS,{path:x,labelX:b,labelY:w,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:d,style:f,markerEnd:g,markerStart:m,interactionWidth:v})});nz.displayName="SmoothStepEdge";let nR=(0,c.memo)(e=>c.createElement(nz,{...e,pathOptions:(0,c.useMemo)(()=>({borderRadius:0,offset:e.pathOptions?.offset}),[e.pathOptions?.offset])}));nR.displayName="StepEdge";let nT=(0,c.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:u,style:d,markerEnd:f,markerStart:h,interactionWidth:p})=>{let[g,m,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:r}){let[o,i,a,l]=nM({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,i,a,l]}({sourceX:e,sourceY:t,targetX:n,targetY:r});return c.createElement(nS,{path:g,labelX:m,labelY:y,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:u,style:d,markerEnd:f,markerStart:h,interactionWidth:p})});function nD(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function nB({pos:e,x1:t,y1:n,x2:r,y2:o,c:i}){switch(e){case u.Left:return[t-nD(t-r,i),n];case u.Right:return[t+nD(r-t,i),n];case u.Top:return[t,n-nD(n-o,i)];case u.Bottom:return[t,n+nD(o-n,i)]}}function nL({sourceX:e,sourceY:t,sourcePosition:n=u.Bottom,targetX:r,targetY:o,targetPosition:i=u.Top,curvature:a=.25}){let[l,s]=nB({pos:n,x1:e,y1:t,x2:r,y2:o,c:a}),[c,d]=nB({pos:i,x1:r,y1:o,x2:e,y2:t,c:a}),[f,h,p,g]=nN({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:l,sourceControlY:s,targetControlX:c,targetControlY:d});return[`M${e},${t} C${l},${s} ${c},${d} ${r},${o}`,f,h,p,g]}nT.displayName="StraightEdge";let nH=(0,c.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=u.Bottom,targetPosition:i=u.Top,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:d,labelBgPadding:f,labelBgBorderRadius:h,style:p,markerEnd:g,markerStart:m,pathOptions:y,interactionWidth:v})=>{let[x,b,w]=nL({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:i,curvature:y?.curvature});return c.createElement(nS,{path:x,labelX:b,labelY:w,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:d,labelBgPadding:f,labelBgBorderRadius:h,style:p,markerEnd:g,markerStart:m,interactionWidth:v})});nH.displayName="BezierEdge";let nj=(0,c.createContext)(null),nV=nj.Provider;nj.Consumer;let nX=()=>(0,c.useContext)(nj),nY=e=>"id"in e&&"source"in e&&"target"in e,nF=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`reactflow__edge-${e}${t||""}-${n}${r||""}`,nK=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`},nq=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),nW=(e,t)=>{let n;return e.source&&e.target?nq(n=nY(e)?{...e}:{...e,id:nF(e)},t)?t:t.concat(n):(ny("006",t3.error006()),t)},nZ=({x:e,y:t},[n,r,o],i,[a,l])=>{let s={x:(e-n)/o,y:(t-r)/o};return i?{x:a*Math.round(s.x/a),y:l*Math.round(s.y/l)}:s},nU=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r}),nG=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};let n=(e.width??0)*t[0],r=(e.height??0)*t[1],o={x:e.position.x-n,y:e.position.y-r};return{...o,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-r}:o}},nQ=(e,t=[0,0])=>0===e.length?{x:0,y:0,width:0,height:0}:nc(e.reduce((e,n)=>{let{x:r,y:o}=nG(n,t).positionAbsolute;return ns(e,nu({x:r,y:o,width:n.width||0,height:n.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),nJ=(e,t,[n,r,o]=[0,0,1],i=!1,a=!1,l=[0,0])=>{let s={x:(t.x-n)/o,y:(t.y-r)/o,width:t.width/o,height:t.height/o},u=[];return e.forEach(e=>{let{width:t,height:n,selectable:r=!0,hidden:o=!1}=e;if(a&&!r||o)return!1;let{positionAbsolute:c}=nG(e,l),d=nf(s,{x:c.x,y:c.y,width:t||0,height:n||0}),f=void 0===t||void 0===n||null===t||null===n,h=(t||0)*(n||0);(f||i&&d>0||d>=h||e.dragging)&&u.push(e)}),u},n0=(e,t)=>{let n=e.map(e=>e.id);return t.filter(e=>n.includes(e.source)||n.includes(e.target))},n1=(e,t,n,r,o,i=.1)=>{let a=nr(Math.min(t/(e.width*(1+i)),n/(e.height*(1+i))),r,o),l=e.x+e.width/2;return{x:t/2-l*a,y:n/2-(e.y+e.height/2)*a,zoom:a}},n2=(e,t=0)=>e.transition().duration(t);function n5(e,t,n,r){return(t[n]||[]).reduce((t,o)=>(`${e.id}-${o.id}-${n}`!==r&&t.push({id:o.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+o.x+o.width/2,y:(e.positionAbsolute?.y??0)+o.y+o.height/2}),t),[])}let n3={source:null,target:null,sourceHandle:null,targetHandle:null},n4=()=>({handleDomNode:null,isValid:!1,connection:n3,endHandle:null});function n8(e,t,n,r,i,a,l){let s="target"===i,u=l.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),c={...n4(),handleDomNode:u};if(u){let e=n9(void 0,u),i=u.getAttribute("data-nodeid"),l=u.getAttribute("data-handleid"),d=u.classList.contains("connectable"),f=u.classList.contains("connectableend"),h={source:s?i:n,sourceHandle:s?l:r,target:s?n:i,targetHandle:s?r:l};c.connection=h,d&&f&&(t===o.Strict?s&&"source"===e||!s&&"target"===e:i!==n||l!==r)&&(c.endHandle={nodeId:i,handleId:l,type:e},c.isValid=a(h))}return c}function n9(e,t){return e?e:t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null}function n6(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function n7({event:e,handleId:t,nodeId:n,onConnect:r,isTarget:o,getState:i,setState:a,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:u}){let c,d,f=nl(e.target),{connectionMode:h,domNode:p,autoPanOnConnect:g,connectionRadius:m,onConnectStart:y,panBy:v,getNodes:x,cancelConnection:b}=i(),w=0,{x:_,y:S}=nw(e),E=n9(s,f?.elementFromPoint(_,S)),M=p?.getBoundingClientRect();if(!M||!E)return;let N=nw(e,M),k=!1,C=null,A=!1,P=null,$=function({nodes:e,nodeId:t,handleId:n,handleType:r}){return e.reduce((e,o)=>{if(o[ng]){let{handleBounds:i}=o[ng],a=[],l=[];i&&(a=n5(o,i,"source",`${t}-${n}-${r}`),l=n5(o,i,"target",`${t}-${n}-${r}`)),e.push(...a,...l)}return e},[])}({nodes:x(),nodeId:n,handleId:t,handleType:E}),I=()=>{if(!g)return;let[e,t]=na(N,M);v({x:e,y:t}),w=requestAnimationFrame(I)};function O(e){var r,s;let u,{transform:p}=i();N=nw(e,M);let{handle:g,validHandleResult:y}=function(e,t,n,r,o,i){let{x:a,y:l}=nw(e),s=t.elementsFromPoint(a,l).find(e=>e.classList.contains("react-flow__handle"));if(s){let e=s.getAttribute("data-nodeid");if(e){let t=n9(void 0,s),r=s.getAttribute("data-handleid"),a=i({nodeId:e,id:r,type:t});if(a){let i=o.find(n=>n.nodeId===e&&n.type===t&&n.id===r);return{handle:{id:r,type:t,nodeId:e,x:i?.x||n.x,y:i?.y||n.y},validHandleResult:a}}}}let u=[],c=1/0;if(o.forEach(e=>{let t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=r){let n=i(e);t<=c&&(t<c?u=[{handle:e,validHandleResult:n}]:t===c&&u.push({handle:e,validHandleResult:n}),c=t)}}),!u.length)return{handle:null,validHandleResult:n4()};if(1===u.length)return u[0];let d=u.some(({validHandleResult:e})=>e.isValid),f=u.some(({handle:e})=>"target"===e.type);return u.find(({handle:e,validHandleResult:t})=>f?"target"===e.type:!d||t.isValid)||u[0]}(e,f,nZ(N,p,!1,[1,1]),m,$,e=>n8(e,h,n,t,o?"target":"source",l,f));if(c=g,k||(I(),k=!0),P=y.handleDomNode,C=y.connection,A=y.isValid,a({connectionPosition:c&&A?nU({x:c.x,y:c.y},p):N,connectionStatus:(r=!!c,u=null,(s=A)?u="valid":r&&!s&&(u="invalid"),u),connectionEndHandle:y.endHandle}),!c&&!A&&!P)return n6(d);C.source!==C.target&&P&&(n6(d),d=P,P.classList.add("connecting","react-flow__handle-connecting"),P.classList.toggle("valid",A),P.classList.toggle("react-flow__handle-valid",A))}function z(e){(c||P)&&C&&A&&r?.(C),i().onConnectEnd?.(e),s&&u?.(e),n6(d),b(),cancelAnimationFrame(w),k=!1,A=!1,C=null,P=null,f.removeEventListener("mousemove",O),f.removeEventListener("mouseup",z),f.removeEventListener("touchmove",O),f.removeEventListener("touchend",z)}a({connectionPosition:N,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:E,connectionStartHandle:{nodeId:n,handleId:t,type:E},connectionEndHandle:null}),y?.(e,{nodeId:n,handleId:t,handleType:E}),f.addEventListener("mousemove",O),f.addEventListener("mouseup",z),f.addEventListener("touchmove",O),f.addEventListener("touchend",z)}let re=()=>!0,rt=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),rn=(e,t,n)=>r=>{let{connectionStartHandle:o,connectionEndHandle:i,connectionClickStartHandle:a}=r;return{connecting:o?.nodeId===e&&o?.handleId===t&&o?.type===n||i?.nodeId===e&&i?.handleId===t&&i?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}},rr=(0,c.forwardRef)(({type:e="source",position:t=u.Top,isValidConnection:n,isConnectable:r=!0,isConnectableStart:o=!0,isConnectableEnd:i=!0,id:a,onConnect:l,children:s,className:f,onMouseDown:h,onTouchStart:p,...g},m)=>{let y=a||null,v="target"===e,x=t9(),b=nX(),{connectOnClick:_,noPanClassName:S}=t8(rt,w),{connecting:E,clickConnecting:M}=t8(rn(b,y,e),w);b||x.getState().onError?.("010",t3.error010());let N=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:r}=x.getState(),o={...t,...e};if(r){let{edges:e,setEdges:t}=x.getState();t(nW(o,e))}n?.(o),l?.(o)},k=e=>{if(!b)return;let t=nb(e);o&&(t&&0===e.button||!t)&&n7({event:e,handleId:y,nodeId:b,onConnect:N,isTarget:v,getState:x.getState,setState:x.setState,isValidConnection:n||x.getState().isValidConnection||re}),t?h?.(e):p?.(e)};return c.createElement("div",{"data-handleid":y,"data-nodeid":b,"data-handlepos":t,"data-id":`${b}-${y}-${e}`,className:d(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",S,f,{source:!v,target:v,connectable:r,connectablestart:o,connectableend:i,connecting:M,connectionindicator:r&&(o&&!E||i&&E)}]),onMouseDown:k,onTouchStart:k,onClick:_?t=>{let{onClickConnectStart:r,onClickConnectEnd:i,connectionClickStartHandle:a,connectionMode:l,isValidConnection:s}=x.getState();if(!b||!a&&!o)return;if(!a){r?.(t,{nodeId:b,handleId:y,handleType:e}),x.setState({connectionClickStartHandle:{nodeId:b,type:e,handleId:y}});return}let u=nl(t.target),c=n||s||re,{connection:d,isValid:f}=n8({nodeId:b,id:y,type:e},l,a.nodeId,a.handleId||null,a.type,c,u);f&&N(d),i?.(t),x.setState({connectionClickStartHandle:null})}:void 0,ref:m,...g},s)});rr.displayName="Handle";var ro=(0,c.memo)(rr);let ri=({data:e,isConnectable:t,targetPosition:n=u.Top,sourcePosition:r=u.Bottom})=>c.createElement(c.Fragment,null,c.createElement(ro,{type:"target",position:n,isConnectable:t}),e?.label,c.createElement(ro,{type:"source",position:r,isConnectable:t}));ri.displayName="DefaultNode";var ra=(0,c.memo)(ri);let rl=({data:e,isConnectable:t,sourcePosition:n=u.Bottom})=>c.createElement(c.Fragment,null,e?.label,c.createElement(ro,{type:"source",position:n,isConnectable:t}));rl.displayName="InputNode";var rs=(0,c.memo)(rl);let ru=({data:e,isConnectable:t,targetPosition:n=u.Top})=>c.createElement(c.Fragment,null,c.createElement(ro,{type:"target",position:n,isConnectable:t}),e?.label);ru.displayName="OutputNode";var rc=(0,c.memo)(ru);let rd=()=>null;rd.displayName="GroupNode";let rf=e=>({selectedNodes:e.getNodes().filter(e=>e.selected),selectedEdges:e.edges.filter(e=>e.selected).map(e=>({...e}))}),rh=e=>e.id;function rp(e,t){return w(e.selectedNodes.map(rh),t.selectedNodes.map(rh))&&w(e.selectedEdges.map(rh),t.selectedEdges.map(rh))}let rg=(0,c.memo)(({onSelectionChange:e})=>{let t=t9(),{selectedNodes:n,selectedEdges:r}=t8(rf,rp);return(0,c.useEffect)(()=>{let o={nodes:n,edges:r};e?.(o),t.getState().onSelectionChange.forEach(e=>e(o))},[n,r,e]),null});rg.displayName="SelectionListener";let rm=e=>!!e.onSelectionChange;function ry({onSelectionChange:e}){let t=t8(rm);return e||t?c.createElement(rg,{onSelectionChange:e}):null}let rv=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function rx(e,t){(0,c.useEffect)(()=>{void 0!==e&&t(e)},[e])}function rb(e,t,n){(0,c.useEffect)(()=>{void 0!==t&&n({[e]:t})},[t])}let rw=({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:o,onConnectStart:i,onConnectEnd:a,onClickConnectStart:l,onClickConnectEnd:s,nodesDraggable:u,nodesConnectable:d,nodesFocusable:f,edgesFocusable:h,edgesUpdatable:p,elevateNodesOnSelect:g,minZoom:m,maxZoom:y,nodeExtent:v,onNodesChange:x,onEdgesChange:b,elementsSelectable:_,connectionMode:S,snapGrid:E,snapToGrid:M,translateExtent:N,connectOnClick:k,defaultEdgeOptions:C,fitView:A,fitViewOptions:P,onNodesDelete:$,onEdgesDelete:I,onNodeDrag:O,onNodeDragStart:z,onNodeDragStop:R,onSelectionDrag:T,onSelectionDragStart:D,onSelectionDragStop:B,noPanClassName:L,nodeOrigin:H,rfId:j,autoPanOnConnect:V,autoPanOnNodeDrag:X,onError:Y,connectionRadius:F,isValidConnection:K,nodeDragThreshold:q})=>{let{setNodes:W,setEdges:Z,setDefaultNodesAndEdges:U,setMinZoom:G,setMaxZoom:Q,setTranslateExtent:J,setNodeExtent:ee,reset:et}=t8(rv,w),en=t9();return(0,c.useEffect)(()=>(U(n,r?.map(e=>({...e,...C}))),()=>{et()}),[]),rb("defaultEdgeOptions",C,en.setState),rb("connectionMode",S,en.setState),rb("onConnect",o,en.setState),rb("onConnectStart",i,en.setState),rb("onConnectEnd",a,en.setState),rb("onClickConnectStart",l,en.setState),rb("onClickConnectEnd",s,en.setState),rb("nodesDraggable",u,en.setState),rb("nodesConnectable",d,en.setState),rb("nodesFocusable",f,en.setState),rb("edgesFocusable",h,en.setState),rb("edgesUpdatable",p,en.setState),rb("elementsSelectable",_,en.setState),rb("elevateNodesOnSelect",g,en.setState),rb("snapToGrid",M,en.setState),rb("snapGrid",E,en.setState),rb("onNodesChange",x,en.setState),rb("onEdgesChange",b,en.setState),rb("connectOnClick",k,en.setState),rb("fitViewOnInit",A,en.setState),rb("fitViewOnInitOptions",P,en.setState),rb("onNodesDelete",$,en.setState),rb("onEdgesDelete",I,en.setState),rb("onNodeDrag",O,en.setState),rb("onNodeDragStart",z,en.setState),rb("onNodeDragStop",R,en.setState),rb("onSelectionDrag",T,en.setState),rb("onSelectionDragStart",D,en.setState),rb("onSelectionDragStop",B,en.setState),rb("noPanClassName",L,en.setState),rb("nodeOrigin",H,en.setState),rb("rfId",j,en.setState),rb("autoPanOnConnect",V,en.setState),rb("autoPanOnNodeDrag",X,en.setState),rb("onError",Y,en.setState),rb("connectionRadius",F,en.setState),rb("isValidConnection",K,en.setState),rb("nodeDragThreshold",q,en.setState),rx(e,W),rx(t,Z),rx(m,G),rx(y,Q),rx(N,J),rx(v,ee),null},r_={display:"none"},rS={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},rE="react-flow__node-desc",rM="react-flow__edge-desc",rN=e=>e.ariaLiveMessage;function rk({rfId:e}){let t=t8(rN);return c.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:rS},t)}function rC({rfId:e,disableKeyboardA11y:t}){return c.createElement(c.Fragment,null,c.createElement("div",{id:`${rE}-${e}`,style:r_},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),c.createElement("div",{id:`${rM}-${e}`,style:r_},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&c.createElement(rk,{rfId:e}))}var rA=(e=null,t={actInsideInputWithModifier:!0})=>{let[n,r]=(0,c.useState)(!1),o=(0,c.useRef)(!1),i=(0,c.useRef)(new Set([])),[a,l]=(0,c.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.split("+")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,c.useEffect)(()=>{let n="undefined"!=typeof document?document:null,s=t?.target||n;if(null!==e){let e=e=>{if(o.current=e.ctrlKey||e.metaKey||e.shiftKey,(!o.current||o.current&&!t.actInsideInputWithModifier)&&nx(e))return!1;let n=r$(e.code,l);i.current.add(e[n]),rP(a,i.current,!1)&&(e.preventDefault(),r(!0))},n=e=>{if((!o.current||o.current&&!t.actInsideInputWithModifier)&&nx(e))return!1;let n=r$(e.code,l);rP(a,i.current,!0)?(r(!1),i.current.clear()):i.current.delete(e[n]),"Meta"===e.key&&i.current.clear(),o.current=!1},u=()=>{i.current.clear(),r(!1)};return s?.addEventListener("keydown",e),s?.addEventListener("keyup",n),window.addEventListener("blur",u),()=>{s?.removeEventListener("keydown",e),s?.removeEventListener("keyup",n),window.removeEventListener("blur",u)}}},[e,r]),n};function rP(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function r$(e,t){return t.includes(e)?"code":"key"}function rI(e,t,n){e.forEach(r=>{let o=r.parentNode||r.parentId;if(o&&!e.has(o))throw Error(`Parent node ${o} not found`);if(o||n?.[r.id]){let{x:o,y:i,z:a}=function e(t,n,r,o){let i=t.parentNode||t.parentId;if(!i)return r;let a=n.get(i),l=nG(a,o);return e(a,n,{x:(r.x??0)+l.x,y:(r.y??0)+l.y,z:(a[ng]?.z??0)>(r.z??0)?a[ng]?.z??0:r.z??0},o)}(r,e,{...r.position,z:r[ng]?.z??0},t);r.positionAbsolute={x:o,y:i},r[ng].z=a,n?.[r.id]&&(r[ng].isParent=!0)}})}function rO(e,t,n,r){let o=new Map,i={},a=1e3*!!r;return e.forEach(e=>{let n=(np(e.zIndex)?e.zIndex:0)+(e.selected?a:0),r=t.get(e.id),l={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},s=e.parentNode||e.parentId;s&&(i[s]=!0),Object.defineProperty(l,ng,{enumerable:!1,value:{handleBounds:r?.type&&r?.type!==e.type?void 0:r?.[ng]?.handleBounds,z:n}}),o.set(e.id,l)}),rI(o,n,i),o}function rz(e,t={}){let{getNodes:n,width:r,height:o,minZoom:i,maxZoom:a,d3Zoom:l,d3Selection:s,fitViewOnInitDone:u,fitViewOnInit:c,nodeOrigin:d}=e(),f=t.initial&&!u&&c;if(l&&s&&(f||!t.initial)){let e=n().filter(e=>{let n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some(t=>t.id===e.id):n}),u=e.every(e=>e.width&&e.height);if(e.length>0&&u){let{x:n,y:u,zoom:c}=n1(nQ(e,d),r,o,t.minZoom??i,t.maxZoom??a,t.padding??.1),f=tj.translate(n,u).scale(c);return"number"==typeof t.duration&&t.duration>0?l.transform(n2(s,t.duration),f):l.transform(s,f),!0}}return!1}function rR({changedNodes:e,changedEdges:t,get:n,set:r}){let{nodeInternals:o,edges:i,onNodesChange:a,onEdgesChange:l,hasDefaultNodes:s,hasDefaultEdges:u}=n();e?.length&&(s&&r({nodeInternals:(e.forEach(e=>{let t=o.get(e.id);t&&o.set(t.id,{...t,[ng]:t[ng],selected:e.selected})}),new Map(o))}),a?.(e)),t?.length&&(u&&r({edges:i.map(e=>{let n=t.find(t=>t.id===e.id);return n&&(e.selected=n.selected),e})}),l?.(t))}let rT=()=>{},rD={zoomIn:rT,zoomOut:rT,zoomTo:rT,getZoom:()=>1,setViewport:rT,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:rT,fitBounds:rT,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},rB=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),rL=()=>{let e=t9(),{d3Zoom:t,d3Selection:n}=t8(rB,w);return(0,c.useMemo)(()=>n&&t?{zoomIn:e=>t.scaleBy(n2(n,e?.duration),1.2),zoomOut:e=>t.scaleBy(n2(n,e?.duration),1/1.2),zoomTo:(e,r)=>t.scaleTo(n2(n,r?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(r,o)=>{let[i,a,l]=e.getState().transform,s=tj.translate(r.x??i,r.y??a).scale(r.zoom??l);t.transform(n2(n,o?.duration),s)},getViewport:()=>{let[t,n,r]=e.getState().transform;return{x:t,y:n,zoom:r}},fitView:t=>rz(e.getState,t),setCenter:(r,o,i)=>{let{width:a,height:l,maxZoom:s}=e.getState(),u=void 0!==i?.zoom?i.zoom:s,c=a/2-r*u,d=l/2-o*u,f=tj.translate(c,d).scale(u);t.transform(n2(n,i?.duration),f)},fitBounds:(r,o)=>{let{width:i,height:a,minZoom:l,maxZoom:s}=e.getState(),{x:u,y:c,zoom:d}=n1(r,i,a,l,s,o?.padding??.1),f=tj.translate(u,c).scale(d);t.transform(n2(n,o?.duration),f)},project:t=>{let{transform:n,snapToGrid:r,snapGrid:o}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),nZ(t,n,r,o)},screenToFlowPosition:t=>{let{transform:n,snapToGrid:r,snapGrid:o,domNode:i}=e.getState();if(!i)return t;let{x:a,y:l}=i.getBoundingClientRect();return nZ({x:t.x-a,y:t.y-l},n,r,o)},flowToScreenPosition:t=>{let{transform:n,domNode:r}=e.getState();if(!r)return t;let{x:o,y:i}=r.getBoundingClientRect(),a=nU(t,n);return{x:a.x+o,y:a.y+i}},viewportInitialized:!0}:rD,[t,n])};function rH(){let e=rL(),t=t9(),n=(0,c.useCallback)(()=>t.getState().getNodes().map(e=>({...e})),[]),r=(0,c.useCallback)(e=>t.getState().nodeInternals.get(e),[]),o=(0,c.useCallback)(()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},[]),i=(0,c.useCallback)(e=>{let{edges:n=[]}=t.getState();return n.find(t=>t.id===e)},[]),a=(0,c.useCallback)(e=>{let{getNodes:n,setNodes:r,hasDefaultNodes:o,onNodesChange:i}=t.getState(),a=n(),l="function"==typeof e?e(a):e;o?r(l):i&&i(0===l.length?a.map(e=>({type:"remove",id:e.id})):l.map(e=>({item:e,type:"reset"})))},[]),l=(0,c.useCallback)(e=>{let{edges:n=[],setEdges:r,hasDefaultEdges:o,onEdgesChange:i}=t.getState(),a="function"==typeof e?e(n):e;o?r(a):i&&i(0===a.length?n.map(e=>({type:"remove",id:e.id})):a.map(e=>({item:e,type:"reset"})))},[]),s=(0,c.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{getNodes:r,setNodes:o,hasDefaultNodes:i,onNodesChange:a}=t.getState();i?o([...r(),...n]):a&&a(n.map(e=>({item:e,type:"add"})))},[]),u=(0,c.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{edges:r=[],setEdges:o,hasDefaultEdges:i,onEdgesChange:a}=t.getState();i?o([...r,...n]):a&&a(n.map(e=>({item:e,type:"add"})))},[]),d=(0,c.useCallback)(()=>{let{getNodes:e,edges:n=[],transform:r}=t.getState(),[o,i,a]=r;return{nodes:e().map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:o,y:i,zoom:a}}},[]),f=(0,c.useCallback)(({nodes:e,edges:n})=>{let{nodeInternals:r,getNodes:o,edges:i,hasDefaultNodes:a,hasDefaultEdges:l,onNodesDelete:s,onEdgesDelete:u,onNodesChange:c,onEdgesChange:d}=t.getState(),f=(e||[]).map(e=>e.id),h=(n||[]).map(e=>e.id),p=o().reduce((e,t)=>{let n=t.parentNode||t.parentId,r=!f.includes(t.id)&&n&&e.find(e=>e.id===n);return("boolean"!=typeof t.deletable||t.deletable)&&(f.includes(t.id)||r)&&e.push(t),e},[]),g=i.filter(e=>"boolean"!=typeof e.deletable||e.deletable),m=g.filter(e=>h.includes(e.id));if(p||m){let e=[...m,...n0(p,g)],n=e.reduce((e,t)=>(e.includes(t.id)||e.push(t.id),e),[]);(l||a)&&(l&&t.setState({edges:i.filter(e=>!n.includes(e.id))}),a&&(p.forEach(e=>{r.delete(e.id)}),t.setState({nodeInternals:new Map(r)}))),n.length>0&&(u?.(e),d&&d(n.map(e=>({id:e,type:"remove"})))),p.length>0&&(s?.(p),c&&c(p.map(e=>({id:e.id,type:"remove"}))))}},[]),h=(0,c.useCallback)(e=>{let n=nh(e),r=n?null:t.getState().nodeInternals.get(e.id);return n||r?[n?e:nd(r),r,n]:[null,null,n]},[]),p=(0,c.useCallback)((e,n=!0,r)=>{let[o,i,a]=h(e);return o?(r||t.getState().getNodes()).filter(e=>{if(!a&&(e.id===i.id||!e.positionAbsolute))return!1;let t=nf(nd(e),o);return n&&t>0||t>=o.width*o.height}):[]},[]),g=(0,c.useCallback)((e,t,n=!0)=>{let[r]=h(e);if(!r)return!1;let o=nf(r,t);return n&&o>0||o>=r.width*r.height},[]);return(0,c.useMemo)(()=>({...e,getNodes:n,getNode:r,getEdges:o,getEdge:i,setNodes:a,setEdges:l,addNodes:s,addEdges:u,toObject:d,deleteElements:f,getIntersectingNodes:p,isNodeIntersecting:g}),[e,n,r,o,i,a,l,s,u,d,f,p,g])}let rj={actInsideInputWithModifier:!1};var rV=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{let n=t9(),{deleteElements:r}=rH(),o=rA(e,rj),i=rA(t);(0,c.useEffect)(()=>{if(o){let{edges:e,getNodes:t}=n.getState();r({nodes:t().filter(e=>e.selected),edges:e.filter(e=>e.selected)}),n.setState({nodesSelectionActive:!1})}},[o]),(0,c.useEffect)(()=>{n.setState({multiSelectionActive:i})},[i])};let rX={position:"absolute",width:"100%",height:"100%",top:0,left:0},rY=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,rF=e=>({x:e.x,y:e.y,zoom:e.k}),rK=(e,t)=>e.target.closest(`.${t}`),rq=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),rW=e=>{let t=e.ctrlKey&&n_()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},rZ=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),rU=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:r,zoomOnScroll:o=!0,zoomOnPinch:a=!0,panOnScroll:l=!1,panOnScrollSpeed:s=.5,panOnScrollMode:u=i.Free,zoomOnDoubleClick:d=!0,elementsSelectable:f,panOnDrag:h=!0,defaultViewport:p,translateExtent:g,minZoom:m,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:x=!0,children:b,noWheelClassName:_,noPanClassName:E})=>{let M=(0,c.useRef)(),N=t9(),k=(0,c.useRef)(!1),C=(0,c.useRef)(!1),A=(0,c.useRef)(null),P=(0,c.useRef)({x:0,y:0,zoom:0}),{d3Zoom:$,d3Selection:I,d3ZoomHandler:O,userSelectionActive:z}=t8(rZ,w),R=rA(v),T=(0,c.useRef)(0),D=(0,c.useRef)(!1),B=(0,c.useRef)();return!function(e){let t=t9();(0,c.useEffect)(()=>{let n,r=()=>{if(!e.current)return;let n=nn(e.current);(0===n.height||0===n.width)&&t.getState().onError?.("004",t3.error004()),t.setState({width:n.width||500,height:n.height||500})};return r(),window.addEventListener("resize",r),e.current&&(n=new ResizeObserver(()=>r())).observe(e.current),()=>{window.removeEventListener("resize",r),n&&e.current&&n.unobserve(e.current)}},[])}(A),(0,c.useEffect)(()=>{if(A.current){let e=A.current.getBoundingClientRect(),t=(function(){var e,t,n,r=tY,o=tF,i=tZ,a=tq,l=tW,s=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],c=250,d=ew,f=S("start","zoom","end"),h=0,p=10;function g(e){e.property("__zoom",tK).on("wheel.zoom",_,{passive:!1}).on("mousedown.zoom",E).on("dblclick.zoom",M).filter(l).on("touchstart.zoom",N).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",C).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function m(e,t){return(t=Math.max(s[0],Math.min(s[1],t)))===e.k?e:new tH(t,e.x,e.y)}function y(e,t,n){var r=t[0]-n[0]*e.k,o=t[1]-n[1]*e.k;return r===e.x&&o===e.y?e:new tH(e.k,r,o)}function v(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function x(e,t,n,r){e.on("start.zoom",function(){b(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){b(this,arguments).event(r).end()}).tween("zoom",function(){var e=arguments,i=b(this,e).event(r),a=o.apply(this,e),l=null==n?v(a):"function"==typeof n?n.apply(this,e):n,s=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),u=this.__zoom,c="function"==typeof t?t.apply(this,e):t,f=d(u.invert(l).concat(s/u.k),c.invert(l).concat(s/c.k));return function(e){if(1===e)e=c;else{var t=f(e),n=s/t[2];e=new tH(n,l[0]-t[0]*n,l[1]-t[1]*n)}i.zoom(null,e)}})}function b(e,t,n){return!n&&e.__zooming||new w(e,t)}function w(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=o.apply(e,t),this.taps=0}function _(e,...t){if(r.apply(this,arguments)){var n=b(this,t).event(e),o=this.__zoom,l=Math.max(s[0],Math.min(s[1],o.k*Math.pow(2,a.apply(this,arguments)))),c=e_(e);if(n.wheel)(n.mouse[0][0]!==c[0]||n.mouse[0][1]!==c[1])&&(n.mouse[1]=o.invert(n.mouse[0]=c)),clearTimeout(n.wheel);else{if(o.k===l)return;n.mouse=[c,o.invert(c)],eq(this),n.start()}tX(e),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",i(y(m(o,l),n.mouse[0],n.mouse[1]),n.extent,u))}}function E(e,...t){if(!n&&r.apply(this,arguments)){var o=e.currentTarget,a=b(this,t,!0).event(e),l=eh(e.view).on("mousemove.zoom",function(e){if(tX(e),!a.moved){var t=e.clientX-c,n=e.clientY-d;a.moved=t*t+n*n>h}a.event(e).zoom("mouse",i(y(a.that.__zoom,a.mouse[0]=e_(e,o),a.mouse[1]),a.extent,u))},!0).on("mouseup.zoom",function(e){l.on("mousemove.zoom mouseup.zoom",null),ex(e.view,a.moved),tX(e),a.event(e).end()},!0),s=e_(e,o),c=e.clientX,d=e.clientY;ev(e.view),tV(e),a.mouse=[s,this.__zoom.invert(s)],eq(this),a.start()}}function M(e,...t){if(r.apply(this,arguments)){var n=this.__zoom,a=e_(e.changedTouches?e.changedTouches[0]:e,this),l=n.invert(a),s=n.k*(e.shiftKey?.5:2),d=i(y(m(n,s),a,l),o.apply(this,t),u);tX(e),c>0?eh(this).transition().duration(c).call(x,d,a,e):eh(this).call(g.transform,d,a,e)}}function N(n,...o){if(r.apply(this,arguments)){var i,a,l,s,u=n.touches,c=u.length,d=b(this,o,n.changedTouches.length===c).event(n);for(tV(n),a=0;a<c;++a)s=[s=e_(l=u[a],this),this.__zoom.invert(s),l.identifier],d.touch0?d.touch1||d.touch0[2]===s[2]||(d.touch1=s,d.taps=0):(d.touch0=s,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=s[0],e=setTimeout(function(){e=null},500)),eq(this),d.start())}}function k(e,...t){if(this.__zooming){var n,r,o,a,l=b(this,t).event(e),s=e.changedTouches,c=s.length;for(tX(e),n=0;n<c;++n)o=e_(r=s[n],this),l.touch0&&l.touch0[2]===r.identifier?l.touch0[0]=o:l.touch1&&l.touch1[2]===r.identifier&&(l.touch1[0]=o);if(r=l.that.__zoom,l.touch1){var d=l.touch0[0],f=l.touch0[1],h=l.touch1[0],p=l.touch1[1],g=(g=h[0]-d[0])*g+(g=h[1]-d[1])*g,v=(v=p[0]-f[0])*v+(v=p[1]-f[1])*v;r=m(r,Math.sqrt(g/v)),o=[(d[0]+h[0])/2,(d[1]+h[1])/2],a=[(f[0]+p[0])/2,(f[1]+p[1])/2]}else{if(!l.touch0)return;o=l.touch0[0],a=l.touch0[1]}l.zoom("touch",i(y(r,o,a),l.extent,u))}}function C(e,...r){if(this.__zooming){var o,i,a=b(this,r).event(e),l=e.changedTouches,s=l.length;for(tV(e),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),o=0;o<s;++o)i=l[o],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=e_(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<p)){var u=eh(this).on("dblclick.zoom");u&&u.apply(this,arguments)}}}return g.transform=function(e,t,n,r){var o=e.selection?e.selection():e;o.property("__zoom",tK),e!==o?x(e,t,n,r):o.interrupt().each(function(){b(this,arguments).event(r).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()})},g.scaleBy=function(e,t,n,r){g.scaleTo(e,function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n},n,r)},g.scaleTo=function(e,t,n,r){g.transform(e,function(){var e=o.apply(this,arguments),r=this.__zoom,a=null==n?v(e):"function"==typeof n?n.apply(this,arguments):n,l=r.invert(a),s="function"==typeof t?t.apply(this,arguments):t;return i(y(m(r,s),a,l),e,u)},n,r)},g.translateBy=function(e,t,n,r){g.transform(e,function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),o.apply(this,arguments),u)},null,r)},g.translateTo=function(e,t,n,r,a){g.transform(e,function(){var e=o.apply(this,arguments),a=this.__zoom,l=null==r?v(e):"function"==typeof r?r.apply(this,arguments):r;return i(tj.translate(l[0],l[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,u)},r,a)},w.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=eh(this.that).datum();f.call(e,this.that,new tL(e,{sourceEvent:this.sourceEvent,target:g,type:e,transform:this.that.__zoom,dispatch:f}),t)}},g.wheelDelta=function(e){return arguments.length?(a="function"==typeof e?e:tB(+e),g):a},g.filter=function(e){return arguments.length?(r="function"==typeof e?e:tB(!!e),g):r},g.touchable=function(e){return arguments.length?(l="function"==typeof e?e:tB(!!e),g):l},g.extent=function(e){return arguments.length?(o="function"==typeof e?e:tB([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),g):o},g.scaleExtent=function(e){return arguments.length?(s[0]=+e[0],s[1]=+e[1],g):[s[0],s[1]]},g.translateExtent=function(e){return arguments.length?(u[0][0]=+e[0][0],u[1][0]=+e[1][0],u[0][1]=+e[0][1],u[1][1]=+e[1][1],g):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},g.constrain=function(e){return arguments.length?(i=e,g):i},g.duration=function(e){return arguments.length?(c=+e,g):c},g.interpolate=function(e){return arguments.length?(d=e,g):d},g.on=function(){var e=f.on.apply(f,arguments);return e===f?g:e},g.clickDistance=function(e){return arguments.length?(h=(e*=1)*e,g):Math.sqrt(h)},g.tapDistance=function(e){return arguments.length?(p=+e,g):p},g})().scaleExtent([m,y]).translateExtent(g),n=eh(A.current).call(t),r=tj.translate(p.x,p.y).scale(nr(p.zoom,m,y)),o=[[0,0],[e.width,e.height]],i=t.constrain()(r,o,g);t.transform(n,i),t.wheelDelta(rW),N.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[i.x,i.y,i.k],domNode:A.current.closest(".react-flow")})}},[]),(0,c.useEffect)(()=>{I&&$&&(!l||R||z?void 0!==O&&I.on("wheel.zoom",function(e,t){if(!x&&"wheel"===e.type&&!e.ctrlKey||rK(e,_))return null;e.preventDefault(),O.call(this,e,t)},{passive:!1}):I.on("wheel.zoom",r=>{if(rK(r,_))return!1;r.preventDefault(),r.stopImmediatePropagation();let o=I.property("__zoom").k||1;if(r.ctrlKey&&a){let e=e_(r),t=o*Math.pow(2,rW(r));$.scaleTo(I,t,e,r);return}let l=1===r.deltaMode?20:1,c=u===i.Vertical?0:r.deltaX*l,d=u===i.Horizontal?0:r.deltaY*l;!n_()&&r.shiftKey&&u!==i.Vertical&&(c=r.deltaY*l,d=0),$.translateBy(I,-(c/o)*s,-(d/o)*s,{internal:!0});let f=rF(I.property("__zoom")),{onViewportChangeStart:h,onViewportChange:p,onViewportChangeEnd:g}=N.getState();clearTimeout(B.current),D.current||(D.current=!0,t?.(r,f),h?.(f)),D.current&&(e?.(r,f),p?.(f),B.current=setTimeout(()=>{n?.(r,f),g?.(f),D.current=!1},150))},{passive:!1}))},[z,l,u,I,$,O,R,a,x,_,t,e,n]),(0,c.useEffect)(()=>{$&&$.on("start",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;T.current=e.sourceEvent?.button;let{onViewportChangeStart:n}=N.getState(),r=rF(e.transform);k.current=!0,P.current=r,e.sourceEvent?.type==="mousedown"&&N.setState({paneDragging:!0}),n?.(r),t?.(e.sourceEvent,r)})},[$,t]),(0,c.useEffect)(()=>{$&&(z&&!k.current?$.on("zoom",null):z||$.on("zoom",t=>{let{onViewportChange:n}=N.getState();if(N.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),C.current=!!(r&&rq(h,T.current??0)),(e||n)&&!t.sourceEvent?.internal){let r=rF(t.transform);n?.(r),e?.(t.sourceEvent,r)}}))},[z,$,e,h,r]),(0,c.useEffect)(()=>{$&&$.on("end",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;let{onViewportChangeEnd:t}=N.getState();if(k.current=!1,N.setState({paneDragging:!1}),r&&rq(h,T.current??0)&&!C.current&&r(e.sourceEvent),C.current=!1,(n||t)&&rY(P.current,e.transform)){let r=rF(e.transform);P.current=r,clearTimeout(M.current),M.current=setTimeout(()=>{t?.(r),n?.(e.sourceEvent,r)},150*!!l)}})},[$,l,h,n,r]),(0,c.useEffect)(()=>{$&&$.filter(e=>{let t=R||o,n=a&&e.ctrlKey;if((!0===h||Array.isArray(h)&&h.includes(1))&&1===e.button&&"mousedown"===e.type&&(rK(e,"react-flow__node")||rK(e,"react-flow__edge")))return!0;if(!h&&!t&&!l&&!d&&!a||z||!d&&"dblclick"===e.type||rK(e,_)&&"wheel"===e.type||rK(e,E)&&("wheel"!==e.type||l&&"wheel"===e.type&&!R)||!a&&e.ctrlKey&&"wheel"===e.type||!t&&!l&&!n&&"wheel"===e.type||!h&&("mousedown"===e.type||"touchstart"===e.type)||Array.isArray(h)&&!h.includes(e.button)&&"mousedown"===e.type)return!1;let r=Array.isArray(h)&&h.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&r})},[z,$,o,a,l,d,h,f,R]),c.createElement("div",{className:"react-flow__renderer",ref:A,style:rX},b)},rG=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function rQ(){let{userSelectionActive:e,userSelectionRect:t}=t8(rG,w);return e&&t?c.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function rJ(e,t){let n=t.parentNode||t.parentId,r=e.find(e=>e.id===n);if(r){let e=t.position.x+t.width-r.width,n=t.position.y+t.height-r.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(r.style={...r.style},r.style.width=r.style.width??r.width,r.style.height=r.style.height??r.height,e>0&&(r.style.width+=e),n>0&&(r.style.height+=n),t.position.x<0){let e=Math.abs(t.position.x);r.position.x=r.position.x-e,r.style.width+=e,t.position.x=0}if(t.position.y<0){let e=Math.abs(t.position.y);r.position.y=r.position.y-e,r.style.height+=e,t.position.y=0}r.width=r.style.width,r.height=r.style.height}}}function r0(e,t){if(e.some(e=>"reset"===e.type))return e.filter(e=>"reset"===e.type).map(e=>e.item);let n=e.filter(e=>"add"===e.type).map(e=>e.item);return t.reduce((t,n)=>{let r=e.filter(e=>e.id===n.id);if(0===r.length)return t.push(n),t;let o={...n};for(let e of r)if(e)switch(e.type){case"select":o.selected=e.selected;break;case"position":void 0!==e.position&&(o.position=e.position),void 0!==e.positionAbsolute&&(o.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(o.dragging=e.dragging),o.expandParent&&rJ(t,o);break;case"dimensions":void 0!==e.dimensions&&(o.width=e.dimensions.width,o.height=e.dimensions.height),void 0!==e.updateStyle&&(o.style={...o.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(o.resizing=e.resizing),o.expandParent&&rJ(t,o);break;case"remove":return t}return t.push(o),t},n)}function r1(e,t){return r0(e,t)}let r2=(e,t)=>({id:e,type:"select",selected:t});function r5(e,t){return e.reduce((e,n)=>{let r=t.includes(n.id);return!n.selected&&r?(n.selected=!0,e.push(r2(n.id,!0))):n.selected&&!r&&(n.selected=!1,e.push(r2(n.id,!1))),e},[])}let r3=(e,t)=>n=>{n.target===t.current&&e?.(n)},r4=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),r8=(0,c.memo)(({isSelecting:e,selectionMode:t=a.Full,panOnDrag:n,onSelectionStart:r,onSelectionEnd:o,onPaneClick:i,onPaneContextMenu:l,onPaneScroll:s,onPaneMouseEnter:u,onPaneMouseMove:f,onPaneMouseLeave:h,children:p})=>{let g=(0,c.useRef)(null),m=t9(),y=(0,c.useRef)(0),v=(0,c.useRef)(0),x=(0,c.useRef)(),{userSelectionActive:b,elementsSelectable:_,dragging:S}=t8(r4,w),E=()=>{m.setState({userSelectionActive:!1,userSelectionRect:null}),y.current=0,v.current=0},M=e=>{i?.(e),m.getState().resetSelectedElements(),m.setState({nodesSelectionActive:!1})},N=_&&(e||b);return c.createElement("div",{className:d(["react-flow__pane",{dragging:S,selection:e}]),onClick:N?void 0:r3(M,g),onContextMenu:r3(e=>{if(Array.isArray(n)&&n?.includes(2))return void e.preventDefault();l?.(e)},g),onWheel:r3(s?e=>s(e):void 0,g),onMouseEnter:N?void 0:u,onMouseDown:N?t=>{let{resetSelectedElements:n,domNode:o}=m.getState();if(x.current=o?.getBoundingClientRect(),!_||!e||0!==t.button||t.target!==g.current||!x.current)return;let{x:i,y:a}=nw(t,x.current);n(),m.setState({userSelectionRect:{width:0,height:0,startX:i,startY:a,x:i,y:a}}),r?.(t)}:void 0,onMouseMove:N?n=>{let{userSelectionRect:r,nodeInternals:o,edges:i,transform:l,onNodesChange:s,onEdgesChange:u,nodeOrigin:c,getNodes:d}=m.getState();if(!e||!x.current||!r)return;m.setState({userSelectionActive:!0,nodesSelectionActive:!1});let f=nw(n,x.current),h=r.startX??0,p=r.startY??0,g={...r,x:f.x<h?f.x:h,y:f.y<p?f.y:p,width:Math.abs(f.x-h),height:Math.abs(f.y-p)},b=d(),w=nJ(o,g,l,t===a.Partial,!0,c),_=n0(w,i).map(e=>e.id),S=w.map(e=>e.id);if(y.current!==S.length){y.current=S.length;let e=r5(b,S);e.length&&s?.(e)}if(v.current!==_.length){v.current=_.length;let e=r5(i,_);e.length&&u?.(e)}m.setState({userSelectionRect:g})}:f,onMouseUp:N?e=>{if(0!==e.button)return;let{userSelectionRect:t}=m.getState();!b&&t&&e.target===g.current&&M?.(e),m.setState({nodesSelectionActive:y.current>0}),E(),o?.(e)}:void 0,onMouseLeave:N?e=>{b&&(m.setState({nodesSelectionActive:y.current>0}),o?.(e)),E()}:h,ref:g,style:rX},p,c.createElement(rQ,null))});function r9(e,t,n){let r=e;do{if(r?.matches(t))return!0;if(r===n.current)break;r=r.parentElement}while(r);return!1}function r6(e,t,n,r,o=[0,0],i){var a;let l=(a=e.extent||r)&&"parent"!==a?[a[0],[a[1][0]-(e.width||0),a[1][1]-(e.height||0)]]:a,s=l,u=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&u&&"parent"!==e.extent){let{x:t,y:r}=nG(n.get(u),o).positionAbsolute;s=[[e.extent[0][0]+t,e.extent[0][1]+r],[e.extent[1][0]+t,e.extent[1][1]+r]]}}else if(u&&e.width&&e.height){let t=n.get(u),{x:r,y:i}=nG(t,o).positionAbsolute;s=t&&np(r)&&np(i)&&np(t.width)&&np(t.height)?[[r+e.width*o[0],i+e.height*o[1]],[r+t.width-e.width+e.width*o[0],i+t.height-e.height+e.height*o[1]]]:s}else i?.("005",t3.error005()),s=l;let c={x:0,y:0};u&&(c=nG(n.get(u),o).positionAbsolute);let d=s&&"parent"!==s?no(t,s):t;return{position:{x:d.x-c.x,y:d.y-c.y},positionAbsolute:d}}function r7({nodeId:e,dragItems:t,nodeInternals:n}){let r=t.map(e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute}));return[e?r.find(t=>t.id===e):r[0],r]}r8.displayName="Pane";let oe=(e,t,n,r)=>{let o=t.querySelectorAll(e);if(!o||!o.length)return null;let i=Array.from(o),a=t.getBoundingClientRect(),l={x:a.width*r[0],y:a.height*r[1]};return i.map(e=>{let t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-l.x)/n,y:(t.top-a.top-l.y)/n,...nn(e)}})};function ot(e,t,n){return void 0===n?n:r=>{let o=t().nodeInternals.get(e);o&&n(r,{...o})}}function on({id:e,store:t,unselect:n=!1,nodeRef:r}){let{addSelectedNodes:o,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:l,onError:s}=t.getState(),u=l.get(e);if(!u)return void s?.("012",t3.error012(e));t.setState({nodesSelectionActive:!1}),u.selected?(n||u.selected&&a)&&(i({nodes:[u],edges:[]}),requestAnimationFrame(()=>r?.current?.blur())):o([e])}function or(e){return(t,n,r)=>e?.(t,r)}function oo({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:r,nodeId:o,isSelectable:i,selectNodesOnDrag:a}){let l=t9(),[s,u]=(0,c.useState)(!1),d=(0,c.useRef)([]),f=(0,c.useRef)({x:null,y:null}),h=(0,c.useRef)(0),p=(0,c.useRef)(null),g=(0,c.useRef)({x:0,y:0}),m=(0,c.useRef)(null),y=(0,c.useRef)(!1),v=(0,c.useRef)(!1),x=(0,c.useRef)(!1),b=function(){let e=t9();return(0,c.useCallback)(({sourceEvent:t})=>{let{transform:n,snapGrid:r,snapToGrid:o}=e.getState(),i=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,l={x:(i-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:o?r[0]*Math.round(l.x/r[0]):l.x,ySnapped:o?r[1]*Math.round(l.y/r[1]):l.y,...l}},[])}();return(0,c.useEffect)(()=>{if(e?.current){let s=eh(e.current),c=({x:e,y:t})=>{let{nodeInternals:n,onNodeDrag:r,onSelectionDrag:i,updateNodePositions:a,nodeExtent:s,snapGrid:c,snapToGrid:h,nodeOrigin:p,onError:g}=l.getState();f.current={x:e,y:t};let y=!1,v={x:0,y:0,x2:0,y2:0};if(d.current.length>1&&s&&(v=nu(nQ(d.current,p))),d.current=d.current.map(r=>{let o={x:e-r.distance.x,y:t-r.distance.y};h&&(o.x=c[0]*Math.round(o.x/c[0]),o.y=c[1]*Math.round(o.y/c[1]));let i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];d.current.length>1&&s&&!r.extent&&(i[0][0]=r.positionAbsolute.x-v.x+s[0][0],i[1][0]=r.positionAbsolute.x+(r.width??0)-v.x2+s[1][0],i[0][1]=r.positionAbsolute.y-v.y+s[0][1],i[1][1]=r.positionAbsolute.y+(r.height??0)-v.y2+s[1][1]);let a=r6(r,o,n,i,p,g);return y=y||r.position.x!==a.position.x||r.position.y!==a.position.y,r.position=a.position,r.positionAbsolute=a.positionAbsolute,r}),!y)return;a(d.current,!0,!0),u(!0);let x=o?r:or(i);if(x&&m.current){let[e,t]=r7({nodeId:o,dragItems:d.current,nodeInternals:n});x(m.current,e,t)}},w=()=>{if(!p.current)return;let[e,t]=na(g.current,p.current);if(0!==e||0!==t){let{transform:n,panBy:r}=l.getState();f.current.x=(f.current.x??0)-e/n[2],f.current.y=(f.current.y??0)-t/n[2],r({x:e,y:t})&&c(f.current)}h.current=requestAnimationFrame(w)},_=t=>{let{nodeInternals:n,multiSelectionActive:r,nodesDraggable:s,unselectNodesAndEdges:u,onNodeDragStart:c,onSelectionDragStart:h}=l.getState();v.current=!0;let p=o?c:or(h);a&&i||r||!o||n.get(o)?.selected||u(),o&&i&&a&&on({id:o,store:l,nodeRef:e});let g=b(t);if(f.current=g,d.current=Array.from(n.values()).filter(e=>(e.selected||e.id===o)&&(!e.parentNode||e.parentId||!function e(t,n){let r=t.parentNode||t.parentId;if(!r)return!1;let o=n.get(r);return!!o&&(!!o.selected||e(o,n))}(e,n))&&(e.draggable||s&&void 0===e.draggable)).map(e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:g.x-(e.positionAbsolute?.x??0),y:g.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})),p&&d.current){let[e,r]=r7({nodeId:o,dragItems:d.current,nodeInternals:n});p(t.sourceEvent,e,r)}};if(t)s.on(".drag",null);else{let t=(function(){var e,t,n,r,o=tQ,i=tJ,a=t0,l=t1,s={},u=S("start","drag","end"),c=0,d=0;function f(e){e.on("mousedown.drag",h).filter(l).on("touchstart.drag",m).on("touchmove.drag",y,ep).on("touchend.drag touchcancel.drag",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function h(a,l){if(!r&&o.call(this,a,l)){var s=x(this,i.call(this,a,l),a,l,"mouse");s&&(eh(a.view).on("mousemove.drag",p,eg).on("mouseup.drag",g,eg),ev(a.view),em(a),n=!1,e=a.clientX,t=a.clientY,s("start",a))}}function p(r){if(ey(r),!n){var o=r.clientX-e,i=r.clientY-t;n=o*o+i*i>d}s.mouse("drag",r)}function g(e){eh(e.view).on("mousemove.drag mouseup.drag",null),ex(e.view,n),ey(e),s.mouse("end",e)}function m(e,t){if(o.call(this,e,t)){var n,r,a=e.changedTouches,l=i.call(this,e,t),s=a.length;for(n=0;n<s;++n)(r=x(this,l,e,t,a[n].identifier,a[n]))&&(em(e),r("start",e,a[n]))}}function y(e){var t,n,r=e.changedTouches,o=r.length;for(t=0;t<o;++t)(n=s[r[t].identifier])&&(ey(e),n("drag",e,r[t]))}function v(e){var t,n,o=e.changedTouches,i=o.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),t=0;t<i;++t)(n=s[o[t].identifier])&&(em(e),n("end",e,o[t]))}function x(e,t,n,r,o,i){var l,d,h,p=u.copy(),g=e_(i||n,t);if(null!=(h=a.call(e,new tG("beforestart",{sourceEvent:n,target:f,identifier:o,active:c,x:g[0],y:g[1],dx:0,dy:0,dispatch:p}),r)))return l=h.x-g[0]||0,d=h.y-g[1]||0,function n(i,a,u){var m,y=g;switch(i){case"start":s[o]=n,m=c++;break;case"end":delete s[o],--c;case"drag":g=e_(u||a,t),m=c}p.call(i,e,new tG(i,{sourceEvent:a,subject:h,target:f,identifier:o,active:m,x:g[0]+l,y:g[1]+d,dx:g[0]-y[0],dy:g[1]-y[1],dispatch:p}),r)}}return f.filter=function(e){return arguments.length?(o="function"==typeof e?e:tU(!!e),f):o},f.container=function(e){return arguments.length?(i="function"==typeof e?e:tU(e),f):i},f.subject=function(e){return arguments.length?(a="function"==typeof e?e:tU(e),f):a},f.touchable=function(e){return arguments.length?(l="function"==typeof e?e:tU(!!e),f):l},f.on=function(){var e=u.on.apply(u,arguments);return e===u?f:e},f.clickDistance=function(e){return arguments.length?(d=(e*=1)*e,f):Math.sqrt(d)},f})().on("start",e=>{let{domNode:t,nodeDragThreshold:n}=l.getState();0===n&&_(e),x.current=!1,f.current=b(e),p.current=t?.getBoundingClientRect()||null,g.current=nw(e.sourceEvent,p.current)}).on("drag",e=>{let t=b(e),{autoPanOnNodeDrag:n,nodeDragThreshold:r}=l.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(x.current=!0),!x.current){if(!y.current&&v.current&&n&&(y.current=!0,w()),!v.current){let n=t.xSnapped-(f?.current?.x??0),o=t.ySnapped-(f?.current?.y??0);Math.sqrt(n*n+o*o)>r&&_(e)}(f.current.x!==t.xSnapped||f.current.y!==t.ySnapped)&&d.current&&v.current&&(m.current=e.sourceEvent,g.current=nw(e.sourceEvent,p.current),c(t))}}).on("end",e=>{if(v.current&&!x.current&&(u(!1),y.current=!1,v.current=!1,cancelAnimationFrame(h.current),d.current)){let{updateNodePositions:t,nodeInternals:n,onNodeDragStop:r,onSelectionDragStop:i}=l.getState(),a=o?r:or(i);if(t(d.current,!1,!1),a){let[t,r]=r7({nodeId:o,dragItems:d.current,nodeInternals:n});a(e.sourceEvent,t,r)}}}).filter(t=>{let o=t.target;return!t.button&&(!n||!r9(o,`.${n}`,e))&&(!r||r9(o,r,e))});return s.call(t),()=>{s.on(".drag",null)}}}},[e,t,n,r,i,l,o,a,b]),s}function oi(){let e=t9();return(0,c.useCallback)(t=>{let{nodeInternals:n,nodeExtent:r,updateNodePositions:o,getNodes:i,snapToGrid:a,snapGrid:l,onError:s,nodesDraggable:u}=e.getState(),c=i().filter(e=>e.selected&&(e.draggable||u&&void 0===e.draggable)),d=a?l[0]:5,f=a?l[1]:5,h=t.isShiftPressed?4:1,p=t.x*d*h,g=t.y*f*h;o(c.map(e=>{if(e.positionAbsolute){let t={x:e.positionAbsolute.x+p,y:e.positionAbsolute.y+g};a&&(t.x=l[0]*Math.round(t.x/l[0]),t.y=l[1]*Math.round(t.y/l[1]));let{positionAbsolute:o,position:i}=r6(e,t,n,r,void 0,s);e.position=i,e.positionAbsolute=o}return e}),!0,!1)},[])}let oa={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var ol=e=>{let t=({id:t,type:n,data:r,xPos:o,yPos:i,xPosOrigin:a,yPosOrigin:l,selected:s,onClick:u,onMouseEnter:f,onMouseMove:h,onMouseLeave:p,onContextMenu:g,onDoubleClick:m,style:y,className:v,isDraggable:x,isSelectable:b,isConnectable:w,isFocusable:_,selectNodesOnDrag:S,sourcePosition:E,targetPosition:M,hidden:N,resizeObserver:k,dragHandle:C,zIndex:A,isParent:P,noDragClassName:$,noPanClassName:I,initialized:O,disableKeyboardA11y:z,ariaLabel:R,rfId:T,hasHandleBounds:D})=>{let B=t9(),L=(0,c.useRef)(null),H=(0,c.useRef)(null),j=(0,c.useRef)(E),V=(0,c.useRef)(M),X=(0,c.useRef)(n),Y=b||x||u||f||h||p,F=oi(),K=ot(t,B.getState,f),q=ot(t,B.getState,h),W=ot(t,B.getState,p),Z=ot(t,B.getState,g),U=ot(t,B.getState,m);(0,c.useEffect)(()=>()=>{H.current&&(k?.unobserve(H.current),H.current=null)},[]),(0,c.useEffect)(()=>{if(L.current&&!N){let e=L.current;O&&D&&H.current===e||(H.current&&k?.unobserve(H.current),k?.observe(e),H.current=e)}},[N,O,D]),(0,c.useEffect)(()=>{let e=X.current!==n,r=j.current!==E,o=V.current!==M;L.current&&(e||r||o)&&(e&&(X.current=n),r&&(j.current=E),o&&(V.current=M),B.getState().updateNodeDimensions([{id:t,nodeElement:L.current,forceUpdate:!0}]))},[t,n,E,M]);let G=oo({nodeRef:L,disabled:N||!x,noDragClassName:$,handleSelector:C,nodeId:t,isSelectable:b,selectNodesOnDrag:S});return N?null:c.createElement("div",{className:d(["react-flow__node",`react-flow__node-${n}`,{[I]:x},v,{selected:s,selectable:b,parent:P,dragging:G}]),ref:L,style:{zIndex:A,transform:`translate(${a}px,${l}px)`,pointerEvents:Y?"all":"none",visibility:O?"visible":"hidden",...y},"data-id":t,"data-testid":`rf__node-${t}`,onMouseEnter:K,onMouseMove:q,onMouseLeave:W,onContextMenu:Z,onClick:e=>{let{nodeDragThreshold:n}=B.getState();if(b&&(!S||!x||n>0)&&on({id:t,store:B,nodeRef:L}),u){let n=B.getState().nodeInternals.get(t);n&&u(e,{...n})}},onDoubleClick:U,onKeyDown:_?e=>{!nx(e)&&!z&&(nm.includes(e.key)&&b?on({id:t,store:B,unselect:"Escape"===e.key,nodeRef:L}):x&&s&&Object.prototype.hasOwnProperty.call(oa,e.key)&&(B.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~o}, y: ${~~i}`}),F({x:oa[e.key].x,y:oa[e.key].y,isShiftPressed:e.shiftKey})))}:void 0,tabIndex:_?0:void 0,role:_?"button":void 0,"aria-describedby":z?void 0:`${rE}-${T}`,"aria-label":R},c.createElement(nV,{value:t},c.createElement(e,{id:t,data:r,type:n,xPos:o,yPos:i,selected:s,isConnectable:w,sourcePosition:E,targetPosition:M,dragging:G,dragHandle:C,zIndex:A})))};return t.displayName="NodeWrapper",(0,c.memo)(t)};let os=e=>({...nQ(e.getNodes().filter(e=>e.selected),e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive});var ou=(0,c.memo)(function({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){let r=t9(),{width:o,height:i,x:a,y:l,transformString:s,userSelectionActive:u}=t8(os,w),f=oi(),h=(0,c.useRef)(null);if((0,c.useEffect)(()=>{n||h.current?.focus({preventScroll:!0})},[n]),oo({nodeRef:h}),u||!o||!i)return null;let p=e?t=>{e(t,r.getState().getNodes().filter(e=>e.selected))}:void 0;return c.createElement("div",{className:d(["react-flow__nodesselection","react-flow__container",t]),style:{transform:s}},c.createElement("div",{ref:h,className:"react-flow__nodesselection-rect",onContextMenu:p,tabIndex:n?void 0:-1,onKeyDown:n?void 0:e=>{Object.prototype.hasOwnProperty.call(oa,e.key)&&f({x:oa[e.key].x,y:oa[e.key].y,isShiftPressed:e.shiftKey})},style:{width:o,height:i,top:l,left:a}}))});let oc=e=>e.nodesSelectionActive,od=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:i,onPaneScroll:a,deleteKeyCode:l,onMove:s,onMoveStart:u,onMoveEnd:d,selectionKeyCode:f,selectionOnDrag:h,selectionMode:p,onSelectionStart:g,onSelectionEnd:m,multiSelectionKeyCode:y,panActivationKeyCode:v,zoomActivationKeyCode:x,elementsSelectable:b,zoomOnScroll:w,zoomOnPinch:_,panOnScroll:S,panOnScrollSpeed:E,panOnScrollMode:M,zoomOnDoubleClick:N,panOnDrag:k,defaultViewport:C,translateExtent:A,minZoom:P,maxZoom:$,preventScrolling:I,onSelectionContextMenu:O,noWheelClassName:z,noPanClassName:R,disableKeyboardA11y:T})=>{let D=t8(oc),B=rA(f),L=rA(v),H=L||k,j=L||S,V=B||h&&!0!==H;return rV({deleteKeyCode:l,multiSelectionKeyCode:y}),c.createElement(rU,{onMove:s,onMoveStart:u,onMoveEnd:d,onPaneContextMenu:i,elementsSelectable:b,zoomOnScroll:w,zoomOnPinch:_,panOnScroll:j,panOnScrollSpeed:E,panOnScrollMode:M,zoomOnDoubleClick:N,panOnDrag:!B&&H,defaultViewport:C,translateExtent:A,minZoom:P,maxZoom:$,zoomActivationKeyCode:x,preventScrolling:I,noWheelClassName:z,noPanClassName:R},c.createElement(r8,{onSelectionStart:g,onSelectionEnd:m,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:i,onPaneScroll:a,panOnDrag:H,isSelecting:!!V,selectionMode:p},e,D&&c.createElement(ou,{onSelectionContextMenu:O,noPanClassName:R,disableKeyboardA11y:T})))};od.displayName="FlowRenderer";var of=(0,c.memo)(od);function oh(e){let t={input:ol(e.input||rs),default:ol(e.default||ra),output:ol(e.output||rc),group:ol(e.group||rd)},n=Object.keys(e).filter(e=>!["input","default","output","group"].includes(e)).reduce((t,n)=>(t[n]=ol(e[n]||ra),t),{});return{...t,...n}}let op=({x:e,y:t,width:n,height:r,origin:o})=>!n||!r||o[0]<0||o[1]<0||o[0]>1||o[1]>1?{x:e,y:t}:{x:e-n*o[0],y:t-r*o[1]},og=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),om=e=>{let{nodesDraggable:t,nodesConnectable:n,nodesFocusable:r,elementsSelectable:o,updateNodeDimensions:i,onError:a}=t8(og,w),l=function(e){return t8((0,c.useCallback)(t=>e?nJ(t.nodeInternals,{x:0,y:0,width:t.width,height:t.height},t.transform,!0):t.getNodes(),[e]))}(e.onlyRenderVisibleElements),s=(0,c.useRef)(),d=(0,c.useMemo)(()=>{if("undefined"==typeof ResizeObserver)return null;let e=new ResizeObserver(e=>{i(e.map(e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})))});return s.current=e,e},[]);return(0,c.useEffect)(()=>()=>{s?.current?.disconnect()},[]),c.createElement("div",{className:"react-flow__nodes",style:rX},l.map(i=>{let l=i.type||"default";e.nodeTypes[l]||(a?.("003",t3.error003(l)),l="default");let s=e.nodeTypes[l]||e.nodeTypes.default,f=!!(i.draggable||t&&void 0===i.draggable),h=!!(i.selectable||o&&void 0===i.selectable),p=!!(i.connectable||n&&void 0===i.connectable),g=!!(i.focusable||r&&void 0===i.focusable),m=e.nodeExtent?no(i.positionAbsolute,e.nodeExtent):i.positionAbsolute,y=m?.x??0,v=m?.y??0,x=op({x:y,y:v,width:i.width??0,height:i.height??0,origin:e.nodeOrigin});return c.createElement(s,{key:i.id,id:i.id,className:i.className,style:i.style,type:l,data:i.data,sourcePosition:i.sourcePosition||u.Bottom,targetPosition:i.targetPosition||u.Top,hidden:i.hidden,xPos:y,yPos:v,xPosOrigin:x.x,yPosOrigin:x.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!i.selected,isDraggable:f,isSelectable:h,isConnectable:p,isFocusable:g,resizeObserver:d,dragHandle:i.dragHandle,zIndex:i[ng]?.z??0,isParent:!!i[ng]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!i.width&&!!i.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:i.ariaLabel,hasHandleBounds:!!i[ng]?.handleBounds})}))};om.displayName="NodeRenderer";var oy=(0,c.memo)(om);let ov=(e,t,n)=>n===u.Left?e-t:n===u.Right?e+t:e,ox=(e,t,n)=>n===u.Top?e-t:n===u.Bottom?e+t:e,ob="react-flow__edgeupdater",ow=({position:e,centerX:t,centerY:n,radius:r=10,onMouseDown:o,onMouseEnter:i,onMouseOut:a,type:l})=>c.createElement("circle",{onMouseDown:o,onMouseEnter:i,onMouseOut:a,className:d([ob,`${ob}-${l}`]),cx:ov(t,r,e),cy:ox(n,r,e),r:r,stroke:"transparent",fill:"transparent"}),o_=()=>!0;var oS=e=>{let t=({id:t,className:n,type:r,data:o,onClick:i,onEdgeDoubleClick:a,selected:l,animated:s,label:u,labelStyle:f,labelShowBg:h,labelBgStyle:p,labelBgPadding:g,labelBgBorderRadius:m,style:y,source:v,target:x,sourceX:b,sourceY:w,targetX:_,targetY:S,sourcePosition:E,targetPosition:M,elementsSelectable:N,hidden:k,sourceHandleId:C,targetHandleId:A,onContextMenu:P,onMouseEnter:$,onMouseMove:I,onMouseLeave:O,reconnectRadius:z,onReconnect:R,onReconnectStart:T,onReconnectEnd:D,markerEnd:B,markerStart:L,rfId:H,ariaLabel:j,isFocusable:V,isReconnectable:X,pathOptions:Y,interactionWidth:F,disableKeyboardA11y:K})=>{let q=(0,c.useRef)(null),[W,Z]=(0,c.useState)(!1),[U,G]=(0,c.useState)(!1),Q=t9(),J=(0,c.useMemo)(()=>`url('#${nK(L,H)}')`,[L,H]),ee=(0,c.useMemo)(()=>`url('#${nK(B,H)}')`,[B,H]);if(k)return null;let et=nE(t,Q.getState,a),en=nE(t,Q.getState,P),er=nE(t,Q.getState,$),eo=nE(t,Q.getState,I),ei=nE(t,Q.getState,O),ea=(e,n)=>{if(0!==e.button)return;let{edges:r,isValidConnection:o}=Q.getState(),i=n?x:v,a=(n?A:C)||null,l=n?"target":"source",s=r.find(e=>e.id===t);G(!0),T?.(e,s,l),n7({event:e,handleId:a,nodeId:i,onConnect:e=>R?.(s,e),isTarget:n,getState:Q.getState,setState:Q.setState,isValidConnection:o||o_,edgeUpdaterType:l,onReconnectEnd:e=>{G(!1),D?.(e,s,l)}})},el=()=>Z(!0),es=()=>Z(!1);return c.createElement("g",{className:d(["react-flow__edge",`react-flow__edge-${r}`,n,{selected:l,animated:s,inactive:!N&&!i,updating:W}]),onClick:e=>{let{edges:n,addSelectedEdges:r,unselectNodesAndEdges:o,multiSelectionActive:a}=Q.getState(),l=n.find(e=>e.id===t);l&&(N&&(Q.setState({nodesSelectionActive:!1}),l.selected&&a?(o({nodes:[],edges:[l]}),q.current?.blur()):r([t])),i&&i(e,l))},onDoubleClick:et,onContextMenu:en,onMouseEnter:er,onMouseMove:eo,onMouseLeave:ei,onKeyDown:V?e=>{if(!K&&nm.includes(e.key)&&N){let{unselectNodesAndEdges:n,addSelectedEdges:r,edges:o}=Q.getState();"Escape"===e.key?(q.current?.blur(),n({edges:[o.find(e=>e.id===t)]})):r([t])}}:void 0,tabIndex:V?0:void 0,role:V?"button":"img","data-testid":`rf__edge-${t}`,"aria-label":null===j?void 0:j||`Edge from ${v} to ${x}`,"aria-describedby":V?`${rM}-${H}`:void 0,ref:q},!U&&c.createElement(e,{id:t,source:v,target:x,selected:l,animated:s,label:u,labelStyle:f,labelShowBg:h,labelBgStyle:p,labelBgPadding:g,labelBgBorderRadius:m,data:o,style:y,sourceX:b,sourceY:w,targetX:_,targetY:S,sourcePosition:E,targetPosition:M,sourceHandleId:C,targetHandleId:A,markerStart:J,markerEnd:ee,pathOptions:Y,interactionWidth:F}),X&&c.createElement(c.Fragment,null,("source"===X||!0===X)&&c.createElement(ow,{position:E,centerX:b,centerY:w,radius:z,onMouseDown:e=>ea(e,!0),onMouseEnter:el,onMouseOut:es,type:"source"}),("target"===X||!0===X)&&c.createElement(ow,{position:M,centerX:_,centerY:S,radius:z,onMouseDown:e=>ea(e,!1),onMouseEnter:el,onMouseOut:es,type:"target"})))};return t.displayName="EdgeWrapper",(0,c.memo)(t)};function oE(e){let t={default:oS(e.default||nH),straight:oS(e.bezier||nT),step:oS(e.step||nR),smoothstep:oS(e.step||nz),simplebezier:oS(e.simplebezier||nA)},n=Object.keys(e).filter(e=>!["default","bezier"].includes(e)).reduce((t,n)=>(t[n]=oS(e[n]||nH),t),{});return{...t,...n}}function oM(e,t,n=null){let r=(n?.x||0)+t.x,o=(n?.y||0)+t.y,i=n?.width||t.width,a=n?.height||t.height;switch(e){case u.Top:return{x:r+i/2,y:o};case u.Right:return{x:r+i,y:o+a/2};case u.Bottom:return{x:r+i/2,y:o+a};case u.Left:return{x:r,y:o+a/2}}}function oN(e,t){return e?1!==e.length&&t?t&&e.find(e=>e.id===t)||null:e[0]:null}let ok=(e,t,n,r,o,i)=>{let a=oM(n,e,t),l=oM(i,r,o);return{sourceX:a.x,sourceY:a.y,targetX:l.x,targetY:l.y}};function oC(e){let t=e?.[ng]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}let oA=[{level:0,isMaxLevel:!0,edges:[]}],oP={[s.Arrow]:({color:e="none",strokeWidth:t=1})=>c.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[s.ArrowClosed]:({color:e="none",strokeWidth:t=1})=>c.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})},o$=({id:e,type:t,color:n,width:r=12.5,height:o=12.5,markerUnits:i="strokeWidth",strokeWidth:a,orient:l="auto-start-reverse"})=>{let s=function(e){let t=t9();return(0,c.useMemo)(()=>Object.prototype.hasOwnProperty.call(oP,e)?oP[e]:(t.getState().onError?.("009",t3.error009(e)),null),[e])}(t);return s?c.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${o}`,viewBox:"-10 -10 20 20",markerUnits:i,orient:l,refX:"0",refY:"0"},c.createElement(s,{color:n,strokeWidth:a})):null},oI=({defaultColor:e,rfId:t})=>n=>{let r=[];return n.edges.reduce((n,o)=>([o.markerStart,o.markerEnd].forEach(o=>{if(o&&"object"==typeof o){let i=nK(o,t);r.includes(i)||(n.push({id:i,color:o.color||e,...o}),r.push(i))}}),n),[]).sort((e,t)=>e.id.localeCompare(t.id))},oO=({defaultColor:e,rfId:t})=>{let n=t8((0,c.useCallback)(oI({defaultColor:e,rfId:t}),[e,t]),(e,t)=>!(e.length!==t.length||e.some((e,n)=>e.id!==t[n].id)));return c.createElement("defs",null,n.map(e=>c.createElement(o$,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient})))};oO.displayName="MarkerDefinitions";var oz=(0,c.memo)(oO);let oR=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),oT=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:r,edgeTypes:i,noPanClassName:a,onEdgeContextMenu:l,onEdgeMouseEnter:s,onEdgeMouseMove:f,onEdgeMouseLeave:h,onEdgeClick:p,onEdgeDoubleClick:g,onReconnect:m,onReconnectStart:y,onReconnectEnd:v,reconnectRadius:x,children:b,disableKeyboardA11y:_})=>{let{edgesFocusable:S,edgesUpdatable:E,elementsSelectable:M,width:N,height:k,connectionMode:C,nodeInternals:A,onError:P}=t8(oR,w),$=function(e,t,n){return function(e,t,n=!1){let r=-1,o=Object.entries(e.reduce((e,o)=>{let i=np(o.zIndex),a=i?o.zIndex:0;if(n){let e=t.get(o.target),n=t.get(o.source),r=o.selected||e?.selected||n?.selected,l=Math.max(n?.[ng]?.z||0,e?.[ng]?.z||0,1e3);a=(i?o.zIndex:0)+(r?l:0)}return e[a]?e[a].push(o):e[a]=[o],r=a>r?a:r,e},{})).map(([e,t])=>{let n=+e;return{edges:t,level:n,isMaxLevel:n===r}});return 0===o.length?oA:o}(t8((0,c.useCallback)(n=>e?n.edges.filter(e=>{let r=t.get(e.source),o=t.get(e.target);return r?.width&&r?.height&&o?.width&&o?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:r,targetWidth:o,targetHeight:i,width:a,height:l,transform:s}){let u={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+o),y2:Math.max(e.y+r,t.y+i)};u.x===u.x2&&(u.x2+=1),u.y===u.y2&&(u.y2+=1);let c=nu({x:(0-s[0])/s[2],y:(0-s[1])/s[2],width:a/s[2],height:l/s[2]});return Math.ceil(Math.max(0,Math.min(c.x2,u.x2)-Math.max(c.x,u.x))*Math.max(0,Math.min(c.y2,u.y2)-Math.max(c.y,u.y)))>0}({sourcePos:r.positionAbsolute||{x:0,y:0},targetPos:o.positionAbsolute||{x:0,y:0},sourceWidth:r.width,sourceHeight:r.height,targetWidth:o.width,targetHeight:o.height,width:n.width,height:n.height,transform:n.transform})}):n.edges,[e,t])),t,n)}(t,A,n);return N?c.createElement(c.Fragment,null,$.map(({level:t,edges:n,isMaxLevel:b})=>c.createElement("svg",{key:t,style:{zIndex:t},width:N,height:k,className:"react-flow__edges react-flow__container"},b&&c.createElement(oz,{defaultColor:e,rfId:r}),c.createElement("g",null,n.map(e=>{let[t,n,b]=oC(A.get(e.source)),[w,N,k]=oC(A.get(e.target));if(!b||!k)return null;let $=e.type||"default";i[$]||(P?.("011",t3.error011($)),$="default");let I=i[$]||i.default,O=C===o.Strict?N.target:(N.target??[]).concat(N.source??[]),z=oN(n.source,e.sourceHandle),R=oN(O,e.targetHandle),T=z?.position||u.Bottom,D=R?.position||u.Top,B=!!(e.focusable||S&&void 0===e.focusable),L=e.reconnectable||e.updatable;if(!z||!R)return P?.("008",t3.error008(z,e)),null;let{sourceX:H,sourceY:j,targetX:V,targetY:X}=ok(t,z,T,w,R,D);return c.createElement(I,{key:e.id,id:e.id,className:d([e.className,a]),type:$,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:H,sourceY:j,targetX:V,targetY:X,sourcePosition:T,targetPosition:D,elementsSelectable:M,onContextMenu:l,onMouseEnter:s,onMouseMove:f,onMouseLeave:h,onClick:p,onEdgeDoubleClick:g,onReconnect:m,onReconnectStart:y,onReconnectEnd:v,reconnectRadius:x,rfId:r,ariaLabel:e.ariaLabel,isFocusable:B,isReconnectable:void 0!==m&&(L||E&&void 0===L),pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:_})})))),b):null};oT.displayName="EdgeRenderer";var oD=(0,c.memo)(oT);let oB=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function oL({children:e}){let t=t8(oB);return c.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}let oH={[u.Left]:u.Right,[u.Right]:u.Left,[u.Top]:u.Bottom,[u.Bottom]:u.Top},oj=({nodeId:e,handleType:t,style:n,type:r=l.Bezier,CustomComponent:i,connectionStatus:a})=>{let{fromNode:s,handleId:u,toX:d,toY:f,connectionMode:h}=t8((0,c.useCallback)(t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode}),[e]),w),p=s?.[ng]?.handleBounds,g=p?.[t];if(h===o.Loose&&(g=g||p?.["source"===t?"target":"source"]),!s||!g)return null;let m=u?g.find(e=>e.id===u):g[0],y=m?m.x+m.width/2:(s.width??0)/2,v=m?m.y+m.height/2:s.height??0,x=(s.positionAbsolute?.x??0)+y,b=(s.positionAbsolute?.y??0)+v,_=m?.position,S=_?oH[_]:null;if(!_||!S)return null;if(i)return c.createElement(i,{connectionLineType:r,connectionLineStyle:n,fromNode:s,fromHandle:m,fromX:x,fromY:b,toX:d,toY:f,fromPosition:_,toPosition:S,connectionStatus:a});let E="",M={sourceX:x,sourceY:b,sourcePosition:_,targetX:d,targetY:f,targetPosition:S};return r===l.Bezier?[E]=nL(M):r===l.Step?[E]=nO({...M,borderRadius:0}):r===l.SmoothStep?[E]=nO(M):r===l.SimpleBezier?[E]=nC(M):E=`M${x},${b} ${d},${f}`,c.createElement("path",{d:E,fill:"none",className:"react-flow__connection-path",style:n})};oj.displayName="ConnectionLine";let oV=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function oX({containerStyle:e,style:t,type:n,component:r}){let{nodeId:o,handleType:i,nodesConnectable:a,width:l,height:s,connectionStatus:u}=t8(oV,w);return o&&i&&l&&a?c.createElement("svg",{style:e,width:l,height:s,className:"react-flow__edges react-flow__connectionline react-flow__container"},c.createElement("g",{className:d(["react-flow__connection",u])},c.createElement(oj,{nodeId:o,handleType:i,style:t,type:n,CustomComponent:r,connectionStatus:u}))):null}function oY(e,t){return(0,c.useRef)(null),t9(),(0,c.useMemo)(()=>t(e),[e])}let oF=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:r,onMoveEnd:o,onInit:i,onNodeClick:a,onEdgeClick:l,onNodeDoubleClick:s,onEdgeDoubleClick:u,onNodeMouseEnter:d,onNodeMouseMove:f,onNodeMouseLeave:h,onNodeContextMenu:p,onSelectionContextMenu:g,onSelectionStart:m,onSelectionEnd:y,connectionLineType:v,connectionLineStyle:x,connectionLineComponent:b,connectionLineContainerStyle:w,selectionKeyCode:_,selectionOnDrag:S,selectionMode:E,multiSelectionKeyCode:M,panActivationKeyCode:N,zoomActivationKeyCode:k,deleteKeyCode:C,onlyRenderVisibleElements:A,elementsSelectable:P,selectNodesOnDrag:$,defaultViewport:I,translateExtent:O,minZoom:z,maxZoom:R,preventScrolling:T,defaultMarkerColor:D,zoomOnScroll:B,zoomOnPinch:L,panOnScroll:H,panOnScrollSpeed:j,panOnScrollMode:V,zoomOnDoubleClick:X,panOnDrag:Y,onPaneClick:F,onPaneMouseEnter:K,onPaneMouseMove:q,onPaneMouseLeave:W,onPaneScroll:Z,onPaneContextMenu:U,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:er,reconnectRadius:eo,noDragClassName:ei,noWheelClassName:ea,noPanClassName:el,elevateEdgesOnSelect:es,disableKeyboardA11y:eu,nodeOrigin:ec,nodeExtent:ed,rfId:ef})=>{let eh=oY(e,oh),ep=oY(t,oE);return!function(e){let t=rH(),n=(0,c.useRef)(!1);(0,c.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(i),c.createElement(of,{onPaneClick:F,onPaneMouseEnter:K,onPaneMouseMove:q,onPaneMouseLeave:W,onPaneContextMenu:U,onPaneScroll:Z,deleteKeyCode:C,selectionKeyCode:_,selectionOnDrag:S,selectionMode:E,onSelectionStart:m,onSelectionEnd:y,multiSelectionKeyCode:M,panActivationKeyCode:N,zoomActivationKeyCode:k,elementsSelectable:P,onMove:n,onMoveStart:r,onMoveEnd:o,zoomOnScroll:B,zoomOnPinch:L,zoomOnDoubleClick:X,panOnScroll:H,panOnScrollSpeed:j,panOnScrollMode:V,panOnDrag:Y,defaultViewport:I,translateExtent:O,minZoom:z,maxZoom:R,onSelectionContextMenu:g,preventScrolling:T,noDragClassName:ei,noWheelClassName:ea,noPanClassName:el,disableKeyboardA11y:eu},c.createElement(oL,null,c.createElement(oD,{edgeTypes:ep,onEdgeClick:l,onEdgeDoubleClick:u,onlyRenderVisibleElements:A,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:er,reconnectRadius:eo,defaultMarkerColor:D,noPanClassName:el,elevateEdgesOnSelect:!!es,disableKeyboardA11y:eu,rfId:ef},c.createElement(oX,{style:x,type:v,component:b,containerStyle:w})),c.createElement("div",{className:"react-flow__edgelabel-renderer"}),c.createElement(oy,{nodeTypes:eh,onNodeClick:a,onNodeDoubleClick:s,onNodeMouseEnter:d,onNodeMouseMove:f,onNodeMouseLeave:h,onNodeContextMenu:p,selectNodesOnDrag:$,onlyRenderVisibleElements:A,noPanClassName:el,noDragClassName:ei,disableKeyboardA11y:eu,nodeOrigin:ec,nodeExtent:ed,rfId:ef})))};oF.displayName="GraphView";var oK=(0,c.memo)(oF);let oq=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],oW={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:oq,nodeExtent:oq,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:o.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:ny,isValidConnection:void 0},oZ=()=>b((e,t)=>({...oW,setNodes:n=>{let{nodeInternals:r,nodeOrigin:o,elevateNodesOnSelect:i}=t();e({nodeInternals:rO(n,r,o,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{let{defaultEdgeOptions:r={}}=t();e({edges:n.map(e=>({...r,...e}))})},setDefaultNodesAndEdges:(n,r)=>{let o=void 0!==n,i=void 0!==r;e({nodeInternals:o?rO(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map,edges:i?r:[],hasDefaultNodes:o,hasDefaultEdges:i})},updateNodeDimensions:n=>{let{onNodesChange:r,nodeInternals:o,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:l,domNode:s,nodeOrigin:u}=t(),c=s?.querySelector(".react-flow__viewport");if(!c)return;let d=window.getComputedStyle(c),{m22:f}=new window.DOMMatrixReadOnly(d.transform),h=n.reduce((e,t)=>{let n=o.get(t.id);if(n?.hidden)o.set(n.id,{...n,[ng]:{...n[ng],handleBounds:void 0}});else if(n){let r=nn(t.nodeElement);r.width&&r.height&&(n.width!==r.width||n.height!==r.height||t.forceUpdate)&&(o.set(n.id,{...n,[ng]:{...n[ng],handleBounds:{source:oe(".source",t.nodeElement,f,u),target:oe(".target",t.nodeElement,f,u)}},...r}),e.push({id:n.id,type:"dimensions",dimensions:r}))}return e},[]);rI(o,u);let p=a||i&&!a&&rz(t,{initial:!0,...l});e({nodeInternals:new Map(o),fitViewOnInitDone:p}),h?.length>0&&r?.(h)},updateNodePositions:(e,n=!0,r=!1)=>{let{triggerNodeChanges:o}=t();o(e.map(e=>{let t={id:e.id,type:"position",dragging:r};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t}))},triggerNodeChanges:n=>{let{onNodesChange:r,nodeInternals:o,hasDefaultNodes:i,nodeOrigin:a,getNodes:l,elevateNodesOnSelect:s}=t();n?.length&&(i&&e({nodeInternals:rO(r1(n,l()),o,a,s)}),r?.(n))},addSelectedNodes:n=>{let r,{multiSelectionActive:o,edges:i,getNodes:a}=t(),l=null;o?r=n.map(e=>r2(e,!0)):(r=r5(a(),n),l=r5(i,[])),rR({changedNodes:r,changedEdges:l,get:t,set:e})},addSelectedEdges:n=>{let r,{multiSelectionActive:o,edges:i,getNodes:a}=t(),l=null;o?r=n.map(e=>r2(e,!0)):(r=r5(i,n),l=r5(a(),[])),rR({changedNodes:l,changedEdges:r,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:r}={})=>{let{edges:o,getNodes:i}=t();rR({changedNodes:(n||i()).map(e=>(e.selected=!1,r2(e.id,!1))),changedEdges:(r||o).map(e=>r2(e.id,!1)),get:t,set:e})},setMinZoom:n=>{let{d3Zoom:r,maxZoom:o}=t();r?.scaleExtent([n,o]),e({minZoom:n})},setMaxZoom:n=>{let{d3Zoom:r,minZoom:o}=t();r?.scaleExtent([o,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{let{edges:n,getNodes:r}=t();rR({changedNodes:r().filter(e=>e.selected).map(e=>r2(e.id,!1)),changedEdges:n.filter(e=>e.selected).map(e=>r2(e.id,!1)),get:t,set:e})},setNodeExtent:n=>{let{nodeInternals:r}=t();r.forEach(e=>{e.positionAbsolute=no(e.position,n)}),e({nodeExtent:n,nodeInternals:new Map(r)})},panBy:e=>{let{transform:n,width:r,height:o,d3Zoom:i,d3Selection:a,translateExtent:l}=t();if(!i||!a||!e.x&&!e.y)return!1;let s=tj.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),u=i?.constrain()(s,[[0,0],[r,o]],l);return i.transform(a,u),n[0]!==u.x||n[1]!==u.y||n[2]!==u.k},cancelConnection:()=>e({connectionNodeId:oW.connectionNodeId,connectionHandleId:oW.connectionHandleId,connectionHandleType:oW.connectionHandleType,connectionStatus:oW.connectionStatus,connectionStartHandle:oW.connectionStartHandle,connectionEndHandle:oW.connectionEndHandle}),reset:()=>e({...oW})}),Object.is),oU=({children:e})=>{let t=(0,c.useRef)(null);return t.current||(t.current=oZ()),c.createElement(t5,{value:t.current},e)};oU.displayName="ReactFlowProvider";let oG=({children:e})=>(0,c.useContext)(t2)?c.createElement(c.Fragment,null,e):c.createElement(oU,null,e);oG.displayName="ReactFlowWrapper";let oQ={input:rs,default:ra,output:rc,group:rd},oJ={default:nH,straight:nT,step:nR,smoothstep:nz,simplebezier:nA},o0=[0,0],o1=[15,15],o2={x:0,y:0,zoom:1},o5={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};(0,c.forwardRef)(({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,className:s,nodeTypes:u=oQ,edgeTypes:f=oJ,onNodeClick:h,onEdgeClick:p,onInit:g,onMove:m,onMoveStart:y,onMoveEnd:v,onConnect:x,onConnectStart:b,onConnectEnd:w,onClickConnectStart:_,onClickConnectEnd:S,onNodeMouseEnter:E,onNodeMouseMove:M,onNodeMouseLeave:N,onNodeContextMenu:k,onNodeDoubleClick:C,onNodeDragStart:A,onNodeDrag:P,onNodeDragStop:$,onNodesDelete:I,onEdgesDelete:O,onSelectionChange:z,onSelectionDragStart:R,onSelectionDrag:T,onSelectionDragStop:D,onSelectionContextMenu:B,onSelectionStart:L,onSelectionEnd:H,connectionMode:j=o.Strict,connectionLineType:V=l.Bezier,connectionLineStyle:X,connectionLineComponent:Y,connectionLineContainerStyle:F,deleteKeyCode:K="Backspace",selectionKeyCode:q="Shift",selectionOnDrag:W=!1,selectionMode:Z=a.Full,panActivationKeyCode:U="Space",multiSelectionKeyCode:G=n_()?"Meta":"Control",zoomActivationKeyCode:Q=n_()?"Meta":"Control",snapToGrid:J=!1,snapGrid:ee=o1,onlyRenderVisibleElements:et=!1,selectNodesOnDrag:en=!0,nodesDraggable:er,nodesConnectable:eo,nodesFocusable:ei,nodeOrigin:ea=o0,edgesFocusable:el,edgesUpdatable:es,elementsSelectable:eu,defaultViewport:ec=o2,minZoom:ed=.5,maxZoom:ef=2,translateExtent:eh=oq,preventScrolling:ep=!0,nodeExtent:eg,defaultMarkerColor:em="#b1b1b7",zoomOnScroll:ey=!0,zoomOnPinch:ev=!0,panOnScroll:ex=!1,panOnScrollSpeed:eb=.5,panOnScrollMode:ew=i.Free,zoomOnDoubleClick:e_=!0,panOnDrag:eS=!0,onPaneClick:eE,onPaneMouseEnter:eM,onPaneMouseMove:eN,onPaneMouseLeave:ek,onPaneScroll:eC,onPaneContextMenu:eA,children:eP,onEdgeContextMenu:e$,onEdgeDoubleClick:eI,onEdgeMouseEnter:eO,onEdgeMouseMove:ez,onEdgeMouseLeave:eR,onEdgeUpdate:eT,onEdgeUpdateStart:eD,onEdgeUpdateEnd:eB,onReconnect:eL,onReconnectStart:eH,onReconnectEnd:ej,reconnectRadius:eV=10,edgeUpdaterRadius:eX=10,onNodesChange:eY,onEdgesChange:eF,noDragClassName:eK="nodrag",noWheelClassName:eq="nowheel",noPanClassName:eW="nopan",fitView:eZ=!1,fitViewOptions:eU,connectOnClick:eG=!0,attributionPosition:eQ,proOptions:eJ,defaultEdgeOptions:e0,elevateNodesOnSelect:e1=!0,elevateEdgesOnSelect:e2=!1,disableKeyboardA11y:e5=!1,autoPanOnConnect:e3=!0,autoPanOnNodeDrag:e4=!0,connectionRadius:e8=20,isValidConnection:e9,onError:e6,style:e7,id:te,nodeDragThreshold:tt,...tn},tr)=>{let to=te||"1";return c.createElement("div",{...tn,style:{...e7,...o5},ref:tr,className:d(["react-flow",s]),"data-testid":"rf__wrapper",id:te},c.createElement(oG,null,c.createElement(oK,{onInit:g,onMove:m,onMoveStart:y,onMoveEnd:v,onNodeClick:h,onEdgeClick:p,onNodeMouseEnter:E,onNodeMouseMove:M,onNodeMouseLeave:N,onNodeContextMenu:k,onNodeDoubleClick:C,nodeTypes:u,edgeTypes:f,connectionLineType:V,connectionLineStyle:X,connectionLineComponent:Y,connectionLineContainerStyle:F,selectionKeyCode:q,selectionOnDrag:W,selectionMode:Z,deleteKeyCode:K,multiSelectionKeyCode:G,panActivationKeyCode:U,zoomActivationKeyCode:Q,onlyRenderVisibleElements:et,selectNodesOnDrag:en,defaultViewport:ec,translateExtent:eh,minZoom:ed,maxZoom:ef,preventScrolling:ep,zoomOnScroll:ey,zoomOnPinch:ev,zoomOnDoubleClick:e_,panOnScroll:ex,panOnScrollSpeed:eb,panOnScrollMode:ew,panOnDrag:eS,onPaneClick:eE,onPaneMouseEnter:eM,onPaneMouseMove:eN,onPaneMouseLeave:ek,onPaneScroll:eC,onPaneContextMenu:eA,onSelectionContextMenu:B,onSelectionStart:L,onSelectionEnd:H,onEdgeContextMenu:e$,onEdgeDoubleClick:eI,onEdgeMouseEnter:eO,onEdgeMouseMove:ez,onEdgeMouseLeave:eR,onReconnect:eL??eT,onReconnectStart:eH??eD,onReconnectEnd:ej??eB,reconnectRadius:eV??eX,defaultMarkerColor:em,noDragClassName:eK,noWheelClassName:eq,noPanClassName:eW,elevateEdgesOnSelect:e2,rfId:to,disableKeyboardA11y:e5,nodeOrigin:ea,nodeExtent:eg}),c.createElement(rw,{nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:x,onConnectStart:b,onConnectEnd:w,onClickConnectStart:_,onClickConnectEnd:S,nodesDraggable:er,nodesConnectable:eo,nodesFocusable:ei,edgesFocusable:el,edgesUpdatable:es,elementsSelectable:eu,elevateNodesOnSelect:e1,minZoom:ed,maxZoom:ef,nodeExtent:eg,onNodesChange:eY,onEdgesChange:eF,snapToGrid:J,snapGrid:ee,connectionMode:j,translateExtent:eh,connectOnClick:eG,defaultEdgeOptions:e0,fitView:eZ,fitViewOptions:eU,onNodesDelete:I,onEdgesDelete:O,onNodeDragStart:A,onNodeDrag:P,onNodeDragStop:$,onSelectionDrag:T,onSelectionDragStart:R,onSelectionDragStop:D,noPanClassName:eW,nodeOrigin:ea,rfId:to,autoPanOnConnect:e3,autoPanOnNodeDrag:e4,onError:e6,connectionRadius:e8,isValidConnection:e9,nodeDragThreshold:tt}),c.createElement(ry,{onSelectionChange:z}),eP,c.createElement(ne,{proOptions:eJ,position:eQ}),c.createElement(rC,{rfId:to,disableKeyboardA11y:e5})))}).displayName="ReactFlow";function o3(e){return t=>{let[n,r]=(0,c.useState)(t),o=(0,c.useCallback)(t=>r(n=>e(t,n)),[]);return[n,r,o]}}o3(r1),o3(function(e,t){return r0(e,t)})},10907:(e,t,n)=>{var r=n(43210),o=n(57379),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=l(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var h=a(e,(d=u(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,o]))[0],d[1]);return s(function(){f.hasValue=!0,f.value=h},[h]),c(h),h}},19587:(e,t)=>{function n(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return n}})},30036:(e,t,n)=>{n.d(t,{default:()=>o.a});var r=n(49587),o=n.n(r)},39733:(e,t,n)=>{e.exports=n(10907)},49587:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let r=n(14985)._(n(64963));function o(e,t){var n;let o={};"function"==typeof e&&(o.loader=e);let i={...o,...t};return(0,r.default)({...i,modules:null==(n=i.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53332:(e,t,n)=>{var r=n(43210),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return l(function(){o.value=n,o.getSnapshot=t,u(o)&&c({inst:o})},[e,n,t]),a(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},56780:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let r=n(81208);function o(e){let{reason:t,children:n}=e;throw Object.defineProperty(new r.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},57379:(e,t,n)=>{e.exports=n(53332)},64777:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return l}});let r=n(60687),o=n(51215),i=n(29294),a=n(19587);function l(e){let{moduleIds:t}=e,n=i.workAsyncStorage.getStore();if(void 0===n)return null;let l=[];if(n.reactLoadableManifest&&t){let e=n.reactLoadableManifest;for(let n of t){if(!e[n])continue;let t=e[n].files;l.push(...t)}}return 0===l.length?null:(0,r.jsx)(r.Fragment,{children:l.map(e=>{let t=n.assetPrefix+"/_next/"+(0,a.encodeURIPath)(e);return e.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,o.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let r=n(60687),o=n(43210),i=n(56780),a=n(64777);function l(e){return{default:e&&"default"in e?e.default:e}}let s={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},u=function(e){let t={...s,...e},n=(0,o.lazy)(()=>t.loader().then(l)),u=t.loading;function c(e){let l=u?(0,r.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,s=!t.ssr||!!t.loading,c=s?o.Suspense:o.Fragment,d=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.PreloadChunks,{moduleIds:t.modules}),(0,r.jsx)(n,{...e})]}):(0,r.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(c,{...s?{fallback:l}:{},children:d})}return c.displayName="LoadableComponent",c}}};
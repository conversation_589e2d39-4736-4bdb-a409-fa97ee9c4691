(()=>{var e={};e.id=6011,e.ids=[6011],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5184:(e,s,r)=>{Promise.resolve().then(r.bind(r,27847))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27847:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\binary-mlm\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\binary-mlm\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39091:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h});var t=r(60687),l=r(43210),a=r(82136),n=r(16189),i=r(68367),d=r(23877);let o=e=>{switch(e.toLowerCase()){case"diamond":return"text-purple-600 bg-purple-100";case"platinum":return"text-blue-600 bg-blue-100";case"gold":return"text-yellow-600 bg-yellow-100";case"silver":return"text-gray-600 bg-gray-200";case"bronze":return"text-orange-600 bg-orange-100";default:return"text-green-600 bg-green-100"}},c=({node:e,isRoot:s=!1,depth:r,maxDepth:a,initialExpandedLevels:n,onUserSelect:i})=>{let[m,x]=(0,l.useState)(r<n),h=null!==e.left||null!==e.right,u=h&&r<a,p=o(e.user.rank.name);return(0,t.jsxs)("div",{className:`mb-2 ${s?"":"ml-6"}`,children:[(0,t.jsxs)("div",{className:`flex items-center p-2 rounded-lg border ${s?"bg-gray-100 border-gray-300":"bg-white border-gray-200"} hover:bg-gray-50 transition-colors`,children:[u&&(0,t.jsx)("button",{onClick:()=>x(!m),className:"mr-2 text-gray-500 hover:text-gray-700",children:m?(0,t.jsx)(d.Vr3,{}):(0,t.jsx)(d.X6T,{})}),(0,t.jsx)("div",{className:"flex-shrink-0 mr-3",children:(0,t.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${p}`,children:(0,t.jsx)(d.x$1,{className:"text-lg"})})}),(0,t.jsxs)("div",{className:"flex-grow",onClick:()=>i&&i(e.user),children:[(0,t.jsx)("div",{className:"font-medium",children:e.user.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.user.email}),(0,t.jsxs)("div",{className:"flex items-center mt-1",children:[(0,t.jsx)("span",{className:`text-xs px-2 py-0.5 rounded-full ${p}`,children:e.user.rank.name}),e.position&&(0,t.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-700",children:"left"===e.position?"Left Leg":"Right Leg"})]})]}),h&&(0,t.jsx)("div",{className:"flex-shrink-0 ml-2 text-gray-500",children:(0,t.jsx)(d.YXz,{})})]}),m&&h&&(0,t.jsxs)("div",{className:"mt-2 grid grid-cols-2 gap-4",children:[(0,t.jsx)("div",{className:"border-t-2 border-l-2 border-gray-200 pt-2 pl-2",children:e.left?(0,t.jsx)(c,{node:e.left,depth:r+1,maxDepth:a,initialExpandedLevels:n,onUserSelect:i}):(0,t.jsxs)("div",{className:"p-4 border border-dashed border-gray-300 rounded-lg text-center text-gray-500",children:[(0,t.jsx)(d.QVr,{className:"mx-auto mb-2"}),"Empty Left Position"]})}),(0,t.jsx)("div",{className:"border-t-2 border-r-2 border-gray-200 pt-2 pr-2",children:e.right?(0,t.jsx)(c,{node:e.right,depth:r+1,maxDepth:a,initialExpandedLevels:n,onUserSelect:i}):(0,t.jsxs)("div",{className:"p-4 border border-dashed border-gray-300 rounded-lg text-center text-gray-500",children:[(0,t.jsx)(d.Z0P,{className:"mx-auto mb-2"}),"Empty Right Position"]})})]})]})},m=({data:e,maxDepth:s=6,initialExpandedLevels:r=2,onUserSelect:a})=>{let[n,i]=(0,l.useState)(!1);return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,t.jsxs)("div",{className:"p-4 border-b",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Binary MLM Structure"}),(0,t.jsx)("button",{onClick:()=>i(!n),className:"px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700",children:n?"Collapse All":"Expand All"})]}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:["Showing binary placement structure up to ",s," levels deep"]})]}),(0,t.jsx)("div",{className:"p-4 overflow-x-auto",children:(0,t.jsx)(c,{node:e,isRoot:!0,depth:0,maxDepth:s,initialExpandedLevels:n?s:r,onUserSelect:a})})]})},x=({performance:e,commissions:s,onExport:r})=>{let l=["January","February","March","April","May","June","July","August","September","October","November","December"][e.month-1];return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,t.jsx)("div",{className:"p-4 border-b",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold",children:["Earnings Report: ",l," ",e.year]}),r&&(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("button",{className:"px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 flex items-center",children:[(0,t.jsx)(d.WCW,{className:"mr-1"})," Export"]}),(0,t.jsx)("div",{className:"absolute right-0 mt-2 w-32 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden group-hover:block z-10",children:(0,t.jsxs)("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[(0,t.jsx)("button",{onClick:()=>r("csv"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as CSV"}),(0,t.jsx)("button",{onClick:()=>r("json"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as JSON"})]})})]})]})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-2 rounded-full mr-3",children:(0,t.jsx)(d.v$b,{className:"text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Total Earnings"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",e.totalEarnings.toFixed(2)]})]})]})}),(0,t.jsx)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-green-100 p-2 rounded-full mr-3",children:(0,t.jsx)(d.YXz,{className:"text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Group Volume"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[e.totalGroupPV.toFixed(0)," PV"]})]})]})}),(0,t.jsx)("div",{className:"bg-purple-50 p-4 rounded-lg border border-purple-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-purple-100 p-2 rounded-full mr-3",children:(0,t.jsx)(d.MxO,{className:"text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"Personal Volume"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[e.personalPV.toFixed(0)," PV"]})]})]})}),(0,t.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg border border-yellow-100",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-yellow-100 p-2 rounded-full mr-3",children:(0,t.jsx)(d.Wp,{className:"text-yellow-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-yellow-600 font-medium",children:"Referral Bonus"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",e.directReferralBonus.toFixed(2)]})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gray-50 p-3 border-b",children:(0,t.jsx)("h4",{className:"font-medium",children:"PV Breakdown"})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Personal PV"}),(0,t.jsx)("span",{className:"font-medium",children:e.personalPV.toFixed(0)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${Math.min(100,e.personalPV/(e.totalGroupPV||1)*100)}%`}})})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Left Leg PV"}),(0,t.jsx)("span",{className:"font-medium",children:e.leftLegPV.toFixed(0)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${Math.min(100,e.leftLegPV/(e.totalGroupPV||1)*100)}%`}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Right Leg PV"}),(0,t.jsx)("span",{className:"font-medium",children:e.rightLegPV.toFixed(0)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-purple-600 h-2 rounded-full",style:{width:`${Math.min(100,e.rightLegPV/(e.totalGroupPV||1)*100)}%`}})})]})]})]}),(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gray-50 p-3 border-b",children:(0,t.jsx)("h4",{className:"font-medium",children:"Commission Breakdown"})}),(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("table",{className:"w-full",children:(0,t.jsxs)("tbody",{children:[(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("td",{className:"py-2 text-sm text-gray-600",children:"Direct Referral Bonus"}),(0,t.jsxs)("td",{className:"py-2 text-right font-medium",children:["₱",e.directReferralBonus.toFixed(2)]})]}),(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("td",{className:"py-2 text-sm text-gray-600",children:"Level Commissions"}),(0,t.jsxs)("td",{className:"py-2 text-right font-medium",children:["₱",e.levelCommissions.toFixed(2)]})]}),(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("td",{className:"py-2 text-sm text-gray-600",children:"Group Volume Bonus"}),(0,t.jsxs)("td",{className:"py-2 text-right font-medium",children:["₱",e.groupVolumeBonus.toFixed(2)]})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"py-2 font-medium",children:"Total Earnings"}),(0,t.jsxs)("td",{className:"py-2 text-right font-bold",children:["₱",e.totalEarnings.toFixed(2)]})]})]})})})]})]}),s&&s.breakdown.levelCommissions.byLevel&&(0,t.jsxs)("div",{className:"mt-6 border rounded-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gray-50 p-3 border-b",children:(0,t.jsx)("h4",{className:"font-medium",children:"Level Commission Details"})}),(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-6 gap-4",children:Object.entries(s.breakdown.levelCommissions.byLevel).map(([e,s])=>(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Level ",e]}),(0,t.jsxs)("div",{className:"font-medium",children:["₱",parseFloat(s.toString()).toFixed(2)]})]},e))})})]})]})]})};function h(){let{data:e,status:s}=(0,a.useSession)();(0,n.useRouter)();let[r,o]=(0,l.useState)(!0),[c,h]=(0,l.useState)(null),[u,p]=(0,l.useState)(null),[g,b]=(0,l.useState)(new Date().getFullYear()),[j,v]=(0,l.useState)(new Date().getMonth()+1),[f,y]=(0,l.useState)({type:"",text:""}),N=async()=>{try{let e=await fetch(`/api/binary-mlm?action=performance&year=${g}&month=${j}`);if(!e.ok)throw Error(`Failed to fetch performance data: ${e.statusText}`);let s=await e.json();s.performance&&s.performance.length>0?p(s.performance[0]):(p(null),y({type:"info",text:`No performance data available for ${L(j)} ${g}.`}))}catch(e){console.error("Error fetching performance data:",e),y({type:"error",text:"Failed to load performance data. Please try again."})}},w=async()=>{o(!0),y({type:"",text:""});try{let e=await fetch("/api/binary-mlm",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"simulate",year:g,month:j})});if(!e.ok)throw Error(`Failed to simulate earnings: ${e.statusText}`);await e.json(),y({type:"success",text:`Successfully simulated earnings for ${L(j)} ${g}.`}),N()}catch(e){console.error("Error simulating earnings:",e),y({type:"error",text:"Failed to simulate earnings. Please try again."})}finally{o(!1)}},P=async e=>{try{if("csv"===e)return void window.open(`/api/binary-mlm/export?type=personal&year=${g}&month=${j}&format=${e}`,"_blank");let s=await fetch(`/api/binary-mlm/export?type=personal&year=${g}&month=${j}&format=${e}`);if(!s.ok)throw Error(`Failed to export report: ${s.statusText}`);let r=await s.json(),t=new Blob([JSON.stringify(r,null,2)],{type:"application/json"}),l=URL.createObjectURL(t),a=document.createElement("a");a.href=l,a.download=`earnings_report_${g}_${j}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(l)}catch(e){console.error("Error exporting report:",e),y({type:"error",text:"Failed to export report. Please try again."})}},L=e=>["January","February","March","April","May","June","July","August","September","October","November","December"][e-1];return"loading"===s?(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,t.jsx)(d.hW,{className:"animate-spin text-green-500 mr-2"}),(0,t.jsx)("span",{children:"Loading..."})]})}):(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Binary MLM Dashboard"}),f.text&&(0,t.jsx)("div",{className:`mb-6 p-4 rounded-md ${"error"===f.type?"bg-red-100 text-red-700":"success"===f.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:f.text}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,t.jsx)(d.YYR,{className:"text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Monthly Earnings"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",u?u.totalEarnings.toFixed(2):"0.00"]})]})]})}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-green-100 p-3 rounded-full mr-4",children:(0,t.jsx)(d.YXz,{className:"text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Group Volume"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[u?u.totalGroupPV.toFixed(0):"0"," PV"]})]})]})}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-purple-100 p-3 rounded-full mr-4",children:(0,t.jsx)(d.NPy,{className:"text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Referral Bonus"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:["₱",u?u.directReferralBonus.toFixed(2):"0.00"]})]})]})}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full mr-4",children:(0,t.jsx)(d.bfZ,{className:"text-yellow-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Period"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[L(j)," ",g]})]})]})})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex flex-wrap items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 mb-4 md:mb-0",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"year",className:"block text-sm text-gray-600 mb-1",children:"Year"}),(0,t.jsx)("select",{id:"year",value:g,onChange:e=>b(parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:Array.from({length:5},(e,s)=>new Date().getFullYear()-2+s).map(e=>(0,t.jsx)("option",{value:e,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"month",className:"block text-sm text-gray-600 mb-1",children:"Month"}),(0,t.jsx)("select",{id:"month",value:j,onChange:e=>v(parseInt(e.target.value)),className:"border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:Array.from({length:12},(e,s)=>s+1).map(e=>(0,t.jsx)("option",{value:e,children:L(e)},e))})]})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{onClick:w,disabled:r,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.hW,{className:"animate-spin inline mr-2"}),"Simulating..."]}):"Simulate Earnings"})})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[u?(0,t.jsx)(x,{performance:u,onExport:P}):(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6 text-center",children:(0,t.jsxs)("p",{className:"text-gray-500",children:["No performance data available for ",L(j)," ",g,'. Click "Simulate Earnings" to generate data.']})}),r?(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 flex items-center justify-center h-64",children:[(0,t.jsx)(d.hW,{className:"animate-spin text-green-500 mr-2"}),(0,t.jsx)("span",{children:"Loading binary structure..."})]}):c?(0,t.jsx)(m,{data:c,maxDepth:6,initialExpandedLevels:2,onUserSelect:e=>console.log("Selected user:",e)}):(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6 text-center",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No binary structure data available."})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},90642:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(65239),l=r(48088),a=r(88170),n=r.n(a),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let o={children:["",{children:["binary-mlm",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27847)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\binary-mlm\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\binary-mlm\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/binary-mlm/page",pathname:"/binary-mlm",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},92032:(e,s,r)=>{Promise.resolve().then(r.bind(r,39091))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>r(90642));module.exports=t})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7313],{3502:(e,s,t)=>{Promise.resolve().then(t.bind(t,18965))},18965:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(95155),i=t(12115),r=t(12108),l=t(35695),n=t(70357),c=t(29911),d=t(66766),m=t(6874),x=t.n(m);function o(e){let{purchase:s,onClose:t}=e,[r,l]=(0,i.useState)("details"),n=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),m=e=>new Date(e).toLocaleString();return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Purchase Details"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 mb-6",children:[(0,a.jsx)("div",{className:"md:w-1/3",children:(0,a.jsx)("div",{className:"relative w-full h-48 rounded-md overflow-hidden border border-gray-200",children:s.product.image?(0,a.jsx)(d.default,{src:s.product.image,alt:s.product.name,fill:!0,className:"object-contain"}):(0,a.jsx)("div",{className:"w-full h-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)(c.AsH,{className:"text-gray-400 text-4xl"})})})}),(0,a.jsxs)("div",{className:"md:w-2/3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:s.product.name}),(0,a.jsxs)("p",{className:"text-gray-500",children:["Order #",s.id]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.bfZ,{className:"text-gray-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:m(s.createdAt)})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Price per unit"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:n(s.product.price)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Quantity"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:s.quantity})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"PV"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:s.totalPV})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Status"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:s.status})]})]}),(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Subtotal:"}),(0,a.jsx)("span",{className:"font-medium",children:n(s.product.price*s.quantity)})]}),null!==s.shippingFee&&s.shippingFee>0&&(0,a.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Shipping Fee:"}),(0,a.jsx)("span",{className:"font-medium",children:n(s.shippingFee)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-2 text-lg font-bold",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsx)("span",{children:n(s.totalAmount)})]})]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsx)("button",{className:"px-4 py-2 font-medium text-sm ".concat("details"===r?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>l("details"),children:"Order Details"}),(0,a.jsx)("button",{className:"px-4 py-2 font-medium text-sm ".concat("shipping"===r?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>l("shipping"),children:"Shipping"}),(0,a.jsx)("button",{className:"px-4 py-2 font-medium text-sm ".concat("payment"===r?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>l("payment"),children:"Payment"})]}),(0,a.jsxs)("div",{className:"py-4",children:["details"===r&&(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Order Summary"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Product:"}),(0,a.jsx)("span",{className:"font-medium",children:s.product.name})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Price per unit:"}),(0,a.jsx)("span",{className:"font-medium",children:n(s.product.price)})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Quantity:"}),(0,a.jsx)("span",{className:"font-medium",children:s.quantity})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"PV:"}),(0,a.jsx)("span",{className:"font-medium",children:s.totalPV})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Order Date:"}),(0,a.jsx)("span",{className:"font-medium",children:m(s.createdAt)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Order Status:"}),(0,a.jsx)("span",{className:"font-medium",children:s.status})]})]})]})}),"shipping"===r&&(0,a.jsx)("div",{className:"space-y-4",children:s.shippingMethod?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)("div",{className:"mr-3 text-xl",children:(e=>{if(!e)return(0,a.jsx)(c.CE5,{className:"text-gray-500"});switch(e){case"pickup":return(0,a.jsx)(c.Tvt,{className:"text-blue-500"});case"lalamove":return(0,a.jsx)(c.N8c,{className:"text-orange-500"});case"jnt":return(0,a.jsx)(c.dv1,{className:"text-red-500"});default:return(0,a.jsx)(c.CE5,{className:"text-gray-500"})}})(s.shippingMethod.code)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:s.shippingMethod.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:null!==s.shippingFee&&s.shippingFee>0?"Shipping Fee: ".concat(n(s.shippingFee)):"Free Shipping"})]})]})]}),s.shippingStatus&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Status"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(e=>{if(!e)return null;switch(e){case"pending":return(0,a.jsxs)("div",{className:"flex items-center text-yellow-600",children:[(0,a.jsx)(c.w_X,{className:"mr-2"}),(0,a.jsx)("span",{children:"Pending"})]});case"processing":return(0,a.jsxs)("div",{className:"flex items-center text-blue-600",children:[(0,a.jsx)(c.rrY,{className:"mr-2"}),(0,a.jsx)("span",{children:"Processing"})]});case"shipped":return(0,a.jsxs)("div",{className:"flex items-center text-purple-600",children:[(0,a.jsx)(c.CE5,{className:"mr-2"}),(0,a.jsx)("span",{children:"Shipped"})]});case"delivered":return(0,a.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,a.jsx)(c.CMH,{className:"mr-2"}),(0,a.jsx)("span",{children:"Delivered"})]});case"cancelled":return(0,a.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,a.jsx)(c.QCr,{className:"mr-2"}),(0,a.jsx)("span",{children:"Cancelled"})]});default:return(0,a.jsx)("div",{className:"flex items-center text-gray-600",children:(0,a.jsx)("span",{children:e})})}})(s.shippingStatus),s.trackingNumber&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Tracking Number:"}),(0,a.jsx)("span",{className:"font-medium ml-2",children:s.trackingNumber})]})]})]}),s.shippingAddress&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Address"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(c.vq8,{className:"text-red-500 mt-1 mr-2"}),(0,a.jsx)("div",{className:"text-gray-800",children:s.shippingAddress})]})]}),s.shippingDetails&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Details"}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(()=>{if(!s.shippingDetails)return null;try{let e=JSON.parse(s.shippingDetails);return(0,a.jsx)("div",{className:"mt-4 space-y-2",children:Object.entries(e).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[s,":"]}),(0,a.jsx)("span",{className:"font-medium",children:String(t)})]},s)})})}catch(e){return console.error("Error parsing shipping details:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing shipping details"})}})()})]}),"pickup"===s.shippingMethod.code&&(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(c.__w,{className:"text-blue-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-blue-700",children:"Store Pickup Information"}),(0,a.jsxs)("p",{className:"text-sm text-blue-600 mt-1",children:["You can pick up your order at our store located at:",(0,a.jsx)("strong",{children:" 123 Main Street, Makati City, Metro Manila"})]}),(0,a.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Store Hours: Monday to Saturday, 9:00 AM to 6:00 PM"})]})]})]}):(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(c.BS8,{className:"text-yellow-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700",children:"No Shipping Information"}),(0,a.jsx)("p",{className:"text-sm text-yellow-600 mt-1",children:"This order does not have shipping information."})]})]})}),"payment"===r&&(0,a.jsxs)("div",{className:"space-y-4",children:[s.paymentMethod?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(c.MxO,{className:"text-green-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:s.paymentMethod.name}),s.referenceNumber&&(0,a.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:["Reference Number: ",s.referenceNumber]})]})]})]}),s.paymentDetails&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Details"}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(()=>{if(!s.paymentDetails)return null;try{let e=JSON.parse(s.paymentDetails);return(0,a.jsx)("div",{className:"mt-4 space-y-2",children:Object.entries(e).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[s,":"]}),(0,a.jsx)("span",{className:"font-medium",children:String(t)})]},s)})})}catch(e){return console.error("Error parsing payment details:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing payment details"})}})()})]})]}):(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(c.BS8,{className:"text-yellow-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700",children:"No Payment Information"}),(0,a.jsx)("p",{className:"text-sm text-yellow-600 mt-1",children:"This order does not have payment information."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Summary"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Subtotal:"}),(0,a.jsx)("span",{className:"font-medium",children:n(s.product.price*s.quantity)})]}),null!==s.shippingFee&&s.shippingFee>0&&(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Shipping Fee:"}),(0,a.jsx)("span",{className:"font-medium",children:n(s.shippingFee)})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-2 border-t border-gray-200 font-bold",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsx)("span",{children:n(s.totalAmount)})]})]})]})]})]})]})]})]})})}function h(){let{data:e,status:s}=(0,r.useSession)(),t=(0,l.useRouter)(),[m,h]=(0,i.useState)(!0),[p,u]=(0,i.useState)([]),[j,g]=(0,i.useState)({total:0,limit:10,offset:0,hasMore:!1}),[N,f]=(0,i.useState)(null),[y,b]=(0,i.useState)(null),[v,w]=(0,i.useState)(!1);(0,i.useEffect)(()=>{"unauthenticated"===s&&t.push("/login")},[s,t]),(0,i.useEffect)(()=>{"authenticated"===s&&S()},[s,j.offset,j.limit]);let S=async()=>{h(!0),f(null);try{let e=await fetch("/api/purchases?limit=".concat(j.limit,"&offset=").concat(j.offset));if(!e.ok)throw Error("Failed to fetch purchases: ".concat(e.statusText));let s=await e.json();u(s.purchases||[]),g(s.pagination||j)}catch(e){console.error("Error fetching purchases:",e),f("Failed to load purchases. Please try again.")}finally{h(!1)}},P=e=>{g({...j,offset:e})},k=e=>{b(e),w(!0)},M=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),C=e=>new Date(e).toLocaleDateString(),D=e=>{if(!e)return(0,a.jsx)(c.CE5,{className:"text-gray-500"});switch(e){case"pickup":return(0,a.jsx)(c.Tvt,{className:"text-blue-500"});case"lalamove":return(0,a.jsx)(c.N8c,{className:"text-orange-500"});case"jnt":return(0,a.jsx)(c.dv1,{className:"text-red-500"});default:return(0,a.jsx)(c.CE5,{className:"text-gray-500"})}},F=e=>{if(!e)return null;switch(e){case"pending":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800",children:[(0,a.jsx)(c.w_X,{className:"mr-1"})," Pending"]});case"processing":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:[(0,a.jsx)(c.rrY,{className:"mr-1"})," Processing"]});case"shipped":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800",children:[(0,a.jsx)(c.CE5,{className:"mr-1"})," Shipped"]});case"delivered":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:[(0,a.jsx)(c.CMH,{className:"mr-1"})," Delivered"]});case"cancelled":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:[(0,a.jsx)(c.QCr,{className:"mr-1"})," Cancelled"]});default:return(0,a.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800",children:e})}};return"loading"===s||m?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading purchases..."})]})}):(0,a.jsxs)(n.A,{children:[(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"My Purchases"}),N&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:N}),0===p.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)(c.AsH,{className:"mx-auto h-12 w-12 text-gray-300 mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"No purchases yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"You haven't made any purchases yet. Start shopping to see your purchase history."}),(0,a.jsx)(x(),{href:"/shop",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Browse Products"})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order Details"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Shipping"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10 relative",children:e.product.image?(0,a.jsx)(d.default,{src:e.product.image,alt:e.product.name,fill:!0,className:"rounded-md object-cover"}):(0,a.jsx)("div",{className:"h-10 w-10 bg-gray-200 rounded-md flex items-center justify-center",children:(0,a.jsx)(c.AsH,{className:"text-gray-500"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.product.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Quantity: ",e.quantity]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:M(e.totalAmount)}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.totalPV," PV"]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.shippingMethod?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center text-sm font-medium text-gray-900",children:[D(e.shippingMethod.code),(0,a.jsx)("span",{className:"ml-1",children:e.shippingMethod.name})]}),(0,a.jsx)("div",{className:"mt-1",children:F(e.shippingStatus)}),e.trackingNumber&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Tracking: ",e.trackingNumber]})]}):(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Not specified"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.paymentMethod?(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.paymentMethod.name}):(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Not specified"}),e.referenceNumber&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Ref: ",e.referenceNumber]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.bfZ,{className:"text-gray-400 mr-1"}),C(e.createdAt)]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsx)("button",{onClick:()=>k(e),className:"text-blue-600 hover:text-blue-900",children:"View Details"})})]},e.id))})]})}),j.total>j.limit&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",j.offset+1," to"," ",Math.min(j.offset+j.limit,j.total)," of"," ",j.total," purchases"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>P(Math.max(0,j.offset-j.limit)),disabled:0===j.offset,className:"px-3 py-1 border rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>P(j.offset+j.limit),disabled:!j.hasMore,className:"px-3 py-1 border rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})]}),v&&y&&(0,a.jsx)(o,{purchase:y,onClose:()=>w(!1)})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(3502)),_N_E=e.O()}]);
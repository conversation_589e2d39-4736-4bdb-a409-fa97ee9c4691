"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9526],{99526:(e,s,t)=>{t.d(s,{A:()=>x});var a=t(95155),r=t(12115),i=t(12108),n=t(6874),l=t.n(n),c=t(66766),d=t(35695),o=t(29911);function m(){let[e,s]=(0,r.useState)([]),[t,i]=(0,r.useState)(!1),[n,c]=(0,r.useState)(null),[d,m]=(0,r.useState)(!1),x=(0,r.useRef)(null),[h,f]=(0,r.useState)(0);(0,r.useEffect)(()=>{u();let e=setInterval(()=>{u()},6e4);return()=>clearInterval(e)},[]),(0,r.useEffect)(()=>{function e(e){x.current&&!x.current.contains(e.target)&&m(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let u=async()=>{try{i(!0),c(null);let e=await fetch("/api/admin/notifications?limit=5");if(!e.ok)throw Error("Failed to fetch notifications");let t=await e.json();s(t.notifications||[]),f(t.unreadCount||0)}catch(e){console.error("Error fetching notifications:",e),c("Failed to load notifications")}finally{i(!1)}},j=async t=>{try{if(!(await fetch("/api/admin/notifications/".concat(t,"/mark-read"),{method:"POST"})).ok)throw Error("Failed to mark notification as read");s(e.map(e=>e.id===t?{...e,isRead:!0}:e)),f(Math.max(0,h-1))}catch(e){console.error("Error marking notification as read:",e)}},b=async()=>{try{if(!(await fetch("/api/admin/notifications/mark-all-read",{method:"POST"})).ok)throw Error("Failed to mark all notifications as read");s(e.map(e=>({...e,isRead:!0}))),f(0)}catch(e){console.error("Error marking all notifications as read:",e)}},N=e=>{switch(e){case"low_stock":return(0,a.jsx)(o.BS8,{className:"text-amber-500"});case"system":return(0,a.jsx)(o.__w,{className:"text-blue-500"});default:return(0,a.jsx)(o.jNV,{className:"text-gray-500"})}},g=e=>{let s=new Date(e),t=new Date().getTime()-s.getTime(),a=Math.floor(t/6e4),r=Math.floor(t/36e5),i=Math.floor(t/864e5);return a<1?"Just now":a<60?"".concat(a," minute").concat(1===a?"":"s"," ago"):r<24?"".concat(r," hour").concat(1===r?"":"s"," ago"):i<7?"".concat(i," day").concat(1===i?"":"s"," ago"):s.toLocaleDateString()};return(0,a.jsxs)("div",{className:"relative",ref:x,children:[(0,a.jsxs)("button",{onClick:()=>m(!d),className:"relative p-1 text-gray-600 hover:text-gray-900 focus:outline-none","aria-label":"Notifications",children:[(0,a.jsx)(o.jNV,{className:"h-6 w-6"}),h>0&&(0,a.jsx)("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full",children:h>9?"9+":h})]}),d&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50",children:[(0,a.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200 flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-700",children:"Notifications"}),h>0&&(0,a.jsx)("button",{onClick:b,className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark all as read"})]}),(0,a.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[t&&(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(o.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Loading notifications..."})]}),n&&(0,a.jsxs)("div",{className:"p-4 text-sm text-red-600",children:[(0,a.jsx)(o._Hm,{className:"inline mr-2"}),n]}),!t&&!n&&0===e.length&&(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(o.jNV,{className:"mx-auto text-gray-300 text-3xl mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No notifications"})]}),e.map(e=>(0,a.jsx)("div",{className:"px-4 py-3 border-b border-gray-100 hover:bg-gray-50 ".concat(e.isRead?"":"bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mr-3 mt-1",children:N(e.type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:g(e.createdAt)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,a.jsxs)("div",{className:"mt-2 flex justify-between items-center",children:["low_stock"===e.type&&e.productId&&(0,a.jsxs)(l(),{href:"/admin/products?highlight=".concat(e.productId),className:"text-xs text-blue-600 hover:text-blue-800 flex items-center",children:[(0,a.jsx)(o.rrY,{className:"mr-1"}),"View Product"]}),!e.isRead&&(0,a.jsxs)("button",{onClick:()=>j(e.id),className:"text-xs text-gray-500 hover:text-gray-700 flex items-center ml-auto",children:[(0,a.jsx)(o.CMH,{className:"mr-1"}),"Mark as read"]})]})]})]})},e.id))]}),(0,a.jsx)("div",{className:"px-4 py-2 border-t border-gray-200 text-center",children:(0,a.jsx)(l(),{href:"/admin/notifications",className:"text-xs text-blue-600 hover:text-blue-800",onClick:()=>m(!1),children:"View all notifications"})})]})]})}let x=e=>{var s,t;let{children:n}=e,{data:x}=(0,i.useSession)(),h=(0,d.usePathname)(),[f,u]=(0,r.useState)(!1),[j,b]=(0,r.useState)(!1),N=[{name:"Dashboard",href:"/admin",icon:(0,a.jsx)(o.rQ8,{})},{name:"User Management",href:"/admin/users",icon:(0,a.jsx)(o.YXz,{})},{name:"Product Management",href:"/admin/products",icon:(0,a.jsx)(o.AsH,{})},{name:"Rebate Management",href:"/admin/rebates",icon:(0,a.jsx)(o.lcY,{})},{name:"Rebate Configurations",href:"/admin/rebate-configs",icon:(0,a.jsx)(o.gdQ,{})},{name:"Reports",href:"/admin/reports",icon:(0,a.jsx)(o.YYR,{})},{name:"Test Users",href:"/admin/test-users",icon:(0,a.jsx)(o.vWM,{})},{name:"Test Data",href:"/admin/test-data",icon:(0,a.jsx)(o.kkc,{})},{name:"Settings",href:"/admin/settings",icon:(0,a.jsx)(o.Pcn,{})}],g=e=>"/admin"===e?"/admin"===h:null==h?void 0:h.startsWith(e),v=()=>{b(!j)};return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,a.jsxs)("div",{className:"bg-white shadow-md hidden md:block transition-all duration-300 ".concat(f?"w-20":"w-64"),children:[(0,a.jsxs)("div",{className:"p-4 border-b flex ".concat(f?"justify-center":"justify-between"," items-center"),children:[!f&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,a.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Extreme Life Admin"})]}),f&&(0,a.jsx)("div",{className:"relative w-10 h-10",children:(0,a.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,a.jsx)("button",{onClick:()=>{u(!f)},className:"p-1 rounded-full hover:bg-gray-100",children:f?(0,a.jsx)(o.X6T,{}):(0,a.jsx)(o._Jj,{})})]}),(0,a.jsx)("nav",{className:"mt-4",children:(0,a.jsx)("ul",{children:N.map(e=>(0,a.jsx)("li",{children:(0,a.jsxs)(l(),{href:e.href,className:"flex items-center px-4 py-3 ".concat(g(e.href)?"bg-blue-50 text-blue-600 border-r-4 border-blue-600":"text-gray-700 hover:bg-blue-50 hover:text-blue-600"," ").concat(f?"justify-center":""),children:[(0,a.jsx)("span",{className:"".concat(f?"text-xl":"mr-3"),children:e.icon}),!f&&(0,a.jsx)("span",{children:e.name})]})},e.name))})}),(0,a.jsx)("div",{className:"absolute bottom-0 w-full border-t p-4",children:(0,a.jsxs)(l(),{href:"/dashboard",className:"flex items-center text-gray-700 hover:text-blue-600 ".concat(f?"justify-center":""),children:[(0,a.jsx)(o.rQ8,{className:"".concat(f?"text-xl":"mr-3")}),!f&&(0,a.jsx)("span",{children:"Back to Main App"})]})})]}),j&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:v}),(0,a.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-md transform transition-transform duration-300 ease-in-out md:hidden ".concat(j?"translate-x-0":"-translate-x-full"),children:[(0,a.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,a.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Extreme Life Admin"})]}),(0,a.jsx)("button",{onClick:v,className:"p-1 rounded-full hover:bg-gray-100",children:(0,a.jsx)(o.QCr,{})})]}),(0,a.jsx)("nav",{className:"mt-4",children:(0,a.jsx)("ul",{children:N.map(e=>(0,a.jsx)("li",{children:(0,a.jsxs)(l(),{href:e.href,className:"flex items-center px-4 py-3 ".concat(g(e.href)?"bg-blue-50 text-blue-600 border-r-4 border-blue-600":"text-gray-700 hover:bg-blue-50 hover:text-blue-600"),onClick:v,children:[(0,a.jsx)("span",{className:"mr-3",children:e.icon}),(0,a.jsx)("span",{children:e.name})]})},e.name))})}),(0,a.jsx)("div",{className:"absolute bottom-0 w-full border-t p-4",children:(0,a.jsxs)(l(),{href:"/dashboard",className:"flex items-center text-gray-700 hover:text-blue-600",onClick:v,children:[(0,a.jsx)(o.rQ8,{className:"mr-3"}),(0,a.jsx)("span",{children:"Back to Main App"})]})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{className:"p-2 rounded-md text-gray-500 md:hidden",onClick:v,children:(0,a.jsx)(o.OXb,{})}),(0,a.jsxs)(l(),{href:"/admin",className:"flex items-center ml-2 md:ml-0",children:[(0,a.jsx)("div",{className:"relative w-8 h-8 mr-2",children:(0,a.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,a.jsx)("h1",{className:"text-xl font-semibold text-blue-700",children:"Extreme Life Admin"})]})]}),(0,a.jsx)("div",{className:"flex items-center",children:x?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"mr-4",children:(0,a.jsx)(m,{})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center mr-4 text-sm text-gray-700",children:[(0,a.jsx)(o.NBi,{className:"mr-2"}),(0,a.jsx)("span",{children:(null==(s=x.user)?void 0:s.name)||(null==(t=x.user)?void 0:t.email)})]}),(0,a.jsxs)(l(),{href:"/api/auth/signout",className:"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center",children:[(0,a.jsx)(o.axc,{className:"mr-2"}),(0,a.jsx)("span",{className:"hidden md:inline",children:"Sign Out"})]})]}):(0,a.jsx)(l(),{href:"/login",className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Sign In"})})]})})}),(0,a.jsx)("main",{className:"flex-1 overflow-auto bg-gray-100",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:n})})]})]})}}}]);
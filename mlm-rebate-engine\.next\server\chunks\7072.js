"use strict";exports.id=7072,exports.ids=[7072],exports.modules={27072:(e,t,r)=>{let a,i;r.d(t,{Z3:()=>l,nl:()=>p,getShareableLinkByCode:()=>d,F2:()=>C,qY:()=>k,Yx:()=>m,GI:()=>w,recordReferralPurchase:()=>y,p1:()=>f});var n=r(31183),o=r(55511);let s=e=>{!a||a.length<e?(a=Buffer.allocUnsafe(128*e),o.randomFillSync(a),i=0):i+e>a.length&&(o.randomFillSync(a),i=0),i+=e},u=(e=21)=>{s(e|=0);let t="";for(let r=i-e;r<i;r++)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&a[r]];return t};async function c(){let e=u(8),t=!1;for(;!t;)await n.z.shareableLink.findUnique({where:{code:e}})?e=u(8):t=!0;return e}async function l(e,t,r){let a=await c();return await n.z.shareableLink.create({data:{userId:e,productId:t,code:a,type:"product",title:r?.title,description:r?.description,customImage:r?.customImage,expiresAt:r?.expiresAt,isActive:!0}})}async function d(e){return await n.z.shareableLink.findUnique({where:{code:e}})}async function m(e,t){let r={userId:e};t?.productId!==void 0&&(r.productId=t.productId),t?.type!==void 0&&(r.type=t.type),t?.isActive!==void 0&&(r.isActive=t.isActive);let a=await n.z.shareableLink.count({where:r});return{links:await n.z.shareableLink.findMany({where:r,orderBy:{createdAt:"desc"},take:t?.limit,skip:t?.offset,include:{product:{select:{id:!0,name:!0,price:!0,image:!0,referralCommissionType:!0,referralCommissionValue:!0}}}}),total:a}}async function f(e,t){return await n.z.shareableLink.update({where:{id:e},data:{...t,updatedAt:new Date}})}async function p(e){return await n.z.shareableLink.delete({where:{id:e}})}async function w(e,t){return await n.z.shareableLink.update({where:{id:e},data:{clickCount:{increment:1},updatedAt:new Date}}),await n.z.linkClick.create({data:{linkId:e,ipAddress:t?.ipAddress,userAgent:t?.userAgent,referrer:t?.referrer,utmSource:t?.utmSource,utmMedium:t?.utmMedium,utmCampaign:t?.utmCampaign}})}async function h(e,t){let r=await n.z.product.findUnique({where:{id:e},select:{referralCommissionType:!0,referralCommissionValue:!0}});return r&&r.referralCommissionType&&r.referralCommissionValue?"percentage"===r.referralCommissionType?{amount:t*(r.referralCommissionValue/100),percentage:r.referralCommissionValue}:{amount:r.referralCommissionValue,percentage:r.referralCommissionValue/t*100}:{amount:.05*t,percentage:5}}async function y(e,t){let r=await n.z.purchase.findUnique({where:{id:e},include:{product:!0,user:!0,referralLink:{include:{user:!0}}}});if(!r)throw Error(`Purchase with ID ${e} not found`);if(!r.referralLink)throw Error(`Purchase with ID ${e} has no referral link`);let{amount:a,percentage:i}=await h(r.productId,r.totalAmount),o=await n.z.referralCommission.create({data:{purchaseId:e,linkId:t,referrerId:r.referralLink.userId,buyerId:r.userId,productId:r.productId,amount:a,percentage:i,status:"pending"}});return await n.z.shareableLink.update({where:{id:t},data:{conversionCount:{increment:1},totalRevenue:{increment:r.totalAmount},totalCommission:{increment:a},updatedAt:new Date}}),o}async function C(e,t){let r={referrerId:e};t?.status!==void 0&&(r.status=t.status);let a=await n.z.referralCommission.count({where:r});return{commissions:await n.z.referralCommission.findMany({where:r,orderBy:{createdAt:"desc"},take:t?.limit,skip:t?.offset,include:{purchase:{select:{id:!0,quantity:!0,totalAmount:!0,createdAt:!0}},buyer:{select:{id:!0,name:!0,email:!0}},product:{select:{id:!0,name:!0,price:!0,image:!0}},link:{select:{id:!0,code:!0,type:!0}}}}),total:a}}async function k(e){let t=await n.z.shareableLink.aggregate({where:{userId:e},_sum:{clickCount:!0,conversionCount:!0,totalRevenue:!0,totalCommission:!0},_count:{id:!0}}),r=t._sum.clickCount||0,a=t._sum.conversionCount||0;return{totalLinks:t._count.id,totalClicks:r,totalConversions:a,totalRevenue:t._sum.totalRevenue||0,totalCommission:t._sum.totalCommission||0,conversionRate:r>0?a/r*100:0}}}};
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9012],{19012:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var n=a(95155),r=a(12115),o=a(74211),i=a(93306),l=a(77581),s=a(57916);a(11687);var d=a(29911),c=a(22406),h=a(10396);let u={userNode:c.A},m={layout:"vertical",nodeSpacing:40,levelSpacing:150,theme:"light",showPerformanceMetrics:!0,animateChanges:!0,showMinimap:!0,showControls:!0,nodeBorderRadius:8,connectionType:"smoothstep",connectionStyle:"solid",connectionWidth:1.5,nodeWidth:200,nodeHeight:150};function g(e){let{userId:t,maxLevel:a=3,initialLayout:c="vertical",initialPageSize:g=20}=e,[f,p,x]=(0,o.ck)([]),[y,w,b]=(0,o.fM)([]),[S,k]=(0,r.useState)(!0),[v,N]=(0,r.useState)(null),[j,C]=(0,r.useState)(null),[E,z]=(0,r.useState)(new Set),[M,A]=(0,r.useState)({...m,layout:c}),[W,B]=(0,r.useState)({x:0,y:0,width:0,height:0}),[_,L]=(0,r.useState)({x:0,y:0,zoom:1}),[V,P]=(0,r.useState)(new Set),[T,Z]=(0,r.useState)(!1),G=(0,o.VH)(),H=(0,r.useRef)(null);(0,r.useEffect)(()=>{if(!H.current)return;let{width:e,height:t}=H.current.getBoundingClientRect(),{x:a,y:n,zoom:r}=_;B({x:-a/r,y:-n/r,width:e/r,height:t/r})},[_]);let R=(0,r.useCallback)(async()=>{k(!0),N(null);try{let e=new URLSearchParams({userId:t.toString(),maxLevel:"1",pageSize:g.toString(),includePerformanceMetrics:"true"}),a=await fetch("/api/genealogy?".concat(e.toString()));if(!a.ok)throw Error("Failed to fetch genealogy data");let n=await a.json(),{nodes:r,edges:o}=D(n);Z(!0),p(r),w(o),P(new Set([n.id.toString()])),setTimeout(()=>{G.fitView({padding:.2}),Z(!1)},300)}catch(e){N(e instanceof Error?e.message:"An unknown error occurred"),Z(!1)}finally{k(!1)}},[t,g,p,w,G]);(0,r.useEffect)(()=>{R()},[R]);let I=(0,r.useCallback)(async e=>{if(!V.has(e)){Z(!0);try{let t=new URLSearchParams({userId:e,maxLevel:"1",pageSize:g.toString(),includePerformanceMetrics:"true"}),a=await fetch("/api/genealogy?".concat(t.toString()));if(!a.ok)throw Error("Failed to fetch node children");let n=await a.json(),r=f.find(t=>t.id===e);if(!r)return;let i=[],l=[];if(n.children&&n.children.length>0){let t,a,s,d,c=r.position.x,h=r.position.y,u=M.nodeWidth,m=M.nodeSpacing,g=M.levelSpacing,f=M.levelSpacing,p=n.children.length*u+(n.children.length-1)*m;if("vertical"===M.layout)t=c-p/2+u/2,d=h+g,n.children.forEach((a,n)=>{s=t+n*(u+m);let r={id:a.id,name:a.name,email:a.email,rankName:a.rank.name,level:1,downlineCount:a._count.downline,createdAt:a.createdAt,walletBalance:a.walletBalance,performanceMetrics:a.performanceMetrics},c=r.id.toString();i.push({id:c,type:"userNode",position:{x:s,y:d},data:{user:r,onExpand:()=>J(c),onSelect:()=>C(r),isExpanded:E.has(c),hasChildren:a._count.downline>0,visualOptions:M}}),l.push({id:"e-".concat(e,"-").concat(c),source:e,target:c,type:F(),animated:!1,style:{stroke:"#888",strokeWidth:M.connectionWidth,strokeDasharray:"dashed"===M.connectionStyle?"5,5":void 0},markerEnd:{type:o.TG.ArrowClosed,width:15,height:15,color:"#888"}})});else if("horizontal"===M.layout)s=c+f,a=h-p/2+u/2,n.children.forEach((t,n)=>{d=a+n*(u+m);let r={id:t.id,name:t.name,email:t.email,rankName:t.rank.name,level:1,downlineCount:t._count.downline,createdAt:t.createdAt,walletBalance:t.walletBalance,performanceMetrics:t.performanceMetrics},c=r.id.toString();i.push({id:c,type:"userNode",position:{x:s,y:d},data:{user:r,onExpand:()=>J(c),onSelect:()=>C(r),isExpanded:E.has(c),hasChildren:t._count.downline>0,visualOptions:M}}),l.push({id:"e-".concat(e,"-").concat(c),source:e,target:c,type:F(),animated:!1,style:{stroke:"#888",strokeWidth:M.connectionWidth,strokeDasharray:"dashed"===M.connectionStyle?"5,5":void 0},markerEnd:{type:o.TG.ArrowClosed,width:15,height:15,color:"#888"}})});else if("radial"===M.layout){let t=2*Math.PI/n.children.length;n.children.forEach((a,n)=>{let r=n*t;s=c+f*Math.cos(r),d=h+f*Math.sin(r);let u={id:a.id,name:a.name,email:a.email,rankName:a.rank.name,level:1,downlineCount:a._count.downline,createdAt:a.createdAt,walletBalance:a.walletBalance,performanceMetrics:a.performanceMetrics},m=u.id.toString();i.push({id:m,type:"userNode",position:{x:s,y:d},data:{user:u,onExpand:()=>J(m),onSelect:()=>C(u),isExpanded:E.has(m),hasChildren:a._count.downline>0,visualOptions:M}}),l.push({id:"e-".concat(e,"-").concat(m),source:e,target:m,type:F(),animated:!1,style:{stroke:"#888",strokeWidth:M.connectionWidth,strokeDasharray:"dashed"===M.connectionStyle?"5,5":void 0},markerEnd:{type:o.TG.ArrowClosed,width:15,height:15,color:"#888"}})})}}p(e=>[...e,...i]),w(e=>[...e,...l]),P(t=>new Set([...t,e])),p(t=>t.map(t=>t.id===e?{...t,data:{...t.data,isExpanded:!0}}:t))}catch(e){console.error("Error fetching node children:",e)}finally{Z(!1)}}},[f,y,p,w,V,E,M,g,F]),D=(0,r.useCallback)(e=>{let t=[],a={id:e.id,name:e.name,email:e.email,rankName:e.rank.name,level:0,downlineCount:e._count.downline,createdAt:e.createdAt,walletBalance:e.walletBalance,performanceMetrics:e.performanceMetrics};return t.push({id:a.id.toString(),type:"userNode",position:{x:0,y:0},data:{user:a,onExpand:()=>J(a.id.toString()),onSelect:()=>C(a),isExpanded:E.has(a.id.toString()),hasChildren:e._count.downline>0,visualOptions:M}}),{nodes:t,edges:[]}},[E,M]),F=(0,r.useCallback)(()=>{switch(M.connectionType){case"straight":return"default";case"step":return"step";case"smoothstep":default:return"smoothstep";case"bezier":return"bezier"}},[M]),J=(0,r.useCallback)(e=>{if(E.has(e)){z(t=>{let a=new Set(t);return a.delete(e),a});let t=y.filter(t=>t.source===e).map(e=>e.target),a=new Set,n=e=>{e.forEach(e=>{a.has(e)||(a.add(e),n(y.filter(t=>t.source===e).map(e=>e.target)))})};n(t),p(e=>e.filter(e=>!a.has(e.id))),w(e=>e.filter(e=>!a.has(e.target)&&!a.has(e.source))),p(t=>t.map(t=>t.id===e?{...t,data:{...t.data,isExpanded:!1}}:t))}else z(t=>{let a=new Set(t);return a.add(e),a}),I(e)},[E,y,p,w,I]),O=(0,r.useCallback)((e,t)=>{C(t.data.user)},[]),Q=(0,r.useCallback)(e=>{L(e)},[]),U=(0,r.useMemo)(()=>{if(f.length<100)return f;let e={x:W.x-500/_.zoom,y:W.y-500/_.zoom,width:W.width+1e3/_.zoom,height:W.height+1e3/_.zoom};return f.filter(t=>{let a=t.position.x,n=t.position.y,r=M.nodeWidth,o=M.nodeHeight;return a+r>=e.x&&a<=e.x+e.width&&n+o>=e.y&&n<=e.y+e.height})},[f,W,_.zoom,M.nodeWidth,M.nodeHeight]),q=(0,r.useMemo)(()=>{if(y.length<100)return y;let e=new Set(U.map(e=>e.id));return y.filter(t=>e.has(t.source)&&e.has(t.target))},[y,U]);return S&&0===f.length?(0,n.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,n.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,n.jsx)("span",{children:"Loading genealogy data..."})]}):v?(0,n.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,n.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,n.jsx)(d.BS8,{className:"mr-2"}),"Error loading genealogy data"]}),(0,n.jsx)("p",{className:"text-red-600",children:v})]}):(0,n.jsx)("div",{className:"flex flex-col border border-gray-200 rounded-md",children:(0,n.jsxs)("div",{className:"h-[600px] relative",ref:H,children:[T&&(0,n.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10",children:(0,n.jsx)(d.hW,{className:"animate-spin text-blue-500 text-2xl"})}),(0,n.jsxs)(o.Gc,{nodes:U,edges:q,onNodesChange:x,onEdgesChange:b,onNodeClick:O,nodeTypes:u,onViewportChange:Q,fitView:!0,attributionPosition:"bottom-right",connectionLineType:F(),minZoom:.1,maxZoom:1.5,defaultViewport:{x:0,y:0,zoom:.8},children:[M.showControls&&(0,n.jsx)(i.H,{}),M.showMinimap&&(0,n.jsx)(l.o,{nodeColor:e=>{var t;let a=null==(t=e.data)?void 0:t.user;if(!a)return"#eee";if(0===a.level)return"#93c5fd";switch(a.rankName){case"Starter":return"#f3f4f6";case"Bronze":return"#fef3c7";case"Silver":return"#e5e7eb";case"Gold":return"#fef08a";case"Platinum":return"#dbeafe";case"Diamond":return"#f3e8ff";default:return"#eee"}},maskColor:"#ffffff50"}),(0,n.jsx)(s.V,{}),j&&(0,n.jsx)(o.Zk,{position:"top-right",className:"p-0 w-80",children:(0,n.jsx)(h.A,{user:j,onClose:()=>C(null),className:"max-h-[80vh]"})}),(0,n.jsx)(o.Zk,{position:"bottom-left",className:"p-0",children:(0,n.jsxs)("div",{className:"flex flex-col bg-white rounded-md shadow-md overflow-hidden",children:[(0,n.jsx)("button",{onClick:()=>G.zoomIn(),className:"p-2 hover:bg-gray-100 border-b border-gray-200",title:"Zoom In",children:(0,n.jsx)(d.OiG,{})}),(0,n.jsx)("button",{onClick:()=>G.zoomOut(),className:"p-2 hover:bg-gray-100 border-b border-gray-200",title:"Zoom Out",children:(0,n.jsx)(d.iu5,{})}),(0,n.jsx)("button",{onClick:()=>G.fitView({padding:.2}),className:"p-2 hover:bg-gray-100",title:"Fit View",children:(0,n.jsx)(d.Ny1,{})})]})}),(0,n.jsx)(o.Zk,{position:"bottom-right",className:"p-0",children:(0,n.jsxs)("div",{className:"flex bg-white rounded-md shadow-md overflow-hidden",children:[(0,n.jsx)("button",{onClick:()=>A(e=>({...e,layout:"vertical"})),className:"p-2 ".concat("vertical"===M.layout?"bg-blue-100 text-blue-700":"hover:bg-gray-100"),title:"Vertical Layout",children:(0,n.jsx)(d.aQJ,{className:"rotate-0"})}),(0,n.jsx)("button",{onClick:()=>A(e=>({...e,layout:"horizontal"})),className:"p-2 ".concat("horizontal"===M.layout?"bg-blue-100 text-blue-700":"hover:bg-gray-100"),title:"Horizontal Layout",children:(0,n.jsx)(d.aQJ,{className:"rotate-90"})}),(0,n.jsx)("button",{onClick:()=>A(e=>({...e,layout:"radial"})),className:"p-2 ".concat("radial"===M.layout?"bg-blue-100 text-blue-700":"hover:bg-gray-100"),title:"Radial Layout",children:(0,n.jsx)(d.aQJ,{className:"rotate-45"})})]})})]})]})})}function f(e){return(0,n.jsx)(o.Ln,{children:(0,n.jsx)(g,{...e})})}}}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9222],{77144:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var i=a(95155),t=a(12115),l=a(12108),n=a(87747),r=a(29911),d=a(6874),c=a.n(d);let o=(0,a(55028).default)(()=>a.e(1407).then(a.bind(a,81407)),{loadableGenerated:{webpack:()=>[81407]},ssr:!1,loading:()=>(0,i.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading genealogy visualization..."})]})});function h(){let{data:e,status:s}=(0,l.useSession)(),[a,d]=(0,t.useState)(void 0),{data:h,isLoading:x}=(0,n.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let a=await fetch("/api/users/me");if(!a.ok)throw Error("Failed to fetch user data");return await a.json()},enabled:"authenticated"===s});return(h&&!a&&d(h.id),"loading"===s||x)?(0,i.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading user data..."})]}):"unauthenticated"===s?(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,i.jsx)(r.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,i.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,i.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]}):(0,i.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,i.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold",children:"Enhanced Genealogy Tree"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Visualize your network with improved performance and interactivity"})]}),(0,i.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,i.jsx)(r.QVr,{className:"mr-1"}),"Back to Standard View"]})]}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:a?(0,i.jsx)(o,{userId:a,maxLevel:6,initialPageSize:10}):(0,i.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading genealogy data..."})]})}),(0,i.jsxs)("div",{className:"mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,i.jsx)("h2",{className:"text-lg font-medium text-blue-800 mb-2",children:"About Enhanced Genealogy View"}),(0,i.jsx)("p",{className:"text-blue-700 mb-2",children:"This optimized view provides better performance for large networks with:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-blue-700 space-y-1",children:[(0,i.jsx)("li",{children:"Faster data loading with efficient caching"}),(0,i.jsx)("li",{children:"Interactive visualization with zoom and pan"}),(0,i.jsx)("li",{children:"Lazy loading of deeper levels for better performance"}),(0,i.jsx)("li",{children:"Detailed statistics and filtering options"}),(0,i.jsx)("li",{children:"Performance metrics for each member"})]})]})]})}},93568:(e,s,a)=>{Promise.resolve().then(a.bind(a,77144))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,5557,6967,7747,8441,1684,7358],()=>s(93568)),_N_E=e.O()}]);
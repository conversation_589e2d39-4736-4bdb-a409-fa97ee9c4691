(()=>{var e={};e.id=2162,e.ids=[2162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10038:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),n=r(43210),i=r(85814),o=r.n(i),a=r(30474),l=r(23877);function d(){let[e,t]=(0,n.useState)(""),[r,i]=(0,n.useState)(!1),[d,c]=(0,n.useState)(!0),[m,u]=(0,n.useState)(!1),[h,p]=(0,n.useState)(""),[x,v]=(0,n.useState)(!1),f=e=>/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(String(e).toLowerCase()),b=async t=>{t.preventDefault();let r=f(e);if(c(r),!r)return void p("Please enter a valid email address");u(!0),p("");try{await new Promise(e=>setTimeout(e,1500)),v(!0)}catch(e){console.error("Error requesting password reset:",e),p(`Failed to send reset email: ${e instanceof Error?e.message:String(e)}`)}finally{u(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col md:flex-row",children:[(0,s.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-gradient-to-br from-green-500 to-green-700 text-white p-12 flex-col justify-between relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)("div",{className:"relative w-12 h-12 mr-3",children:(0,s.jsx)(a.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain invert"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Extreme Life Herbal"})]}),(0,s.jsxs)("div",{className:"mt-16 mb-8",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold mb-6",children:"Reset Your Password"}),(0,s.jsx)("p",{className:"text-xl opacity-90 mb-8",children:"We'll send you instructions to reset your password and get you back to your account."}),(0,s.jsxs)("div",{className:"bg-white/10 p-6 rounded-lg backdrop-blur-sm",children:[(0,s.jsx)("p",{className:"italic text-white/90 mb-4",children:'"Our support team is always ready to help you with any account issues. We\'re committed to providing excellent service to all our distributors."'}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-white/30 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Customer Support"}),(0,s.jsx)("p",{className:"text-sm opacity-75",children:"Extreme Life Herbal"})]})]})]})]})]}),(0,s.jsx)("div",{className:"absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -mr-48 -mt-48"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-96 h-96 bg-white/5 rounded-full -ml-48 -mb-48"}),(0,s.jsx)("div",{className:"relative z-10",children:(0,s.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",new Date().getFullYear()," Extreme Life Herbal Products. All rights reserved."]})})]}),(0,s.jsxs)("div",{className:"flex flex-col justify-center md:w-1/2 p-6 sm:p-12 bg-white",children:[(0,s.jsx)("div",{className:"md:hidden flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,s.jsx)(a.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})}),(0,s.jsx)("h1",{className:"text-xl font-bold text-green-700",children:"Extreme Life Herbal"})]})}),(0,s.jsxs)("div",{className:"max-w-md mx-auto w-full",children:[(0,s.jsxs)(o(),{href:"/login",className:"inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 mb-6 transition-colors",children:[(0,s.jsx)(l.QVr,{className:"mr-2"})," Back to login"]}),x?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,s.jsx)(l.CMH,{className:"h-6 w-6 text-green-600"})}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Check your email"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-6",children:["We've sent password reset instructions to:",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"font-medium",children:e})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"If you don't see the email, check your spam folder or make sure you entered the correct email address."}),(0,s.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,s.jsx)(o(),{href:"/login",className:"inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors",children:"Return to Login"}),(0,s.jsx)("button",{onClick:()=>v(!1),className:"inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:"Try a different email"})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Forgot Password"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"Enter your email address and we'll send you instructions to reset your password."}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:b,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email-address",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(l.maD,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:`appearance-none block w-full pl-10 pr-3 py-2 border ${!d?"border-red-300":r?"border-green-500":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 transition-colors sm:text-sm`,placeholder:"<EMAIL>",value:e,onChange:e=>{let r=e.target.value;t(r),r?c(f(r)):c(!0)},onFocus:()=>i(!0),onBlur:()=>i(!1)}),!d&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:"Please enter a valid email address"})]})]}),h&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:h}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:m,className:`w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors ${m?"opacity-70 cursor-not-allowed":""}`,children:m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):"Send Reset Instructions"})})]})]})]})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>a,_:()=>o});var s=r(60687),n=r(43210);let i=(0,n.createContext)(void 0),o=()=>{let e=(0,n.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},a=({children:e})=>{let[t,r]=(0,n.useState)([]);(0,n.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{r(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,n.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let o=e=>{r(t=>t.filter(t=>t.id!==e))},a=t.reduce((e,t)=>e+t.quantity,0),l=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,s.jsx)(i.Provider,{value:{items:t,addItem:e=>{r(t=>{let r=t.findIndex(t=>t.id===e.id);if(!(r>=0))return[...t,e];{let s=[...t];return s[r]={...s[r],quantity:s[r].quantity+e.quantity},s}})},removeItem:o,updateQuantity:(e,t)=>{if(t<=0)return void o(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},itemCount:a,subtotal:l,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\forgot-password\\page.tsx","default")},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>n});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},45851:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(60687),n=r(25217),i=r(8693),o=r(43210);function a({children:e}){let[t]=(0,o.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,s.jsx)(i.Ht,{client:t,children:e})}},46603:(e,t,r)=>{Promise.resolve().then(r.bind(r,10038))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(60687),n=r(43210);let i=()=>"serviceWorker"in navigator,o=async()=>{if(!i())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),a(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},a=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,r]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{o();let e=()=>{r(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,s.jsxs)(s.Fragment,{children:[e,t&&(0,s.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,s.jsxs)("div",{className:"mr-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,s.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,s.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,r)=>{Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,23229)),Promise.resolve().then(r.bind(r,82113)),Promise.resolve().then(r.bind(r,41750))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,r)=>{Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,97695)),Promise.resolve().then(r.bind(r,45851)),Promise.resolve().then(r.bind(r,63345))},77916:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),o=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36200)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\forgot-password\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},86355:(e,t,r)=>{Promise.resolve().then(r.bind(r,36200))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var s=r(37413),n=r(22376),i=r.n(n),o=r(68726),a=r.n(o);r(61135);var l=r(23229),d=r(37043),c=r(82113),m=r(41750);let u={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/********.svg",apple:"/images/********.svg"},themeColor:"#4CAF50"};function h({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,s.jsx)(l.AuthProvider,{children:(0,s.jsx)(c.default,{children:(0,s.jsx)(d.CartProvider,{children:(0,s.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},97695:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(60687),n=r(82136);function i({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,8414,9567,3877,474],()=>r(77916));module.exports=s})();
exports.id=3024,exports.ids=[3024],exports.modules={23229:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>a,_:()=>i});var r=s(60687),l=s(43210);let n=(0,l.createContext)(void 0),i=()=>{let e=(0,l.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},a=({children:e})=>{let[t,s]=(0,l.useState)([]);(0,l.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,l.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let i=e=>{s(t=>t.filter(t=>t.id!==e))},a=t.reduce((e,t)=>e+t.quantity,0),o=t.reduce((e,t)=>e+t.price*t.quantity,0),c=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,r.jsx)(n.Provider,{value:{items:t,addItem:e=>{s(t=>{let s=t.findIndex(t=>t.id===e.id);if(!(s>=0))return[...t,e];{let r=[...t];return r[s]={...r[s],quantity:r[s].quantity+e.quantity},r}})},removeItem:i,updateQuantity:(e,t)=>{if(t<=0)return void i(e);s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{s([])},itemCount:a,subtotal:o,totalPV:c},children:e})}},37043:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>l});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let l=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},45851:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687),l=s(25217),n=s(8693),i=s(43210);function a({children:e}){let[t]=(0,i.useState)(()=>new l.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(n.Ht,{client:t,children:e})}},61135:()=>{},63345:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687),l=s(43210);let n=()=>"serviceWorker"in navigator,i=async()=>{if(!n())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),a(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},a=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&o()})})},o=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},c=({children:e})=>{let[t,s]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{i();let e=()=>{s(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,t&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,s)=>{Promise.resolve().then(s.bind(s,37043)),Promise.resolve().then(s.bind(s,23229)),Promise.resolve().then(s.bind(s,82113)),Promise.resolve().then(s.bind(s,41750))},68367:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(60687),l=s(43210),n=s(82136),i=s(85814),a=s.n(i),o=s(30474),c=s(23877),d=s(28253),x=s(13516),m=s(94408),h=s(16189);let u=({isOpen:e,onClose:t})=>{let s=(0,h.useRouter)(),{items:n,removeItem:i,updateQuantity:a,clearCart:u,subtotal:v,totalPV:f}=(0,d._)(),p=e=>`₱${e.toFixed(2)}`;return(0,r.jsx)(x.e.Root,{show:e,as:l.Fragment,children:(0,r.jsxs)(m.lG,{as:"div",className:"relative z-50",onClose:t,children:[(0,r.jsx)(x.e.Child,{as:l.Fragment,enter:"ease-in-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-hidden",children:(0,r.jsx)("div",{className:"absolute inset-0 overflow-hidden",children:(0,r.jsx)("div",{className:"pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10",children:(0,r.jsx)(x.e.Child,{as:l.Fragment,enter:"transform transition ease-in-out duration-300",enterFrom:"translate-x-full",enterTo:"translate-x-0",leave:"transform transition ease-in-out duration-300",leaveFrom:"translate-x-0",leaveTo:"translate-x-full",children:(0,r.jsx)(m.lG.Panel,{className:"pointer-events-auto w-screen max-w-md",children:(0,r.jsxs)("div",{className:"flex h-full flex-col overflow-y-scroll bg-white shadow-xl",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto px-4 py-6 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)(m.lG.Title,{className:"text-lg font-medium text-gray-900 flex items-center",children:[(0,r.jsx)(c.AsH,{className:"mr-2"})," Shopping Cart"]}),(0,r.jsx)("div",{className:"ml-3 flex h-7 items-center",children:(0,r.jsxs)("button",{type:"button",className:"relative -m-2 p-2 text-gray-400 hover:text-gray-500",onClick:t,children:[(0,r.jsx)("span",{className:"absolute -inset-0.5"}),(0,r.jsx)("span",{className:"sr-only",children:"Close panel"}),(0,r.jsx)(c.QCr,{className:"h-6 w-6","aria-hidden":"true"})]})})]}),(0,r.jsx)("div",{className:"mt-8",children:n.length>0?(0,r.jsxs)("div",{className:"flow-root",children:[(0,r.jsx)("ul",{role:"list",className:"-my-6 divide-y divide-gray-200",children:n.map(e=>(0,r.jsxs)("li",{className:"flex py-6",children:[(0,r.jsx)("div",{className:"h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200 relative",children:e.image?(0,r.jsx)(o.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover object-center"}):(0,r.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(c.AsH,{className:"text-gray-400 h-8 w-8"})})}),(0,r.jsxs)("div",{className:"ml-4 flex flex-1 flex-col",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[(0,r.jsx)("h3",{children:e.name}),(0,r.jsx)("p",{className:"ml-4",children:p(e.price*e.quantity)})]}),(0,r.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:["PV: ",e.pv*e.quantity]})]}),(0,r.jsxs)("div",{className:"flex flex-1 items-end justify-between text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center border rounded-md",children:[(0,r.jsx)("button",{type:"button",className:"p-2 text-gray-600 hover:text-gray-800",onClick:()=>a(e.id,e.quantity-1),children:(0,r.jsx)(c.iu5,{size:12})}),(0,r.jsx)("span",{className:"px-2 py-1 text-gray-900 min-w-[40px] text-center",children:e.quantity}),(0,r.jsx)("button",{type:"button",className:"p-2 text-gray-600 hover:text-gray-800",onClick:()=>a(e.id,e.quantity+1),children:(0,r.jsx)(c.OiG,{size:12})})]}),(0,r.jsxs)("button",{type:"button",className:"font-medium text-red-600 hover:text-red-500 flex items-center",onClick:()=>i(e.id),children:[(0,r.jsx)(c.qbC,{className:"mr-1",size:14}),"Remove"]})]})]})]},e.id))}),(0,r.jsx)("div",{className:"mt-4 text-right",children:(0,r.jsx)("button",{type:"button",className:"text-sm font-medium text-red-600 hover:text-red-500",onClick:u,children:"Clear Cart"})})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"mx-auto h-24 w-24 text-gray-400",children:(0,r.jsx)(c.AsH,{className:"h-full w-full"})}),(0,r.jsx)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Start adding products to your cart to see them here."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)("button",{type:"button",className:"inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500",onClick:t,children:"Continue Shopping"})})]})})]}),n.length>0&&(0,r.jsxs)("div",{className:"border-t border-gray-200 px-4 py-6 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-base font-medium text-gray-900 mb-1",children:[(0,r.jsx)("p",{children:"Subtotal"}),(0,r.jsx)("p",{children:p(v)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mb-4",children:[(0,r.jsx)("p",{children:"Total PV"}),(0,r.jsxs)("p",{children:[f," PV"]})]}),(0,r.jsx)("p",{className:"mt-0.5 text-sm text-gray-500",children:"Shipping and taxes calculated at checkout."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("button",{type:"button",className:"flex w-full items-center justify-center rounded-md border border-transparent bg-green-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-green-700",onClick:()=>{t(),s.push("/checkout")},children:["Checkout ",(0,r.jsx)(c.Z0P,{className:"ml-2"})]})}),(0,r.jsx)("div",{className:"mt-6 flex justify-center text-center text-sm text-gray-500",children:(0,r.jsxs)("p",{children:["or"," ",(0,r.jsxs)("button",{type:"button",className:"font-medium text-green-600 hover:text-green-500",onClick:t,children:["Continue Shopping",(0,r.jsx)("span",{"aria-hidden":"true",children:" →"})]})]})})]})]})})})})})})]})})},v=()=>{let{itemCount:e}=(0,d._)(),[t,s]=(0,l.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{type:"button",className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none",onClick:()=>s(!0),"aria-label":"Open shopping cart",children:[(0,r.jsx)(c.AsH,{className:"h-6 w-6"}),e>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 rounded-full bg-green-600 flex items-center justify-center text-xs text-white",children:e})]}),(0,r.jsx)(u,{isOpen:t,onClose:()=>s(!1)})]})},f=(0,s(30036).default)(async()=>{},{loadableGenerated:{modules:["components\\layout\\MainLayout.tsx -> @/components/common/PerformanceMonitor"]},ssr:!1}),p=({children:e})=>{let{data:t}=(0,n.useSession)();return(0,r.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[t&&(0,r.jsxs)("div",{className:"w-64 bg-white shadow-md",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,r.jsx)(o.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Extreme Life Rewards"})]})}),(0,r.jsx)("nav",{className:"mt-4",children:(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/dashboard",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.rQ8,{className:"mr-3"}),(0,r.jsx)("span",{children:"Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/genealogy",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.YXz,{className:"mr-3"}),(0,r.jsx)("span",{children:"Genealogy"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/shop",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.AsH,{className:"mr-3"}),(0,r.jsx)("span",{children:"Shop"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/wallet",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.lcY,{className:"mr-3"}),(0,r.jsx)("span",{children:"Wallet"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/rebates",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.YYR,{className:"mr-3"}),(0,r.jsx)("span",{children:"Rebates"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/binary-mlm",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.ph9,{className:"mr-3"}),(0,r.jsx)("span",{children:"Binary MLM"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/referrals",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.AnD,{className:"mr-3"}),(0,r.jsx)("span",{children:"My Referrals"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/rank-advancement",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.SBv,{className:"mr-3"}),(0,r.jsx)("span",{children:"Rank Advancement"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/profile",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.x$1,{className:"mr-3"}),(0,r.jsx)("span",{children:"My Profile"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/about",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.__w,{className:"mr-3"}),(0,r.jsx)("span",{children:"About Us"})]})}),(0,r.jsx)("li",{className:"mt-8 border-t pt-2",children:(0,r.jsxs)(a(),{href:"/admin",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600",children:[(0,r.jsx)(c.Pcn,{className:"mr-3"}),(0,r.jsx)("span",{children:"Admin Panel"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/admin/mlm-config",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 pl-10",children:[(0,r.jsx)(c.yk7,{className:"mr-3"}),(0,r.jsx)("span",{children:"MLM Configuration"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(a(),{href:"/admin/mlm-config/cutoff",className:"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 pl-10",children:[(0,r.jsx)(c.bfZ,{className:"mr-3"}),(0,r.jsx)("span",{children:"Monthly Cutoff"})]})})]})})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(a(),{href:"/",className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,r.jsx)(o.default,{src:"/images/20250503.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-green-700",children:"Extreme Life Herbal Product Rewards"})]})}),(0,r.jsx)("div",{className:"flex items-center",children:t?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v,{}),(0,r.jsx)("div",{className:"mx-4 h-6 border-l border-gray-300"}),(0,r.jsxs)(a(),{href:"/profile",className:"flex items-center mr-4 hover:text-blue-600",children:[(0,r.jsx)(c.x$1,{className:"mr-2"}),(0,r.jsx)("span",{children:t.user?.name||t.user?.email})]}),(0,r.jsx)(a(),{href:"/api/auth/signout",className:"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600",children:"Sign Out"})]}):(0,r.jsx)(a(),{href:"/login",className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Sign In"})})]})})}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e}),(0,r.jsx)(f,{})]})]})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(31658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,s)=>{Promise.resolve().then(s.bind(s,28253)),Promise.resolve().then(s.bind(s,97695)),Promise.resolve().then(s.bind(s,45851)),Promise.resolve().then(s.bind(s,63345))},82113:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h,metadata:()=>m});var r=s(37413),l=s(22376),n=s.n(l),i=s(68726),a=s.n(i);s(61135);var o=s(23229),c=s(37043),d=s(82113),x=s(41750);let m={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function h({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${a().variable} antialiased`,children:(0,r.jsx)(o.AuthProvider,{children:(0,r.jsx)(d.default,{children:(0,r.jsx)(c.CartProvider,{children:(0,r.jsx)(x.default,{children:e})})})})})})}},96111:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},97695:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n});var r=s(60687),l=s(82136);function n({children:e}){return(0,r.jsx)(l.SessionProvider,{children:e})}}};
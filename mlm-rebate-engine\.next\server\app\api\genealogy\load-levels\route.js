"use strict";(()=>{var e={};e.id=4004,e.ids=[4004],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12269:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{e.exports=require("assert")},19854:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var n={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var u=o?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(n,s,u):n[s]=e[s]}return n.default=e,t&&t.set(e,n),n}(t(35426));function a(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(a=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41348:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>v,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var n={};t.r(n),t.d(n,{GET:()=>c});var o=t(96559),s=t(48088),a=t(37719),u=t(31183),i=t(32190),l=t(19854),p=t(12909),d=t(6375);async function c(e){try{let r=await (0,l.getServerSession)(p.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to view genealogy"},{status:401});let t=r.user.email;if(!t)return i.NextResponse.json({error:"User email not found in session"},{status:400});if(!await u.z.user.findUnique({where:{email:t},select:{id:!0}}))return i.NextResponse.json({error:"User not found"},{status:404});let n=new URL(e.url),o=n.searchParams.get("userId"),s=n.searchParams.get("currentLevel"),a=n.searchParams.get("maxLevel");if(!o||!s)return i.NextResponse.json({error:"Missing required parameters: userId and currentLevel"},{status:400});let c=parseInt(o),f=parseInt(s),g=a?parseInt(a):10,x=await (0,d.Z3)(c,f,g);return i.NextResponse.json(x)}catch(e){return console.error("Error loading additional levels:",e),i.NextResponse.json({error:"Failed to load additional levels"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/genealogy/load-levels/route",pathname:"/api/genealogy/load-levels",filename:"route",bundlePath:"app/api/genealogy/load-levels/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\load-levels\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:v}=f;function y(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4243,580,8044,3112,6719],()=>t(41348));module.exports=n})();
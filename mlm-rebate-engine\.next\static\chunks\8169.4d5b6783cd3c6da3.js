"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8169],{58169:(e,s,l)=>{l.r(s),l.d(s,{default:()=>c});var r=l(95155),a=l(12115),n=l(29911);let t=e=>{switch(e){case"Starter":default:return"bg-gray-100 text-gray-800";case"Bronze":return"bg-yellow-100 text-yellow-800";case"Silver":return"bg-gray-200 text-gray-800";case"Gold":return"bg-yellow-200 text-yellow-800";case"Platinum":return"bg-blue-100 text-blue-800";case"Diamond":return"bg-purple-100 text-purple-800"}},i=e=>{let{user:s,isRoot:l=!1,depth:c,maxDepth:d,initialExpandedLevels:o}=e,[x,m]=(0,a.useState)(c<o),u=s.children&&s.children.length>0,h=u&&c<d,p=t(s.rank.name);return(0,r.jsxs)("div",{className:"mb-2 ".concat(l?"":"ml-6"),children:[(0,r.jsxs)("div",{className:"flex items-center p-3 rounded-md ".concat(l?"bg-blue-50 border border-blue-200":"bg-white border border-gray-200"),children:[h&&(0,r.jsx)("button",{onClick:()=>m(!x),className:"mr-2 text-gray-500 hover:text-gray-700 focus:outline-none","aria-label":x?"Collapse":"Expand",children:x?(0,r.jsx)(n.Vr3,{}):(0,r.jsx)(n.X6T,{})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[l?(0,r.jsx)(n.x$1,{className:"mr-2 text-blue-500"}):(0,r.jsx)(n.YXz,{className:"mr-2 text-blue-500"}),(0,r.jsx)("span",{className:"font-medium",children:s.name}),(0,r.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full ".concat(p),children:s.rank.name})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[(0,r.jsxs)("span",{className:"mr-3",children:["ID: ",s.id]}),(0,r.jsxs)("span",{className:"mr-3",children:["Downline: ",s._count.downline]}),!l&&(0,r.jsxs)("span",{children:["Level ",s.level]})]})]}),!l&&(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:["Level ",s.level]})})]}),u&&x&&(0,r.jsx)("div",{className:"mt-2 border-l-2 border-gray-200 pl-2",children:s.children.map(e=>(0,r.jsx)(i,{user:e,depth:c+1,maxDepth:d,initialExpandedLevels:o},e.id))})]})},c=e=>{let{data:s,maxDepth:l=10,initialExpandedLevels:n=2}=e,[t,c]=(0,a.useState)(""),[d,o]=(0,a.useState)(!1),x=(e,s)=>{if(e.name.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase()))return e;if(e.children&&e.children.length>0){let l=e.children.map(e=>x(e,s)).filter(Boolean);if(l.length>0)return{...e,children:l}}return null},m=t&&x(s,t)||s;return(0,r.jsxs)("div",{className:"genealogy-tree",children:[(0,r.jsxs)("div",{className:"mb-4 flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name or email...",value:t,onChange:e=>c(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pl-10"}),(0,r.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{onClick:()=>o(!d),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:d?"Collapse All":"Expand All"})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("div",{className:"mb-4 flex items-center",children:(0,r.jsxs)("div",{className:"flex space-x-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-gray-100 mr-1"}),(0,r.jsx)("span",{children:"Starter"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-yellow-100 mr-1"}),(0,r.jsx)("span",{children:"Bronze"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-gray-200 mr-1"}),(0,r.jsx)("span",{children:"Silver"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-yellow-200 mr-1"}),(0,r.jsx)("span",{children:"Gold"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-blue-100 mr-1"}),(0,r.jsx)("span",{children:"Platinum"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-purple-100 mr-1"}),(0,r.jsx)("span",{children:"Diamond"})]})]})}),(0,r.jsx)(i,{user:m,isRoot:!0,depth:0,maxDepth:l,initialExpandedLevels:d?l:n})]})]})}}}]);
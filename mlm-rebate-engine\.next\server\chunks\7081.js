"use strict";exports.id=7081,exports.ids=[7081],exports.modules={47081:(e,s,a)=>{a.d(s,{A:()=>o});var t=a(60687),r=a(43210),i=a(82136),l=a(85814),n=a.n(l),c=a(30474),d=a(16189),x=a(23877);function m(){let[e,s]=(0,r.useState)([]),[a,i]=(0,r.useState)(!1),[l,c]=(0,r.useState)(null),[d,m]=(0,r.useState)(!1),o=(0,r.useRef)(null),[h,j]=(0,r.useState)(0),f=async a=>{try{if(!(await fetch(`/api/admin/notifications/${a}/mark-read`,{method:"POST"})).ok)throw Error("Failed to mark notification as read");s(e.map(e=>e.id===a?{...e,isRead:!0}:e)),j(Math.max(0,h-1))}catch(e){console.error("Error marking notification as read:",e)}},u=async()=>{try{if(!(await fetch("/api/admin/notifications/mark-all-read",{method:"POST"})).ok)throw Error("Failed to mark all notifications as read");s(e.map(e=>({...e,isRead:!0}))),j(0)}catch(e){console.error("Error marking all notifications as read:",e)}},b=e=>{switch(e){case"low_stock":return(0,t.jsx)(x.BS8,{className:"text-amber-500"});case"system":return(0,t.jsx)(x.__w,{className:"text-blue-500"});default:return(0,t.jsx)(x.jNV,{className:"text-gray-500"})}},N=e=>{let s=new Date(e),a=new Date().getTime()-s.getTime(),t=Math.floor(a/6e4),r=Math.floor(a/36e5),i=Math.floor(a/864e5);return t<1?"Just now":t<60?`${t} minute${1===t?"":"s"} ago`:r<24?`${r} hour${1===r?"":"s"} ago`:i<7?`${i} day${1===i?"":"s"} ago`:s.toLocaleDateString()};return(0,t.jsxs)("div",{className:"relative",ref:o,children:[(0,t.jsxs)("button",{onClick:()=>m(!d),className:"relative p-1 text-gray-600 hover:text-gray-900 focus:outline-none","aria-label":"Notifications",children:[(0,t.jsx)(x.jNV,{className:"h-6 w-6"}),h>0&&(0,t.jsx)("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full",children:h>9?"9+":h})]}),d&&(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50",children:[(0,t.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200 flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-700",children:"Notifications"}),h>0&&(0,t.jsx)("button",{onClick:u,className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark all as read"})]}),(0,t.jsxs)("div",{className:"max-h-96 overflow-y-auto",children:[a&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)(x.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Loading notifications..."})]}),l&&(0,t.jsxs)("div",{className:"p-4 text-sm text-red-600",children:[(0,t.jsx)(x._Hm,{className:"inline mr-2"}),l]}),!a&&!l&&0===e.length&&(0,t.jsxs)("div",{className:"py-8 text-center",children:[(0,t.jsx)(x.jNV,{className:"mx-auto text-gray-300 text-3xl mb-2"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No notifications"})]}),e.map(e=>(0,t.jsx)("div",{className:`px-4 py-3 border-b border-gray-100 hover:bg-gray-50 ${!e.isRead?"bg-blue-50":""}`,children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0 mr-3 mt-1",children:b(e.type)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:N(e.createdAt)})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,t.jsxs)("div",{className:"mt-2 flex justify-between items-center",children:["low_stock"===e.type&&e.productId&&(0,t.jsxs)(n(),{href:`/admin/products?highlight=${e.productId}`,className:"text-xs text-blue-600 hover:text-blue-800 flex items-center",children:[(0,t.jsx)(x.rrY,{className:"mr-1"}),"View Product"]}),!e.isRead&&(0,t.jsxs)("button",{onClick:()=>f(e.id),className:"text-xs text-gray-500 hover:text-gray-700 flex items-center ml-auto",children:[(0,t.jsx)(x.CMH,{className:"mr-1"}),"Mark as read"]})]})]})]})},e.id))]}),(0,t.jsx)("div",{className:"px-4 py-2 border-t border-gray-200 text-center",children:(0,t.jsx)(n(),{href:"/admin/notifications",className:"text-xs text-blue-600 hover:text-blue-800",onClick:()=>m(!1),children:"View all notifications"})})]})]})}let o=({children:e})=>{let{data:s}=(0,i.useSession)(),a=(0,d.usePathname)(),[l,o]=(0,r.useState)(!1),[h,j]=(0,r.useState)(!1),f=[{name:"Dashboard",href:"/admin",icon:(0,t.jsx)(x.rQ8,{})},{name:"User Management",href:"/admin/users",icon:(0,t.jsx)(x.YXz,{})},{name:"Product Management",href:"/admin/products",icon:(0,t.jsx)(x.AsH,{})},{name:"Rebate Management",href:"/admin/rebates",icon:(0,t.jsx)(x.lcY,{})},{name:"Rebate Configurations",href:"/admin/rebate-configs",icon:(0,t.jsx)(x.gdQ,{})},{name:"Reports",href:"/admin/reports",icon:(0,t.jsx)(x.YYR,{})},{name:"Test Users",href:"/admin/test-users",icon:(0,t.jsx)(x.vWM,{})},{name:"Test Data",href:"/admin/test-data",icon:(0,t.jsx)(x.kkc,{})},{name:"Settings",href:"/admin/settings",icon:(0,t.jsx)(x.Pcn,{})}],u=e=>"/admin"===e?"/admin"===a:a?.startsWith(e),b=()=>{j(!h)};return(0,t.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,t.jsxs)("div",{className:`bg-white shadow-md hidden md:block transition-all duration-300 ${l?"w-20":"w-64"}`,children:[(0,t.jsxs)("div",{className:`p-4 border-b flex ${l?"justify-center":"justify-between"} items-center`,children:[!l&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,t.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Extreme Life Admin"})]}),l&&(0,t.jsx)("div",{className:"relative w-10 h-10",children:(0,t.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,t.jsx)("button",{onClick:()=>{o(!l)},className:"p-1 rounded-full hover:bg-gray-100",children:l?(0,t.jsx)(x.X6T,{}):(0,t.jsx)(x._Jj,{})})]}),(0,t.jsx)("nav",{className:"mt-4",children:(0,t.jsx)("ul",{children:f.map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)(n(),{href:e.href,className:`flex items-center px-4 py-3 ${u(e.href)?"bg-blue-50 text-blue-600 border-r-4 border-blue-600":"text-gray-700 hover:bg-blue-50 hover:text-blue-600"} ${l?"justify-center":""}`,children:[(0,t.jsx)("span",{className:`${l?"text-xl":"mr-3"}`,children:e.icon}),!l&&(0,t.jsx)("span",{children:e.name})]})},e.name))})}),(0,t.jsx)("div",{className:"absolute bottom-0 w-full border-t p-4",children:(0,t.jsxs)(n(),{href:"/dashboard",className:`flex items-center text-gray-700 hover:text-blue-600 ${l?"justify-center":""}`,children:[(0,t.jsx)(x.rQ8,{className:`${l?"text-xl":"mr-3"}`}),!l&&(0,t.jsx)("span",{children:"Back to Main App"})]})})]}),h&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:b}),(0,t.jsxs)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-md transform transition-transform duration-300 ease-in-out md:hidden ${h?"translate-x-0":"-translate-x-full"}`,children:[(0,t.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,t.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Extreme Life Admin"})]}),(0,t.jsx)("button",{onClick:b,className:"p-1 rounded-full hover:bg-gray-100",children:(0,t.jsx)(x.QCr,{})})]}),(0,t.jsx)("nav",{className:"mt-4",children:(0,t.jsx)("ul",{children:f.map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)(n(),{href:e.href,className:`flex items-center px-4 py-3 ${u(e.href)?"bg-blue-50 text-blue-600 border-r-4 border-blue-600":"text-gray-700 hover:bg-blue-50 hover:text-blue-600"}`,onClick:b,children:[(0,t.jsx)("span",{className:"mr-3",children:e.icon}),(0,t.jsx)("span",{children:e.name})]})},e.name))})}),(0,t.jsx)("div",{className:"absolute bottom-0 w-full border-t p-4",children:(0,t.jsxs)(n(),{href:"/dashboard",className:"flex items-center text-gray-700 hover:text-blue-600",onClick:b,children:[(0,t.jsx)(x.rQ8,{className:"mr-3"}),(0,t.jsx)("span",{children:"Back to Main App"})]})})]}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm",children:(0,t.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("button",{className:"p-2 rounded-md text-gray-500 md:hidden",onClick:b,children:(0,t.jsx)(x.OXb,{})}),(0,t.jsxs)(n(),{href:"/admin",className:"flex items-center ml-2 md:ml-0",children:[(0,t.jsx)("div",{className:"relative w-8 h-8 mr-2",children:(0,t.jsx)(c.default,{src:"/images/20250503.svg",alt:"Extreme Life Logo",fill:!0,className:"object-contain"})}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-blue-700",children:"Extreme Life Admin"})]})]}),(0,t.jsx)("div",{className:"flex items-center",children:s?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"mr-4",children:(0,t.jsx)(m,{})}),(0,t.jsxs)("div",{className:"hidden md:flex items-center mr-4 text-sm text-gray-700",children:[(0,t.jsx)(x.NBi,{className:"mr-2"}),(0,t.jsx)("span",{children:s.user?.name||s.user?.email})]}),(0,t.jsxs)(n(),{href:"/api/auth/signout",className:"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center",children:[(0,t.jsx)(x.axc,{className:"mr-2"}),(0,t.jsx)("span",{className:"hidden md:inline",children:"Sign Out"})]})]}):(0,t.jsx)(n(),{href:"/login",className:"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Sign In"})})]})})}),(0,t.jsx)("main",{className:"flex-1 overflow-auto bg-gray-100",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e})})]})]})}}};
(()=>{var e={};e.id=9093,e.ids=[9093],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>u,Nh:()=>p,aP:()=>l});var s=t(96330),o=t(13581),i=t(85663),n=t(55511),a=t.n(n);async function u(e){return await i.Ay.hash(e,10)}function l(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let p={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await i.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:n,...a}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(s,i,a):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(t(35426));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var s=t(96330);let o=global.prisma||new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50013:(e,r,t)=>{"use strict";t.d(r,{$W:()=>a,Uv:()=>i,Vy:()=>n,_1:()=>o});var s=t(31183);async function o(e=!0){return await s.z.shippingMethod.findMany({where:e?{isActive:!0}:void 0,orderBy:{name:"asc"}})}async function i(e){return await s.z.shippingMethod.findUnique({where:{id:e}})}async function n(e,r){try{let t=await i(e);if(!t)return{isValid:!1,errors:["Shipping method not found"]};if(!t.requiresDetails||!t.detailsSchema)return{isValid:!0};let s=JSON.parse(t.detailsSchema),o=[];if(s.required&&Array.isArray(s.required))for(let e of s.required)r[e]||o.push(`${e} is required`);if(s.properties&&"object"==typeof s.properties)for(let[e,t]of Object.entries(s.properties))void 0!==r[e]&&("number"===t.type&&"number"!=typeof r[e]?o.push(`${e} must be a number`):"string"===t.type&&"string"!=typeof r[e]?o.push(`${e} must be a string`):"boolean"===t.type&&"boolean"!=typeof r[e]&&o.push(`${e} must be a boolean`));return{isValid:0===o.length,errors:o.length>0?o:void 0}}catch(e){return console.error("Error validating shipping details:",e),{isValid:!1,errors:["Error validating shipping details"]}}}async function a(e,r,t,s){try{let r=await i(e);if(!r)throw Error("Shipping method not found");if("pickup"===r.code)return 0;return r.baseFee}catch(e){throw console.error("Error calculating shipping fee:",e),e}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84495:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>c});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),u=t(19854),l=t(12909),p=t(50013);async function c(e){try{let r=await (0,u.getServerSession)(l.Nh);if(!r||!r.user)return a.NextResponse.json({error:"You must be logged in to view shipping methods"},{status:401});let t=new URL(e.url),s=t.searchParams.get("id"),o=t.searchParams.get("activeOnly");if(s){let e=parseInt(s);if(isNaN(e))return a.NextResponse.json({error:"Invalid shipping method ID"},{status:400});let r=await (0,p.Uv)(e);if(!r)return a.NextResponse.json({error:"Shipping method not found"},{status:404});return a.NextResponse.json(r)}let i=await (0,p._1)("false"!==o);return a.NextResponse.json(i)}catch(e){return console.error("Error fetching shipping methods:",e),a.NextResponse.json({error:"Failed to fetch shipping methods"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/shipping-methods/route",pathname:"/api/shipping-methods",filename:"route",bundlePath:"app/api/shipping-methods/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping-methods\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:g}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(84495));module.exports=s})();
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1694],{6232:(e,t,n)=>{n.d(t,{Y:()=>l});var r=n(12115),o=n(21231);function l(e){let t=(0,r.useRef)(e);return(0,o.s)(()=>{t.current=e},[e]),t}},7856:(e,t,n)=>{n.d(t,{_:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},13250:(e,t,n)=>{n.d(t,{a:()=>l});var r=n(12115),o=n(21231);function l(){let e=(0,r.useRef)(!1);return(0,o.s)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},15939:(e,t,n)=>{n.d(t,{e:()=>R,_:()=>x});var r,o,l=n(12115),i=n(48014),a=n(30797),u=n(13250),s=n(21231),c=n(6232),d=n(89925),f=n(47769),m=n(45261),p=n(87358);void 0!==p&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(r=null==p?void 0:p.env)?void 0:r.NODE_ENV)==="test"&&void 0===(null==(o=null==Element?void 0:Element.prototype)?void 0:o.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn("Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\nPlease install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\n\nExample usage:\n```js\nimport { mockAnimationsApi } from 'jsdom-testing-mocks'\nmockAnimationsApi()\n```"),[]});var v=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(v||{}),h=n(91525),g=n(20379),E=n(27279),b=n(84554);function w(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:A)!==l.Fragment||1===l.Children.count(e.children)}let y=(0,l.createContext)(null);y.displayName="TransitionContext";var F=(e=>(e.Visible="visible",e.Hidden="hidden",e))(F||{});let P=(0,l.createContext)(null);function C(e){return"children"in e?C(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function T(e,t){let n=(0,c.Y)(e),r=(0,l.useRef)([]),o=(0,u.a)(),s=(0,i.L)(),d=(0,a._)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:b.mK.Hidden,l=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==l&&((0,E.Y)(t,{[b.mK.Unmount](){r.current.splice(l,1)},[b.mK.Hidden](){r.current[l].state="hidden"}}),s.microTask(()=>{var e;!C(r)&&o.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,a._)(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,b.mK.Unmount)}),m=(0,l.useRef)([]),p=(0,l.useRef)(Promise.resolve()),v=(0,l.useRef)({enter:[],leave:[]}),h=(0,a._)((e,n,r)=>{m.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{m.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(v.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?p.current=p.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),g=(0,a._)((e,t,n)=>{Promise.all(v.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=m.current.shift())||e()}).then(()=>n(t))});return(0,l.useMemo)(()=>({children:r,register:f,unregister:d,onStart:h,onStop:g,wait:p,chains:v}),[f,d,r,h,g,v,p])}P.displayName="NestingContext";let A=l.Fragment,S=b.Ac.RenderStrategy,L=(0,b.FX)(function(e,t){let{show:n,appear:r=!1,unmount:o=!0,...i}=e,u=(0,l.useRef)(null),c=w(e),m=(0,f.P)(...c?[u,t]:null===t?[]:[t]);(0,d.g)();let p=(0,h.O_)();if(void 0===n&&null!==p&&(n=(p&h.Uw.Open)===h.Uw.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[v,g]=(0,l.useState)(n?"visible":"hidden"),E=T(()=>{n||g("hidden")}),[F,A]=(0,l.useState)(!0),L=(0,l.useRef)([n]);(0,s.s)(()=>{!1!==F&&L.current[L.current.length-1]!==n&&(L.current.push(n),A(!1))},[L,n]);let x=(0,l.useMemo)(()=>({show:n,appear:r,initial:F}),[n,r,F]);(0,s.s)(()=>{n?g("visible"):C(E)||null===u.current||g("hidden")},[n,E]);let R={unmount:o},k=(0,a._)(()=>{var t;F&&A(!1),null==(t=e.beforeEnter)||t.call(e)}),N=(0,a._)(()=>{var t;F&&A(!1),null==(t=e.beforeLeave)||t.call(e)}),M=(0,b.Ci)();return l.createElement(P.Provider,{value:E},l.createElement(y.Provider,{value:x},M({ourProps:{...R,as:l.Fragment,children:l.createElement(O,{ref:m,...R,...i,beforeEnter:k,beforeLeave:N})},theirProps:{},defaultTag:l.Fragment,features:S,visible:"visible"===v,name:"Transition"})))}),O=(0,b.FX)(function(e,t){var n,r;let{transition:o=!0,beforeEnter:u,afterEnter:c,beforeLeave:p,afterLeave:v,enter:F,enterFrom:L,enterTo:O,entered:x,leave:R,leaveFrom:k,leaveTo:N,...M}=e,[_,D]=(0,l.useState)(null),I=(0,l.useRef)(null),H=w(e),j=(0,f.P)(...H?[I,t,D]:null===t?[]:[t]),U=null==(n=M.unmount)||n?b.mK.Unmount:b.mK.Hidden,{show:Y,appear:X,initial:K}=function(){let e=(0,l.useContext)(y);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[V,W]=(0,l.useState)(Y?"visible":"hidden"),B=function(){let e=(0,l.useContext)(P);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:q,unregister:G}=B;(0,s.s)(()=>q(I),[q,I]),(0,s.s)(()=>{if(U===b.mK.Hidden&&I.current)return Y&&"visible"!==V?void W("visible"):(0,E.Y)(V,{hidden:()=>G(I),visible:()=>q(I)})},[V,I,q,G,Y,U]);let $=(0,d.g)();(0,s.s)(()=>{if(H&&$&&"visible"===V&&null===I.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[I,V,$,H]);let z=K&&!X,Z=X&&Y&&K,J=(0,l.useRef)(!1),Q=T(()=>{J.current||(W("hidden"),G(I))},B),ee=(0,a._)(e=>{J.current=!0,Q.onStart(I,e?"enter":"leave",e=>{"enter"===e?null==u||u():"leave"===e&&(null==p||p())})}),et=(0,a._)(e=>{let t=e?"enter":"leave";J.current=!1,Q.onStop(I,t,e=>{"enter"===e?null==c||c():"leave"===e&&(null==v||v())}),"leave"!==t||C(Q)||(W("hidden"),G(I))});(0,l.useEffect)(()=>{H&&o||(ee(Y),et(Y))},[Y,H,o]);let[,en]=function(e,t,n,r){let[o,a]=(0,l.useState)(n),{hasFlag:u,addFlag:c,removeFlag:d}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,l.useState)(e),r=(0,l.useCallback)(e=>n(e),[t]),o=(0,l.useCallback)(e=>n(t=>t|e),[t]),i=(0,l.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:o,hasFlag:i,removeFlag:(0,l.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,l.useCallback)(e=>n(t=>t^e),[n])}}(e&&o?3:0),f=(0,l.useRef)(!1),p=(0,l.useRef)(!1),v=(0,i.L)();return(0,s.s)(()=>{var o;if(e){if(n&&a(!0),!t){n&&c(3);return}return null==(o=null==r?void 0:r.start)||o.call(r,n),function(e,t){let{prepare:n,run:r,done:o,inFlight:l}=t,i=(0,m.e)();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return r();let o=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=o}(e,{prepare:n,inFlight:l}),i.nextFrame(()=>{r(),i.requestAnimationFrame(()=>{i.add(function(e,t){var n,r;let o=(0,m.e)();if(!e)return o.dispose;let l=!1;o.add(()=>{l=!0});let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===i.length?t():Promise.allSettled(i.map(e=>e.finished)).then(()=>{l||t()}),o.dispose}(e,o))})}),i.dispose}(t,{inFlight:f,prepare(){p.current?p.current=!1:p.current=f.current,f.current=!0,p.current||(n?(c(3),d(4)):(c(4),d(2)))},run(){p.current?n?(d(3),c(4)):(d(4),c(3)):n?d(1):c(1)},done(){var e;p.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(f.current=!1,d(7),n||a(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,v]),e?[o,{closed:u(1),enter:u(2),leave:u(4),transition:u(2)||u(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!o||!H||!$||z),_,Y,{start:ee,end:et}),er=(0,b.oE)({ref:j,className:(null==(r=(0,g.x)(M.className,Z&&F,Z&&L,en.enter&&F,en.enter&&en.closed&&L,en.enter&&!en.closed&&O,en.leave&&R,en.leave&&!en.closed&&k,en.leave&&en.closed&&N,!en.transition&&Y&&x))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}(en)}),eo=0;"visible"===V&&(eo|=h.Uw.Open),"hidden"===V&&(eo|=h.Uw.Closed),Y&&"hidden"===V&&(eo|=h.Uw.Opening),Y||"visible"!==V||(eo|=h.Uw.Closing);let el=(0,b.Ci)();return l.createElement(P.Provider,{value:Q},l.createElement(h.El,{value:eo},el({ourProps:er,theirProps:M,defaultTag:A,features:S,visible:"visible"===V,name:"Transition.Child"})))}),x=(0,b.FX)(function(e,t){let n=null!==(0,l.useContext)(y),r=null!==(0,h.O_)();return l.createElement(l.Fragment,null,!n&&r?l.createElement(L,{ref:t,...e}):l.createElement(O,{ref:t,...e}))}),R=Object.assign(L,{Child:x,Root:L})},20379:(e,t,n)=>{n.d(t,{x:()=>r});function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},21231:(e,t,n)=>{n.d(t,{s:()=>l});var r=n(12115),o=n(87657);let l=(e,t)=>{o._.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},27279:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e,t){for(var n=arguments.length,o=Array(n>2?n-2:0),l=2;l<n;l++)o[l-2]=arguments[l];if(e in t){let n=t[e];return"function"==typeof n?n(...o):n}let i=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(i,r),i}},30797:(e,t,n)=>{n.d(t,{_:()=>l});var r=n(12115),o=n(6232);let l=function(e){let t=(0,o.Y)(e);return r.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[t])}},35695:(e,t,n)=>{var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},45261:(e,t,n)=>{n.d(t,{e:()=>function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];let l={current:!0};return(0,r._)(()=>{l.current&&t[0]()}),n.add(()=>{l.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(7856)},47769:(e,t,n)=>{n.d(t,{P:()=>a,a:()=>i});var r=n(12115),o=n(30797);let l=Symbol();function i(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[l]:t})}function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let i=(0,r.useRef)(t);(0,r.useEffect)(()=>{i.current=t},[t]);let a=(0,o._)(e=>{for(let t of i.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[l]))?void 0:a}},48014:(e,t,n)=>{n.d(t,{L:()=>l});var r=n(12115),o=n(45261);function l(){let[e]=(0,r.useState)(o.e);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},84554:(e,t,n)=>{n.d(t,{Ac:()=>i,Ci:()=>u,FX:()=>f,mK:()=>a,oE:()=>m});var r=n(12115),o=n(20379),l=n(27279),i=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(i||{}),a=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(a||{});function u(){let e,t,n=(e=(0,r.useRef)([]),t=(0,r.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.every(e=>null==e))return e.current=r,t});return(0,r.useCallback)(e=>(function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:o,features:i,visible:a=!0,name:u,mergeRefs:f}=e;f=null!=f?f:c;let m=d(n,t);if(a)return s(m,r,o,u,f);let p=null!=i?i:0;if(2&p){let{static:e=!1,...t}=m;if(e)return s(t,r,o,u,f)}if(1&p){let{unmount:e=!0,...t}=m;return(0,l.Y)(+!e,{0:()=>null,1:()=>s({...t,hidden:!0,style:{display:"none"}},r,o,u,f)})}return s(m,r,o,u,f)})({mergeRefs:n,...e}),[n])}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,l=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,{as:a=n,children:u,refName:s="ref",...c}=p(e,["unmount","static"]),f=void 0!==e.ref?{[s]:e.ref}:{},v="function"==typeof u?u(t):u;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let h={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e)for(let e of(h["data-headlessui-state"]=n.join(" "),n))h["data-".concat(e)]=""}if(a===r.Fragment&&(Object.keys(m(c)).length>0||Object.keys(m(h)).length>0))if(!(0,r.isValidElement)(v)||Array.isArray(v)&&v.length>1){if(Object.keys(m(c)).length>0)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(l,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(m(c)).concat(Object.keys(m(h))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}else{var g;let e=v.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0,o.x)(t(...n),c.className)}:(0,o.x)(t,c.className),l=d(v.props,m(p(c,["ref"])));for(let e in h)e in l&&delete h[e];return(0,r.cloneElement)(v,Object.assign({},l,h,f,{ref:i((g=v,r.version.split(".")[0]>="19"?g.props.ref:g.ref),f.ref)},n?{className:n}:{}))}return(0,r.createElement)(a,Object.assign({},p(c,["ref"]),a!==r.Fragment&&f,a!==r.Fragment&&h),v)}function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function d(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])for(let e in o)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(o[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in o)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];for(let n of o[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function f(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function m(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function p(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},87657:(e,t,n)=>{n.d(t,{_:()=>a});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class i{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}}let a=new i},89925:(e,t,n)=>{n.d(t,{g:()=>i});var r,o=n(12115),l=n(87657);function i(){let e,t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,a]=o.useState(l._.isHandoffComplete);return i&&!1===l._.isHandoffComplete&&a(!1),o.useEffect(()=>{!0!==i&&a(!0)},[i]),o.useEffect(()=>l._.handoff(),[]),!t&&i}},91525:(e,t,n)=>{n.d(t,{$x:()=>u,El:()=>a,O_:()=>i,Uw:()=>l});var r=n(12115);let o=(0,r.createContext)(null);o.displayName="OpenClosedContext";var l=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(l||{});function i(){return(0,r.useContext)(o)}function a(e){let{value:t,children:n}=e;return r.createElement(o.Provider,{value:t},n)}function u(e){let{children:t}=e;return r.createElement(o.Provider,{value:null},t)}},98212:(e,t,n)=>{n.d(t,{lG:()=>eS});var r=n(12115),o=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(o||{}),l=n(6232);function i(e,t,n,o){let i=(0,l.Y)(n);(0,r.useEffect)(()=>{function n(e){i.current(e)}return(e=null!=e?e:window).addEventListener(t,n,o),()=>e.removeEventListener(t,n,o)},[e,t,o])}class a extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}constructor(e){super(),this.factory=e}}function u(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var o=arguments.length,l=Array(o>1?o-1:0),i=1;i<o;i++)l[i-1]=arguments[i];let a=t[e].call(n,...l);a&&(n=a,r.forEach(e=>e()))}}}var s=n(21231);function c(e){return(0,r.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}let d=new a(()=>u(()=>[],{ADD(e){return this.includes(e)?this:[...this,e]},REMOVE(e){let t=this.indexOf(e);if(-1===t)return this;let n=this.slice();return n.splice(t,1),n}}));function f(e,t){let n=d.get(t),o=(0,r.useId)(),l=c(n);if((0,s.s)(()=>{if(e)return n.dispatch("ADD",o),()=>n.dispatch("REMOVE",o)},[n,e]),!e)return!1;let i=l.indexOf(o),a=l.length;return -1===i&&(i=a,a+=1),i===a-1}var m=n(30797),p=n(45261),v=n(87657);function h(e){var t,n;return v._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let g=new Map,E=new Map;function b(e){var t;let n=null!=(t=E.get(e))?t:0;return E.set(e,n+1),0!==n||(g.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=E.get(e))?t:1;if(1===n?E.delete(e):E.set(e,n-1),1!==n)return;let r=g.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,g.delete(e))})(e)}var w=n(27279);let y=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(","),F=["[data-autofocus]"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var P=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(P||{}),C=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(C||{}),T=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(T||{}),A=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(A||{}),S=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(S||{});function L(e){null==e||e.focus({preventScroll:!0})}function O(e,t){var n,r,o;let{sorted:l=!0,relativeTo:i=null,skipElements:a=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?l?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(F)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(y)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);a.length>0&&s.length>1&&(s=s.filter(e=>!a.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),i=null!=i?i:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(i))-1;if(4&t)return Math.max(0,s.indexOf(i))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},m=0,p=s.length,v;do{if(m>=p||m+p<=0)return 0;let e=d+m;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}null==(v=s[e])||v.focus(f),m+=c}while(v!==u.activeElement);return 6&t&&null!=(o=null==(r=null==(n=v)?void 0:n.matches)?void 0:r.call(n,"textarea,input"))&&o&&v.select(),2}function x(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function R(e,t,n,o){let i=(0,l.Y)(n);(0,r.useEffect)(()=>{if(e)return document.addEventListener(t,n,o),()=>document.removeEventListener(t,n,o);function n(e){i.current(e)}},[e,t,o])}function k(e,t,n,o){let i=(0,l.Y)(n);(0,r.useEffect)(()=>{if(e)return window.addEventListener(t,n,o),()=>window.removeEventListener(t,n,o);function n(e){i.current(e)}},[e,t,o])}function N(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>h(...t),[...t])}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var M=n(84554),_=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(_||{});let D=(0,M.FX)(function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,M.Ci)()({ourProps:l,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}),I=(0,r.createContext)(null);function H(e){let{children:t,node:n}=e,[o,l]=(0,r.useState)(null),i=j(null!=n?n:o);return r.createElement(I.Provider,{value:i},t,null===i&&r.createElement(D,{features:_.Hidden,ref:e=>{var t,n;if(e){for(let r of null!=(n=null==(t=h(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&r instanceof HTMLElement&&null!=r&&r.contains(e)){l(r);break}}}}))}function j(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!=(e=(0,r.useContext)(I))?e:t}let U=u(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,p.e)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let t,{doc:n,d:r,meta:o}=e,l={doc:n,d:r,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(o)},i=[x()?{before(e){let{doc:t,d:n,meta:r}=e;function o(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=(0,p.e)();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,l=null;n.addEventListener(t,"click",e=>{if(e.target instanceof HTMLElement)try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),i=t.querySelector(r);i&&!o(i)&&(l=i)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{if(e.target instanceof HTMLElement)if(o(e.target)){let t=e.target;for(;t.parentElement&&o(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if(e.target instanceof HTMLElement&&"INPUT"!==e.target.tagName)if(o(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()},{passive:!1}),n.add(()=>{var e;r!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,r),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before(e){var n;let{doc:r}=e,o=r.documentElement;t=Math.max(0,(null!=(n=r.defaultView)?n:window).innerWidth-o.clientWidth)},after(e){let{doc:n,d:r}=e,o=n.documentElement,l=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,t-l);r.style(o,"paddingRight","".concat(i,"px"))}},{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];i.forEach(e=>{let{before:t}=e;return null==t?void 0:t(l)}),i.forEach(e=>{let{after:t}=e;return null==t?void 0:t(l)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});U.subscribe(()=>{let e=U.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&U.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&U.dispatch("TEARDOWN",n)}});var Y=n(89925),X=n(47769);let K=(0,r.createContext)(()=>{});function V(e){let{value:t,children:n}=e;return r.createElement(K.Provider,{value:t},n)}var W=n(91525);let B=(0,r.createContext)(!1);function q(e){return r.createElement(B.Provider,{value:e.force},e.children)}let G=(0,r.createContext)(void 0),$=(0,r.createContext)(null);$.displayName="DescriptionContext";let z=Object.assign((0,M.FX)(function(e,t){let n=(0,r.useId)(),o=(0,r.useContext)(G),{id:l="headlessui-description-".concat(n),...i}=e,a=function e(){let t=(0,r.useContext)($);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),u=(0,X.P)(t);(0,s.s)(()=>a.register(l),[l,a.register]);let c=o||!1,d=(0,r.useMemo)(()=>({...a.slot,disabled:c}),[a.slot,c]),f={ref:u,...a.props,id:l};return(0,M.Ci)()({ourProps:f,theirProps:i,slot:d,defaultTag:"p",name:a.name||"Description"})}),{});var Z=n(48014),J=n(13250),Q=n(7856);function ee(e){let t=(0,m._)(e),n=(0,r.useRef)(!1);(0,r.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,Q._)(()=>{n.current&&t()})}),[t])}var et=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(et||{});function en(e,t){let n=(0,r.useRef)([]),o=(0,m._)(e);(0,r.useEffect)(()=>{let e=[...n.current];for(let[r,l]of t.entries())if(n.current[r]!==l){let r=o(t,e);return n.current=t,r}},[o,...t])}let er=[];function eo(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!(e.target instanceof HTMLElement)||e.target===document.body||er[0]===e.target)return;let t=e.target;t=t.closest(y),er.unshift(null!=t?t:e.target),(er=er.filter(e=>null!=e&&e.isConnected)).splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var el=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(el||{});let ei=Object.assign((0,M.FX)(function(e,t){let n,o=(0,r.useRef)(null),l=(0,X.P)(o,t),{initialFocus:a,initialFocusFallback:u,containers:s,features:c=15,...d}=e;(0,Y.g)()||(c=0);let p=N(o);!function(e,t){let{ownerDocument:n}=t,o=!!(8&e),l=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,r.useRef)(er.slice());return en((e,n)=>{let[r]=e,[o]=n;!0===o&&!1===r&&(0,Q._)(()=>{t.current.splice(0)}),!1===o&&!0===r&&(t.current=er.slice())},[e,er,t]),(0,m._)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(o);en(()=>{o||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&L(l())},[o]),ee(()=>{o&&L(l())})}(c,{ownerDocument:p});let v=function(e,t){let{ownerDocument:n,container:o,initialFocus:l,initialFocusFallback:i}=t,a=(0,r.useRef)(null),u=f(!!(1&e),"focus-trap#initial-focus"),s=(0,J.a)();return en(()=>{if(0===e)return;if(!u){null!=i&&i.current&&L(i.current);return}let t=o.current;t&&(0,Q._)(()=>{if(!s.current)return;let r=null==n?void 0:n.activeElement;if(null!=l&&l.current){if((null==l?void 0:l.current)===r){a.current=r;return}}else if(t.contains(r)){a.current=r;return}if(null!=l&&l.current)L(l.current);else{if(16&e){if(O(t,P.First|P.AutoFocus)!==C.Error)return}else if(O(t,P.First)!==C.Error)return;if(null!=i&&i.current&&(L(i.current),(null==n?void 0:n.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}a.current=null==n?void 0:n.activeElement})},[i,u,e]),a}(c,{ownerDocument:p,container:o,initialFocus:a,initialFocusFallback:u});!function(e,t){let{ownerDocument:n,container:r,containers:o,previousActiveElement:l}=t,a=(0,J.a)(),u=!!(4&e);i(null==n?void 0:n.defaultView,"focus",e=>{if(!u||!a.current)return;let t=eo(o);r.current instanceof HTMLElement&&t.add(r.current);let n=l.current;if(!n)return;let i=e.target;i&&i instanceof HTMLElement?ea(t,i)?(l.current=i,L(i)):(e.preventDefault(),e.stopPropagation(),L(n)):L(l.current)},!0)}(c,{ownerDocument:p,container:o,containers:s,previousActiveElement:v});let h=(n=(0,r.useRef)(0),k(!0,"keydown",e=>{"Tab"===e.key&&(n.current=+!!e.shiftKey)},!0),n),g=(0,m._)(e=>{let t=o.current;t&&(0,w.Y)(h.current,{[et.Forwards]:()=>{O(t,P.First,{skipElements:[e.relatedTarget,u]})},[et.Backwards]:()=>{O(t,P.Last,{skipElements:[e.relatedTarget,u]})}})}),E=f(!!(2&c),"focus-trap#tab-lock"),b=(0,Z.L)(),y=(0,r.useRef)(!1),F=(0,M.Ci)();return r.createElement(r.Fragment,null,E&&r.createElement(D,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:g,features:_.Focusable}),F({ourProps:{ref:l,onKeyDown(e){"Tab"==e.key&&(y.current=!0,b.requestAnimationFrame(()=>{y.current=!1}))},onBlur(e){if(!(4&c))return;let t=eo(s);o.current instanceof HTMLElement&&t.add(o.current);let n=e.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(ea(t,n)||(y.current?O(o.current,(0,w.Y)(h.current,{[et.Forwards]:()=>P.Next,[et.Backwards]:()=>P.Previous})|P.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&L(e.target)))}},theirProps:d,defaultTag:"div",name:"FocusTrap"}),E&&r.createElement(D,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:g,features:_.Focusable}))}),{features:el});function ea(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var eu=n(47650);let es=r.Fragment,ec=(0,M.FX)(function(e,t){let{ownerDocument:n=null,...o}=e,l=(0,r.useRef)(null),i=(0,X.P)((0,X.a)(e=>{l.current=e}),t),a=N(l),u=null!=n?n:a,c=function(e){let t=(0,r.useContext)(B),n=(0,r.useContext)(ef),[o,l]=(0,r.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(v._.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)});return(0,r.useEffect)(()=>{null!==o&&(null!=e&&e.body.contains(o)||null==e||e.body.appendChild(o))},[o,e]),(0,r.useEffect)(()=>{t||null!==n&&l(n.current)},[n,l,t]),o}(u),[d]=(0,r.useState)(()=>{var e;return v._.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null}),f=(0,r.useContext)(em),m=(0,Y.g)();(0,s.s)(()=>{!c||!d||c.contains(d)||(d.setAttribute("data-headlessui-portal",""),c.appendChild(d))},[c,d]),(0,s.s)(()=>{if(d&&f)return f.register(d)},[f,d]),ee(()=>{var e;c&&d&&(d instanceof Node&&c.contains(d)&&c.removeChild(d),c.childNodes.length<=0&&(null==(e=c.parentElement)||e.removeChild(c)))});let p=(0,M.Ci)();return m&&c&&d?(0,eu.createPortal)(p({ourProps:{ref:i},theirProps:o,slot:{},defaultTag:es,name:"Portal"}),d):null}),ed=r.Fragment,ef=(0,r.createContext)(null),em=(0,r.createContext)(null),ep=(0,M.FX)(function(e,t){let n=(0,X.P)(t),{enabled:o=!0,ownerDocument:l,...i}=e,a=(0,M.Ci)();return o?r.createElement(ec,{...i,ownerDocument:l,ref:n}):a({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:es,name:"Portal"})}),ev=(0,M.FX)(function(e,t){let{target:n,...o}=e,l={ref:(0,X.P)(t)},i=(0,M.Ci)();return r.createElement(ef.Provider,{value:n},i({ourProps:l,theirProps:o,defaultTag:ed,name:"Popover.Group"}))}),eh=Object.assign(ep,{Group:ev});var eg=n(15939),eE=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(eE||{}),eb=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(eb||{});let ew={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},ey=(0,r.createContext)(null);function eF(e){let t=(0,r.useContext)(ey);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,eF),t}return t}function eP(e,t){return(0,w.Y)(t.type,ew,e,t)}ey.displayName="DialogContext";let eC=(0,M.FX)(function(e,t){let n,a,u,d,v,g,E,F,P,C,T,S=(0,r.useId)(),{id:L="headlessui-dialog-".concat(S),open:O,onClose:_,initialFocus:D,role:I="dialog",autoFocus:H=!0,__demoMode:K=!1,unmount:B=!1,...G}=e,z=(0,r.useRef)(!1);I="dialog"===I||"alertdialog"===I?I:(z.current||(z.current=!0,console.warn("Invalid role [".concat(I,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let Z=(0,W.O_)();void 0===O&&null!==Z&&(O=(Z&W.Uw.Open)===W.Uw.Open);let J=(0,r.useRef)(null),Q=(0,X.P)(J,t),ee=N(J),et=+!O,[en,er]=(0,r.useReducer)(eP,{titleId:null,descriptionId:null,panelRef:(0,r.createRef)()}),eo=(0,m._)(()=>_(!1)),ea=(0,m._)(e=>er({type:0,id:e})),eu=!!(0,Y.g)()&&0===et,[es,ec]=(n=(0,r.useContext)(em),a=(0,r.useRef)([]),u=(0,m._)(e=>(a.current.push(e),n&&n.register(e),()=>d(e))),d=(0,m._)(e=>{let t=a.current.indexOf(e);-1!==t&&a.current.splice(t,1),n&&n.unregister(e)}),v=(0,r.useMemo)(()=>({register:u,unregister:d,portals:a}),[u,d,a]),[a,(0,r.useMemo)(()=>function(e){let{children:t}=e;return r.createElement(em.Provider,{value:v},t)},[v])]),ed=j(),{resolveContainers:ef}=function(){let{defaultContainers:e=[],portals:t,mainTreeNode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=N(n),o=(0,m._)(()=>{var o,l;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"current"in t&&t.current instanceof HTMLElement&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(l=null==n?void 0:n.getRootNode())?void 0:l.host))||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:o,contains:(0,m._)(e=>o().some(t=>t.contains(e)))}}({mainTreeNode:ed,portals:es,defaultContainers:[{get current(){var ep;return null!=(ep=en.panelRef.current)?ep:J.current}}]}),eg=null!==Z&&(Z&W.Uw.Closing)===W.Uw.Closing;(function(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=f(e,"inert-others");(0,s.s)(()=>{var e,o;if(!r)return;let l=(0,p.e)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(b(t));let i=null!=(o=null==t?void 0:t())?o:[];for(let e of i){if(!e)continue;let t=h(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)i.some(t=>e.contains(t))||l.add(b(e));n=n.parentElement}}return l.dispose},[r,t,n])})(!K&&!eg&&eu,{allowed:(0,m._)(()=>{var e,t;return[null!=(t=null==(e=J.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,m._)(()=>{var e;return[null!=(e=null==ed?void 0:ed.closest("body > *:not(#headlessui-portal-root)"))?e:null]})}),g=f(eu,"outside-click"),E=(0,l.Y)(e=>{e.preventDefault(),eo()}),F=(0,r.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(ef))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e!==(null==(t=h(e))?void 0:t.body)&&(0,w.Y)(n,{0:()=>e.matches(y),1(){let t=e;for(;null!==t;){if(t.matches(y))return!0;t=t.parentElement}return!1}})}(n,A.Loose)||-1===n.tabIndex||e.preventDefault(),E.current(e,n)}},[E,ef]),P=(0,r.useRef)(null),R(g,"pointerdown",e=>{var t,n;P.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target},!0),R(g,"mousedown",e=>{var t,n;P.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target},!0),R(g,"click",e=>{x()||/Android/gi.test(window.navigator.userAgent)||P.current&&(F(e,()=>P.current),P.current=null)},!0),C=(0,r.useRef)({x:0,y:0}),R(g,"touchstart",e=>{C.current.x=e.touches[0].clientX,C.current.y=e.touches[0].clientY},!0),R(g,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-C.current.x)>=30||Math.abs(t.y-C.current.y)>=30))return F(e,()=>e.target instanceof HTMLElement?e.target:null)},!0),k(g,"blur",e=>F(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0),function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"!=typeof document?document.defaultView:null,n=arguments.length>2?arguments[2]:void 0,r=f(e,"escape");i(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===o.Escape&&n(e))})}(eu,null==ee?void 0:ee.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),eo()}),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=c(U),o=t?r.get(t):void 0;o&&o.count,(0,s.s)(()=>{if(!(!t||!e))return U.dispatch("PUSH",t,n),()=>U.dispatch("POP",t,n)},[e,t])}(f(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!K&&!eg&&eu,ee,ef),T=(0,l.Y)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&eo()}),(0,r.useEffect)(()=>{if(!eu)return;let e=null===J?null:J instanceof HTMLElement?J:J.current;if(!e)return;let t=(0,p.e)();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>T.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>T.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[J,T,eu]);let[eE,eb]=function(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)(()=>function(e){let n=(0,m._)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),o=(0,r.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return r.createElement($.Provider,{value:o},e.children)},[t])]}(),ew=(0,r.useMemo)(()=>[{dialogState:et,close:eo,setTitleId:ea,unmount:B},en],[et,en,eo,ea,B]),eF=(0,r.useMemo)(()=>({open:0===et}),[et]),eC={ref:Q,id:L,role:I,tabIndex:-1,"aria-modal":K?void 0:0===et||void 0,"aria-labelledby":en.titleId,"aria-describedby":eE,unmount:B},eS=!function(){var e;let[t]=(0,r.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,o]=(0,r.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,s.s)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){o(e.matches)}},[t]),n}(),eL=el.None;eu&&!K&&(eL|=el.RestoreFocus,eL|=el.TabLock,H&&(eL|=el.AutoFocus),eS&&(eL|=el.InitialFocus));let eO=(0,M.Ci)();return r.createElement(W.$x,null,r.createElement(q,{force:!0},r.createElement(eh,null,r.createElement(ey.Provider,{value:ew},r.createElement(ev,{target:J},r.createElement(q,{force:!1},r.createElement(eb,{slot:eF},r.createElement(ec,null,r.createElement(ei,{initialFocus:D,initialFocusFallback:J,containers:ef,features:eL},r.createElement(V,{value:eo},eO({ourProps:eC,theirProps:G,slot:eF,defaultTag:eT,features:eA,visible:0===et,name:"Dialog"})))))))))))}),eT="div",eA=M.Ac.RenderStrategy|M.Ac.Static,eS=Object.assign((0,M.FX)(function(e,t){let{transition:n=!1,open:o,...l}=e,i=(0,W.O_)(),a=e.hasOwnProperty("open")||null!==i,u=e.hasOwnProperty("onClose");if(!a&&!u)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&"boolean"!=typeof e.open)throw Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(e.open));if("function"!=typeof e.onClose)throw Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(e.onClose));return(void 0!==o||n)&&!l.static?r.createElement(H,null,r.createElement(eg.e,{show:o,transition:n,unmount:l.unmount},r.createElement(eC,{ref:t,...l}))):r.createElement(H,null,r.createElement(eC,{ref:t,open:o,...l}))}),{Panel:(0,M.FX)(function(e,t){let n=(0,r.useId)(),{id:o="headlessui-dialog-panel-".concat(n),transition:l=!1,...i}=e,[{dialogState:a,unmount:u},s]=eF("Dialog.Panel"),c=(0,X.P)(t,s.panelRef),d=(0,r.useMemo)(()=>({open:0===a}),[a]),f=(0,m._)(e=>{e.stopPropagation()}),p=l?eg._:r.Fragment,v=(0,M.Ci)();return r.createElement(p,{...l?{unmount:u}:{}},v({ourProps:{ref:c,id:o,onClick:f},theirProps:i,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,M.FX)(function(e,t){let{transition:n=!1,...o}=e,[{dialogState:l,unmount:i}]=eF("Dialog.Backdrop"),a=(0,r.useMemo)(()=>({open:0===l}),[l]),u=n?eg._:r.Fragment,s=(0,M.Ci)();return r.createElement(u,{...n?{unmount:i}:{}},s({ourProps:{ref:t,"aria-hidden":!0},theirProps:o,slot:a,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,M.FX)(function(e,t){let n=(0,r.useId)(),{id:o="headlessui-dialog-title-".concat(n),...l}=e,[{dialogState:i,setTitleId:a}]=eF("Dialog.Title"),u=(0,X.P)(t);(0,r.useEffect)(()=>(a(o),()=>a(null)),[o,a]);let s=(0,r.useMemo)(()=>({open:0===i}),[i]);return(0,M.Ci)()({ourProps:{ref:u,id:o},theirProps:l,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),Description:z})}}]);
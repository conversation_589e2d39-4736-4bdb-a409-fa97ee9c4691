"use strict";(()=>{var e={};e.id=1004,e.ids=[1004],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},24704:(e,r,a)=>{a.d(r,{k0:()=>i,oX:()=>u,oc:()=>o});var n=a(31183),t=a(61904),s=a(6375);async function o(e){try{let r=await n.z.user.findUnique({where:{id:e},include:{rank:!0}});if(!r)throw Error(`User with ID ${e} not found`);let a=await n.z.rank.findMany({where:{level:{gt:r.rank.level}},orderBy:{level:"asc"}});if(0===a.length)return{eligible:!1,currentRank:r.rank,nextRank:null,message:"You are already at the highest rank."};let t=a[0],s=await n.z.rankRequirement.findUnique({where:{rankId:t.id}});if(!s)throw Error(`Requirements for rank ${t.name} not found`);let o=await l(e),i=o>=s.requiredPersonalSales,u=await d(e),m=u>=s.requiredGroupSales,f=await c(e),g=f>=s.requiredDirectDownline,w=0,k=!0;s.requiredQualifiedDownline>0&&s.qualifiedRankId&&(k=(w=await p(e,s.qualifiedRankId))>=s.requiredQualifiedDownline);let v=i&&m&&g&&k;return{eligible:v,currentRank:r.rank,nextRank:t,requirements:{personalSales:{required:s.requiredPersonalSales,actual:o,qualified:i},groupSales:{required:s.requiredGroupSales,actual:u,qualified:m},directDownline:{required:s.requiredDirectDownline,actual:f,qualified:g},qualifiedDownline:{required:s.requiredQualifiedDownline,actual:w,qualified:k,qualifiedRankId:s.qualifiedRankId}},message:v?`Congratulations! You qualify for advancement to ${t.name} rank.`:`You do not yet qualify for advancement to ${t.name} rank.`}}catch(e){throw console.error("Error checking rank advancement eligibility:",e),e}}async function i(e){try{let r=await o(e);if(!r.eligible||!r.nextRank)return{success:!1,message:r.message,previousRank:r.currentRank,newRank:null};let a=await n.z.user.findUnique({where:{id:e},select:{id:!0,name:!0,email:!0,rankId:!0}});if(!a)throw Error(`User with ID ${e} not found`);let s=await n.z.user.update({where:{id:e},data:{rankId:r.nextRank.id},include:{rank:!0}});if(await n.z.rankAdvancement.create({data:{userId:e,previousRankId:r.currentRank.id,newRankId:r.nextRank.id,personalSales:r.requirements.personalSales.actual,groupSales:r.requirements.groupSales.actual,directDownlineCount:r.requirements.directDownline.actual,qualifiedDownlineCount:r.requirements.qualifiedDownline.actual}}),a.email)try{await (0,t.Z)(a.email,"rankAdvancement",{userName:a.name,previousRank:r.currentRank.name,newRank:r.nextRank.name})}catch(r){console.error(`Failed to send rank advancement email to user ${e}:`,r)}return{success:!0,message:`Congratulations! You have been advanced to ${r.nextRank.name} rank.`,previousRank:r.currentRank,newRank:s.rank}}catch(e){throw console.error("Error processing rank advancement:",e),e}}async function u(){try{let e=await n.z.user.findMany({select:{id:!0}}),r={processed:0,advanced:0,failed:0,advancedUsers:[],failedUsers:[]};for(let a of e)try{r.processed++;let e=await o(a.id);if(e.eligible&&e.nextRank){let e=await i(a.id);e.success?(r.advanced++,r.advancedUsers.push({userId:a.id,previousRank:e.previousRank.name,newRank:e.newRank.name})):(r.failed++,r.failedUsers.push({userId:a.id,reason:e.message}))}}catch(e){console.error(`Error processing rank advancement for user ${a.id}:`,e),r.failed++,r.failedUsers.push({userId:a.id,reason:e instanceof Error?e.message:"Unknown error"})}return r}catch(e){throw console.error("Error processing all rank advancements:",e),e}}async function l(e){return(await n.z.purchase.aggregate({where:{userId:e},_sum:{totalAmount:!0}}))._sum.totalAmount||0}async function d(e){let r=await (0,s.Op)(e),a=[e,...r];return(await n.z.purchase.aggregate({where:{userId:{in:a}},_sum:{totalAmount:!0}}))._sum.totalAmount||0}async function c(e){return await n.z.user.count({where:{uplineId:e}})}async function p(e,r){let a=await n.z.rank.findUnique({where:{id:r},select:{level:!0}});if(!a)throw Error(`Qualified rank with ID ${r} not found`);let t=await (0,s.Op)(e);return await n.z.user.count({where:{id:{in:t},rank:{level:{gte:a.level}}}})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30539:(e,r,a)=>{a.r(r),a.d(r,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>k,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>w});var n={};a.r(n),a.d(n,{GET:()=>m,POST:()=>p});var t=a(96559),s=a(48088),o=a(37719),i=a(31183),u=a(32190),l=a(19854),d=a(12909),c=a(24704);async function p(e){try{let e=await (0,l.getServerSession)(d.Nh);if(!e||!e.user)return u.NextResponse.json({error:"You must be logged in to process rank advancements"},{status:401});let r=e.user.email;if(!r)return u.NextResponse.json({error:"User email not found in session"},{status:400});let a=await i.z.user.findUnique({where:{email:r},select:{id:!0,rankId:!0}});if(!a)return u.NextResponse.json({error:"User not found"},{status:404});if(6!==a.rankId)return u.NextResponse.json({error:"You do not have permission to process rank advancements"},{status:403});let n=await (0,c.oX)();return u.NextResponse.json({success:!0,...n,message:`Processed ${n.processed} users, advanced ${n.advanced} users, failed ${n.failed} users`})}catch(e){return console.error("Error processing all rank advancements:",e),u.NextResponse.json({error:"Failed to process all rank advancements"},{status:500})}}async function m(e){try{let r=await (0,l.getServerSession)(d.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view rank advancement history"},{status:401});let a=r.user.email;if(!a)return u.NextResponse.json({error:"User email not found in session"},{status:400});let n=await i.z.user.findUnique({where:{email:a},select:{id:!0,rankId:!0}});if(!n)return u.NextResponse.json({error:"User not found"},{status:404});if(6!==n.rankId)return u.NextResponse.json({error:"You do not have permission to view rank advancement history"},{status:403});let t=new URL(e.url),s=t.searchParams.get("page"),o=s?parseInt(s):1,c=t.searchParams.get("pageSize"),p=c?parseInt(c):10,m=t.searchParams.get("userId"),f=m?parseInt(m):void 0,g={};f&&(g.userId=f);let w=(o-1)*p,k=await i.z.rankAdvancement.findMany({where:g,include:{user:{select:{id:!0,name:!0,email:!0}},previousRank:!0,newRank:!0},orderBy:{createdAt:"desc"},skip:w,take:p}),v=await i.z.rankAdvancement.count({where:g}),h=Math.ceil(v/p);return u.NextResponse.json({rankAdvancements:k,pagination:{page:o,pageSize:p,totalItems:v,totalPages:h}})}catch(e){return console.error("Error fetching rank advancement history:",e),u.NextResponse.json({error:"Failed to fetch rank advancement history"},{status:500})}}let f=new t.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/rank-advancements/route",pathname:"/api/admin/rank-advancements",filename:"route",bundlePath:"app/api/admin/rank-advancements/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\rank-advancements\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:w,serverHooks:k}=f;function v(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:w})}},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},61904:(e,r,a)=>{a.d(r,{Z:()=>s});let n=a(49526).createTransport({host:process.env.EMAIL_HOST||"smtp.example.com",port:parseInt(process.env.EMAIL_PORT||"587"),secure:"true"===process.env.EMAIL_SECURE,auth:{user:process.env.EMAIL_USER||"<EMAIL>",pass:process.env.EMAIL_PASSWORD||"password"}}),t={rebateReceived:e=>({subject:`You've Received a Rebate of $${e.amount.toFixed(2)}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">New Rebate Received!</h2>
          <p>Hello ${e.userName},</p>
          <p>Great news! You've received a rebate of <strong style="color: #48bb78;">$${e.amount.toFixed(2)}</strong>.</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Rebate Details:</h3>
            <ul style="padding-left: 20px;">
              <li>Amount: <strong>$${e.amount.toFixed(2)}</strong></li>
              <li>From: <strong>${e.generatorName}</strong></li>
              <li>Level: <strong>${e.level}</strong></li>
              <li>Product: <strong>${e.productName}</strong></li>
            </ul>
          </div>
          <p>This rebate has been added to your wallet balance. You can view your rebate details and wallet balance by logging into your account.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/wallet" style="background-color: #4299e1; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Wallet</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for being part of our MLM network!</p>
        </div>
      `}),rankAdvancement:e=>({subject:`Congratulations on Your Rank Advancement to ${e.newRank}!`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">Rank Advancement Achievement!</h2>
          <p>Hello ${e.userName},</p>
          <p>Congratulations! You've advanced from <strong>${e.oldRank}</strong> to <strong style="color: #805ad5;">${e.newRank}</strong>!</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Your New Benefits:</h3>
            <ul style="padding-left: 20px;">
              ${e.benefits.map(e=>`<li>${e}</li>`).join("")}
            </ul>
          </div>
          <p>Keep up the great work! As you continue to grow your network and increase your sales, you'll unlock even more benefits and higher rebate percentages.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" style="background-color: #805ad5; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Dashboard</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for your dedication and commitment to our MLM network!</p>
        </div>
      `})};async function s(e,r,a){try{let{subject:s,html:o}=t[r](a),i={from:process.env.EMAIL_FROM||"MLM Rebate Engine <<EMAIL>>",to:e,subject:s,html:o},u=await n.sendMail(i);return console.log("Email sent:",u.messageId),{success:!0,messageId:u.messageId}}catch(e){return console.error("Error sending email:",e),{success:!1,error:e}}}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),n=r.X(0,[4243,580,8044,3112,4079,6719],()=>a(30539));module.exports=n})();
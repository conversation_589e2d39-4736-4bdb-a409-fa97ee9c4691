(()=>{var e={};e.id=7017,e.ids=[7017],e.modules={1998:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["genealogy",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68545)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\search\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\search\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/genealogy/search/page",pathname:"/genealogy/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9031:(e,t,s)=>{Promise.resolve().then(s.bind(s,68545))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},17168:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(60687),a=s(43210),n=s(82136),l=s(16189),i=s(23877),o=s(85814),d=s.n(o);function c({onSearch:e,ranks:t,isLoading:s=!1}){let[n,l]=(0,a.useState)(""),[o,d]=(0,a.useState)(!1),[c,m]=(0,a.useState)({}),x=t=>{t.preventDefault(),e(n,c)},u=(e,t)=>{if(""===t||void 0===t){let t={...c};delete t[e],m(t)}else m(s=>({...s,[e]:t}))},h=Object.keys(c).length;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-4",children:[(0,r.jsx)("form",{onSubmit:x,className:"mb-3",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by ID, name, or email...",value:n,onChange:e=>l(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,r.jsx)(i.KSO,{})}),n&&(0,r.jsx)("button",{type:"button",onClick:()=>l(""),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(i.QCr,{})})]}),(0,r.jsx)("button",{type:"submit",disabled:s,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:s?"Searching...":"Search"})]})}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>d(!o),className:"flex items-center text-sm text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(i.YsJ,{className:"mr-1"}),"Advanced Filters",o?(0,r.jsx)(i.Ucs,{className:"ml-1"}):(0,r.jsx)(i.Vr3,{className:"ml-1"}),h>0&&(0,r.jsx)("span",{className:"ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs",children:h})]}),h>0&&(0,r.jsx)("button",{type:"button",onClick:()=>{m({})},className:"text-sm text-red-600 hover:text-red-800",children:"Clear All Filters"})]}),o&&(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md mt-2 animate-fade-in",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name Contains"}),(0,r.jsx)("input",{type:"text",value:c.name||"",onChange:e=>u("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter name..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Contains"}),(0,r.jsx)("input",{type:"text",value:c.email||"",onChange:e=>u("email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter email..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,r.jsxs)("select",{value:c.rankId||"",onChange:e=>u("rankId",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Any Rank"}),t.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Downline"}),(0,r.jsx)("input",{type:"number",min:"0",value:c.minDownline||"",onChange:e=>u("minDownline",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Min"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Downline"}),(0,r.jsx)("input",{type:"number",min:"0",value:c.maxDownline||"",onChange:e=>u("maxDownline",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Max"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Balance"}),(0,r.jsx)("input",{type:"number",min:"0",value:c.minWalletBalance||"",onChange:e=>u("minWalletBalance",e.target.value?parseFloat(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Min ₱"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Balance"}),(0,r.jsx)("input",{type:"number",min:"0",value:c.maxWalletBalance||"",onChange:e=>u("maxWalletBalance",e.target.value?parseFloat(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Max ₱"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Joined After"}),(0,r.jsx)("input",{type:"date",value:c.joinedAfter||"",onChange:e=>u("joinedAfter",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Joined Before"}),(0,r.jsx)("input",{type:"date",value:c.joinedBefore||"",onChange:e=>u("joinedBefore",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),h>0&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Active Filters:"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[c.name&&(0,r.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Name: ",c.name,(0,r.jsx)("button",{onClick:()=>u("name",void 0),className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,r.jsx)(i.QCr,{size:10})})]}),c.email&&(0,r.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Email: ",c.email,(0,r.jsx)("button",{onClick:()=>u("email",void 0),className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,r.jsx)(i.QCr,{size:10})})]}),void 0!==c.rankId&&(0,r.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Rank: ",t.find(e=>e.id===c.rankId)?.name||c.rankId,(0,r.jsx)("button",{onClick:()=>u("rankId",void 0),className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,r.jsx)(i.QCr,{size:10})})]}),(void 0!==c.minDownline||void 0!==c.maxDownline)&&(0,r.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Downline: ",c.minDownline||"0"," - ",c.maxDownline||"∞",(0,r.jsx)("button",{onClick:()=>{u("minDownline",void 0),u("maxDownline",void 0)},className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,r.jsx)(i.QCr,{size:10})})]}),(void 0!==c.minWalletBalance||void 0!==c.maxWalletBalance)&&(0,r.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Balance: ₱",c.minWalletBalance||"0"," - ₱",c.maxWalletBalance||"∞",(0,r.jsx)("button",{onClick:()=>{u("minWalletBalance",void 0),u("maxWalletBalance",void 0)},className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,r.jsx)(i.QCr,{size:10})})]}),(void 0!==c.joinedAfter||void 0!==c.joinedBefore)&&(0,r.jsxs)("div",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center",children:["Joined: ",c.joinedAfter||"Any"," to ",c.joinedBefore||"Now",(0,r.jsx)("button",{onClick:()=>{u("joinedAfter",void 0),u("joinedBefore",void 0)},className:"ml-1 text-blue-800 hover:text-blue-900",children:(0,r.jsx)(i.QCr,{size:10})})]})]})]}),(0,r.jsx)("div",{className:"mt-4 flex justify-end",children:(0,r.jsx)("button",{type:"button",onClick:x,disabled:s,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:s?"Applying...":"Apply Filters"})})]})]})}function m({results:e,pagination:t,isLoading:s,onPageChange:n,onViewGenealogy:l}){let[o,c]=(0,a.useState)(new Set),m=e=>{c(t=>{let s=new Set(t);return s.has(e)?s.delete(e):s.add(e),s})},x=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),u=e=>e?new Date(e).toLocaleDateString():"N/A";return s?(0,r.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,r.jsx)(i.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading search results..."})]}):0===e.length?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-500 mb-4",children:"No users found matching your search criteria."}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Try adjusting your search filters or query."})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border-b border-blue-100 flex justify-between items-center",children:[(0,r.jsxs)("h3",{className:"font-medium",children:["Search Results (",t.totalItems," users found)"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Page ",t.page," of ",t.totalPages]})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Downline"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)(React.Fragment,{children:[(0,r.jsxs)("tr",{className:o.has(e.id)?"bg-blue-50":"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>m(e.id),className:"mr-3 text-gray-400 hover:text-gray-600",children:o.has(e.id)?(0,r.jsx)(i.Vr3,{}):(0,r.jsx)(i.X6T,{})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:e.rank.name})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e._count.downline}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:void 0!==e.walletBalance?x(e.walletBalance):"N/A"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:u(e.createdAt)}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{onClick:()=>l(e.id),className:"text-blue-600 hover:text-blue-900 mr-3",children:"View Genealogy"}),(0,r.jsx)(d(),{href:`/users/${e.id}`,className:"text-indigo-600 hover:text-indigo-900",children:"Profile"})]})]}),o.has(e.id)&&(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:6,className:"px-6 py-4 bg-blue-50",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-white p-3 rounded-md shadow-sm",children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-2",children:"User Details"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"ID:"})," ",e.id]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Rank:"})," ",e.rank.name]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Upline ID:"})," ",e.uplineId||"None"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Downline Count:"})," ",e._count.downline]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Joined:"})," ",u(e.createdAt)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Wallet Balance:"})," ",void 0!==e.walletBalance?x(e.walletBalance):"N/A"]})]})]}),e.performanceMetrics?(0,r.jsxs)("div",{className:"bg-white p-3 rounded-md shadow-sm",children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Performance Metrics"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.AsH,{className:"text-green-500 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,r.jsx)("div",{className:"font-medium",children:x(e.performanceMetrics.personalSales)})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.YXz,{className:"text-blue-500 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,r.jsx)("div",{className:"font-medium",children:x(e.performanceMetrics.teamSales)})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.lcY,{className:"text-yellow-500 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Rebates Earned"}),(0,r.jsx)("div",{className:"font-medium",children:x(e.performanceMetrics.rebatesEarned)})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.NPy,{className:"text-purple-500 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"New Members (30d)"}),(0,r.jsx)("div",{className:"font-medium",children:e.performanceMetrics.newTeamMembers})]})]})]})]}):(0,r.jsx)("div",{className:"bg-white p-3 rounded-md shadow-sm flex items-center justify-center text-gray-500 text-sm",children:"No performance metrics available"})]})})})]},e.id))})]})}),(0,r.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>n(t.page-1),disabled:!t.hasPreviousPage,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsx)("button",{onClick:()=>n(t.page+1),disabled:!t.hasNextPage,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing ",(0,r.jsx)("span",{className:"font-medium",children:(t.page-1)*t.pageSize+1})," to"," ",(0,r.jsx)("span",{className:"font-medium",children:Math.min(t.page*t.pageSize,t.totalItems)})," ","of ",(0,r.jsx)("span",{className:"font-medium",children:t.totalItems})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>n(t.page-1),disabled:!t.hasPreviousPage,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)("span",{className:"sr-only",children:"Previous"}),(0,r.jsx)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:Math.min(5,t.totalPages)},(e,s)=>{let a;return a=t.totalPages<=5||t.page<=3?s+1:t.page>=t.totalPages-2?t.totalPages-4+s:t.page-2+s,(0,r.jsx)("button",{onClick:()=>n(a),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${t.page===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"}`,children:a},a)}),(0,r.jsxs)("button",{onClick:()=>n(t.page+1),disabled:!t.hasNextPage,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)("span",{className:"sr-only",children:"Next"}),(0,r.jsx)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})}function x(){let{data:e,status:t}=(0,n.useSession)(),s=(0,l.useRouter)(),[o,x]=(0,a.useState)([]),[u,h]=(0,a.useState)({page:1,pageSize:20,totalItems:0,totalPages:0,hasNextPage:!1,hasPreviousPage:!1}),[p,b]=(0,a.useState)(!1),[g,v]=(0,a.useState)(null),[f,j]=(0,a.useState)(!1),[y,N]=(0,a.useState)([]),w=async(e,s,r=1)=>{if("authenticated"===t){b(!0),v(null);try{let t=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,...s,page:r,pageSize:20,includePerformanceMetrics:!0})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to search genealogy")}let a=await t.json();x(a.users),h(a.pagination),j(!0)}catch(e){v(e instanceof Error?e.message:"An unknown error occurred")}finally{b(!1)}}};return"loading"===t?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(i.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading..."})]})}):"unauthenticated"===t?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(i.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to search the genealogy."}),(0,r.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Advanced Genealogy Search"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Search for users in your genealogy with advanced filtering options"})]}),(0,r.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(i.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)(c,{onSearch:w,ranks:y,isLoading:p})}),g&&(0,r.jsxs)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:[(0,r.jsx)("p",{className:"font-medium",children:"Error"}),(0,r.jsx)("p",{children:g})]}),f?(0,r.jsx)(m,{results:o,pagination:u,isLoading:p,onPageChange:e=>{w("",{},e)},onViewGenealogy:e=>{s.push(`/genealogy?userId=${e}`)}}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,r.jsx)("div",{className:"text-6xl text-gray-300 mb-4",children:(0,r.jsx)(i.KSO,{className:"inline"})}),(0,r.jsx)("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"Search the Genealogy"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Use the search form above to find users in your genealogy."}),(0,r.jsx)("p",{className:"text-gray-500 mt-2",children:"You can search by name, email, rank, and more."})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>i,_:()=>l});var r=s(60687),a=s(43210);let n=(0,a.createContext)(void 0),l=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},i=({children:e})=>{let[t,s]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let l=e=>{s(t=>t.filter(t=>t.id!==e))},i=t.reduce((e,t)=>e+t.quantity,0),o=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,r.jsx)(n.Provider,{value:{items:t,addItem:e=>{s(t=>{let s=t.findIndex(t=>t.id===e.id);if(!(s>=0))return[...t,e];{let r=[...t];return r[s]={...r[s],quantity:r[s].quantity+e.quantity},r}})},removeItem:l,updateQuantity:(e,t)=>{if(t<=0)return void l(e);s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},clearCart:()=>{s([])},itemCount:i,subtotal:o,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},39295:(e,t,s)=>{Promise.resolve().then(s.bind(s,17168))},41750:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},45851:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),a=s(25217),n=s(8693),l=s(43210);function i({children:e}){let[t]=(0,l.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,r.jsx)(n.Ht,{client:t,children:e})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(60687),a=s(43210);let n=()=>"serviceWorker"in navigator,l=async()=>{if(!n())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),i(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},i=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&o()})})},o=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,s]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{l();let e=()=>{s(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,r.jsxs)(r.Fragment,{children:[e,t&&(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,r.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,s)=>{Promise.resolve().then(s.bind(s,37043)),Promise.resolve().then(s.bind(s,23229)),Promise.resolve().then(s.bind(s,82113)),Promise.resolve().then(s.bind(s,41750))},68545:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\genealogy\\\\search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\genealogy\\search\\page.tsx","default")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,s)=>{Promise.resolve().then(s.bind(s,28253)),Promise.resolve().then(s.bind(s,97695)),Promise.resolve().then(s.bind(s,45851)),Promise.resolve().then(s.bind(s,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>x});var r=s(37413),a=s(22376),n=s.n(a),l=s(68726),i=s.n(l);s(61135);var o=s(23229),d=s(37043),c=s(82113),m=s(41750);let x={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${i().variable} antialiased`,children:(0,r.jsx)(o.AuthProvider,{children:(0,r.jsx)(c.default,{children:(0,r.jsx)(d.CartProvider,{children:(0,r.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},97695:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n});var r=s(60687),a=s(82136);function n({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877],()=>s(1998));module.exports=r})();
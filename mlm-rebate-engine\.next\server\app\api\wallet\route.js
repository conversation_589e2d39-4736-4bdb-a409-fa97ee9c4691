(()=>{var e={};e.id=3329,e.ids=[3329],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>l,Nh:()=>c,aP:()=>u});var s=t(96330),a=t(13581),o=t(85663),n=t(55511),i=t.n(n);async function l(e){return await o.Ay.hash(e,10)}function u(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new s.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,a.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new s.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let a=await o.Ay.compare(e.password,t.password);if(console.log("Password valid:",a),!a)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:n,...i}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var s={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var a=t(12269);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(s,o,i):s[o]=e[o]}return s.default=e,t&&t.set(e,s),s}(t(35426));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(96330);let a=global.prisma||new s.PrismaClient({log:["query"]})},36296:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>w,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>p});var a=t(96559),o=t(48088),n=t(37719),i=t(31183),l=t(32190),u=t(19854),c=t(12909);async function d(e){try{let r=await (0,u.getServerSession)(c.Nh);if(!r||!r.user)return l.NextResponse.json({error:"You must be logged in to view wallet transactions"},{status:401});let t=parseInt(r.user.id);new URL(e.url).searchParams.get("userId");let s={};s={userId:t};let a=await i.z.walletTransaction.findMany({where:s,orderBy:{createdAt:"desc"}}),o=await i.z.user.findUnique({where:{id:t},select:{walletBalance:!0}});return l.NextResponse.json({balance:o?.walletBalance||0,transactions:a})}catch(e){return console.error("Error fetching wallet transactions:",e),l.NextResponse.json({error:"Failed to fetch wallet transactions"},{status:500})}}async function p(e){try{let r=await (0,u.getServerSession)(c.Nh);if(!r||!r.user)return l.NextResponse.json({error:"You must be logged in to create wallet transactions"},{status:401});let{amount:t,type:s,description:a}=await e.json(),o=parseInt(r.user.id);if("withdrawal"!==s)return l.NextResponse.json({error:"Only withdrawal transactions are allowed"},{status:400});let n=await i.z.user.findUnique({where:{id:o},select:{walletBalance:!0}});if(!n||n.walletBalance<t)return l.NextResponse.json({error:"Insufficient wallet balance"},{status:400});let d=await i.z.$transaction(async e=>(await e.user.update({where:{id:o},data:{walletBalance:{decrement:t}}}),await e.walletTransaction.create({data:{userId:o,amount:t,type:s,description:a||"Withdrawal request"}})));return l.NextResponse.json(d,{status:201})}catch(e){return console.error("Error creating wallet transaction:",e),l.NextResponse.json({error:"Failed to create wallet transaction"},{status:500})}}let w=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/wallet/route",pathname:"/api/wallet",filename:"route",bundlePath:"app/api/wallet/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\wallet\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:h}=w;function m(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112],()=>t(36296));module.exports=s})();
exports.id=1426,exports.ids=[1426],exports.modules={12909:(e,a,t)=>{"use strict";t.d(a,{Er:()=>l,Nh:()=>u,aP:()=>d});var r=t(96330),n=t(13581),i=t(85663),s=t(55511),o=t.n(s);async function l(e){return await i.Ay.hash(e,10)}function d(){let e=o().randomBytes(32).toString("hex"),a=new Date;return a.setHours(a.getHours()+1),{token:e,expiresAt:a}}new r.PrismaClient;let u={debug:!0,logger:{error:(e,a)=>{console.error(`NextAuth error: ${e}`,a)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,a)=>{console.log(`NextAuth debug: ${e}`,a)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,a){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let a=new r.PrismaClient,t=await a.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await a.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await i.Ay.compare(e.password,t.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:s,...o}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:a})=>(console.log("Session callback called",{session:e,token:a}),a&&e.user&&(e.user.id=a.sub),e),jwt:async({token:e,user:a})=>(console.log("JWT callback called",{token:e,user:a}),a&&(e.sub=a.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},31183:(e,a,t)=>{"use strict";t.d(a,{z:()=>n});var r=t(96330);let n=global.prisma||new r.PrismaClient({log:["query"]})},78335:()=>{},91818:(e,a,t)=>{"use strict";t.d(a,{Nl:()=>d,Ol:()=>u,U4:()=>s,y6:()=>l});var r=t(81437),n=t(31183),i=t(85663);async function s(e){let a=r.LF(e,{type:"buffer"}),t=a.SheetNames,n=[];for(let e of t){let t=a.Sheets[e],i=r.Wp.sheet_to_json(t,{header:1,defval:null});if(i.length<=1)continue;let s=i[0],l=e.match(/([A-Za-z0-9]+)-([A-Za-z0-9]+)/),d=null,u=null;l&&(d=l[1],u=l[2]);for(let e=1;e<i.length;e++){let a=i[e];if(!a.some(e=>null!==e&&""!==e))continue;let t=await o(a,s,e+1,d,u);n.push(t)}}return n}async function o(e,a,t,r,i){let s=[],o={};for(let t=0;t<a.length;t++)a[t]&&(o[a[t].toLowerCase().trim()]=e[t]);let l=o["member id"]||o.memberid||o.id||r,d=o.name||o["full name"]||o.fullname,u=o.email||o["email address"]||o.emailaddress,m=o.phone||o["phone number"]||o.phonenumber,c=o.rank||o["rank name"]||o.rankname,p=o["registration date"]||o.registrationdate||o["join date"]||o.joindate||o.date,h=o["upline id"]||o.uplineid||o.upline||i;if(l||s.push("Member ID is required"),d?d.length<2&&s.push("Name must be at least 2 characters"):s.push("Name is required"),u?(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u)||s.push("Invalid email format"),await n.z.user.findUnique({where:{email:u}})&&s.push(`Email already exists: ${u}`)):l?u=`member${l}@extremelife.ph`:s.push("Email is required when Member ID is not provided"),p&&!(p instanceof Date))try{p=new Date(p),isNaN(p.getTime())&&(s.push("Invalid registration date format"),p=null)}catch(e){s.push("Invalid registration date format"),p=null}let w={memberId:l?.toString(),name:d||"",email:u||"",uplineId:h?.toString()||null,registrationDate:p||null,rank:c||null,phone:m?.toString()||null};return{isValid:0===s.length,errors:s,data:0===s.length?w:void 0,row:t}}async function l(e,a,t,r){let s={totalProcessed:e.length,successful:0,failed:0,duplicates:0,errors:[],importedUsers:[]},o=await i.Ay.hash(a,10),l=new Map((await n.z.rank.findMany()).map(e=>[e.name.toLowerCase(),e.id]));for(let a of e){if(!a.isValid||!a.data){s.failed++,s.errors.push({row:a.row,errors:a.errors});continue}try{let e=a.data;if(await n.z.user.findUnique({where:{email:e.email}})){s.duplicates++,s.errors.push({row:a.row,errors:[`User with email ${e.email} already exists`]});continue}let i=1;if(e.rank){let a=e.rank.toLowerCase();l.has(a)&&(i=l.get(a))}let d=null;if(e.uplineId){let a=await n.z.user.findFirst({where:{metadata:{path:["memberId"],equals:e.uplineId}}});if(a)d=a.id;else try{let a=parseInt(e.uplineId);if(!isNaN(a)){let e=await n.z.user.findUnique({where:{id:a}});e&&(d=e.id)}}catch(e){}}let u=e.registrationDate||new Date,m=await n.z.user.create({data:{name:e.name,email:e.email,password:o,phone:e.phone||null,rankId:i,uplineId:d,createdAt:u,updatedAt:new Date,metadata:{memberId:e.memberId,importedBy:t,importedAt:new Date().toISOString()}}});await n.z.userAudit.create({data:{userId:m.id,action:"import",details:JSON.stringify({adminId:t,adminName:r,importSource:"excel",originalData:e}),createdAt:new Date}}),s.successful++,s.importedUsers.push({id:m.id,name:m.name,email:m.email,memberId:e.memberId,uplineId:m.uplineId,rankId:m.rankId})}catch(e){console.error("Error importing user:",e),s.failed++,s.errors.push({row:a.row,errors:[e.message||"Unknown error"]})}}return s}async function d(e={}){let a={};e.rankFilter&&(a.rankId=e.rankFilter),(e.dateRangeStart||e.dateRangeEnd)&&(a.createdAt={},e.dateRangeStart&&(a.createdAt.gte=e.dateRangeStart),e.dateRangeEnd&&(a.createdAt.lte=e.dateRangeEnd));let t=await n.z.user.findMany({where:a,include:{rank:!0,_count:{select:{downline:!0}}},orderBy:{id:"asc"}}),i=await Promise.all(t.map(async a=>{let t={"User ID":a.id,Name:a.name,Email:a.email,Phone:a.phone||"",Rank:a.rank.name};if(e.includeDownlineCount&&(t["Downline Count"]=a._count.downline),e.includeJoinDate&&(t["Join Date"]=a.createdAt.toISOString().split("T")[0]),e.includeEarnings){let e=await n.z.walletTransaction.aggregate({where:{userId:a.id,type:"rebate"},_sum:{amount:!0}});t["Total Earnings"]=e._sum.amount||0}if(a.metadata&&"object"==typeof a.metadata&&"memberId"in a.metadata&&(t["Member ID"]=a.metadata.memberId),a.uplineId){let e=await n.z.user.findUnique({where:{id:a.uplineId},select:{id:!0,name:!0}});e&&(t["Upline ID"]=e.id,t["Upline Name"]=e.name)}return t})),s=r.Wp.book_new(),o=r.Wp.json_to_sheet(i);return r.Wp.book_append_sheet(s,o,"Users"),r.M9(s,{type:"buffer",bookType:"xlsx"})}function u(){let e=r.Wp.book_new(),a=r.Wp.json_to_sheet([{"Member ID":"00001",Name:"John Doe",Email:"<EMAIL>",Phone:"09123456789",Rank:"Gold","Upline ID":"00000","Registration Date":"2023-01-15"},{"Member ID":"00002",Name:"Jane Smith",Email:"<EMAIL>",Phone:"09987654321",Rank:"Silver","Upline ID":"00001","Registration Date":"2023-02-20"}]);r.Wp.book_append_sheet(e,a,"Users");let t=r.Wp.json_to_sheet([{Name:"Alice Johnson",Email:"<EMAIL>",Phone:"09111222333",Rank:"Bronze","Registration Date":"2023-03-10"},{Name:"Bob Williams",Email:"<EMAIL>",Phone:"09444555666",Rank:"Starter","Registration Date":"2023-03-15"}]);r.Wp.book_append_sheet(e,t,"00003-00001");let n=r.Wp.json_to_sheet([{Instructions:"How to use this template:",Details:""},{Instructions:"1. Main Sheet (Users)",Details:"Fill in user details in the Users sheet. All users listed here will be imported."},{Instructions:"2. Hierarchy Sheets",Details:'Create additional sheets with names in the format "ChildID-UplineID" to establish hierarchical relationships.'},{Instructions:"3. Required Fields",Details:"Member ID, Name, and Email are required. Email will be auto-generated if missing."},{Instructions:"4. Optional Fields",Details:"Phone, Rank, Upline ID, and Registration Date are optional."},{Instructions:"5. Default Values",Details:"Default rank is Starter. Registration date defaults to import date if not provided."}]);return r.Wp.book_append_sheet(e,n,"Instructions"),r.M9(e,{type:"buffer",bookType:"xlsx"})}},96487:()=>{}};
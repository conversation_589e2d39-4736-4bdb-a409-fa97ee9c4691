(()=>{var e={};e.id=6473,e.ids=[6473],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{Er:()=>u,Nh:()=>l,aP:()=>c});var t=s(96330),o=s(13581),n=s(85663),i=s(55511),a=s.n(i);async function u(e){return await n.Ay.hash(e,10)}function c(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new t.PrismaClient;let l={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new t.PrismaClient,s=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!s)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",s.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await n.Ay.compare(e.password,s.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",s.id);let{password:i,...a}=s;return{id:s.id.toString(),email:s.email,name:s.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>o});var t=s(96330);let o=global.prisma||new t.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},98632:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{GET:()=>d});var o=s(96559),n=s(48088),i=s(37719),a=s(32190),u=s(35426),c=s(12909),l=s(31183);async function d(e,{params:r}){try{let s=await (0,u.getServerSession)(c.Nh);if(!s||!s.user)return a.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});if("admin"!==s.user.role)return a.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let t=parseInt(r.id);if(isNaN(t))return a.NextResponse.json({error:"Invalid product ID"},{status:400});let o=new URL(e.url),n=parseInt(o.searchParams.get("limit")||"10"),i=parseInt(o.searchParams.get("offset")||"0");if(!await l.z.product.findUnique({where:{id:t}}))return a.NextResponse.json({error:"Product not found"},{status:404});let d=await l.z.inventoryTransaction.findMany({where:{productId:t},orderBy:{createdAt:"desc"},take:n,skip:i}),p=await l.z.inventoryTransaction.count({where:{productId:t}});return a.NextResponse.json({transactions:d,total:p,limit:n,offset:i})}catch(e){return console.error("Error fetching inventory transactions:",e),a.NextResponse.json({error:"Failed to fetch inventory transactions"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/products/[id]/inventory-transactions/route",pathname:"/api/admin/products/[id]/inventory-transactions",filename:"route",bundlePath:"app/api/admin/products/[id]/inventory-transactions/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\products\\[id]\\inventory-transactions\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:w,serverHooks:m}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:w})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,8044,3112],()=>s(98632));module.exports=t})();
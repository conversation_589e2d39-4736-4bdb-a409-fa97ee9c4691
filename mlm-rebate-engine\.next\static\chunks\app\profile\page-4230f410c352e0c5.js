(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{33246:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var r=a(95155),l=a(12115),t=a(12108),n=a(35695),d=a(6874),i=a.n(d),o=a(70357),c=a(29911);function m(){let{data:e,status:s}=(0,t.useSession)(),a=(0,n.useRouter)(),[d,m]=(0,l.useState)(null),[x,u]=(0,l.useState)(!0),[h,f]=(0,l.useState)(""),[p,g]=(0,l.useState)(!1),[b,j]=(0,l.useState)(!1),[N,w]=(0,l.useState)({name:"",phone:"",profileImage:""}),[y,v]=(0,l.useState)({currentPassword:"",newPassword:"",confirmNewPassword:""}),[P,C]=(0,l.useState)(!1),[k,E]=(0,l.useState)("");(0,l.useEffect)(()=>{"unauthenticated"===s&&a.push("/login")},[s,a]),(0,l.useEffect)(()=>{"authenticated"===s&&S()},[s]);let S=async()=>{u(!0),f("");try{var s;let a=null==e||null==(s=e.user)?void 0:s.email;if(!a)throw Error("User email not found in session");let r=await fetch("/api/users?search=".concat(encodeURIComponent(a))),l=await r.json();if(!l.users||0===l.users.length)throw Error("User not found");let t=l.users[0].id,n=await fetch("/api/users/".concat(t));if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to fetch profile")}let d=await n.json();m(d),w({name:d.name,phone:d.phone||"",profileImage:d.profileImage||""})}catch(e){console.error("Error fetching profile:",e),f(e.message||"An error occurred while fetching profile")}finally{u(!1)}},I=e=>{let{name:s,value:a}=e.target;w(e=>({...e,[s]:a}))},R=e=>{let{name:s,value:a}=e.target;v(e=>({...e,[s]:a}))},A=async e=>{e.preventDefault(),C(!0),f(""),E("");try{if(!d)throw Error("Profile not loaded");let e={name:N.name,phone:N.phone||null,profileImage:N.profileImage||null};if(b){if(y.newPassword!==y.confirmNewPassword)throw Error("New passwords don't match");Object.assign(e,{currentPassword:y.currentPassword,newPassword:y.newPassword,confirmNewPassword:y.confirmNewPassword})}let s=await fetch("/api/users/".concat(d.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update profile")}await S(),E("Profile updated successfully"),g(!1),j(!1),v({currentPassword:"",newPassword:"",confirmNewPassword:""})}catch(e){console.error("Error updating profile:",e),f(e.message||"An error occurred while updating profile")}finally{C(!1)}};return"loading"===s||x?(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})]})}):(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-semibold mb-6 flex items-center",children:[(0,r.jsx)(c.x$1,{className:"mr-2 text-blue-500"})," User Profile"]}),h&&(0,r.jsx)("div",{className:"bg-red-100 text-red-700 p-4 rounded-md mb-6",children:h}),k&&(0,r.jsx)("div",{className:"bg-green-100 text-green-700 p-4 rounded-md mb-6",children:k}),d?(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"relative mb-4",children:p?(0,r.jsxs)("div",{className:"w-32 h-32 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center relative",children:[N.profileImage?(0,r.jsx)("img",{src:N.profileImage,alt:d.name,className:"w-full h-full object-cover"}):(0,r.jsx)(c.x$1,{className:"text-gray-400 text-5xl"}),(0,r.jsxs)("label",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer",children:[(0,r.jsx)("span",{className:"text-white text-sm",children:"Change Photo"}),(0,r.jsx)("input",{type:"file",accept:"image/*",className:"hidden",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];if(a){let e=new FileReader;e.onloadend=()=>{w(s=>({...s,profileImage:e.result}))},e.readAsDataURL(a)}}})]})]}):(0,r.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center",children:d.profileImage?(0,r.jsx)("img",{src:d.profileImage,alt:d.name,className:"w-full h-full object-cover"}):(0,r.jsx)(c.x$1,{className:"text-gray-400 text-5xl"})})}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:d.name}),(0,r.jsx)("p",{className:"text-gray-500 mb-2",children:d.email}),(0,r.jsx)("div",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mb-4",children:d.rank.name}),(0,r.jsx)("div",{className:"w-full mt-4 space-y-2",children:p?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>{d&&w({name:d.name,phone:d.phone||"",profileImage:d.profileImage||""}),v({currentPassword:"",newPassword:"",confirmNewPassword:""}),g(!1),j(!1),f("")},className:"flex-1 flex items-center justify-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300",children:[(0,r.jsx)(c.QCr,{className:"mr-2"})," Cancel"]}),(0,r.jsxs)("button",{type:"submit",form:"profile-form",className:"flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:P,children:[P?(0,r.jsx)(c.hW,{className:"animate-spin mr-2"}):(0,r.jsx)(c.CMH,{className:"mr-2"}),"Save"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{onClick:()=>g(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[(0,r.jsx)(c.uO9,{className:"mr-2"})," Edit Profile"]}),(0,r.jsxs)(i(),{href:"/profile/payment-methods",className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[(0,r.jsx)(c.x1c,{className:"mr-2"})," Manage Payment Methods"]})]})})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 md:col-span-2",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Profile Information"}),(0,r.jsx)("form",{id:"profile-form",onSubmit:A,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),p?(0,r.jsx)("input",{type:"text",name:"name",value:N.name,onChange:I,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0}):(0,r.jsx)("p",{className:"text-gray-900",children:d.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,r.jsx)("p",{className:"text-gray-900",children:d.email}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Email address cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),p?(0,r.jsx)("input",{type:"tel",name:"phone",value:N.phone,onChange:I,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}):(0,r.jsx)("p",{className:"text-gray-900",children:d.phone||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Member Since"}),(0,r.jsx)("p",{className:"text-gray-900",children:new Date(d.createdAt).toLocaleDateString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Upline"}),(0,r.jsx)("p",{className:"text-gray-900",children:d.upline?"".concat(d.upline.name," (").concat(d.upline.email,")"):"None"})]}),p&&(0,r.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-md font-medium",children:"Change Password"}),(0,r.jsxs)("button",{type:"button",onClick:()=>j(!b),className:"text-blue-600 hover:text-blue-800 flex items-center",children:[(0,r.jsx)(c.pXu,{className:"mr-1"}),b?"Cancel":"Change"]})]}),b&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),(0,r.jsx)("input",{type:"password",name:"currentPassword",value:y.currentPassword,onChange:R,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),(0,r.jsx)("input",{type:"password",name:"newPassword",value:y.newPassword,onChange:R,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0,minLength:8})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),(0,r.jsx)("input",{type:"password",name:"confirmNewPassword",value:y.confirmNewPassword,onChange:R,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]})]})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 md:col-span-3",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Account Statistics"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Wallet Balance"}),(0,r.jsxs)("p",{className:"text-xl font-semibold",children:["₱",d.walletBalance.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Purchases"}),(0,r.jsxs)("p",{className:"text-xl font-semibold",children:["₱",d.stats.totalPurchases.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Rebates Received"}),(0,r.jsxs)("p",{className:"text-xl font-semibold",children:["₱",d.stats.totalRebatesReceived.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Rebates Generated"}),(0,r.jsxs)("p",{className:"text-xl font-semibold",children:["₱",d.stats.totalRebatesGenerated.toFixed(2)]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Direct Downline"}),(0,r.jsx)("p",{className:"text-xl font-semibold",children:d.stats.directDownlineCount})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Downline"}),(0,r.jsx)("p",{className:"text-xl font-semibold",children:d._count.downline})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Purchases"}),(0,r.jsx)("p",{className:"text-xl font-semibold",children:d._count.purchases})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Rebate Transactions"}),(0,r.jsx)("p",{className:"text-xl font-semibold",children:d._count.rebatesReceived})]})]})]})]}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6 text-center",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Profile not found."})})]})})}},56735:(e,s,a)=>{Promise.resolve().then(a.bind(a,33246))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(56735)),_N_E=e.O()}]);
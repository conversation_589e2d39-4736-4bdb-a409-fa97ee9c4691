(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6242],{14117:(e,s,i)=>{Promise.resolve().then(i.bind(i,50786))},50786:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>o});var r=i(95155),n=i(12115),t=i(12108),a=i(35695),l=i(70357),c=i(29911);let d=e=>{let{ranks:s=[],currentRankId:i}=e,[t,a]=(0,n.useState)(i||null),l=e=>{a(t===e?null:e)},d=e=>{switch(null==e?void 0:e.toLowerCase()){case"starter":default:return"bg-gray-100 text-gray-800 border-gray-300";case"bronze":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"silver":return"bg-gray-200 text-gray-800 border-gray-400";case"gold":return"bg-yellow-200 text-yellow-800 border-yellow-400";case"platinum":return"bg-blue-100 text-blue-800 border-blue-300";case"diamond":return"bg-purple-100 text-purple-800 border-purple-300"}},o=[{id:1,name:"Starter",level:1,commissionRate:5,overrideBonus:0,leadershipBonus:0,benefits:[{icon:(0,r.jsx)(c.gdQ,{}),title:"5% Commission",description:"Earn 5% commission on personal sales"},{icon:(0,r.jsx)(c.YXz,{}),title:"Binary Structure",description:"Start building your downline with a binary structure"}]},{id:2,name:"Bronze",level:2,commissionRate:8,overrideBonus:1,leadershipBonus:0,benefits:[{icon:(0,r.jsx)(c.gdQ,{}),title:"8% Commission",description:"Earn 8% commission on personal sales"},{icon:(0,r.jsx)(c.MxO,{}),title:"1% Override Bonus",description:"Earn 1% override bonus on your direct downline's sales"},{icon:(0,r.jsx)(c.YXz,{}),title:"Team Building Tools",description:"Access to basic team building tools and resources"}]},{id:3,name:"Silver",level:3,commissionRate:10,overrideBonus:2,leadershipBonus:.5,benefits:[{icon:(0,r.jsx)(c.gdQ,{}),title:"10% Commission",description:"Earn 10% commission on personal sales"},{icon:(0,r.jsx)(c.MxO,{}),title:"2% Override Bonus",description:"Earn 2% override bonus on your direct downline's sales"},{icon:(0,r.jsx)(c.Z0L,{}),title:"0.5% Leadership Bonus",description:"Earn 0.5% leadership bonus on your entire organization's sales"},{icon:(0,r.jsx)(c.Wp,{}),title:"Silver Welcome Kit",description:"Receive a Silver rank welcome kit with exclusive products"}]},{id:4,name:"Gold",level:4,commissionRate:12,overrideBonus:3,leadershipBonus:1,benefits:[{icon:(0,r.jsx)(c.gdQ,{}),title:"12% Commission",description:"Earn 12% commission on personal sales"},{icon:(0,r.jsx)(c.MxO,{}),title:"3% Override Bonus",description:"Earn 3% override bonus on your direct downline's sales"},{icon:(0,r.jsx)(c.Z0L,{}),title:"1% Leadership Bonus",description:"Earn 1% leadership bonus on your entire organization's sales"},{icon:(0,r.jsx)(c.Wp,{}),title:"Gold Welcome Kit",description:"Receive a Gold rank welcome kit with exclusive products"},{icon:(0,r.jsx)(c.SBv,{}),title:"Recognition",description:"Recognition at regional events and online platforms"}]},{id:5,name:"Platinum",level:5,commissionRate:15,overrideBonus:4,leadershipBonus:1.5,benefits:[{icon:(0,r.jsx)(c.gdQ,{}),title:"15% Commission",description:"Earn 15% commission on personal sales"},{icon:(0,r.jsx)(c.MxO,{}),title:"4% Override Bonus",description:"Earn 4% override bonus on your direct downline's sales"},{icon:(0,r.jsx)(c.Z0L,{}),title:"1.5% Leadership Bonus",description:"Earn 1.5% leadership bonus on your entire organization's sales"},{icon:(0,r.jsx)(c.Wp,{}),title:"Platinum Welcome Kit",description:"Receive a Platinum rank welcome kit with exclusive products"},{icon:(0,r.jsx)(c.SBv,{}),title:"VIP Recognition",description:"VIP recognition at national events and online platforms"},{icon:(0,r.jsx)(c.YXz,{}),title:"Leadership Training",description:"Access to exclusive leadership training and development programs"}]},{id:6,name:"Diamond",level:6,commissionRate:20,overrideBonus:5,leadershipBonus:2,benefits:[{icon:(0,r.jsx)(c.gdQ,{}),title:"20% Commission",description:"Earn 20% commission on personal sales"},{icon:(0,r.jsx)(c.MxO,{}),title:"5% Override Bonus",description:"Earn 5% override bonus on your direct downline's sales"},{icon:(0,r.jsx)(c.Z0L,{}),title:"2% Leadership Bonus",description:"Earn 2% leadership bonus on your entire organization's sales"},{icon:(0,r.jsx)(c.Wp,{}),title:"Diamond Welcome Kit",description:"Receive a Diamond rank welcome kit with exclusive products and luxury items"},{icon:(0,r.jsx)(c.SBv,{}),title:"Elite Recognition",description:"Elite recognition at international events and online platforms"},{icon:(0,r.jsx)(c.YXz,{}),title:"Executive Training",description:"Access to exclusive executive training and development programs"},{icon:(0,r.jsx)(c.MxO,{}),title:"Annual Bonus Pool",description:"Participate in the annual Diamond bonus pool"}]}],m=s.length>0?s:o;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,r.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,r.jsx)(c.SBv,{className:"mr-2 text-yellow-500"})," Rank Benefits"]})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:m.map(e=>(0,r.jsxs)("div",{className:"overflow-hidden",children:[(0,r.jsxs)("button",{onClick:()=>l(e.id),className:"w-full px-4 py-3 flex justify-between items-center text-left transition-colors ".concat(e.id===i?"bg-blue-50":"hover:bg-gray-50"),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3 ".concat(d(e.name)),children:e.level}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Level ",e.level]})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[e.id===i&&(0,r.jsx)("span",{className:"mr-3 text-xs bg-green-100 text-green-800 px-2 py-1 rounded",children:"Current"}),t===e.id?(0,r.jsx)(c.Ucs,{className:"text-gray-400"}):(0,r.jsx)(c.Vr3,{className:"text-gray-400"})]})]}),t===e.id&&(0,r.jsxs)("div",{className:"px-4 py-3 bg-gray-50",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-white p-3 rounded border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center text-blue-600 mb-1",children:[(0,r.jsx)(c.gdQ,{className:"mr-1"}),(0,r.jsx)("span",{className:"font-medium",children:"Commission Rate"})]}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[e.commissionRate,"%"]})]}),(0,r.jsxs)("div",{className:"bg-white p-3 rounded border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center text-green-600 mb-1",children:[(0,r.jsx)(c.MxO,{className:"mr-1"}),(0,r.jsx)("span",{className:"font-medium",children:"Override Bonus"})]}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[e.overrideBonus,"%"]})]}),(0,r.jsxs)("div",{className:"bg-white p-3 rounded border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center text-purple-600 mb-1",children:[(0,r.jsx)(c.Z0L,{className:"mr-1"}),(0,r.jsx)("span",{className:"font-medium",children:"Leadership Bonus"})]}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[e.leadershipBonus,"%"]})]})]}),(0,r.jsx)("h5",{className:"font-medium text-gray-700 mb-2",children:"Additional Benefits"}),(0,r.jsx)("ul",{className:"space-y-2",children:e.benefits.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"mt-1 mr-2 text-blue-500",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-800",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},s))})]})]},e.id))})]})};function o(){var e;let{data:s,status:i}=(0,t.useSession)(),o=(0,a.useRouter)(),[m,x]=(0,n.useState)(!0),[u,h]=(0,n.useState)(null),[p,g]=(0,n.useState)([]),[j,b]=(0,n.useState)(!1),[v,f]=(0,n.useState)(null),[N,y]=(0,n.useState)(!1);(0,n.useEffect)(()=>{"unauthenticated"===i&&o.push("/login")},[i,o]),(0,n.useEffect)(()=>{"authenticated"===i&&(w(),k())},[i]);let w=async()=>{x(!0);try{let e=await fetch("/api/users/rank-advancement");if(!e.ok)throw Error("Failed to fetch rank advancement eligibility");let s=await e.json();h(s)}catch(e){console.error("Error fetching rank advancement eligibility:",e)}finally{x(!1)}},k=async()=>{try{let e=await fetch("/api/users/rank-advancement/history");if(!e.ok)throw Error("Failed to fetch rank advancement history");let s=await e.json();g(s.rankAdvancements||[])}catch(e){console.error("Error fetching rank advancement history:",e)}},q=async()=>{if(null==u?void 0:u.eligible){b(!0);try{let e=await fetch("/api/users/rank-advancement",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to process rank advancement");let s=await e.json();f(s),s.success&&(await w(),await k())}catch(e){console.error("Error processing rank advancement:",e),f({success:!1,message:"An error occurred while processing your rank advancement."})}finally{b(!1)}}},S=e=>{switch(null==e?void 0:e.toLowerCase()){case"starter":default:return"bg-gray-100 text-gray-800";case"bronze":return"bg-yellow-100 text-yellow-800";case"silver":return"bg-gray-200 text-gray-800";case"gold":return"bg-yellow-200 text-yellow-800";case"platinum":return"bg-blue-100 text-blue-800";case"diamond":return"bg-purple-100 text-purple-800"}},R=e=>e>=100?"bg-green-500":e>=75?"bg-blue-500":e>=50?"bg-yellow-500":"bg-red-500";return"loading"===i||m?(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})]})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-semibold mb-6 flex items-center",children:[(0,r.jsx)(c.SBv,{className:"mr-2 text-yellow-500"})," Rank Advancement"]}),u&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,r.jsx)(c.tz0,{className:"mr-2 text-blue-500"})," Your Current Rank"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"px-4 py-2 rounded-full text-lg font-medium ".concat(S(u.currentRank.name)),children:u.currentRank.name}),u.nextRank&&(0,r.jsxs)("div",{className:"flex items-center ml-4",children:[(0,r.jsx)(c.uCC,{className:"text-gray-400 mx-2"}),(0,r.jsxs)("div",{className:"text-gray-500",children:["Next: ",u.nextRank.name]})]})]})]}),u&&u.nextRank&&u.requirements&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,r.jsx)(c.YYR,{className:"mr-2 text-green-500"})," Requirements for ",u.nextRank.name]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.AsH,{className:"text-blue-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Personal Sales"})]}),u.requirements.personalSales.qualified?(0,r.jsx)(c.CMH,{className:"text-green-500"}):(0,r.jsx)(c.QCr,{className:"text-red-500"})]}),(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:["₱",u.requirements.personalSales.actual.toLocaleString()," / ₱",u.requirements.personalSales.required.toLocaleString()]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,r.jsx)("div",{className:"h-2.5 rounded-full ".concat(R(u.requirements.personalSales.actual/u.requirements.personalSales.required*100)),style:{width:"".concat(Math.min(u.requirements.personalSales.actual/u.requirements.personalSales.required*100,100),"%")}})})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.AsH,{className:"text-purple-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Group Sales"})]}),u.requirements.groupSales.qualified?(0,r.jsx)(c.CMH,{className:"text-green-500"}):(0,r.jsx)(c.QCr,{className:"text-red-500"})]}),(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:["₱",u.requirements.groupSales.actual.toLocaleString()," / ₱",u.requirements.groupSales.required.toLocaleString()]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,r.jsx)("div",{className:"h-2.5 rounded-full ".concat(R(u.requirements.groupSales.actual/u.requirements.groupSales.required*100)),style:{width:"".concat(Math.min(u.requirements.groupSales.actual/u.requirements.groupSales.required*100,100),"%")}})})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.YXz,{className:"text-green-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Direct Downline"})]}),u.requirements.directDownline.qualified?(0,r.jsx)(c.CMH,{className:"text-green-500"}):(0,r.jsx)(c.QCr,{className:"text-red-500"})]}),(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[u.requirements.directDownline.actual," /",u.requirements.directDownline.required]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,r.jsx)("div",{className:"h-2.5 rounded-full ".concat(R(u.requirements.directDownline.actual/u.requirements.directDownline.required*100)),style:{width:"".concat(Math.min(u.requirements.directDownline.actual/u.requirements.directDownline.required*100,100),"%")}})})]})]}),u.requirements.qualifiedDownline.required>0&&(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.YXz,{className:"text-yellow-500 mr-2"}),(0,r.jsx)("h3",{className:"font-medium",children:"Qualified Downline"})]}),u.requirements.qualifiedDownline.qualified?(0,r.jsx)(c.CMH,{className:"text-green-500"}):(0,r.jsx)(c.QCr,{className:"text-red-500"})]}),(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[u.requirements.qualifiedDownline.actual," /",u.requirements.qualifiedDownline.required]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,r.jsx)("div",{className:"h-2.5 rounded-full ".concat(R(u.requirements.qualifiedDownline.actual/u.requirements.qualifiedDownline.required*100)),style:{width:"".concat(Math.min(u.requirements.qualifiedDownline.actual/u.requirements.qualifiedDownline.required*100,100),"%")}})})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 rounded-lg border border-gray-200",children:[(0,r.jsx)("div",{className:"flex items-center",children:u.eligible?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-green-100 text-green-500 mr-3",children:(0,r.jsx)(c.CMH,{})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-green-600",children:"You are eligible for advancement!"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["You have met all the requirements to advance to ",u.nextRank.name,"."]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-yellow-100 text-yellow-500 mr-3",children:(0,r.jsx)(c.uCC,{})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-600",children:"Keep working towards your next rank!"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Continue building your network and increasing your sales to reach ",u.nextRank.name,"."]})]})]})}),u.eligible&&(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("button",{onClick:q,disabled:j,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.hW,{className:"animate-spin mr-2"})," Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.uCC,{className:"mr-2"})," Advance to ",u.nextRank.name]})})})]}),v&&(0,r.jsx)("div",{className:"mt-4 p-4 rounded-lg ".concat(v.success?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[v.success?(0,r.jsx)(c.CMH,{className:"text-green-500 mr-2"}):(0,r.jsx)(c.QCr,{className:"text-red-500 mr-2"}),(0,r.jsx)("p",{className:v.success?"text-green-700":"text-red-700",children:v.message})]})})]}),u&&!u.nextRank&&(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-3 rounded-full bg-purple-100 text-purple-500 mr-4",children:(0,r.jsx)(c.SBv,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Congratulations!"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["You have reached the highest rank in our program: ",(0,r.jsx)("span",{className:"font-medium",children:u.currentRank.name}),"."]})]})]})}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,r.jsx)(c.__w,{className:"mr-2 text-blue-500"})," Rank Benefits"]}),(0,r.jsx)("div",{className:"ml-2 text-sm text-gray-500",children:"Learn about the benefits of each rank"})]}),(0,r.jsx)(d,{ranks:[],currentRankId:null==u||null==(e=u.currentRank)?void 0:e.id})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,r.jsx)(c.OKX,{className:"mr-2 text-blue-500"})," Rank Advancement History"]}),(0,r.jsx)("button",{onClick:()=>y(!N),className:"text-blue-500 hover:text-blue-700",children:N?"Hide":"Show"})]}),N&&(0,r.jsx)("div",{className:"p-6",children:p.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"From"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"To"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Personal Sales"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Group Sales"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Direct Downline"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(S(e.previousRank.name)),children:e.previousRank.name})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(S(e.newRank.name)),children:e.newRank.name})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.personalSales.toLocaleString()]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.groupSales.toLocaleString()]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.directDownlineCount})]},e.id))})]})}):(0,r.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No rank advancement history found."})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(14117)),_N_E=e.O()}]);
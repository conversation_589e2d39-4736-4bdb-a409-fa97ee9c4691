(()=>{var e={};e.id=7250,e.ids=[7072,7250],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27072:(e,r,t)=>{"use strict";let a,n;t.d(r,{Z3:()=>d,nl:()=>f,getShareableLinkByCode:()=>l,F2:()=>y,qY:()=>k,Yx:()=>m,GI:()=>w,recordReferralPurchase:()=>g,p1:()=>p});var i=t(31183),s=t(55511);let o=e=>{!a||a.length<e?(a=Buffer.allocUnsafe(128*e),s.randomFillSync(a),n=0):n+e>a.length&&(s.randomFillSync(a),n=0),n+=e},u=(e=21)=>{o(e|=0);let r="";for(let t=n-e;t<n;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&a[t]];return r};async function c(){let e=u(8),r=!1;for(;!r;)await i.z.shareableLink.findUnique({where:{code:e}})?e=u(8):r=!0;return e}async function d(e,r,t){let a=await c();return await i.z.shareableLink.create({data:{userId:e,productId:r,code:a,type:"product",title:t?.title,description:t?.description,customImage:t?.customImage,expiresAt:t?.expiresAt,isActive:!0}})}async function l(e){return await i.z.shareableLink.findUnique({where:{code:e}})}async function m(e,r){let t={userId:e};r?.productId!==void 0&&(t.productId=r.productId),r?.type!==void 0&&(t.type=r.type),r?.isActive!==void 0&&(t.isActive=r.isActive);let a=await i.z.shareableLink.count({where:t});return{links:await i.z.shareableLink.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{product:{select:{id:!0,name:!0,price:!0,image:!0,referralCommissionType:!0,referralCommissionValue:!0}}}}),total:a}}async function p(e,r){return await i.z.shareableLink.update({where:{id:e},data:{...r,updatedAt:new Date}})}async function f(e){return await i.z.shareableLink.delete({where:{id:e}})}async function w(e,r){return await i.z.shareableLink.update({where:{id:e},data:{clickCount:{increment:1},updatedAt:new Date}}),await i.z.linkClick.create({data:{linkId:e,ipAddress:r?.ipAddress,userAgent:r?.userAgent,referrer:r?.referrer,utmSource:r?.utmSource,utmMedium:r?.utmMedium,utmCampaign:r?.utmCampaign}})}async function h(e,r){let t=await i.z.product.findUnique({where:{id:e},select:{referralCommissionType:!0,referralCommissionValue:!0}});return t&&t.referralCommissionType&&t.referralCommissionValue?"percentage"===t.referralCommissionType?{amount:r*(t.referralCommissionValue/100),percentage:t.referralCommissionValue}:{amount:t.referralCommissionValue,percentage:t.referralCommissionValue/r*100}:{amount:.05*r,percentage:5}}async function g(e,r){let t=await i.z.purchase.findUnique({where:{id:e},include:{product:!0,user:!0,referralLink:{include:{user:!0}}}});if(!t)throw Error(`Purchase with ID ${e} not found`);if(!t.referralLink)throw Error(`Purchase with ID ${e} has no referral link`);let{amount:a,percentage:n}=await h(t.productId,t.totalAmount),s=await i.z.referralCommission.create({data:{purchaseId:e,linkId:r,referrerId:t.referralLink.userId,buyerId:t.userId,productId:t.productId,amount:a,percentage:n,status:"pending"}});return await i.z.shareableLink.update({where:{id:r},data:{conversionCount:{increment:1},totalRevenue:{increment:t.totalAmount},totalCommission:{increment:a},updatedAt:new Date}}),s}async function y(e,r){let t={referrerId:e};r?.status!==void 0&&(t.status=r.status);let a=await i.z.referralCommission.count({where:t});return{commissions:await i.z.referralCommission.findMany({where:t,orderBy:{createdAt:"desc"},take:r?.limit,skip:r?.offset,include:{purchase:{select:{id:!0,quantity:!0,totalAmount:!0,createdAt:!0}},buyer:{select:{id:!0,name:!0,email:!0}},product:{select:{id:!0,name:!0,price:!0,image:!0}},link:{select:{id:!0,code:!0,type:!0}}}}),total:a}}async function k(e){let r=await i.z.shareableLink.aggregate({where:{userId:e},_sum:{clickCount:!0,conversionCount:!0,totalRevenue:!0,totalCommission:!0},_count:{id:!0}}),t=r._sum.clickCount||0,a=r._sum.conversionCount||0;return{totalLinks:r._count.id,totalClicks:t,totalConversions:a,totalRevenue:r._sum.totalRevenue||0,totalCommission:r._sum.totalCommission||0,conversionRate:t>0?a/t*100:0}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var a=t(96330);let n=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},81720:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>p,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{GET:()=>c});var n=t(96559),i=t(48088),s=t(37719),o=t(32190),u=t(27072);async function c(e,{params:r}){try{let{code:t}=r,a=await (0,u.getShareableLinkByCode)(t);if(!a)return new o.NextResponse("Link not found",{status:404});if(!a.isActive)return new o.NextResponse("This link is no longer active",{status:400});if(a.expiresAt&&new Date(a.expiresAt)<new Date)return new o.NextResponse("This link has expired",{status:400});let n=e.headers,i=n.get("user-agent"),s=n.get("referer"),c=n.get("x-forwarded-for")||"unknown",d=new URL(e.url),l=d.searchParams.get("utm_source"),m=d.searchParams.get("utm_medium"),p=d.searchParams.get("utm_campaign");await (0,u.GI)(a.id,{ipAddress:c,userAgent:i||void 0,referrer:s||void 0,utmSource:l||void 0,utmMedium:m||void 0,utmCampaign:p||void 0});let f="/";return"product"===a.type&&a.productId&&(f=`/shop/product/${a.productId}?ref=${t}`),o.NextResponse.redirect(new URL(f,e.url))}catch(e){return console.error("Error processing shareable link:",e),new o.NextResponse("An error occurred",{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/s/[code]/route",pathname:"/s/[code]",filename:"route",bundlePath:"app/s/[code]/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\s\\[code]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:p}=d;function f(){return(0,s.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4243,580],()=>t(81720));module.exports=a})();
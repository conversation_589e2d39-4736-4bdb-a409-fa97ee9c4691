(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3666],{16116:(e,s,a)=>{Promise.resolve().then(a.bind(a,70908))},70908:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var i=a(95155),l=a(12115),t=a(12108),n=a(87747),r=a(29911),c=a(6874),d=a.n(c),o=a(55028),h=a(74211);let x=(0,o.default)(()=>a.e(9465).then(a.bind(a,39465)),{loadableGenerated:{webpack:()=>[39465]},ssr:!1,loading:()=>(0,i.jsxs)("div",{className:"flex items-center justify-center h-40",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading statistics..."})]})}),m=(0,o.default)(()=>Promise.all([a.e(1294),a.e(6808),a.e(186)]).then(a.bind(a,20186)),{loadableGenerated:{webpack:()=>[20186]},ssr:!1,loading:()=>(0,i.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading genealogy visualization..."})]})});function u(){let{data:e,status:s}=(0,t.useSession)(),[a,c]=(0,l.useState)(void 0),[o,u]=(0,l.useState)("vertical"),{data:j,isLoading:b}=(0,n.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let a=await fetch("/api/users/me");if(!a.ok)throw Error("Failed to fetch user data");return await a.json()},enabled:"authenticated"===s});return(j&&!a&&c(j.id),"loading"===s||b)?(0,i.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,i.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,i.jsx)(r.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,i.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,i.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,i.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,i.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold",children:"Enhanced Genealogy Flow"}),(0,i.jsx)("p",{className:"text-gray-600",children:"An improved implementation of the genealogy tree with animation and better layout"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("button",{onClick:()=>u("vertical"===o?"horizontal":"vertical"),className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700",children:["Switch to ","vertical"===o?"Horizontal":"Vertical"," Layout"]}),(0,i.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,i.jsx)(r.QVr,{className:"mr-1"}),"Back to Genealogy"]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:[(0,i.jsx)("div",{className:"lg:col-span-3",children:(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:a?(0,i.jsx)(h.Ln,{children:(0,i.jsx)(m,{userId:a,maxLevel:3,initialLayout:o})}):(0,i.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading genealogy data..."})]})})}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,i.jsxs)("div",{className:"bg-blue-50 p-3 border-b border-blue-100 flex items-center",children:[(0,i.jsx)(r.v$b,{className:"text-blue-500 mr-2"}),(0,i.jsx)("h3",{className:"font-medium",children:"Network Statistics"})]}),a?(0,i.jsx)(x,{userId:a}):(0,i.jsxs)("div",{className:"flex items-center justify-center h-40",children:[(0,i.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading statistics..."})]})]})})]}),(0,i.jsxs)("div",{className:"mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,i.jsx)("h2",{className:"text-lg font-medium text-blue-800 mb-2",children:"About Enhanced Genealogy Flow"}),(0,i.jsx)("p",{className:"text-blue-700 mb-2",children:"This is an enhanced implementation of the genealogy tree using React Flow. It provides:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-blue-700 space-y-1",children:[(0,i.jsx)("li",{children:"Interactive node-based visualization with animation"}),(0,i.jsx)("li",{children:"Multiple layout options (vertical and horizontal)"}),(0,i.jsx)("li",{children:"Enhanced node design with performance metrics"}),(0,i.jsx)("li",{children:"Improved user details panel"}),(0,i.jsx)("li",{children:"Color-coded minimap for better navigation"}),(0,i.jsx)("li",{children:"Advanced filtering and search capabilities"})]}),(0,i.jsx)("p",{className:"text-blue-700 mt-2",children:"This implementation showcases the power of React Flow for creating interactive genealogy visualizations."})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,8702,6874,2108,5557,6967,7747,6113,8441,1684,7358],()=>s(16116)),_N_E=e.O()}]);
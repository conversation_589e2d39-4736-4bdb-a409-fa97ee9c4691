(()=>{var e={};e.id=4388,e.ids=[4388],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>l,Nh:()=>c,aP:()=>u});var a=r(96330),n=r(13581),s=r(85663),o=r(55511),i=r.n(o);async function l(e){return await s.Ay.hash(e,10)}function u(){let e=i().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new a.PrismaClient;let c={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,n.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new a.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let n=await s.Ay.compare(e.password,r.password);if(console.log("Password valid:",n),!n)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:o,...i}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},16816:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{POST:()=>m});var n=r(96559),s=r(48088),o=r(37719),i=r(32190),l=r(19854),u=r(12909),c=r(31183),d=r(70762);let p=d.z.object({query:d.z.string().optional(),name:d.z.string().optional(),email:d.z.string().optional(),rankId:d.z.number().int().positive().optional(),minDownline:d.z.number().int().nonnegative().optional(),maxDownline:d.z.number().int().nonnegative().optional(),joinedAfter:d.z.string().optional(),joinedBefore:d.z.string().optional(),minWalletBalance:d.z.number().nonnegative().optional(),maxWalletBalance:d.z.number().nonnegative().optional(),page:d.z.number().int().positive().default(1),pageSize:d.z.number().int().positive().max(100).default(20),includePerformanceMetrics:d.z.boolean().default(!1)});async function m(e){try{let t=await (0,l.getServerSession)(u.Nh);if(!t||!t.user)return i.NextResponse.json({error:"You must be logged in to search genealogy"},{status:401});let r=await e.json(),a=p.safeParse(r);if(!a.success)return i.NextResponse.json({error:"Invalid search parameters",details:a.error.format()},{status:400});let{query:n,name:s,email:o,rankId:d,minDownline:m,maxDownline:g,joinedAfter:w,joinedBefore:f,minWalletBalance:h,maxWalletBalance:v,page:y,pageSize:b,includePerformanceMetrics:x}=a.data,P={};if(n){let e=parseInt(n);isNaN(e)?P.OR=[{name:{contains:n,mode:"insensitive"}},{email:{contains:n,mode:"insensitive"}}]:P.OR=[{id:e},{name:{contains:n,mode:"insensitive"}},{email:{contains:n,mode:"insensitive"}}]}s&&(P.name={contains:s,mode:"insensitive"}),o&&(P.email={contains:o,mode:"insensitive"}),d&&(P.rankId=d),(w||f)&&(P.createdAt={},w&&(P.createdAt.gte=new Date(w)),f&&(P.createdAt.lte=new Date(f))),(void 0!==h||void 0!==v)&&(P.walletBalance={},void 0!==h&&(P.walletBalance.gte=h),void 0!==v&&(P.walletBalance.lte=v));let j=(y-1)*b,A=await c.z.user.count({where:P}),_=await c.z.user.findMany({where:P,select:{id:!0,name:!0,email:!0,rankId:!0,walletBalance:!0,createdAt:!0,uplineId:!0,rank:{select:{id:!0,name:!0}},_count:{select:{downline:!0}}},skip:j,take:b,orderBy:{createdAt:"desc"}}),k=_;(void 0!==m||void 0!==g)&&(k=_.filter(e=>{let t=e._count.downline;return(void 0===m||!(t<m))&&(void 0===g||!(t>g))}));let z=k;if(x){let e=k.map(e=>e.id),t=await Promise.all(e.map(async e=>{let t=await c.z.purchase.aggregate({where:{userId:e,status:"completed"},_sum:{totalAmount:!0}}),r=(await c.z.user.findMany({where:{uplineId:e},select:{id:!0}})).map(e=>e.id),a=r.length>0?await c.z.purchase.aggregate({where:{userId:{in:r},status:"completed"},_sum:{totalAmount:!0}}):{_sum:{totalAmount:null}},n=await c.z.rebate.aggregate({where:{receiverId:e,status:"completed"},_sum:{amount:!0}}),s=await c.z.user.count({where:{uplineId:e,createdAt:{gte:new Date(Date.now()-2592e6)}}});return{userId:e,personalSales:t._sum.totalAmount||0,teamSales:a._sum.totalAmount||0,totalSales:(t._sum.totalAmount||0)+(a._sum.totalAmount||0),rebatesEarned:n._sum.amount||0,teamSize:r.length,newTeamMembers:s,lastUpdated:new Date}}));z=k.map(e=>{let r=t.find(t=>t.userId===e.id);return{...e,performanceMetrics:r||null}})}let O=Math.ceil(A/b);return i.NextResponse.json({users:z,pagination:{page:y,pageSize:b,totalItems:A,totalPages:O,hasNextPage:y<O,hasPreviousPage:y>1},metadata:{query:n,filters:{name:s,email:o,rankId:d,minDownline:m,maxDownline:g,joinedAfter:w,joinedBefore:f,minWalletBalance:h,maxWalletBalance:v},includePerformanceMetrics:x}})}catch(e){return console.error("Error searching genealogy:",e),i.NextResponse.json({error:"Failed to search genealogy"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/genealogy/search/route",pathname:"/api/genealogy/search",filename:"route",bundlePath:"app/api/genealogy/search/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\genealogy\\search\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:h}=g;function v(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var n=r(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var i=n?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(a,s,i):a[s]=e[s]}return a.default=e,r&&r.set(e,a),a}(r(35426));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var a=r(96330);let n=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8044,3112,8381],()=>r(16816));module.exports=a})();
"use strict";(()=>{var e={};e.id=902,e.ids=[902],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35565:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>q,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>c});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(35426),p=t(12909),d=t(28990);async function c(e){try{let r=await (0,u.getServerSession)(p.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});if("admin"!==r.user.role)return i.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let t=await e.json();if(!t.cleanupToken)return i.NextResponse.json({error:"Cleanup token is required"},{status:400});let s=await (0,d.T)(t.cleanupToken);if(!s.success)return i.NextResponse.json({error:s.message},{status:500});return i.NextResponse.json(s)}catch(e){return console.error("Error cleaning up test data:",e),i.NextResponse.json({error:"Failed to clean up test data"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/test-data/cleanup/route",pathname:"/api/admin/test-data/cleanup",filename:"route",bundlePath:"app/api/admin/test-data/cleanup/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-data\\cleanup\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:q}=l;function g(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,1742,5626],()=>t(35565));module.exports=s})();
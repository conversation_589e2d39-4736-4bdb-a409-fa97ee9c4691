(()=>{var e={};e.id=1730,e.ids=[1730],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49386:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\wallet\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\wallet\\page.tsx","default")},62682:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["wallet",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49386)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\wallet\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\wallet\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/wallet/page",pathname:"/wallet",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63683:(e,t,s)=>{Promise.resolve().then(s.bind(s,49386))},70280:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687),a=s(43210),n=s(82136),i=s(16189),l=s(68367),d=s(23877);function o(){let{data:e,status:t}=(0,n.useSession)();(0,i.useRouter)();let[s,o]=(0,a.useState)({balance:0,transactions:[]}),[c,p]=(0,a.useState)(!0),[x,m]=(0,a.useState)(""),[u,h]=(0,a.useState)(""),[b,g]=(0,a.useState)(!1),[f,y]=(0,a.useState)({type:"",text:""}),w=async t=>{if(t.preventDefault(),!e)return;let r=parseFloat(x);if(isNaN(r)||r<=0)return void y({type:"error",text:"Please enter a valid amount"});if(r>s.balance)return void y({type:"error",text:"Insufficient balance"});g(!0),y({type:"",text:""});try{let e=await fetch("/api/wallet",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:r,type:"withdrawal",description:u||"Withdrawal request"})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to process withdrawal");let s=await fetch("/api/wallet"),a=await s.json();o(a),y({type:"success",text:"Withdrawal request processed successfully"}),m(""),h("")}catch(e){y({type:"error",text:e.message||"An error occurred during withdrawal"})}finally{g(!1)}};return"loading"===t||c?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold mb-6",children:"Wallet"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-500 mr-4",children:(0,r.jsx)(d.lcY,{className:"h-6 w-6"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Wallet Balance"})]}),(0,r.jsxs)("div",{className:"text-3xl font-bold mb-4",children:["₱",s.balance.toFixed(2)]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Withdraw Funds"}),f.text&&(0,r.jsx)("div",{className:`mb-4 p-3 rounded-md text-sm ${"success"===f.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,children:f.text}),(0,r.jsxs)("form",{onSubmit:w,children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 mb-1",children:"Amount"}),(0,r.jsx)("input",{type:"number",id:"amount",value:x,onChange:e=>m(e.target.value),min:"0.01",step:"0.01",max:s.balance,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (Optional)"}),(0,r.jsx)("input",{type:"text",id:"description",value:u,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsx)("button",{type:"submit",disabled:b,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300",children:b?"Processing...":"Withdraw"})]})]})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Transaction History"})}),(0,r.jsx)("div",{className:"p-6",children:s.transactions.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.transactions.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"rebate"===e.type?"bg-green-100 text-green-800":"withdrawal"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:["rebate"===e.type?(0,r.jsx)(d.$TP,{className:"mr-1"}):(0,r.jsx)(d.uCC,{className:"mr-1"}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("span",{className:"withdrawal"===e.type?"text-red-600":"text-green-600",children:["withdrawal"===e.type?"-":"+","₱",e.amount.toFixed(2)]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.description}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}):(0,r.jsx)("p",{className:"text-gray-500",children:"No transactions yet."})})]})})]})]})})}},77755:(e,t,s)=>{Promise.resolve().then(s.bind(s,70280))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>s(62682));module.exports=r})();
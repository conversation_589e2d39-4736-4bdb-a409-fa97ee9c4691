(()=>{var e={};e.id=9987,e.ids=[9987],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6319:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\referrals\\\\generate\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\generate\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["referrals",{children:["generate",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6319)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\generate\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\referrals\\generate\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/referrals/generate/page",pathname:"/referrals/generate",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32717:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),n=r(43210),a=r(82136),i=r(16189),l=r(30474),o=r(85814),d=r.n(o),c=r(68367),m=r(23877);function u(){let{data:e,status:t}=(0,a.useSession)();(0,i.useRouter)(),(0,i.useSearchParams)();let[r,o]=(0,n.useState)(!0),[u,x]=(0,n.useState)([]),[p,h]=(0,n.useState)(null),[g,b]=(0,n.useState)(null),[f,j]=(0,n.useState)(!1),[y,w]=(0,n.useState)(null),[v,N]=(0,n.useState)(!1),[k,C]=(0,n.useState)(!1),[P,$]=(0,n.useState)(""),[S,_]=(0,n.useState)(""),L=async()=>{if(!p)return void w("Please select a product to share");try{N(!0),w(null);let e=await fetch("/api/shareable-links",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:p.id,title:P||p.name,description:S||p.description,customImage:p.image})});if(!e.ok)throw Error("Failed to generate link");let t=await e.json();b(t.link)}catch(e){console.error("Error generating link:",e),w("Failed to generate link. Please try again.")}finally{N(!1)}},M=async()=>{if(g)try{let e=window.location.origin,t=`${e}/s/${g.code}`;await navigator.clipboard.writeText(t),j(!0),setTimeout(()=>{j(!1)},2e3)}catch(e){console.error("Error copying to clipboard:",e),w("Failed to copy link to clipboard")}},A=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e);return"loading"===t||r?(0,s.jsx)(c.A,{children:(0,s.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,s.jsx)(m.hW,{className:"animate-spin text-green-500 mr-2"}),(0,s.jsx)("span",{children:"Loading..."})]})}):(0,s.jsx)(c.A,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)(d(),{href:"/referrals",className:"inline-flex items-center text-blue-600 hover:underline",children:[(0,s.jsx)(m.QVr,{className:"mr-2"}),"Back to Referrals"]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,s.jsxs)("div",{className:"p-4 bg-green-50 border-b border-green-100",children:[(0,s.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,s.jsx)(m.AnD,{className:"mr-2 text-green-600"}),"Generate Shareable Link"]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Create a custom link to share products and earn commissions"})]}),(0,s.jsxs)("div",{className:"p-6",children:[y&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 text-red-700 rounded-md",children:y}),g?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Your Shareable Link"}),(0,s.jsxs)("div",{className:"mb-4 p-4 bg-gray-50 rounded-md flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-gray-700 mr-2 break-all",children:[window.location.origin,"/s/",g.code]}),(0,s.jsx)("button",{type:"button",className:"flex-shrink-0 text-blue-600 hover:text-blue-800",onClick:M,title:"Copy link",children:f?(0,s.jsx)(m.CMH,{className:"text-green-600"}):(0,s.jsx)(m.paH,{})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Share Your Link"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-green-50",onClick:()=>{if(!g)return;let e=window.location.origin,t=`${e}/s/${g.code}`,r=`Check out ${P||p?.name}! ${t}`;window.open(`https://wa.me/?text=${encodeURIComponent(r)}`,"_blank")},children:[(0,s.jsx)(m.EcP,{className:"text-green-600 text-2xl mb-2"}),(0,s.jsx)("span",{className:"text-sm",children:"WhatsApp"})]}),(0,s.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-blue-50",onClick:()=>{if(!g)return;let e=window.location.origin,t=`${e}/s/${g.code}`;window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(t)}`,"_blank")},children:[(0,s.jsx)(m.iYk,{className:"text-blue-600 text-2xl mb-2"}),(0,s.jsx)("span",{className:"text-sm",children:"Facebook"})]}),(0,s.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-blue-50",onClick:()=>{if(!g)return;let e=window.location.origin,t=`${e}/s/${g.code}`,r=`Check out ${P||p?.name}! ${t}`;window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(r)}`,"_blank")},children:[(0,s.jsx)(m.feZ,{className:"text-blue-400 text-2xl mb-2"}),(0,s.jsx)("span",{className:"text-sm",children:"Twitter"})]}),(0,s.jsxs)("button",{type:"button",className:"flex flex-col items-center justify-center p-4 border border-gray-200 rounded-md hover:bg-purple-50",onClick:()=>{if(!g)return;let e=window.location.origin,t=`${e}/s/${g.code}`,r=`Check out ${P||p?.name}!`,s=`I thought you might be interested in this product:

${P||p?.name}

${S||p?.description}

${t}`;window.open(`mailto:?subject=${encodeURIComponent(r)}&body=${encodeURIComponent(s)}`)},children:[(0,s.jsx)(m.maD,{className:"text-purple-600 text-2xl mb-2"}),(0,s.jsx)("span",{className:"text-sm",children:"Email"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("button",{type:"button",className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",onClick:()=>b(null),children:"Generate Another Link"}),(0,s.jsx)(d(),{href:"/referrals",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"View All My Links"})]})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select a product to share"}),(0,s.jsxs)("select",{className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500",value:p?.id||"",onChange:e=>{let t=parseInt(e.target.value),r=u.find(e=>e.id===t)||null;h(r),r&&($(r.name),_(r.description||""))},children:[(0,s.jsx)("option",{value:"",children:"-- Select a product --"}),u.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," - ",A(e.price)," (",e.pv," PV)"]},e.id))]})]}),p&&(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-24 w-24 bg-gray-100 rounded-md overflow-hidden mr-4",children:p.image?(0,s.jsx)(l.default,{src:p.image,alt:p.name,width:96,height:96,className:"object-cover w-full h-full"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,s.jsx)(m.AsH,{className:"text-gray-400 h-8 w-8"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:p.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500 line-clamp-2",children:p.description}),(0,s.jsxs)("div",{className:"mt-1 flex items-center",children:[(0,s.jsx)("span",{className:"text-green-600 font-medium mr-3",children:A(p.price)}),(0,s.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:[p.pv," PV"]})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Title (Optional)"}),(0,s.jsx)("input",{type:"text",className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500",placeholder:"Enter a custom title for your link",value:P,onChange:e=>$(e.target.value)}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This will be displayed when your link is shared on social media"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Description (Optional)"}),(0,s.jsx)("textarea",{className:"w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500",rows:3,placeholder:"Enter a custom description for your link",value:S,onChange:e=>_(e.target.value)}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"This will be displayed when your link is shared on social media"})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"button",className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",onClick:L,disabled:!p||v,children:v?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.hW,{className:"animate-spin mr-2"}),"Generating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.AnD,{className:"mr-2"}),"Generate Shareable Link"]})})})]})]})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},42407:(e,t,r)=>{Promise.resolve().then(r.bind(r,6319))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72671:(e,t,r)=>{Promise.resolve().then(r.bind(r,32717))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,8414,9567,3877,474,4859,3024],()=>r(26142));module.exports=s})();
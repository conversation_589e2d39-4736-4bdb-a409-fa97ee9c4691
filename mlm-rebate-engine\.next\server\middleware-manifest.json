{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "SfTHDGnElQRS_my0fwI_O", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "rRsb5Qggg+dlbwNeBai2mrJvMy1WFxCEb6EuHDLVGQ0=", "__NEXT_PREVIEW_MODE_ID": "8772be69543c5a82f2bb1ef3ee501886", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e707894df497f23160485f8718fec4f6b42c182e418accbbb10dac38fdbe006", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3ea6ed0d357cfd85c79fbd18621bbcb56efa7098514c7cb63ce2a33ada42b138"}}}, "functions": {}, "sortedMiddleware": ["/"]}
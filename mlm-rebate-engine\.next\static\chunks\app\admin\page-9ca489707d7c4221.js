(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{2437:(e,s,a)=>{Promise.resolve().then(a.bind(a,91539))},91539:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var t=a(95155),r=a(12115),l=a(12108),n=a(35695),i=a(70357),o=a(99526),d=a(6874),c=a.n(d),m=a(29911);let x=e=>{let{title:s,value:a,icon:r,borderColor:l,iconBgColor:n,iconColor:i,percentageChange:o,footer:d}=e,c="number"==typeof a?a.toLocaleString():a,x=o&&o>0,u=o&&o<0;return(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6 border-l-4 ".concat(l),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:s}),(0,t.jsxs)("div",{className:"flex items-baseline",children:[(0,t.jsx)("p",{className:"text-2xl font-bold",children:c}),void 0!==o&&(0,t.jsxs)("span",{className:"ml-2 text-xs font-medium flex items-center\n                  ".concat(x?"text-green-500":u?"text-red-500":"text-gray-500"),children:[x?(0,t.jsx)(m.uCC,{className:"mr-1"}):u?(0,t.jsx)(m.$TP,{className:"mr-1"}):null,Math.abs(o),"%"]})]}),d&&(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:d})]}),(0,t.jsx)("div",{className:"p-3 rounded-full ".concat(n," ").concat(i),children:r})]})})},u=e=>{let{distributors:s,showEarnings:a=!1,showReferrals:r=!1,title:l="Top Distributors",viewAllLink:n="/admin/reports"}=e;return s&&0!==s.length?(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold",children:l}),(0,t.jsxs)(c(),{href:n,className:"text-blue-600 hover:text-blue-800 text-sm flex items-center",children:["View All ",(0,t.jsx)(m.EQc,{className:"ml-1",size:12})]})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Distributor"}),(0,t.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),a&&(0,t.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Earnings"}),(0,t.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:r?"Direct Referrals":"Downline"})]})}),(0,t.jsx)("tbody",{className:"divide-y divide-gray-200",children:s.map(e=>{var s;return(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 font-medium",children:e.name.charAt(0)}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",e.id]})]})]})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,t.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:e.rank})}),a&&(0,t.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-500",children:["₱",(null==(s=e.earnings)?void 0:s.toLocaleString())||0]}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-500",children:r?e.directReferrals||0:e.downlineCount})]},e.id)})})]})})})]}):(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b",children:(0,t.jsx)("h2",{className:"text-lg font-semibold",children:l})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No distributor data available"})})]})},h=e=>{let{users:s,purchases:a,title:l="Recent Activity"}=e,[n,i]=(0,r.useState)("users"),o="users"===n&&s&&s.length>0||"purchases"===n&&a&&a.length>0;return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold",children:l}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"px-2 py-1 text-xs rounded-md ".concat("users"===n?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"),onClick:()=>i("users"),children:"Users"}),(0,t.jsx)("button",{className:"px-2 py-1 text-xs rounded-md ".concat("purchases"===n?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"),onClick:()=>i("purchases"),children:"Purchases"})]})]}),(0,t.jsx)("div",{className:"p-6",children:o?(0,t.jsxs)("div",{className:"space-y-4",children:["users"===n&&s.map(e=>(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center text-green-500",children:(0,t.jsx)(m.NPy,{})}),(0,t.jsxs)("div",{className:"ml-3 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["New ",e.rank," member joined"]})]})]},e.id)),"purchases"===n&&a.map(e=>(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-500",children:(0,t.jsx)(m.AsH,{})}),(0,t.jsxs)("div",{className:"ml-3 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.date).toLocaleDateString()})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Purchased ",e.productName," for ₱",e.amount.toLocaleString()]})]})]},e.id))]}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:["No ",n," activity available"]})})]})},g=e=>{let{onFilterChange:s,ranks:a=[]}=e,[l,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)({dateRange:"last30days",rankId:"",sponsorId:"",searchTerm:""}),d=a.length>0?a:[{id:1,name:"Starter"},{id:2,name:"Bronze"},{id:3,name:"Silver"},{id:4,name:"Gold"},{id:5,name:"Platinum"},{id:6,name:"Diamond"}],c=(e,a)=>{let t={...i,[e]:a};o(t),s&&s(t)},x=()=>{let e={dateRange:"last30days",rankId:"",sponsorId:"",searchTerm:""};o(e),s&&s(e)},u=i.rankId||i.sponsorId||i.searchTerm||"last30days"!==i.dateRange;return(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID",value:i.searchTerm,onChange:e=>c("searchTerm",e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(m.KSO,{className:"text-gray-400"})}),i.searchTerm&&(0,t.jsx)("button",{onClick:()=>c("searchTerm",""),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:(0,t.jsx)(m.QCr,{})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("button",{className:"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",onClick:()=>n(!l),children:[(0,t.jsx)(m.bfZ,{className:"mr-2"}),(()=>{switch(i.dateRange){case"today":return"Today";case"yesterday":return"Yesterday";case"last7days":return"Last 7 Days";case"last30days":default:return"Last 30 Days";case"thisMonth":return"This Month";case"lastMonth":return"Last Month";case"thisYear":return"This Year";case"lastYear":return"Last Year";case"custom":return"Custom Range"}})(),(0,t.jsx)(m.Vr3,{className:"ml-2"})]})}),(0,t.jsxs)("button",{onClick:()=>n(!l),className:"flex items-center px-4 py-2 rounded-md ".concat(u?"bg-blue-100 text-blue-700 hover:bg-blue-200":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,t.jsx)(m.YsJ,{className:"mr-2"}),"Filters",l?(0,t.jsx)(m.Ucs,{className:"ml-2"}):(0,t.jsx)(m.Vr3,{className:"ml-2"}),u&&(0,t.jsx)("span",{className:"ml-2 px-1.5 py-0.5 text-xs bg-blue-500 text-white rounded-full",children:+!!i.rankId+ +!!i.sponsorId+ +("last30days"!==i.dateRange)})]}),u&&(0,t.jsx)("button",{onClick:x,className:"px-3 py-2 text-sm text-red-600 hover:text-red-800",children:"Clear"})]})]}),l&&(0,t.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),(0,t.jsxs)("select",{value:i.dateRange,onChange:e=>c("dateRange",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"today",children:"Today"}),(0,t.jsx)("option",{value:"yesterday",children:"Yesterday"}),(0,t.jsx)("option",{value:"last7days",children:"Last 7 Days"}),(0,t.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,t.jsx)("option",{value:"thisMonth",children:"This Month"}),(0,t.jsx)("option",{value:"lastMonth",children:"Last Month"}),(0,t.jsx)("option",{value:"thisYear",children:"This Year"}),(0,t.jsx)("option",{value:"lastYear",children:"Last Year"}),(0,t.jsx)("option",{value:"custom",children:"Custom Range"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,t.jsxs)("select",{value:i.rankId,onChange:e=>c("rankId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"",children:"All Ranks"}),d.map(e=>(0,t.jsx)("option",{value:e.id.toString(),children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sponsor ID"}),(0,t.jsx)("input",{type:"text",placeholder:"Enter sponsor ID",value:i.sponsorId,onChange:e=>c("sponsorId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),"custom"===i.dateRange&&(0,t.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,t.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,t.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex justify-end",children:[(0,t.jsx)("button",{onClick:x,className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-100 mr-2",children:"Reset"}),(0,t.jsx)("button",{onClick:()=>n(!1),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Apply Filters"})]})]})]})},b=e=>{var s;let{node:a,maxLevel:l}=e,[n,i]=(0,r.useState)(a.level<1),o=a.children&&a.children.length>0,d=o&&a.level<l,c=a.personalVolume/a.groupVolume*100;return(0,t.jsxs)("div",{className:"mb-2 ".concat(a.level>0?"ml-6":""),children:[(0,t.jsx)("div",{className:"p-3 rounded-lg border ".concat(a.isActive?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:(0,t.jsxs)("div",{className:"flex items-center",children:[d?(0,t.jsx)("button",{onClick:()=>i(!n),className:"mr-2 text-gray-500 hover:text-gray-700",children:n?(0,t.jsx)(m.Vr3,{}):(0,t.jsx)(m.X6T,{})}):(0,t.jsx)("span",{className:"mr-2 w-4"}),(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3 ".concat(a.isActive?"bg-green-100 text-green-600":"bg-red-100 text-red-600"),children:a.isActive?(0,t.jsx)(m.A7C,{}):(0,t.jsx)(m._Hm,{})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex flex-wrap justify-between items-center",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:a.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Level ",a.level]})]}),(0,t.jsxs)("div",{className:"mt-2 grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Personal Volume"}),(0,t.jsxs)("div",{className:"font-medium",children:["₱",a.personalVolume.toLocaleString()]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Group Volume"}),(0,t.jsxs)("div",{className:"font-medium",children:["₱",a.groupVolume.toLocaleString()]})]})]}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{children:"PV Contribution"}),(0,t.jsxs)("span",{children:[Math.round(c),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full ".concat(a.isActive?"bg-green-500":"bg-red-500"),style:{width:"".concat(Math.min(c,100),"%")}})})]})]})]})}),n&&o&&(0,t.jsx)("div",{className:"mt-2 border-l-2 border-gray-200 pl-2",children:null==(s=a.children)?void 0:s.map(e=>(0,t.jsx)(b,{node:e,maxLevel:l},e.id))})]})},p=e=>{let{rootMember:s,maxLevel:a=6,title:r="Group Volume Tracker"}=e,l={id:1,name:"John Doe",personalVolume:1200,groupVolume:25e3,isActive:!0,level:0,children:[{id:2,name:"Alice Smith",personalVolume:800,groupVolume:12e3,isActive:!0,level:1,children:[{id:5,name:"Bob Johnson",personalVolume:500,groupVolume:3e3,isActive:!0,level:2,children:[{id:9,name:"Charlie Brown",personalVolume:300,groupVolume:300,isActive:!0,level:3}]},{id:6,name:"Diana Prince",personalVolume:700,groupVolume:700,isActive:!0,level:2}]},{id:3,name:"Mark Wilson",personalVolume:600,groupVolume:8e3,isActive:!0,level:1,children:[{id:7,name:"Eve Adams",personalVolume:400,groupVolume:2400,isActive:!1,level:2,children:[{id:10,name:"Frank Miller",personalVolume:200,groupVolume:200,isActive:!0,level:3}]}]},{id:4,name:"Sarah Lee",personalVolume:900,groupVolume:3200,isActive:!0,level:1,children:[{id:8,name:"George Davis",personalVolume:350,groupVolume:350,isActive:!0,level:2}]}]};return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b",children:(0,t.jsx)("h2",{className:"text-lg font-semibold",children:r})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full bg-green-500 mr-1"}),(0,t.jsx)("span",{children:"Active"})]}),(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full bg-red-500 mr-1"}),(0,t.jsx)("span",{children:"Inactive"})]}),(0,t.jsxs)("div",{className:"flex items-center text-sm ml-4",children:[(0,t.jsx)("span",{className:"font-medium mr-1",children:"PV:"}),(0,t.jsx)("span",{children:"Personal Volume"})]}),(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)("span",{className:"font-medium mr-1",children:"GV:"}),(0,t.jsx)("span",{children:"Group Volume"})]})]}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsx)(b,{node:s||l,maxLevel:a})})]})]})};var j=a(32502),v=a(64065);j.t1.register(j.PP,j.kc,j.FN,j.No,j.hE,j.m_,j.s$,j.dN);let f=e=>{let{title:s="Monthly Sales",currentYearData:a=[],previousYearData:l=[],labels:n=[]}=e,[i,o]=(0,r.useState)("current"),d=n.length>0?n:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],c=a.length>0?a:[12500,15e3,18e3,16500,21e3,22500,25e3,23e3,27e3,28500,3e4,32500],m=l.length>0?l:[1e4,12e3,14500,13e3,17500,19e3,21500,2e4,23500,25e3,26500,28e3];return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold",children:s}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{className:"px-3 py-1 text-sm rounded-md ".concat("current"===i?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"),onClick:()=>o("current"),children:"This Year"}),(0,t.jsx)("button",{className:"px-3 py-1 text-sm rounded-md ".concat("previous"===i?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"),onClick:()=>o("previous"),children:"Last Year"})]})]}),(0,t.jsx)("div",{className:"h-80",children:(0,t.jsx)(v.N1,{data:{labels:d,datasets:[{label:"current"===i?"This Year":"Last Year",data:"current"===i?c:m,borderColor:"rgb(79, 70, 229)",backgroundColor:"rgba(79, 70, 229, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"rgb(79, 70, 229)",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(255, 255, 255, 0.9)",titleColor:"#1f2937",bodyColor:"#4b5563",borderColor:"#e5e7eb",borderWidth:1,padding:12,boxPadding:6,usePointStyle:!0,callbacks:{label:function(e){let s=e.dataset.label||"";return s&&(s+=": "),null!==e.parsed.y&&(s+=new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e.parsed.y)),s}}}},scales:{x:{grid:{display:!1},ticks:{color:"#6b7280"}},y:{grid:{color:"rgba(243, 244, 246, 1)"},ticks:{color:"#6b7280",callback:function(e){return"₱"+e.toLocaleString()}},beginAtZero:!0}}}})})]})};j.t1.register(j.Bs,j.m_,j.s$);let N=e=>{let{title:s="Member Distribution",data:a=[]}=e,r=a.length>0?a:[{label:"Starter",value:120,color:"#94a3b8"},{label:"Bronze",value:85,color:"#ca8a04"},{label:"Silver",value:65,color:"#94a3b8"},{label:"Gold",value:40,color:"#eab308"},{label:"Platinum",value:25,color:"#0ea5e9"},{label:"Diamond",value:10,color:"#6366f1"}],l={labels:r.map(e=>e.label),datasets:[{data:r.map(e=>e.value),backgroundColor:r.map(e=>e.color),borderColor:r.map(()=>"#ffffff"),borderWidth:2,hoverOffset:15}]};return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:s}),(0,t.jsx)("div",{className:"h-80 relative",children:(0,t.jsx)(v.Fq,{data:l,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{boxWidth:15,padding:15,font:{size:12},generateLabels:function(e){let s=e.data;return s.labels.length&&s.datasets.length?s.labels.map((e,a)=>{let t=s.datasets[0],r=t.data[a],l=Math.round(r/t.data.reduce((e,s)=>e+s,0)*100);return{text:"".concat(e,": ").concat(l,"% (").concat(r,")"),fillStyle:t.backgroundColor[a],strokeStyle:t.borderColor[a],lineWidth:t.borderWidth,hidden:!1,index:a}}):[]}}},tooltip:{backgroundColor:"rgba(255, 255, 255, 0.9)",titleColor:"#1f2937",bodyColor:"#4b5563",borderColor:"#e5e7eb",borderWidth:1,padding:12,boxPadding:6,usePointStyle:!0,callbacks:{label:function(e){let s=e.label||"",a=e.raw||0,t=Math.round(a/e.dataset.data.reduce((e,s)=>e+s,0)*100);return"".concat(s,": ").concat(t,"% (").concat(a," members)")}}}}}})})]})};j.t1.register(j.PP,j.kc,j.E8,j.hE,j.m_,j.s$);let y=e=>{let{title:s="Rebates by Rank",data:a=[]}=e,r=a.length>0?a:[{rank:"Starter",amount:25e3,color:"rgba(148, 163, 184, 0.8)"},{rank:"Bronze",amount:45e3,color:"rgba(202, 138, 4, 0.8)"},{rank:"Silver",amount:75e3,color:"rgba(148, 163, 184, 0.8)"},{rank:"Gold",amount:12e4,color:"rgba(234, 179, 8, 0.8)"},{rank:"Platinum",amount:18e4,color:"rgba(14, 165, 233, 0.8)"},{rank:"Diamond",amount:25e4,color:"rgba(99, 102, 241, 0.8)"}],l={labels:r.map(e=>e.rank),datasets:[{label:"Rebate Amount",data:r.map(e=>e.amount),backgroundColor:r.map(e=>e.color),borderColor:r.map(e=>e.color.replace("0.8","1")),borderWidth:1,borderRadius:4,barThickness:30}]};return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:s}),(0,t.jsx)("div",{className:"h-80",children:(0,t.jsx)(v.yP,{data:l,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(255, 255, 255, 0.9)",titleColor:"#1f2937",bodyColor:"#4b5563",borderColor:"#e5e7eb",borderWidth:1,padding:12,boxPadding:6,usePointStyle:!0,callbacks:{label:function(e){let s=e.dataset.label||"";return s&&(s+=": "),null!==e.parsed.y&&(s+=new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e.parsed.y)),s}}}},scales:{x:{grid:{display:!1},ticks:{color:"#6b7280"}},y:{grid:{color:"rgba(243, 244, 246, 1)"},ticks:{color:"#6b7280",callback:function(e){return"₱"+e.toLocaleString()}},beginAtZero:!0}}}})})]})},w=e=>{let{title:s,description:a,icon:r,href:l,iconBgColor:n,iconColor:i}=e;return(0,t.jsxs)(c(),{href:l,className:"bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"p-3 rounded-full ".concat(n," ").concat(i," mr-4"),children:r}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:s})]}),(0,t.jsx)("p",{className:"text-gray-500 text-sm mb-4",children:a})]})};function k(){let{data:e,status:s}=(0,l.useSession)(),a=(0,n.useRouter)(),[d,b]=(0,r.useState)(!0),[j,v]=(0,r.useState)(!1),[k,C]=(0,r.useState)({totalUsers:0,totalProducts:0,totalPurchases:0,totalRebates:0,pendingRebates:0,processedRebates:0,totalRebateAmount:0}),[A,S]=(0,r.useState)({users:[],purchases:[]}),[R,D]=(0,r.useState)({products:[],distributors:[]}),[P,V]=(0,r.useState)("");(0,r.useEffect)(()=>{"unauthenticated"===s&&a.push("/login")},[s,a]),(0,r.useEffect)(()=>{"authenticated"===s&&(async()=>{try{let e=await fetch("/api/users/me"),s=await e.json(),a=6===s.rankId;v(a),a?L():b(!1)}catch(e){console.error("Error checking admin status:",e),V("Failed to verify admin access. Please try again."),b(!1)}})()},[s]);let L=async()=>{b(!0),V("");try{let e=await fetch("/api/admin/stats");if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch admin statistics")}let s=await e.json();C(s.stats),S(s.recentActivity),D(s.topPerformers),b(!1)}catch(e){console.error("Error fetching admin stats:",e),V(e.message||"Failed to load admin dashboard data"),b(!1)}};return"loading"===s||d?(0,t.jsx)(o.A,{children:(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center h-64",children:[(0,t.jsx)(m.hW,{className:"animate-spin text-blue-500 text-4xl mb-4"}),(0,t.jsx)("div",{className:"text-xl",children:"Loading Admin Dashboard..."})]})}):j?P?(0,t.jsx)(o.A,{children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(m.BS8,{className:"text-red-500 text-5xl mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Error Loading Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:P}),(0,t.jsx)("button",{onClick:L,className:"inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Try Again"})]})})}):(0,t.jsx)(o.A,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold",children:"Extreme Life Herbal Product Rewards - Admin"}),(0,t.jsxs)("div",{className:"mt-4 md:mt-0 flex items-center space-x-3",children:[(0,t.jsxs)("button",{onClick:L,className:"flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200",disabled:d,children:[(0,t.jsx)(m.hW,{className:"mr-2 ".concat(d?"animate-spin":"hidden")}),(0,t.jsx)(m.DIg,{className:"mr-2 ".concat(d?"hidden":"")}),"Refresh Data"]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("button",{className:"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",onClick:()=>{alert("Test mode toggle would be implemented here")},children:[(0,t.jsx)(m.vWM,{className:"mr-2"}),"Live Data",(0,t.jsx)(m.Vr3,{className:"ml-2"})]})})]})]}),(0,t.jsx)(g,{onFilterChange:e=>{console.log("Filters changed:",e)}}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(x,{title:"Total Members",value:k.totalUsers,icon:(0,t.jsx)(m.YXz,{className:"h-6 w-6"}),borderColor:"border-blue-500",iconBgColor:"bg-blue-100",iconColor:"text-blue-500",percentageChange:12,footer:"From previous period"}),(0,t.jsx)(x,{title:"Active Products",value:k.totalProducts,icon:(0,t.jsx)(m.AsH,{className:"h-6 w-6"}),borderColor:"border-green-500",iconBgColor:"bg-green-100",iconColor:"text-green-500",percentageChange:5,footer:"From previous period"}),(0,t.jsx)(x,{title:"Monthly Sales",value:"₱".concat((1200*k.totalPurchases).toLocaleString()),icon:(0,t.jsx)(m.MxO,{className:"h-6 w-6"}),borderColor:"border-purple-500",iconBgColor:"bg-purple-100",iconColor:"text-purple-500",percentageChange:-3,footer:"Compared to last month"}),(0,t.jsx)(x,{title:"Total Rebates",value:"₱".concat(k.totalRebateAmount.toLocaleString()),icon:(0,t.jsx)(m.YYR,{className:"h-6 w-6"}),borderColor:"border-orange-500",iconBgColor:"bg-orange-100",iconColor:"text-orange-500",percentageChange:8,footer:"".concat(k.pendingRebates," pending / ").concat(k.processedRebates," processed")})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)(f,{})}),(0,t.jsx)(N,{})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,t.jsx)(y,{}),(0,t.jsx)(p,{})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,t.jsx)(u,{distributors:R.distributors||[],title:"Top Distributors",viewAllLink:"/admin/reports"}),(0,t.jsx)(h,{users:A.users||[],purchases:A.purchases||[],title:"Recent Activity"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(w,{title:"User Management",description:"Manage users, ranks, and genealogy structure.",icon:(0,t.jsx)(m.YXz,{className:"h-6 w-6"}),href:"/admin/users",iconBgColor:"bg-blue-100",iconColor:"text-blue-500"}),(0,t.jsx)(w,{title:"Product Management",description:"Add, edit, and manage products in the system.",icon:(0,t.jsx)(m.AsH,{className:"h-6 w-6"}),href:"/admin/products",iconBgColor:"bg-green-100",iconColor:"text-green-500"}),(0,t.jsx)(w,{title:"Rebate Management",description:"Process rebates and manage wallet transactions.",icon:(0,t.jsx)(m.lcY,{className:"h-6 w-6"}),href:"/admin/rebates",iconBgColor:"bg-purple-100",iconColor:"text-purple-500"}),(0,t.jsx)(w,{title:"Reports",description:"View sales reports, rebate reports, and user statistics.",icon:(0,t.jsx)(m.YYR,{className:"h-6 w-6"}),href:"/admin/reports",iconBgColor:"bg-orange-100",iconColor:"text-orange-500"}),(0,t.jsx)(w,{title:"Test Data Generator",description:"Generate test data for different user scenarios.",icon:(0,t.jsx)(m.kkc,{className:"h-6 w-6"}),href:"/admin/test-data",iconBgColor:"bg-indigo-100",iconColor:"text-indigo-500"})]})]})}):(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(m.BS8,{className:"text-yellow-500 text-5xl mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"You do not have permission to access the admin panel. Admin access is restricted to Diamond rank members only."}),(0,t.jsx)(c(),{href:"/dashboard",className:"inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Return to Dashboard"})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,5647,6874,2108,6766,5557,1694,8579,357,9526,8441,1684,7358],()=>s(2437)),_N_E=e.O()}]);
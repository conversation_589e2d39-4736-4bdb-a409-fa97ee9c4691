"use strict";(()=>{var e={};e.id=5599,e.ids=[5599],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},90844:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>c});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(35426),p=t(12909),d=t(28990);async function c(e){try{let r=await (0,u.getServerSession)(p.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});if("admin"!==r.user.role)return i.NextResponse.json({error:"You do not have permission to access this endpoint"},{status:403});let t=await e.json();if(!t.scenario)return i.NextResponse.json({error:"Scenario is required"},{status:400});if(!Object.values(d.Rt).includes(t.scenario))return i.NextResponse.json({error:"Invalid scenario"},{status:400});let s=parseInt(t.count)||1;if(s<1||s>10)return i.NextResponse.json({error:"Count must be between 1 and 10"},{status:400});let n=await (0,d.SF)({scenario:t.scenario,count:s,prefix:t.prefix||"test",cleanupToken:t.cleanupToken||`cleanup_${Date.now()}`});if(!n.success)return i.NextResponse.json({error:n.message},{status:500});return i.NextResponse.json(n)}catch(e){return console.error("Error generating test data:",e),i.NextResponse.json({error:"Failed to generate test data"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/test-data/generate/route",pathname:"/api/admin/test-data/generate",filename:"route",bundlePath:"app/api/admin/test-data/generate/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\test-data\\generate\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:m}=x;function q(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,1742,5626],()=>t(90844));module.exports=s})();
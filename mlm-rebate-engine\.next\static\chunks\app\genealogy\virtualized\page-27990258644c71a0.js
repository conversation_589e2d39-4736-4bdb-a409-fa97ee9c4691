(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5922],{52:(e,s,l)=>{Promise.resolve().then(l.bind(l,48684))},48684:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>m});var a=l(95155),i=l(12115),t=l(12108),n=l(87747),r=l(29911),o=l(6874),d=l.n(o);let c=(0,l(55028).default)(()=>Promise.all([l.e(1294),l.e(8702),l.e(6113),l.e(6808),l.e(2406),l.e(9012)]).then(l.bind(l,19012)),{loadableGenerated:{webpack:()=>[19012]},ssr:!1,loading:()=>(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading virtualized visualization..."})]})});function m(){let{data:e,status:s}=(0,t.useSession)(),[l,o]=(0,i.useState)(void 0),[m,x]=(0,i.useState)("vertical"),{data:h,isLoading:u}=(0,n.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let l=await fetch("/api/users/me");if(!l.ok)throw Error("Failed to fetch user data");return await l.json()},enabled:"authenticated"===s});return(h&&!l&&o(h.id),"loading"===s||u)?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,a.jsx)(r.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,a.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Virtualized Genealogy Tree"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Optimized for large networks with thousands of members"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex bg-white rounded-md shadow-md overflow-hidden",children:[(0,a.jsxs)("button",{onClick:()=>x("vertical"),className:"px-3 py-2 flex items-center ".concat("vertical"===m?"bg-blue-100 text-blue-700":"hover:bg-gray-100"),children:[(0,a.jsx)(r.aQJ,{className:"mr-2"})," Vertical"]}),(0,a.jsxs)("button",{onClick:()=>x("horizontal"),className:"px-3 py-2 flex items-center ".concat("horizontal"===m?"bg-blue-100 text-blue-700":"hover:bg-gray-100"),children:[(0,a.jsx)(r.aQJ,{className:"mr-2 rotate-90"})," Horizontal"]}),(0,a.jsxs)("button",{onClick:()=>x("radial"),className:"px-3 py-2 flex items-center ".concat("radial"===m?"bg-blue-100 text-blue-700":"hover:bg-gray-100"),children:[(0,a.jsx)(r.aQJ,{className:"mr-2 rotate-45"})," Radial"]})]}),(0,a.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(r.QVr,{className:"mr-1"}),"Back to Genealogy"]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:(0,a.jsx)("div",{className:"lg:col-span-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:l?(0,a.jsx)(c,{userId:l,maxLevel:6,initialLayout:m,initialPageSize:20}):(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading genealogy data..."})]})})})}),(0,a.jsxs)("div",{className:"mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-blue-800 mb-2",children:"About Virtualized Genealogy"}),(0,a.jsx)("p",{className:"text-blue-700 mb-2",children:"This virtualized genealogy tree is optimized for large networks with thousands of members:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"Lazy loading of nodes - only loads data when needed"}),(0,a.jsx)("li",{children:"Virtualization - only renders nodes that are visible on screen"}),(0,a.jsx)("li",{children:"Efficient memory usage - reduces RAM consumption for large trees"}),(0,a.jsx)("li",{children:"Smooth performance even with thousands of members"}),(0,a.jsx)("li",{children:"Multiple layout options for different visualization needs"})]}),(0,a.jsxs)("p",{className:"text-blue-700 mt-2",children:[(0,a.jsx)("strong",{children:"How to use:"})," Click on a node to expand it and load its children. The tree will automatically optimize which nodes are rendered based on your view."]})]}),(0,a.jsxs)("div",{className:"mt-4 bg-yellow-50 p-4 rounded-lg border border-yellow-200",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-yellow-800 mb-2",children:"Performance Tips"}),(0,a.jsxs)("ul",{className:"list-disc list-inside text-yellow-700 space-y-1",children:[(0,a.jsx)("li",{children:"Only expand nodes you need to see to maintain optimal performance"}),(0,a.jsx)("li",{children:"Use the minimap for navigation in large networks"}),(0,a.jsx)("li",{children:"Adjust your zoom level to see more or fewer nodes at once"}),(0,a.jsx)("li",{children:"Switch layouts to find the best visualization for your network structure"})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,5557,6967,7747,8441,1684,7358],()=>s(52)),_N_E=e.O()}]);
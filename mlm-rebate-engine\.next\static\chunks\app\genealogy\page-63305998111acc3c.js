(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2292,8169],{58169:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>c});var t=l(95155),r=l(12115),a=l(29911);let n=e=>{switch(e){case"Starter":default:return"bg-gray-100 text-gray-800";case"Bronze":return"bg-yellow-100 text-yellow-800";case"Silver":return"bg-gray-200 text-gray-800";case"Gold":return"bg-yellow-200 text-yellow-800";case"Platinum":return"bg-blue-100 text-blue-800";case"Diamond":return"bg-purple-100 text-purple-800"}},i=e=>{let{user:s,isRoot:l=!1,depth:c,maxDepth:o,initialExpandedLevels:d}=e,[x,m]=(0,r.useState)(c<d),g=s.children&&s.children.length>0,h=g&&c<o,u=n(s.rank.name);return(0,t.jsxs)("div",{className:"mb-2 ".concat(l?"":"ml-6"),children:[(0,t.jsxs)("div",{className:"flex items-center p-3 rounded-md ".concat(l?"bg-blue-50 border border-blue-200":"bg-white border border-gray-200"),children:[h&&(0,t.jsx)("button",{onClick:()=>m(!x),className:"mr-2 text-gray-500 hover:text-gray-700 focus:outline-none","aria-label":x?"Collapse":"Expand",children:x?(0,t.jsx)(a.Vr3,{}):(0,t.jsx)(a.X6T,{})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[l?(0,t.jsx)(a.x$1,{className:"mr-2 text-blue-500"}):(0,t.jsx)(a.YXz,{className:"mr-2 text-blue-500"}),(0,t.jsx)("span",{className:"font-medium",children:s.name}),(0,t.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 rounded-full ".concat(u),children:s.rank.name})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[(0,t.jsxs)("span",{className:"mr-3",children:["ID: ",s.id]}),(0,t.jsxs)("span",{className:"mr-3",children:["Downline: ",s._count.downline]}),!l&&(0,t.jsxs)("span",{children:["Level ",s.level]})]})]}),!l&&(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:["Level ",s.level]})})]}),g&&x&&(0,t.jsx)("div",{className:"mt-2 border-l-2 border-gray-200 pl-2",children:s.children.map(e=>(0,t.jsx)(i,{user:e,depth:c+1,maxDepth:o,initialExpandedLevels:d},e.id))})]})},c=e=>{let{data:s,maxDepth:l=10,initialExpandedLevels:a=2}=e,[n,c]=(0,r.useState)(""),[o,d]=(0,r.useState)(!1),x=(e,s)=>{if(e.name.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase()))return e;if(e.children&&e.children.length>0){let l=e.children.map(e=>x(e,s)).filter(Boolean);if(l.length>0)return{...e,children:l}}return null},m=n&&x(s,n)||s;return(0,t.jsxs)("div",{className:"genealogy-tree",children:[(0,t.jsxs)("div",{className:"mb-4 flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search by name or email...",value:n,onChange:e=>c(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pl-10"}),(0,t.jsx)("div",{className:"absolute left-3 top-2.5 text-gray-400",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{onClick:()=>d(!o),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:o?"Collapse All":"Expand All"})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,t.jsx)("div",{className:"mb-4 flex items-center",children:(0,t.jsxs)("div",{className:"flex space-x-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-gray-100 mr-1"}),(0,t.jsx)("span",{children:"Starter"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-yellow-100 mr-1"}),(0,t.jsx)("span",{children:"Bronze"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-gray-200 mr-1"}),(0,t.jsx)("span",{children:"Silver"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-yellow-200 mr-1"}),(0,t.jsx)("span",{children:"Gold"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-blue-100 mr-1"}),(0,t.jsx)("span",{children:"Platinum"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-3 h-3 inline-block rounded-full bg-purple-100 mr-1"}),(0,t.jsx)("span",{children:"Diamond"})]})]})}),(0,t.jsx)(i,{user:m,isRoot:!0,depth:0,maxDepth:l,initialExpandedLevels:o?l:a})]})]})}},62446:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>g});var t=l(95155),r=l(12115),a=l(12108),n=l(35695),i=l(6874),c=l.n(i),o=l(70357),d=l(58169),x=l(81407),m=l(29911);function g(){let{data:e,status:s}=(0,a.useSession)(),l=(0,n.useRouter)(),[i,g]=(0,r.useState)(null),[h,u]=(0,r.useState)(!0),[p,b]=(0,r.useState)(""),[f,j]=(0,r.useState)(10),[N,v]=(0,r.useState)(""),[y,w]=(0,r.useState)(null),[k,S]=(0,r.useState)(null),[L,C]=(0,r.useState)(!0),[E,D]=(0,r.useState)(!0);(0,r.useEffect)(()=>{"unauthenticated"===s&&l.push("/login")},[s,l]),(0,r.useEffect)(()=>{"authenticated"===s&&O()},[s,f,y]);let O=async()=>{u(!0);try{let e=new URLSearchParams;e.append("maxLevel",f.toString()),y&&e.append("userId",y.toString()),e.append("includeStats","true"),e.append("includePerformanceMetrics","true"),e.append("lazyLoad","true");let s=await fetch("/api/genealogy?".concat(e.toString()));if(!s.ok)throw Error("Failed to fetch genealogy: ".concat(s.statusText));let l=await s.json();if(g(l),l.statistics&&S(l.statistics),!y&&l.id){let e=window.location.origin;b("".concat(e,"/register?uplineId=").concat(l.id))}u(!1)}catch(e){console.error("Error fetching genealogy:",e),u(!1)}},U=async e=>{try{let s=new URLSearchParams;if(s.append("maxLevel",f.toString()),s.append("format",e),y&&s.append("userId",y.toString()),"csv"===e)return void window.open("/api/genealogy/export?".concat(s.toString()),"_blank");let l=await fetch("/api/genealogy/export?".concat(s.toString()));if(!l.ok)throw Error("Failed to export genealogy: ".concat(l.statusText));let t=await l.json(),r=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),a=URL.createObjectURL(r),n=document.createElement("a");n.href=a,n.download="genealogy_export_".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(a)}catch(e){console.error("Error exporting genealogy:",e),alert("Failed to export genealogy data. Please try again.")}};return"loading"===s||h?(0,t.jsx)(o.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,t.jsx)(o.A,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center mb-6 gap-4",children:[(0,t.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,t.jsx)(m.YXz,{className:"mr-2 text-green-500"})," Network Genealogy"]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)("button",{onClick:()=>D(!0),className:"px-4 py-2 rounded-md ".concat(E?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Enhanced View"}),(0,t.jsx)("button",{onClick:()=>D(!1),className:"px-4 py-2 rounded-md ".concat(E?"bg-gray-200 text-gray-700 hover:bg-gray-300":"bg-green-600 text-white"),children:"Standard View"}),(0,t.jsxs)(c(),{href:"/genealogy/optimized",className:"px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 flex items-center",children:[(0,t.jsx)(m.aQJ,{className:"mr-2"})," Try New Optimized View"]}),(0,t.jsxs)(c(),{href:"/genealogy/basic-flow",className:"px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700 flex items-center",children:[(0,t.jsx)(m.aQJ,{className:"mr-2"})," Basic Flow View"]}),(0,t.jsxs)(c(),{href:"/genealogy/enhanced-flow",className:"px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 flex items-center",children:[(0,t.jsx)(m.aQJ,{className:"mr-2"})," Enhanced Flow View"]}),(0,t.jsxs)(c(),{href:"/genealogy/compare",className:"px-4 py-2 rounded-md bg-yellow-600 text-white hover:bg-yellow-700 flex items-center",children:[(0,t.jsx)(m.yk7,{className:"mr-2"})," Compare Views"]}),(0,t.jsxs)(c(),{href:"/genealogy/search",className:"px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 flex items-center",children:[(0,t.jsx)(m.KSO,{className:"mr-2"})," Advanced Search"]}),(0,t.jsxs)(c(),{href:"/genealogy/export",className:"px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 flex items-center",children:[(0,t.jsx)(m.Mbn,{className:"mr-2"})," Export Data"]}),(0,t.jsxs)(c(),{href:"/genealogy/interactive",className:"px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700 flex items-center",children:[(0,t.jsx)(m.uO9,{className:"mr-2"})," Interactive Tree"]}),(0,t.jsxs)(c(),{href:"/genealogy/virtualized",className:"px-4 py-2 rounded-md bg-teal-600 text-white hover:bg-teal-700 flex items-center",children:[(0,t.jsx)(m.aQJ,{className:"mr-2"})," Virtualized Tree"]}),(0,t.jsxs)(c(),{href:"/genealogy/mobile",className:"px-4 py-2 rounded-md bg-orange-600 text-white hover:bg-orange-700 flex items-center",children:[(0,t.jsx)(m.q5F,{className:"mr-2"})," Mobile View"]}),(0,t.jsxs)(c(),{href:"/genealogy/integration",className:"px-4 py-2 rounded-md bg-pink-600 text-white hover:bg-pink-700 flex items-center",children:[(0,t.jsx)(m.O2x,{className:"mr-2"})," Integration"]}),(0,t.jsxs)(c(),{href:"/genealogy/metrics",className:"px-4 py-2 rounded-md bg-cyan-600 text-white hover:bg-cyan-700 flex items-center",children:[(0,t.jsx)(m.YYR,{className:"mr-2"})," Metrics"]}),(0,t.jsxs)(c(),{href:"/genealogy/notifications",className:"px-4 py-2 rounded-md bg-amber-600 text-white hover:bg-amber-700 flex items-center",children:[(0,t.jsx)(m.jNV,{className:"mr-2"})," Notifications"]}),(0,t.jsxs)(c(),{href:"/genealogy/compare-users",className:"px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 flex items-center",children:[(0,t.jsx)(m.yk7,{className:"mr-2"})," Compare Users"]})]})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),""===N.trim())w(null);else{let e=parseInt(N);if(isNaN(e))return void alert("Please enter a valid user ID");w(e)}},className:"flex gap-2",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("input",{type:"text",placeholder:"Search by User ID",value:N,onChange:e=>v(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})}),(0,t.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[(0,t.jsx)(m.KSO,{className:"mr-2 inline-block"})," Search"]})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:()=>{v(""),w(null)},className:"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"View My Network"}),(0,t.jsxs)("select",{value:f,onChange:e=>j(parseInt(e.target.value)),className:"px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"1",children:"1 Level"}),(0,t.jsx)("option",{value:"2",children:"2 Levels"}),(0,t.jsx)("option",{value:"3",children:"3 Levels"}),(0,t.jsx)("option",{value:"5",children:"5 Levels"}),(0,t.jsx)("option",{value:"10",children:"10 Levels"})]})]})]})}),!y&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,t.jsx)(m.NPy,{className:"mr-2 text-green-500"})," Your Referral Link"]}),(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("input",{type:"text",value:p,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,t.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(p),alert("Referral link copied to clipboard!")},className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700",children:"Copy"})]}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Share this link with others to invite them to join your downline."})]}),k&&L&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(m.v$b,{className:"mr-2 text-blue-500"})," Network Statistics"]}),(0,t.jsx)("button",{onClick:()=>C(!1),className:"text-gray-500 hover:text-gray-700",children:(0,t.jsx)("span",{className:"text-sm",children:"Hide"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Total Members"}),(0,t.jsx)("div",{className:"text-xl font-semibold",children:k.totalUsers})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-3 rounded-md",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Direct Downline"}),(0,t.jsx)("div",{className:"text-xl font-semibold",children:k.directDownlineCount})]}),(0,t.jsxs)("div",{className:"bg-purple-50 p-3 rounded-md",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Network Levels"}),(0,t.jsx)("div",{className:"text-xl font-semibold",children:Object.keys(k.levelCounts).length})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-md",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Downline Earnings"}),(0,t.jsxs)("div",{className:"text-xl font-semibold",children:["₱",k.totalDownlineBalance.toFixed(2)]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"Members by Level"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:Object.entries(k.levelCounts).map(e=>{let[s,l]=e;return(0,t.jsxs)("div",{className:"bg-gray-50 p-2 rounded-md text-center",children:[(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",s]}),(0,t.jsx)("div",{className:"font-medium",children:l})]},s)})})]}),k.rankDistribution&&k.rankDistribution.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"Members by Rank"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:k.rankDistribution.map(e=>(0,t.jsxs)("div",{className:"bg-gray-50 p-2 rounded-md text-center",children:[(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.rankName}),(0,t.jsx)("div",{className:"font-medium",children:e.count})]},e.rankId))})]})]}),k.lastUpdated&&(0,t.jsxs)("div",{className:"mt-3 text-xs text-gray-500 text-right",children:["Last updated: ",new Date(k.lastUpdated).toLocaleString()]})]}),!L&&k&&(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-3 mb-6",children:(0,t.jsxs)("button",{onClick:()=>C(!0),className:"text-green-600 hover:text-green-700 flex items-center",children:[(0,t.jsx)(m.v$b,{className:"mr-2"}),(0,t.jsx)("span",{children:"Show Network Statistics"}),(0,t.jsx)(m.Vr3,{className:"ml-2"})]})}),(0,t.jsxs)("div",{className:"mb-6 flex flex-wrap gap-2",children:[(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[(0,t.jsx)(m.WCW,{className:"mr-2"})," Export Genealogy"]}),(0,t.jsx)("div",{className:"absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden group-hover:block z-10",children:(0,t.jsxs)("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[(0,t.jsx)("button",{onClick:()=>U("csv"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as CSV"}),(0,t.jsx)("button",{onClick:()=>U("json"),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left",role:"menuitem",children:"Export as JSON"})]})})]}),(0,t.jsxs)("button",{onClick:()=>window.print(),className:"flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:[(0,t.jsx)(m.n4o,{className:"mr-2"})," Print View"]}),(0,t.jsxs)("button",{onClick:()=>{if(i){let e="Check out my Extreme Life Herbal Products network with ".concat((null==k?void 0:k.totalUsers)||0," members!");navigator.share?navigator.share({title:"My Extreme Life Network",text:e,url:window.location.href}).catch(e=>console.error("Error sharing:",e)):(navigator.clipboard.writeText("".concat(e," ").concat(window.location.href)),alert("Network link copied to clipboard!"))}},className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:[(0,t.jsx)(m.Zzu,{className:"mr-2"})," Share Network"]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:y?"User #".concat(y,"'s Downline"):"Your Downline"}),h?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(m.hW,{className:"animate-spin text-green-500 mr-2"}),(0,t.jsx)("span",{children:"Loading genealogy data..."})]}):i?E?(0,t.jsx)(x.default,{data:i,maxDepth:f,initialExpandedLevels:2,onUserSelect:e=>console.log("Selected user:",e)}):(0,t.jsx)(d.default,{data:i,maxDepth:f,initialExpandedLevels:2}):(0,t.jsx)("p",{className:"text-gray-500",children:"No genealogy data available."})]})]})})}},74231:(e,s,l)=>{Promise.resolve().then(l.bind(l,62446))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,1407,8441,1684,7358],()=>s(74231)),_N_E=e.O()}]);
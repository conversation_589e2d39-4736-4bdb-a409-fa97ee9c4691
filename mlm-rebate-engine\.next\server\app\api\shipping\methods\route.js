(()=>{var e={};e.id=5283,e.ids=[5283],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{Er:()=>u,Nh:()=>c,aP:()=>l});var t=s(96330),o=s(13581),i=s(85663),n=s(55511),a=s.n(n);async function u(e){return await i.Ay.hash(e,10)}function l(){let e=a().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new t.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new t.PrismaClient,s=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!s)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",s.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await i.Ay.compare(e.password,s.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",s.id);let{password:n,...a}=s;return{id:s.id.toString(),email:s.email,name:s.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>o});var t=s(96330);let o=global.prisma||new t.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60776:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>x,routeModule:()=>h,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{GET:()=>p,POST:()=>d});var o=s(96559),i=s(48088),n=s(37719),a=s(32190),u=s(35426),l=s(12909),c=s(31183);async function p(e){try{let e=await c.z.shippingMethod.findMany({where:{isActive:!0},orderBy:{price:"asc"}});return a.NextResponse.json(e)}catch(e){return console.error("Error fetching shipping methods:",e),a.NextResponse.json({error:"An error occurred while fetching shipping methods"},{status:500})}}async function d(e){try{let r=await (0,u.getServerSession)(l.Nh);if(!r||!r.user||"ADMIN"!==r.user.role)return a.NextResponse.json({error:"You must be an admin to create shipping methods"},{status:401});let{name:s,description:t,price:o,estimatedDeliveryDays:i,isActive:n}=await e.json();if(!s||void 0===o||void 0===i)return a.NextResponse.json({error:"Missing required fields"},{status:400});if(await c.z.shippingMethod.findUnique({where:{name:s}}))return a.NextResponse.json({error:"A shipping method with this name already exists"},{status:400});let p=await c.z.shippingMethod.create({data:{name:s,description:t||null,price:o,estimatedDeliveryDays:i,isActive:void 0===n||n}});return a.NextResponse.json({message:"Shipping method created successfully",method:p})}catch(e){return console.error("Error creating shipping method:",e),a.NextResponse.json({error:"An error occurred while creating the shipping method"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/shipping/methods/route",pathname:"/api/shipping/methods",filename:"route",bundlePath:"app/api/shipping/methods/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\shipping\\methods\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:w}=h;function x(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,8044,3112],()=>s(60776));module.exports=t})();
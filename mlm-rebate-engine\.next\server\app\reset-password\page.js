(()=>{var e={};e.id=4700,e.ids=[4700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>o,_:()=>i});var s=r(60687),n=r(43210);let a=(0,n.createContext)(void 0),i=()=>{let e=(0,n.useContext)(a);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},o=({children:e})=>{let[t,r]=(0,n.useState)([]);(0,n.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{r(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,n.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]);let i=e=>{r(t=>t.filter(t=>t.id!==e))},o=t.reduce((e,t)=>e+t.quantity,0),l=t.reduce((e,t)=>e+t.price*t.quantity,0),d=t.reduce((e,t)=>e+t.pv*t.quantity,0);return(0,s.jsx)(a.Provider,{value:{items:t,addItem:e=>{r(t=>{let r=t.findIndex(t=>t.id===e.id);if(!(r>=0))return[...t,e];{let s=[...t];return s[r]={...s[r],quantity:s[r].quantity+e.quantity},s}})},removeItem:i,updateQuantity:(e,t)=>{if(t<=0)return void i(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},itemCount:o,subtotal:l,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>n});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},45851:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),n=r(25217),a=r(8693),i=r(43210);function o({children:e}){let[t]=(0,i.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,s.jsx)(a.Ht,{client:t,children:e})}},57322:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85316)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\reset-password\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},59345:(e,t,r)=>{Promise.resolve().then(r.bind(r,90322))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(60687),n=r(43210);let a=()=>"serviceWorker"in navigator,i=async()=>{if(!a())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),o(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},o=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[t,r]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{i();let e=()=>{r(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,s.jsxs)(s.Fragment,{children:[e,t&&(0,s.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,s.jsxs)("div",{className:"mr-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,s.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,s.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,t,r)=>{Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,23229)),Promise.resolve().then(r.bind(r,82113)),Promise.resolve().then(r.bind(r,41750))},69073:(e,t,r)=>{Promise.resolve().then(r.bind(r,85316))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74104:(e,t,r)=>{Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,97695)),Promise.resolve().then(r.bind(r,45851)),Promise.resolve().then(r.bind(r,63345))},79551:e=>{"use strict";e.exports=require("url")},82113:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},85316:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\reset-password\\page.tsx","default")},90322:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),n=r(43210),a=r(16189),i=r(85814),o=r.n(i),l=r(30474),d=r(23877);function c(){(0,a.useRouter)(),(0,a.useSearchParams)();let[e,t]=(0,n.useState)(null),[r,i]=(0,n.useState)(""),[c,m]=(0,n.useState)(""),[u,x]=(0,n.useState)(!1),[h,p]=(0,n.useState)(!1),[f,v]=(0,n.useState)(!0),[g,b]=(0,n.useState)(!0),[w,j]=(0,n.useState)(!1),[y,N]=(0,n.useState)(!1),[P,C]=(0,n.useState)(!1),[k,L]=(0,n.useState)(""),[S,M]=(0,n.useState)(!1),[A,E]=(0,n.useState)(!0),[q,R]=(0,n.useState)(!1),$=/[A-Z]/.test(r),_=/[a-z]/.test(r),W=/[0-9]/.test(r),F=/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(r),I=[r.length>=8,$,_,W,F].filter(Boolean).length,z=async e=>{e.preventDefault();let t=r.length>=8,s=c===r;if(v(t),b(s),!t||!s)return void L("Please correct the errors in the form");C(!0),L("");try{await new Promise(e=>setTimeout(e,1500)),M(!0)}catch(e){console.error("Error resetting password:",e),L(`Failed to reset password: ${e instanceof Error?e.message:String(e)}`)}finally{C(!1)}};return q?A?(0,s.jsxs)("div",{className:"min-h-screen flex flex-col md:flex-row",children:[(0,s.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-gradient-to-br from-green-500 to-green-700 text-white p-12 flex-col justify-between relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)("div",{className:"relative w-12 h-12 mr-3",children:(0,s.jsx)(l.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain invert"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Extreme Life Herbal"})]}),(0,s.jsxs)("div",{className:"mt-16 mb-8",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold mb-6",children:"Create New Password"}),(0,s.jsx)("p",{className:"text-xl opacity-90 mb-8",children:"Choose a strong password to protect your account and keep your business secure."}),(0,s.jsxs)("div",{className:"bg-white/10 p-6 rounded-lg backdrop-blur-sm",children:[(0,s.jsx)("p",{className:"italic text-white/90 mb-4",children:'"A strong password is the first line of defense for your account. Make sure to use a unique password that you don\'t use for other services."'}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-white/30 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Security Team"}),(0,s.jsx)("p",{className:"text-sm opacity-75",children:"Extreme Life Herbal"})]})]})]})]})]}),(0,s.jsx)("div",{className:"absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -mr-48 -mt-48"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-96 h-96 bg-white/5 rounded-full -ml-48 -mb-48"}),(0,s.jsx)("div",{className:"relative z-10",children:(0,s.jsxs)("p",{className:"text-sm opacity-75",children:["\xa9 ",new Date().getFullYear()," Extreme Life Herbal Products. All rights reserved."]})})]}),(0,s.jsxs)("div",{className:"flex flex-col justify-center md:w-1/2 p-6 sm:p-12 bg-white",children:[(0,s.jsx)("div",{className:"md:hidden flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative w-10 h-10 mr-2",children:(0,s.jsx)(l.default,{src:"/images/********.svg",alt:"Extreme Life Herbal Products Logo",fill:!0,className:"object-contain"})}),(0,s.jsx)("h1",{className:"text-xl font-bold text-green-700",children:"Extreme Life Herbal"})]})}),(0,s.jsxs)("div",{className:"max-w-md mx-auto w-full",children:[(0,s.jsxs)(o(),{href:"/login",className:"inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 mb-6 transition-colors",children:[(0,s.jsx)(d.QVr,{className:"mr-2"})," Back to login"]}),S?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,s.jsx)(d.CMH,{className:"h-6 w-6 text-green-600"})}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Password Reset Successful"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Your password has been successfully reset. You can now log in with your new password."}),(0,s.jsx)(o(),{href:"/login",className:"inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors",children:"Go to Login"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Reset Password"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"Create a new password for your account. Make sure it's strong and secure."}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:z,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:w?"text":"password",autoComplete:"new-password",required:!0,className:`appearance-none block w-full pl-10 pr-10 py-2 border ${!f?"border-red-300":u?"border-green-500":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 transition-colors sm:text-sm`,placeholder:"••••••••",value:r,onChange:e=>{let t=e.target.value;i(t),v(t.length>=8),c&&b(c===t)},onFocus:()=>x(!0),onBlur:()=>x(!1)}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>j(!w),children:w?(0,s.jsx)(d.mx3,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(d.Ny1,{className:"h-5 w-5 text-gray-400"})})]}),(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Password strength:"}),(0,s.jsxs)("span",{className:"text-xs font-medium",children:[0===I&&"Very Weak",1===I&&"Weak",2===I&&"Fair",3===I&&"Good",4===I&&"Strong",5===I&&"Very Strong"]})]}),(0,s.jsx)("div",{className:"h-1.5 w-full bg-gray-200 rounded-full overflow-hidden",children:(0,s.jsx)("div",{className:`h-full ${0===I?"w-0":1===I?"w-1/5 bg-red-500":2===I?"w-2/5 bg-orange-500":3===I?"w-3/5 bg-yellow-500":4===I?"w-4/5 bg-blue-500":"w-full bg-green-500"} transition-all duration-300`})}),(0,s.jsxs)("ul",{className:"mt-2 space-y-1 text-xs text-gray-500",children:[(0,s.jsxs)("li",{className:`flex items-center ${r.length>=8?"text-green-600":""}`,children:[(0,s.jsx)("span",{className:`mr-1 ${r.length>=8?"text-green-600":""}`,children:"•"}),"At least ",8," characters"]}),(0,s.jsxs)("li",{className:`flex items-center ${$?"text-green-600":""}`,children:[(0,s.jsx)("span",{className:`mr-1 ${$?"text-green-600":""}`,children:"•"}),"At least one uppercase letter"]}),(0,s.jsxs)("li",{className:`flex items-center ${_?"text-green-600":""}`,children:[(0,s.jsx)("span",{className:`mr-1 ${_?"text-green-600":""}`,children:"•"}),"At least one lowercase letter"]}),(0,s.jsxs)("li",{className:`flex items-center ${W?"text-green-600":""}`,children:[(0,s.jsx)("span",{className:`mr-1 ${W?"text-green-600":""}`,children:"•"}),"At least one number"]}),(0,s.jsxs)("li",{className:`flex items-center ${F?"text-green-600":""}`,children:[(0,s.jsx)("span",{className:`mr-1 ${F?"text-green-600":""}`,children:"•"}),"At least one special character"]})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.JhU,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"confirm-password",name:"confirm-password",type:y?"text":"password",autoComplete:"new-password",required:!0,className:`appearance-none block w-full pl-10 pr-10 py-2 border ${!g?"border-red-300":h?"border-green-500":"border-gray-300"} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 transition-colors sm:text-sm`,placeholder:"••••••••",value:c,onChange:e=>{let t=e.target.value;m(t),b(t===r)},onFocus:()=>p(!0),onBlur:()=>p(!1)}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>N(!y),children:y?(0,s.jsx)(d.mx3,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(d.Ny1,{className:"h-5 w-5 text-gray-400"})})]}),!g&&c&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:"Passwords do not match"})]}),k&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:k}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:P,className:`w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors ${P?"opacity-70 cursor-not-allowed":""}`,children:P?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Resetting..."]}):"Reset Password"})})]})]})]})]})]}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-red-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Invalid or Expired Link"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"The password reset link you clicked is invalid or has expired. Please request a new password reset link."}),(0,s.jsx)(o(),{href:"/forgot-password",className:"inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors",children:"Request New Link"})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("svg",{className:"animate-spin h-10 w-10 text-green-600 mx-auto mb-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("p",{className:"text-gray-600",children:"Verifying your reset link..."})]})})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x,metadata:()=>u});var s=r(37413),n=r(22376),a=r.n(n),i=r(68726),o=r.n(i);r(61135);var l=r(23229),d=r(37043),c=r(82113),m=r(41750);let u={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/********.svg",apple:"/images/********.svg"},themeColor:"#4CAF50"};function x({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:(0,s.jsx)(l.AuthProvider,{children:(0,s.jsx)(c.default,{children:(0,s.jsx)(d.CartProvider,{children:(0,s.jsx)(m.default,{children:e})})})})})})}},96111:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},97695:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(60687),n=r(82136);function a({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,8414,9567,3877,474],()=>r(57322));module.exports=s})();
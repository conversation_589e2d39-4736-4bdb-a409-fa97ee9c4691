"use strict";exports.id=9391,exports.ids=[9391],exports.modules={59391:(e,t,r)=>{r.d(t,{I:()=>x});var s=r(39850),i=r(33465),n=r(61489),u=r(35536),a=r(73458),h=r(31212),c=class extends u.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#t=null,this.#r=(0,a.T)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#s=void 0;#i=void 0;#n=void 0;#u;#a;#r;#t;#h;#c;#o;#l;#d;#p;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#s.addObserver(this),o(this.#s,this.options)?this.#y():this.updateResult(),this.#R())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.#s,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.#s,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#v(),this.#Q(),this.#s.removeObserver(this)}setOptions(e){let t=this.options,r=this.#s;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,h.Eh)(this.options.enabled,this.#s))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#b(),this.#s.setOptions(this.options),t._defaulted&&!(0,h.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#s,observer:this});let s=this.hasListeners();s&&d(this.#s,r,this.options,t)&&this.#y(),this.updateResult(),s&&(this.#s!==r||(0,h.Eh)(this.options.enabled,this.#s)!==(0,h.Eh)(t.enabled,this.#s)||(0,h.d2)(this.options.staleTime,this.#s)!==(0,h.d2)(t.staleTime,this.#s))&&this.#m();let i=this.#I();s&&(this.#s!==r||(0,h.Eh)(this.options.enabled,this.#s)!==(0,h.Eh)(t.enabled,this.#s)||i!==this.#p)&&this.#g(i)}getOptimisticResult(e){var t,r;let s=this.#e.getQueryCache().build(this.#e,e),i=this.createResult(s,e);return t=this,r=i,(0,h.f8)(t.getCurrentResult(),r)||(this.#n=i,this.#a=this.options,this.#u=this.#s.state),i}getCurrentResult(){return this.#n}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#f.add(e)}getCurrentQuery(){return this.#s}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#y({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#n))}#y(e){this.#b();let t=this.#s.fetch(this.options,e);return e?.throwOnError||(t=t.catch(h.lQ)),t}#m(){this.#v();let e=(0,h.d2)(this.options.staleTime,this.#s);if(h.S$||this.#n.isStale||!(0,h.gn)(e))return;let t=(0,h.j3)(this.#n.dataUpdatedAt,e);this.#l=setTimeout(()=>{this.#n.isStale||this.updateResult()},t+1)}#I(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#s):this.options.refetchInterval)??!1}#g(e){this.#Q(),this.#p=e,!h.S$&&!1!==(0,h.Eh)(this.options.enabled,this.#s)&&(0,h.gn)(this.#p)&&0!==this.#p&&(this.#d=setInterval(()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#y()},this.#p))}#R(){this.#m(),this.#g(this.#I())}#v(){this.#l&&(clearTimeout(this.#l),this.#l=void 0)}#Q(){this.#d&&(clearInterval(this.#d),this.#d=void 0)}createResult(e,t){let r,s=this.#s,i=this.options,u=this.#n,c=this.#u,l=this.#a,f=e!==s?e.state:this.#i,{state:y}=e,R={...y},v=!1;if(t._optimisticResults){let r=this.hasListeners(),u=!r&&o(e,t),a=r&&d(e,s,t,i);(u||a)&&(R={...R,...(0,n.k)(y.data,e.options)}),"isRestoring"===t._optimisticResults&&(R.fetchStatus="idle")}let{error:Q,errorUpdatedAt:b,status:m}=R;r=R.data;let I=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===m){let e;u?.isPlaceholderData&&t.placeholderData===l?.placeholderData?(e=u.data,I=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#o?.state.data,this.#o):t.placeholderData,void 0!==e&&(m="success",r=(0,h.pl)(u?.data,e,t),v=!0)}if(t.select&&void 0!==r&&!I)if(u&&r===c?.data&&t.select===this.#h)r=this.#c;else try{this.#h=t.select,r=t.select(r),r=(0,h.pl)(u?.data,r,t),this.#c=r,this.#t=null}catch(e){this.#t=e}this.#t&&(Q=this.#t,r=this.#c,b=Date.now(),m="error");let g="fetching"===R.fetchStatus,O="pending"===m,S="error"===m,T=O&&g,E=void 0!==r,C={status:m,fetchStatus:R.fetchStatus,isPending:O,isSuccess:"success"===m,isError:S,isInitialLoading:T,isLoading:T,data:r,dataUpdatedAt:R.dataUpdatedAt,error:Q,errorUpdatedAt:b,failureCount:R.fetchFailureCount,failureReason:R.fetchFailureReason,errorUpdateCount:R.errorUpdateCount,isFetched:R.dataUpdateCount>0||R.errorUpdateCount>0,isFetchedAfterMount:R.dataUpdateCount>f.dataUpdateCount||R.errorUpdateCount>f.errorUpdateCount,isFetching:g,isRefetching:g&&!O,isLoadingError:S&&!E,isPaused:"paused"===R.fetchStatus,isPlaceholderData:v,isRefetchError:S&&E,isStale:p(e,t),refetch:this.refetch,promise:this.#r};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===C.status?e.reject(C.error):void 0!==C.data&&e.resolve(C.data)},r=()=>{t(this.#r=C.promise=(0,a.T)())},i=this.#r;switch(i.status){case"pending":e.queryHash===s.queryHash&&t(i);break;case"fulfilled":("error"===C.status||C.data!==i.value)&&r();break;case"rejected":("error"!==C.status||C.error!==i.reason)&&r()}}return C}updateResult(){let e=this.#n,t=this.createResult(this.#s,this.options);this.#u=this.#s.state,this.#a=this.options,void 0!==this.#u.data&&(this.#o=this.#s),(0,h.f8)(t,e)||(this.#n=t,this.#O({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#f.size)return!0;let s=new Set(r??this.#f);return this.options.throwOnError&&s.add("error"),Object.keys(this.#n).some(t=>this.#n[t]!==e[t]&&s.has(t))})()}))}#b(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#s)return;let t=this.#s;this.#s=e,this.#i=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#R()}#O(e){i.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#n)}),this.#e.getQueryCache().notify({query:this.#s,type:"observerResultsUpdated"})})}};function o(e,t){return!1!==(0,h.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&l(e,t,t.refetchOnMount)}function l(e,t,r){if(!1!==(0,h.Eh)(t.enabled,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&p(e,t)}return!1}function d(e,t,r,s){return(e!==t||!1===(0,h.Eh)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,h.Eh)(t.enabled,e)&&e.isStaleByTime((0,h.d2)(t.staleTime,e))}var f=r(43210),y=r(8693);r(60687);var R=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),v=()=>f.useContext(R);function Q(){}var b=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},m=e=>{f.useEffect(()=>{e.clearReset()},[e])},I=({result:e,errorResetBoundary:t,throwOnError:r,query:s,suspense:i})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(i&&void 0===e.data||function(e,t){return"function"==typeof e?e(...t):!!e}(r,[e.error,s])),g=f.createContext(!1),O=()=>f.useContext(g);g.Provider;var S=e=>{let t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},T=(e,t)=>e.isLoading&&e.isFetching&&!t,E=(e,t)=>e?.suspense&&t.isPending,C=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function x(e,t){return function(e,t,r){let s=(0,y.jE)(r),n=O(),u=v(),a=s.defaultQueryOptions(e);s.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=n?"isRestoring":"optimistic",S(a),b(a,u),m(u);let c=!s.getQueryCache().get(a.queryHash),[o]=f.useState(()=>new t(s,a)),l=o.getOptimisticResult(a),d=!n&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback(e=>{let t=d?o.subscribe(i.jG.batchCalls(e)):Q;return o.updateResult(),t},[o,d]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),f.useEffect(()=>{o.setOptions(a)},[a,o]),E(a,l))throw C(a,o,u);if(I({result:l,errorResetBoundary:u,throwOnError:a.throwOnError,query:s.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw l.error;if(s.getDefaultOptions().queries?._experimental_afterQuery?.(a,l),a.experimental_prefetchInRender&&!h.S$&&T(l,n)){let e=c?C(a,o,u):s.getQueryCache().get(a.queryHash)?.promise;e?.catch(Q).finally(()=>{o.updateResult()})}return a.notifyOnChangeProps?l:o.trackResult(l)}(e,c,t)}}};
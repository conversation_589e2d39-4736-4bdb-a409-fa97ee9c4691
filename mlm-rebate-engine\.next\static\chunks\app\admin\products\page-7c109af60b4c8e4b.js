(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7575],{84015:(e,t,s)=>{Promise.resolve().then(s.bind(s,92563))},92563:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(95155),a=s(12115),l=s(12108),i=s(35695),c=s(70357),d=s(66766),n=s(29911);let o=e=>{let{className:t=""}=e;return(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-100 ".concat(t),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.dkL,{className:"mx-auto text-gray-400 text-4xl mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No image available"})]})})};function m(){let{data:e,status:t}=(0,l.useSession)(),s=(0,i.useRouter)(),[m,x]=(0,a.useState)([]),[u,p]=(0,a.useState)(!0),[h,g]=(0,a.useState)(!1),[b,j]=(0,a.useState)(null),[f,v]=(0,a.useState)({name:"",description:"",price:"",image:""}),[y,N]=(0,a.useState)(null),[w,C]=(0,a.useState)(null),[k,P]=(0,a.useState)(!1),S=(0,a.useRef)(null),[A,F]=(0,a.useState)([{level:1,percentage:"10"}]),[E,R]=(0,a.useState)({type:"",text:""}),[D,O]=(0,a.useState)(null),[I,L]=(0,a.useState)(""),[U,T]=(0,a.useState)(!1),[G,_]=(0,a.useState)(null),[z,J]=(0,a.useState)("createdAt"),[V,q]=(0,a.useState)("desc"),[B,M]=(0,a.useState)(null),[W,Z]=(0,a.useState)(!1),[H,Q]=(0,a.useState)(!1),[K,Y]=(0,a.useState)(null);(0,a.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,a.useEffect)(()=>{"authenticated"===t&&X()},[t,I,G,z,V]);let X=async()=>{p(!0);try{let e=new URLSearchParams;I&&e.append("search",I),null!==G&&e.append("isActive",G.toString()),e.append("sortBy",z),e.append("sortOrder",V);let t=await fetch("/api/products?".concat(e.toString()));if(!t.ok)throw Error("Failed to fetch products: ".concat(t.statusText));let s=await t.json(),r=Array.isArray(s)?s:s.products||[];x(r),p(!1)}catch(e){console.error("Error fetching products:",e),p(!1)}},$=e=>{let{name:t,value:s}=e.target;v(e=>({...e,[t]:s}))},ee=async()=>{if(!y)return null;P(!0);try{let e=new FormData;e.append("file",y);let t=await fetch("/api/upload",{method:"POST",body:e}),s=await t.json();if(!t.ok)throw Error(s.error||"Failed to upload image");return P(!1),s.url}catch(e){return R({type:"error",text:e.message||"An error occurred while uploading the image"}),P(!1),null}},et=(e,t,s)=>{let r=[...A];r[e]={...r[e],[t]:"level"===t?parseInt(s):s},F(r)},es=e=>{let t=[...A];t.splice(e,1),F(t)},er=()=>{v({name:"",description:"",price:"",image:""}),F([{level:1,percentage:"10"}]),j(null),g(!1),N(null),C(null),S.current&&(S.current.value="")},ea=e=>{j(e),v({name:e.name,description:e.description||"",price:e.price.toString(),image:e.image||""}),e.image?C(e.image):C(null),N(null),S.current&&(S.current.value="");let t=e.rebateConfigs.map(e=>({level:e.level,percentage:e.percentage.toString()}));F(t.length>0?t:[{level:1,percentage:"10"}]),g(!0)},el=async e=>{e.preventDefault(),R({type:"",text:""});try{let e;if(!f.name||!f.price)return void R({type:"error",text:"Name and price are required"});for(let e of A)if(e.level<=0||0>=parseFloat(e.percentage))return void R({type:"error",text:"Rebate level and percentage must be greater than 0"});let t=A.map(e=>e.level);if(new Set(t).size!==t.length)return void R({type:"error",text:"Duplicate rebate levels are not allowed"});let s=f.image;if(y){let e=await ee();e&&(s=e)}let r={...f,price:parseFloat(f.price),image:s,rebateConfigs:A.map(e=>({level:e.level,percentage:parseFloat(e.percentage)}))};e=b?await fetch("/api/products/".concat(b.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}):await fetch("/api/products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});let a=await e.json();if(!e.ok)throw Error(a.error||"Failed to save product");R({type:"success",text:b?"Product updated successfully":"Product created successfully"}),X(),er()}catch(e){R({type:"error",text:e.message||"An error occurred while saving the product"})}},ei=e=>{O(D===e?null:e)};return"loading"===t||u?(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})})}):(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"Product Management"}),(0,r.jsxs)("button",{onClick:()=>g(!h),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center",children:[(0,r.jsx)(n.OiG,{className:"mr-2"})," ",h?"Cancel":"Add Product"]})]}),E.text&&(0,r.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("success"===E.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"),children:E.text}),h&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:b?"Edit Product":"Add New Product"}),(0,r.jsxs)("form",{onSubmit:el,children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:f.name,onChange:$,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"price",className:"block text-sm font-medium text-gray-700 mb-1",children:"Price *"}),(0,r.jsx)("input",{type:"number",id:"price",name:"price",value:f.price,onChange:$,min:"0.01",step:"0.01",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Image"}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[w&&(0,r.jsxs)("div",{className:"relative w-full h-40 mb-2 border rounded-md overflow-hidden",children:[(0,r.jsx)(d.default,{src:w,alt:"Product preview",fill:!0,className:"object-contain"}),(0,r.jsx)("button",{type:"button",onClick:()=>{C(null),N(null),v(e=>({...e,image:""})),S.current&&(S.current.value="")},className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600",title:"Remove image",children:(0,r.jsx)(n.QCr,{size:14})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"file",id:"imageUpload",ref:S,onChange:e=>{var t;let s=null==(t=e.target.files)?void 0:t[0];if(!s)return;if(!["image/jpeg","image/png","image/webp","image/gif"].includes(s.type))return void R({type:"error",text:"File type not allowed. Please upload a JPEG, PNG, WebP, or GIF image."});if(s.size>5242880)return void R({type:"error",text:"File size exceeds the 5MB limit"});N(s);let r=new FileReader;r.onloadend=()=>{C(r.result)},r.readAsDataURL(s)},accept:"image/jpeg,image/png,image/webp,image/gif",className:"hidden"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=S.current)?void 0:e.click()},className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 flex items-center",disabled:k,children:[k?(0,r.jsx)(n.hW,{className:"mr-2 animate-spin"}):(0,r.jsx)(n.HVe,{className:"mr-2"}),y?"Change Image":"Upload Image"]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("input",{type:"text",id:"image",name:"image",value:f.image,onChange:$,placeholder:"Or enter image URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Upload a JPEG, PNG, WebP, or GIF image (max 5MB)"})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,r.jsx)("textarea",{id:"description",name:"description",value:f.description,onChange:$,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("h3",{className:"text-md font-semibold",children:"Rebate Configuration"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{let e=A.length>0?Math.max(...A.map(e=>e.level))+1:1;F([...A,{level:e,percentage:"5"}])},className:"px-2 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center",children:[(0,r.jsx)(n.OiG,{className:"mr-1"})," Add Level"]})]}),(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:A.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center mb-2 last:mb-0",children:[(0,r.jsxs)("div",{className:"w-1/3 pr-2",children:[(0,r.jsx)("label",{htmlFor:"level-".concat(t),className:"block text-xs font-medium text-gray-700 mb-1",children:"Level"}),(0,r.jsx)("input",{type:"number",id:"level-".concat(t),value:e.level,onChange:e=>et(t,"level",e.target.value),min:"1",max:"10",className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),(0,r.jsxs)("div",{className:"w-1/3 px-2",children:[(0,r.jsx)("label",{htmlFor:"percentage-".concat(t),className:"block text-xs font-medium text-gray-700 mb-1",children:"Percentage (%)"}),(0,r.jsx)("input",{type:"number",id:"percentage-".concat(t),value:e.percentage,onChange:e=>et(t,"percentage",e.target.value),min:"0.1",step:"0.1",max:"100",className:"w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),(0,r.jsx)("div",{className:"w-1/3 pl-2 flex items-end",children:(0,r.jsx)("button",{type:"button",onClick:()=>es(t),disabled:1===A.length,className:"mt-5 px-2 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-red-300 text-sm",children:"Remove"})})]},t))})]}),(0,r.jsxs)("div",{className:"flex justify-end",children:[(0,r.jsx)("button",{type:"button",onClick:er,className:"px-4 py-2 border border-gray-300 rounded-md mr-2 hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:b?"Update Product":"Create Product"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search products by name or description",value:I,onChange:e=>L(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(n.KSO,{className:"text-gray-400"})})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("button",{onClick:()=>T(!U),className:"flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:[(0,r.jsx)(n.YsJ,{className:"mr-2"}),"Filters",U?(0,r.jsx)(n.Ucs,{className:"ml-2"}):(0,r.jsx)(n.Vr3,{className:"ml-2"})]})})]}),U&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>_(!0!==G||null),className:"px-3 py-1 rounded-md flex items-center ".concat(!0===G?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-700 border border-gray-200"),children:[!0===G?(0,r.jsx)(n.RIx,{className:"mr-2"}):(0,r.jsx)(n.jZj,{className:"mr-2"}),"Active"]}),(0,r.jsxs)("button",{onClick:()=>_(!1===G&&null),className:"px-3 py-1 rounded-md flex items-center ".concat(!1===G?"bg-red-100 text-red-800 border border-red-300":"bg-gray-100 text-gray-700 border border-gray-200"),children:[!1===G?(0,r.jsx)(n.RIx,{className:"mr-2"}):(0,r.jsx)(n.jZj,{className:"mr-2"}),"Inactive"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort By"}),(0,r.jsxs)("select",{value:z,onChange:e=>J(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"name",children:"Name"}),(0,r.jsx)("option",{value:"price",children:"Price"}),(0,r.jsx)("option",{value:"createdAt",children:"Date Created"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sort Order"}),(0,r.jsxs)("select",{value:V,onChange:e=>q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"asc",children:"Ascending"}),(0,r.jsx)("option",{value:"desc",children:"Descending"})]})]})]}),(0,r.jsx)("div",{className:"mt-4 flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{L(""),_(null),J("createdAt"),q("desc")},className:"px-4 py-2 text-gray-700 hover:text-gray-900",children:"Reset Filters"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Products"})}),(0,r.jsx)("div",{className:"p-6",children:m.length>0?(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rebate Levels"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>ei(e.id),className:"mr-2 text-gray-500",children:D===e.id?(0,r.jsx)(n.Ucs,{}):(0,r.jsx)(n.Vr3,{})}),(0,r.jsx)("div",{className:"h-10 w-10 flex-shrink-0 mr-3 relative rounded-md overflow-hidden",children:e.image?(0,r.jsx)(d.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover"}):(0,r.jsx)(o,{className:"rounded-md"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),D===e.id&&(0,r.jsx)("div",{className:"text-sm text-gray-500 mt-1",children:e.description||"No description"})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.price.toFixed(2)]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.rebateConfigs.length," levels",D===e.id&&(0,r.jsx)("div",{className:"mt-2 text-xs",children:e.rebateConfigs.sort((e,t)=>e.level-t.level).map(e=>(0,r.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,r.jsxs)("span",{children:["Level ",e.level,":"]}),(0,r.jsxs)("span",{children:[e.percentage,"%"]})]},e.id))})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{M(e),Z(!0)},className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,r.jsx)(n.Ny1,{})}),(0,r.jsx)("button",{onClick:()=>ea(e),className:"text-green-600 hover:text-green-900",title:"Edit Product",children:(0,r.jsx)(n.uO9,{})}),(0,r.jsx)("button",{onClick:async()=>{try{if(!(await fetch("/api/products/".concat(e.id,"/toggle-status"),{method:"PATCH"})).ok)throw Error("Failed to toggle product status");X(),R({type:"success",text:"Product ".concat(e.isActive?"deactivated":"activated"," successfully")})}catch(e){console.error("Error toggling product status:",e),R({type:"error",text:"Failed to toggle product status"})}},className:"".concat(e.isActive?"text-orange-600 hover:text-orange-900":"text-green-600 hover:text-green-900"),title:e.isActive?"Deactivate Product":"Activate Product",children:e.isActive?(0,r.jsx)(n.RIx,{}):(0,r.jsx)(n.jZj,{})}),(0,r.jsx)("button",{onClick:()=>{Y(e),Q(!0)},className:"text-red-600 hover:text-red-900",title:"Delete Product",children:(0,r.jsx)(n.qbC,{})})]})})]},e.id))})]})}):(0,r.jsx)("p",{className:"text-gray-500",children:"No products found."})})]}),W&&B&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Product Details"}),(0,r.jsx)("button",{onClick:()=>Z(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)(n.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"md:w-1/3",children:[(0,r.jsx)("div",{className:"relative w-full h-64 rounded-md overflow-hidden border border-gray-200",children:B.image?(0,r.jsx)(d.default,{src:B.image,alt:B.name,fill:!0,className:"object-contain"}):(0,r.jsx)(o,{})}),B.image&&(0,r.jsx)("div",{className:"mt-2 flex justify-center",children:(0,r.jsxs)("a",{href:B.image,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800 flex items-center",children:[(0,r.jsx)(n.Ny1,{className:"mr-1"})," View full image"]})})]}),(0,r.jsxs)("div",{className:"md:w-2/3",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:B.name}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(B.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:B.isActive?"Active":"Inactive"})}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Price"}),(0,r.jsxs)("p",{className:"text-lg font-semibold",children:["₱",B.price.toFixed(2)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Description"}),(0,r.jsx)("p",{className:"text-gray-700",children:B.description||"No description provided"})]})]})]}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("h3",{className:"text-md font-semibold mb-3",children:"Rebate Configuration"}),B.rebateConfigs.length>0?(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-2",children:"Level"}),(0,r.jsx)("th",{className:"text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-2",children:"Percentage"}),(0,r.jsx)("th",{className:"text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-2",children:"Rebate Amount"})]})}),(0,r.jsx)("tbody",{children:B.rebateConfigs.sort((e,t)=>e.level-t.level).map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"py-2 text-sm",children:["Level ",e.level]}),(0,r.jsxs)("td",{className:"py-2 text-sm",children:[e.percentage,"%"]}),(0,r.jsxs)("td",{className:"py-2 text-sm",children:["₱",(B.price*e.percentage/100).toFixed(2)]})]},e.id))})]})}):(0,r.jsx)("p",{className:"text-gray-500",children:"No rebate configuration"})]}),(0,r.jsxs)("div",{className:"flex justify-end mt-6 space-x-3",children:[(0,r.jsx)("button",{onClick:()=>{Z(!1),ea(B)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Edit Product"}),(0,r.jsx)("button",{onClick:()=>Z(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Close"})]})]})]})}),H&&K&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-md w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Confirm Delete"}),(0,r.jsxs)("p",{className:"text-gray-700 mb-6",children:['Are you sure you want to delete the product "',K.name,'"? This action cannot be undone.']}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:async()=>{try{if(!(await fetch("/api/products/".concat(K.id),{method:"DELETE"})).ok)throw Error("Failed to delete product");X(),R({type:"success",text:"Product deleted successfully"}),Q(!1),Y(null)}catch(e){console.error("Error deleting product:",e),R({type:"error",text:"Failed to delete product"})}},className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Delete"}),(0,r.jsx)("button",{onClick:()=>{Q(!1),Y(null)},className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"})]})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>t(84015)),_N_E=e.O()}]);
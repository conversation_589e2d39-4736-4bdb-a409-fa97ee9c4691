(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2992],{56718:(e,s,a)=>{Promise.resolve().then(a.bind(a,78250))},78250:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(95155),i=a(12115),r=a(12108),n=a(87747),l=a(29911),c=a(6874),d=a.n(c),x=a(55028),o=a(74211);let m=(0,x.default)(()=>Promise.all([a.e(1294),a.e(6808),a.e(7402)]).then(a.bind(a,27402)),{loadableGenerated:{webpack:()=>[27402]},ssr:!1,loading:()=>(0,t.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,t.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("span",{children:"Loading visualization..."})]})}),h=(0,x.default)(()=>Promise.all([a.e(1294),a.e(6808),a.e(186)]).then(a.bind(a,20186)),{loadableGenerated:{webpack:()=>[20186]},ssr:!1,loading:()=>(0,t.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,t.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("span",{children:"Loading visualization..."})]})}),p=(0,x.default)(()=>a.e(8169).then(a.bind(a,58169)),{loadableGenerated:{webpack:()=>[58169]},ssr:!1,loading:()=>(0,t.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,t.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("span",{children:"Loading visualization..."})]})});function u(){let{data:e,status:s}=(0,r.useSession)(),[a,c]=(0,i.useState)(void 0),[x,u]=(0,i.useState)("original"),[g,j]=(0,i.useState)("enhanced"),{data:y,isLoading:w}=(0,n.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let a=await fetch("/api/users/me");if(!a.ok)throw Error("Failed to fetch user data");return await a.json()},enabled:"authenticated"===s});y&&!a&&c(y.id);let N=e=>{if(!a)return(0,t.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,t.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("span",{children:"Loading user data..."})]});switch(e){case"original":return(0,t.jsx)(p,{userId:a,maxLevel:3});case"basic":return(0,t.jsx)(m,{userId:a,maxLevel:3});case"enhanced":return(0,t.jsx)(o.Ln,{children:(0,t.jsx)(h,{userId:a,maxLevel:3})});default:return null}},f=e=>{switch(e){case"original":return"Original Tree";case"basic":return"Basic Flow";case"enhanced":return"Enhanced Flow";default:return""}};return"loading"===s||w?(0,t.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,t.jsx)(l.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,t.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,t.jsx)(l.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,t.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,t.jsx)(d(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,t.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,t.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy Visualization Comparison"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Compare different genealogy visualization methods side by side"})]}),(0,t.jsxs)(d(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,t.jsx)(l.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,t.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-lg mb-4 flex flex-wrap items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Left Visualization"}),(0,t.jsxs)("select",{value:x,onChange:e=>u(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"original",children:"Original Tree"}),(0,t.jsx)("option",{value:"basic",children:"Basic Flow"}),(0,t.jsx)("option",{value:"enhanced",children:"Enhanced Flow"})]})]}),(0,t.jsx)("button",{onClick:()=>{u(g),j(x)},className:"mt-6 p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200",title:"Swap visualizations",children:(0,t.jsx)(l.yk7,{})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Right Visualization"}),(0,t.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"original",children:"Original Tree"}),(0,t.jsx)("option",{value:"basic",children:"Basic Flow"}),(0,t.jsx)("option",{value:"enhanced",children:"Enhanced Flow"})]})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 mt-4 md:mt-0",children:[(0,t.jsx)("p",{children:"Compare different visualization methods to see which one works best for your needs."}),(0,t.jsx)("p",{children:"Each method has its own strengths and features."})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-blue-50 p-3 border-b border-blue-100",children:(0,t.jsx)("h3",{className:"font-medium",children:f(x)})}),(0,t.jsx)("div",{className:"h-[600px] overflow-hidden",children:N(x)})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-blue-50 p-3 border-b border-blue-100",children:(0,t.jsx)("h3",{className:"font-medium",children:f(g)})}),(0,t.jsx)("div",{className:"h-[600px] overflow-hidden",children:N(g)})]})]}),(0,t.jsxs)("div",{className:"mt-6 bg-white p-4 rounded-lg shadow-lg",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Feature Comparison"}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Feature"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Original Tree"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Basic Flow"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Enhanced Flow"})]})}),(0,t.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Interactive Navigation"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Good"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Excellent"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Performance with Large Trees"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Limited"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Good"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Excellent"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Visual Appeal"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Good"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Excellent"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Information Density"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Low"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Medium"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"High"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Layout Options"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Fixed"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Fixed"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Multiple"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Animation"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"None"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Advanced"})]}),(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:"Performance Metrics"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Limited"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Basic"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Comprehensive"})]})]})]})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,8702,6874,2108,5557,6967,7747,6113,8441,1684,7358],()=>s(56718)),_N_E=e.O()}]);
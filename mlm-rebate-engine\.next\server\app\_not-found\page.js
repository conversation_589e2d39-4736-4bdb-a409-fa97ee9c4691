(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23229:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\AuthProvider.tsx","AuthProvider")},28253:(e,r,t)=>{"use strict";t.d(r,{CartProvider:()=>a,_:()=>s});var n=t(60687),o=t(43210);let i=(0,o.createContext)(void 0),s=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},a=({children:e})=>{let[r,t]=(0,o.useState)([]);(0,o.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e),localStorage.removeItem("cart")}},[]),(0,o.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let s=e=>{t(r=>r.filter(r=>r.id!==e))},a=r.reduce((e,r)=>e+r.quantity,0),l=r.reduce((e,r)=>e+r.price*r.quantity,0),d=r.reduce((e,r)=>e+r.pv*r.quantity,0);return(0,n.jsx)(i.Provider,{value:{items:r,addItem:e=>{t(r=>{let t=r.findIndex(r=>r.id===e.id);if(!(t>=0))return[...r,e];{let n=[...r];return n[t]={...n[t],quantity:n[t].quantity+e.quantity},n}})},removeItem:s,updateQuantity:(e,r)=>{if(r<=0)return void s(e);t(t=>t.map(t=>t.id===e?{...t,quantity:r}:t))},clearCart:()=>{t([])},itemCount:a,subtotal:l,totalPV:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37043:(e,r,t)=>{"use strict";t.d(r,{CartProvider:()=>o});var n=t(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","useCart");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\contexts\\CartContext.tsx","CartProvider")},41750:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\ServiceWorkerProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\ServiceWorkerProvider.tsx","default")},42967:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},45851:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});var n=t(60687),o=t(25217),i=t(8693),s=t(43210);function a({children:e}){let[r]=(0,s.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1,retry:1}}}));return(0,n.jsx)(i.Ht,{client:r,children:e})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63345:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var n=t(60687),o=t(43210);let i=()=>"serviceWorker"in navigator,s=async()=>{if(!i())return console.log("Service workers are not supported in this browser"),!1;try{let e=await navigator.serviceWorker.register("/service-worker.js");return console.log("Service worker registered successfully:",e.scope),a(e),!0}catch(e){return console.error("Service worker registration failed:",e),!1}},a=e=>{setInterval(()=>{e.update()},36e5),e.addEventListener("updatefound",()=>{let r=e.installing;r&&r.addEventListener("statechange",()=>{"installed"===r.state&&navigator.serviceWorker.controller&&l()})})},l=()=>{console.log("New version available! Refresh to update.");let e=new CustomEvent("serviceWorkerUpdateAvailable");window.dispatchEvent(e)},d=({children:e})=>{let[r,t]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{s();let e=()=>{t(!0)};return window.addEventListener("serviceWorkerUpdateAvailable",e),()=>{window.removeEventListener("serviceWorkerUpdateAvailable",e)}},[]),(0,n.jsxs)(n.Fragment,{children:[e,r&&(0,n.jsxs)("div",{className:"fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center",children:[(0,n.jsxs)("div",{className:"mr-4",children:[(0,n.jsx)("p",{className:"font-medium",children:"Update Available"}),(0,n.jsx)("p",{className:"text-sm",children:"A new version of the app is available"})]}),(0,n.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors",children:"Refresh"})]})]})}},64376:(e,r,t)=>{Promise.resolve().then(t.bind(t,37043)),Promise.resolve().then(t.bind(t,23229)),Promise.resolve().then(t.bind(t,82113)),Promise.resolve().then(t.bind(t,41750))},67882:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>v,tree:()=>d});var n=t(65239),o=t(48088),i=t(88170),s=t.n(i),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:t,loadChunk:()=>Promise.resolve()},v=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74104:(e,r,t)=>{Promise.resolve().then(t.bind(t,28253)),Promise.resolve().then(t.bind(t,97695)),Promise.resolve().then(t.bind(t,45851)),Promise.resolve().then(t.bind(t,63345))},82113:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\providers\\QueryProvider.tsx","default")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>v});var n=t(37413),o=t(22376),i=t.n(o),s=t(68726),a=t.n(s);t(61135);var l=t(23229),d=t(37043),c=t(82113),u=t(41750);let v={title:"Extreme Life Herbal Product Rewards",description:"Extreme Life Herbal Products Trading rewards program for distributors",manifest:"/manifest.json",icons:{icon:"/images/20250503.svg",apple:"/images/20250503.svg"},themeColor:"#4CAF50"};function m({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,n.jsx)(l.AuthProvider,{children:(0,n.jsx)(c.default,{children:(0,n.jsx)(d.CartProvider,{children:(0,n.jsx)(u.default,{children:e})})})})})})}},96111:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},97695:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>i});var n=t(60687),o=t(82136);function i({children:e}){return(0,n.jsx)(o.SessionProvider,{children:e})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4243,8414],()=>t(67882));module.exports=n})();
exports.id=2896,exports.ids=[2896],exports.modules={12909:(e,r,o)=>{"use strict";o.d(r,{Er:()=>l,Nh:()=>c,aP:()=>d});var a=o(96330),s=o(13581),t=o(85663),n=o(55511),i=o.n(n);async function l(e){return await t.Ay.hash(e,10)}function d(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new a.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,s.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new a.PrismaClient,o=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!o)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",o.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let s=await t.Ay.compare(e.password,o.password);if(console.log("Password valid:",s),!s)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",o.id);let{password:n,...i}=o;return{id:o.id.toString(),email:o.email,name:o.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},31183:(e,r,o)=>{"use strict";o.d(r,{z:()=>s});var a=o(96330);let s=global.prisma||new a.PrismaClient({log:["query"]})},61904:(e,r,o)=>{"use strict";o.d(r,{Z:()=>t});let a=o(49526).createTransport({host:process.env.EMAIL_HOST||"smtp.example.com",port:parseInt(process.env.EMAIL_PORT||"587"),secure:"true"===process.env.EMAIL_SECURE,auth:{user:process.env.EMAIL_USER||"<EMAIL>",pass:process.env.EMAIL_PASSWORD||"password"}}),s={rebateReceived:e=>({subject:`You've Received a Rebate of $${e.amount.toFixed(2)}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">New Rebate Received!</h2>
          <p>Hello ${e.userName},</p>
          <p>Great news! You've received a rebate of <strong style="color: #48bb78;">$${e.amount.toFixed(2)}</strong>.</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Rebate Details:</h3>
            <ul style="padding-left: 20px;">
              <li>Amount: <strong>$${e.amount.toFixed(2)}</strong></li>
              <li>From: <strong>${e.generatorName}</strong></li>
              <li>Level: <strong>${e.level}</strong></li>
              <li>Product: <strong>${e.productName}</strong></li>
            </ul>
          </div>
          <p>This rebate has been added to your wallet balance. You can view your rebate details and wallet balance by logging into your account.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/wallet" style="background-color: #4299e1; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Wallet</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for being part of our MLM network!</p>
        </div>
      `}),rankAdvancement:e=>({subject:`Congratulations on Your Rank Advancement to ${e.newRank}!`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">Rank Advancement Achievement!</h2>
          <p>Hello ${e.userName},</p>
          <p>Congratulations! You've advanced from <strong>${e.oldRank}</strong> to <strong style="color: #805ad5;">${e.newRank}</strong>!</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Your New Benefits:</h3>
            <ul style="padding-left: 20px;">
              ${e.benefits.map(e=>`<li>${e}</li>`).join("")}
            </ul>
          </div>
          <p>Keep up the great work! As you continue to grow your network and increase your sales, you'll unlock even more benefits and higher rebate percentages.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" style="background-color: #805ad5; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Dashboard</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for your dedication and commitment to our MLM network!</p>
        </div>
      `})};async function t(e,r,o){try{let{subject:t,html:n}=s[r](o),i={from:process.env.EMAIL_FROM||"MLM Rebate Engine <<EMAIL>>",to:e,subject:t,html:n},l=await a.sendMail(i);return console.log("Email sent:",l.messageId),{success:!0,messageId:l.messageId}}catch(e){return console.error("Error sending email:",e),{success:!1,error:e}}}},78335:()=>{},92168:(e,r,o)=>{"use strict";o.d(r,{sQ:()=>t});var a=o(31183),s=o(61904);async function t(){try{let e=await a.z.rebate.findMany({where:{status:"pending"},include:{receiver:!0,generator:!0,purchase:{include:{product:!0}}},orderBy:{receiverId:"asc"}});if(0===e.length)return{success:!0,processed:0,failed:0,message:"No pending rebates found",processedRebates:[],failedRebates:[]};let r=0,o=0,t={processed:0,failed:0,processedRebates:[],failedRebates:[]},n=e.reduce((e,r)=>{let o=r.receiverId;return e[o]||(e[o]=[]),e[o].push(r),e},{});for(let[e,i]of Object.entries(n))try{let n=i.reduce((e,r)=>e+r.amount,0);for(let l of(await a.z.user.update({where:{id:parseInt(e)},data:{walletBalance:{increment:n}}}),i))try{let e=await a.z.walletTransaction.create({data:{userId:l.receiverId,amount:l.amount,type:"rebate",description:`Rebate from level ${l.level} purchase`,status:"completed"}});await a.z.rebate.update({where:{id:l.id},data:{status:"processed",processedAt:new Date,walletTransactionId:e.id}}),l.receiver.email&&(0,s.Z)(l.receiver.email,"rebateReceived",{userName:l.receiver.name,amount:l.amount,generatorName:l.generator.name,level:l.level,productName:l.purchase.product.name}).catch(e=>{console.error(`Failed to send email for rebate ID ${l.id}:`,e)}),r++,t.processedRebates.push({id:l.id,amount:l.amount,receiverId:l.receiverId,receiverName:l.receiver.name})}catch(e){console.error(`Failed to process individual rebate ID ${l.id}:`,e),await a.z.rebate.update({where:{id:l.id},data:{status:"failed"}}),o++,t.failedRebates.push({id:l.id,amount:l.amount,receiverId:l.receiverId,receiverName:l.receiver.name,error:e instanceof Error?e.message:"Unknown error"})}}catch(r){for(let s of(console.error(`Failed to process batch for receiver ID ${e}:`,r),i))await a.z.rebate.update({where:{id:s.id},data:{status:"failed"}}),o++,t.failedRebates.push({id:s.id,amount:s.amount,receiverId:s.receiverId,receiverName:s.receiver.name,error:r instanceof Error?r.message:"Unknown error"})}return t.processed=r,t.failed=o,{success:!0,...t,message:`Processed ${r} rebates, failed ${o} rebates`}}catch(e){return console.error("Error processing rebates:",e),{success:!1,processed:0,failed:0,processedRebates:[],failedRebates:[],message:`Error: ${e instanceof Error?e.message:"Unknown error"}`}}}},96487:()=>{}};
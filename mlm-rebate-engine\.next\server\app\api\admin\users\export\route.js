"use strict";(()=>{var e={};e.id=9871,e.ids=[9871],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},60512:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>v,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>q});var s={};t.r(s),t.d(s,{POST:()=>m});var n=t(96559),o=t(48088),a=t(37719),i=t(31183),u=t(32190),p=t(19854),d=t(12909),l=t(91818),x=t(70762);let c=x.z.object({includeRank:x.z.boolean().default(!0),includeDownlineCount:x.z.boolean().default(!0),includeJoinDate:x.z.boolean().default(!0),includeEarnings:x.z.boolean().default(!1),rankFilter:x.z.number().optional(),dateRangeStart:x.z.string().optional().transform(e=>e?new Date(e):void 0),dateRangeEnd:x.z.string().optional().transform(e=>e?new Date(e):void 0),activeOnly:x.z.boolean().default(!1)});async function m(e){try{let r=await (0,p.getServerSession)(d.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=parseInt(r.user.id),s=await i.z.user.findUnique({where:{id:t},select:{id:!0,name:!0,rankId:!0}});if(s?.rankId!==6)return u.NextResponse.json({error:"You must be an admin to access this endpoint"},{status:403});let n=await e.json(),o=c.safeParse(n);if(!o.success)return u.NextResponse.json({error:"Invalid export options",details:o.error.format()},{status:400});let a=o.data,x=await (0,l.Nl)(a);return await i.z.userAudit.create({data:{userId:s.id,action:"export",details:JSON.stringify({options:a,timestamp:new Date().toISOString()})}}),new u.NextResponse(x,{headers:{"Content-Type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","Content-Disposition":"attachment; filename=users_export.xlsx"}})}catch(e){return console.error("Error exporting users:",e),u.NextResponse.json({error:"Failed to export users"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/users/export/route",pathname:"/api/admin/users/export",filename:"route",bundlePath:"app/api/admin/users/export/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\admin\\users\\export\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:q,serverHooks:v}=f;function h(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:q})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,8381,632,1426],()=>t(60512));module.exports=s})();
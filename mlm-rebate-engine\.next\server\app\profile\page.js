(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14248:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(65239),a=r(48088),l=r(88170),n=r.n(l),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(s,o);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75758)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46785:(e,s,r)=>{Promise.resolve().then(r.bind(r,75758))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75758:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\profile\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},83233:(e,s,r)=>{Promise.resolve().then(r.bind(r,87032))},87032:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(60687),a=r(43210),l=r(82136),n=r(16189),i=r(85814),o=r.n(i),d=r(68367),c=r(23877);function m(){let{data:e,status:s}=(0,l.useSession)();(0,n.useRouter)();let[r,i]=(0,a.useState)(null),[m,x]=(0,a.useState)(!0),[u,p]=(0,a.useState)(""),[h,f]=(0,a.useState)(!1),[g,b]=(0,a.useState)(!1),[j,w]=(0,a.useState)({name:"",phone:"",profileImage:""}),[N,y]=(0,a.useState)({currentPassword:"",newPassword:"",confirmNewPassword:""}),[v,P]=(0,a.useState)(!1),[C,k]=(0,a.useState)(""),_=async()=>{x(!0),p("");try{let s=e?.user?.email;if(!s)throw Error("User email not found in session");let r=await fetch(`/api/users?search=${encodeURIComponent(s)}`),t=await r.json();if(!t.users||0===t.users.length)throw Error("User not found");let a=t.users[0].id,l=await fetch(`/api/users/${a}`);if(!l.ok){let e=await l.json();throw Error(e.error||"Failed to fetch profile")}let n=await l.json();i(n),w({name:n.name,phone:n.phone||"",profileImage:n.profileImage||""})}catch(e){console.error("Error fetching profile:",e),p(e.message||"An error occurred while fetching profile")}finally{x(!1)}},M=e=>{let{name:s,value:r}=e.target;w(e=>({...e,[s]:r}))},E=e=>{let{name:s,value:r}=e.target;y(e=>({...e,[s]:r}))},S=async e=>{e.preventDefault(),P(!0),p(""),k("");try{if(!r)throw Error("Profile not loaded");let e={name:j.name,phone:j.phone||null,profileImage:j.profileImage||null};if(g){if(N.newPassword!==N.confirmNewPassword)throw Error("New passwords don't match");Object.assign(e,{currentPassword:N.currentPassword,newPassword:N.newPassword,confirmNewPassword:N.confirmNewPassword})}let s=await fetch(`/api/users/${r.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update profile")}await _(),k("Profile updated successfully"),f(!1),b(!1),y({currentPassword:"",newPassword:"",confirmNewPassword:""})}catch(e){console.error("Error updating profile:",e),p(e.message||"An error occurred while updating profile")}finally{P(!1)}};return"loading"===s||m?(0,t.jsx)(d.A,{children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("div",{className:"text-xl",children:"Loading..."})]})}):(0,t.jsx)(d.A,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-semibold mb-6 flex items-center",children:[(0,t.jsx)(c.x$1,{className:"mr-2 text-blue-500"})," User Profile"]}),u&&(0,t.jsx)("div",{className:"bg-red-100 text-red-700 p-4 rounded-md mb-6",children:u}),C&&(0,t.jsx)("div",{className:"bg-green-100 text-green-700 p-4 rounded-md mb-6",children:C}),r?(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("div",{className:"relative mb-4",children:h?(0,t.jsxs)("div",{className:"w-32 h-32 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center relative",children:[j.profileImage?(0,t.jsx)("img",{src:j.profileImage,alt:r.name,className:"w-full h-full object-cover"}):(0,t.jsx)(c.x$1,{className:"text-gray-400 text-5xl"}),(0,t.jsxs)("label",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer",children:[(0,t.jsx)("span",{className:"text-white text-sm",children:"Change Photo"}),(0,t.jsx)("input",{type:"file",accept:"image/*",className:"hidden",onChange:e=>{let s=e.target.files?.[0];if(s){let e=new FileReader;e.onloadend=()=>{w(s=>({...s,profileImage:e.result}))},e.readAsDataURL(s)}}})]})]}):(0,t.jsx)("div",{className:"w-32 h-32 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center",children:r.profileImage?(0,t.jsx)("img",{src:r.profileImage,alt:r.name,className:"w-full h-full object-cover"}):(0,t.jsx)(c.x$1,{className:"text-gray-400 text-5xl"})})}),(0,t.jsx)("h2",{className:"text-xl font-semibold",children:r.name}),(0,t.jsx)("p",{className:"text-gray-500 mb-2",children:r.email}),(0,t.jsx)("div",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mb-4",children:r.rank.name}),(0,t.jsx)("div",{className:"w-full mt-4 space-y-2",children:h?(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)("button",{onClick:()=>{r&&w({name:r.name,phone:r.phone||"",profileImage:r.profileImage||""}),y({currentPassword:"",newPassword:"",confirmNewPassword:""}),f(!1),b(!1),p("")},className:"flex-1 flex items-center justify-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300",children:[(0,t.jsx)(c.QCr,{className:"mr-2"})," Cancel"]}),(0,t.jsxs)("button",{type:"submit",form:"profile-form",className:"flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:v,children:[v?(0,t.jsx)(c.hW,{className:"animate-spin mr-2"}):(0,t.jsx)(c.CMH,{className:"mr-2"}),"Save"]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:()=>f(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[(0,t.jsx)(c.uO9,{className:"mr-2"})," Edit Profile"]}),(0,t.jsxs)(o(),{href:"/profile/payment-methods",className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[(0,t.jsx)(c.x1c,{className:"mr-2"})," Manage Payment Methods"]})]})})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 md:col-span-2",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Profile Information"}),(0,t.jsx)("form",{id:"profile-form",onSubmit:S,children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),h?(0,t.jsx)("input",{type:"text",name:"name",value:j.name,onChange:M,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0}):(0,t.jsx)("p",{className:"text-gray-900",children:r.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,t.jsx)("p",{className:"text-gray-900",children:r.email}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Email address cannot be changed"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),h?(0,t.jsx)("input",{type:"tel",name:"phone",value:j.phone,onChange:M,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}):(0,t.jsx)("p",{className:"text-gray-900",children:r.phone||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Member Since"}),(0,t.jsx)("p",{className:"text-gray-900",children:new Date(r.createdAt).toLocaleDateString()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Upline"}),(0,t.jsx)("p",{className:"text-gray-900",children:r.upline?`${r.upline.name} (${r.upline.email})`:"None"})]}),h&&(0,t.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-md font-medium",children:"Change Password"}),(0,t.jsxs)("button",{type:"button",onClick:()=>b(!g),className:"text-blue-600 hover:text-blue-800 flex items-center",children:[(0,t.jsx)(c.pXu,{className:"mr-1"}),g?"Cancel":"Change"]})]}),g&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),(0,t.jsx)("input",{type:"password",name:"currentPassword",value:N.currentPassword,onChange:E,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),(0,t.jsx)("input",{type:"password",name:"newPassword",value:N.newPassword,onChange:E,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0,minLength:8})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),(0,t.jsx)("input",{type:"password",name:"confirmNewPassword",value:N.confirmNewPassword,onChange:E,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]})]})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 md:col-span-3",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Account Statistics"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Wallet Balance"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:["₱",r.walletBalance.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Total Purchases"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:["₱",r.stats.totalPurchases.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"bg-purple-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Total Rebates Received"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:["₱",r.stats.totalRebatesReceived.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Total Rebates Generated"}),(0,t.jsxs)("p",{className:"text-xl font-semibold",children:["₱",r.stats.totalRebatesGenerated.toFixed(2)]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-4",children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Direct Downline"}),(0,t.jsx)("p",{className:"text-xl font-semibold",children:r.stats.directDownlineCount})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Total Downline"}),(0,t.jsx)("p",{className:"text-xl font-semibold",children:r._count.downline})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Total Purchases"}),(0,t.jsx)("p",{className:"text-xl font-semibold",children:r._count.purchases})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Rebate Transactions"}),(0,t.jsx)("p",{className:"text-xl font-semibold",children:r._count.rebatesReceived})]})]})]})]}):(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6 text-center",children:(0,t.jsx)("p",{className:"text-gray-500",children:"Profile not found."})})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>r(14248));module.exports=t})();
(()=>{var e={};e.id=2614,e.ids=[2614],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17046:(e,r,t)=>{Promise.resolve().then(t.bind(t,83952))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},53998:(e,r,t)=>{Promise.resolve().then(t.bind(t,62542))},62542:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\rebate-configs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebate-configs\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70386:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=t(65239),n=t(48088),a=t(88170),i=t.n(a),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let o={children:["",{children:["admin",{children:["rebate-configs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,62542)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebate-configs\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\rebate-configs\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/rebate-configs/page",pathname:"/admin/rebate-configs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},83952:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(60687),n=t(43210),a=t(82136),i=t(16189),l=t(68367),d=t(23877);function o(){let{data:e,status:r}=(0,a.useSession)();(0,i.useRouter)();let[t,o]=(0,n.useState)(!0),[c,m]=(0,n.useState)([]),[u,x]=(0,n.useState)([]),[p,g]=(0,n.useState)(!1),[b,f]=(0,n.useState)(null),[h,y]=(0,n.useState)(null),[j,v]=(0,n.useState)(!1),[N,w]=(0,n.useState)(null),[C,k]=(0,n.useState)(null),P=async()=>{try{let e=await fetch("/api/admin/rebate-configs");if(!e.ok)throw Error("Failed to fetch rebate configurations");let r=await e.json();m(r.rebateConfigs)}catch(e){console.error("Error fetching rebate configurations:",e),w("Failed to load rebate configurations")}finally{o(!1)}},A=e=>{f({...e}),y(null)},T=()=>{f(null),y(null)},E=(e,r)=>{let{name:t,value:s,type:n}=e.target,a="number"===n?parseFloat(s):s;"editing"===r&&b?f({...b,[t]:a}):"new"===r&&h&&y({...h,[t]:a})},F=(e,r)=>{"editing"===r&&b?f({...b,rewardType:e}):"new"===r&&h&&y({...h,rewardType:e})},S=async e=>{v(!0),w(null),k(null);try{let r="editing"===e?b:h;if(!r)throw Error("No configuration data to save");let t="editing"===e?`/api/admin/rebate-configs/${r.id}`:"/api/admin/rebate-configs",s=await fetch(t,{method:"editing"===e?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to save configuration")}await P(),f(null),y(null),k("editing"===e?"Configuration updated successfully":"Configuration created successfully")}catch(e){console.error("Error saving configuration:",e),w(e instanceof Error?e.message:"Failed to save configuration")}finally{v(!1)}},L=async e=>{if(confirm("Are you sure you want to delete this configuration?")){w(null),k(null);try{let r=await fetch(`/api/admin/rebate-configs/${e}`,{method:"DELETE"});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to delete configuration")}await P(),k("Configuration deleted successfully")}catch(e){console.error("Error deleting configuration:",e),w(e instanceof Error?e.message:"Failed to delete configuration")}}},_=e=>{let r=u.find(r=>r.id===e);return r?r.name:`Product ${e}`};return"loading"===r||t?(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,s.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,s.jsx)("div",{className:"text-xl",children:"Loading..."})]})}):p?(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,s.jsx)(d.Pcn,{className:"mr-2 text-blue-500"})," Rebate Configurations"]}),(0,s.jsxs)("button",{onClick:()=>{y({productId:u[0]?.id||0,level:1,rewardType:"percentage",percentage:5,fixedAmount:0}),f(null)},className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center",disabled:!!h||!!b,children:[(0,s.jsx)(d.OiG,{className:"mr-2"})," Add Configuration"]})]}),N&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center",children:[(0,s.jsx)(d.QCr,{className:"mr-2"})," ",N]}),C&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center",children:[(0,s.jsx)(FaCheck,{className:"mr-2"})," ",C]}),h&&(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Add New Configuration"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product"}),(0,s.jsx)("select",{name:"productId",value:h.productId,onChange:e=>E(e,"new"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:u.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Level"}),(0,s.jsx)("input",{type:"number",name:"level",value:h.level,onChange:e=>E(e,"new"),min:"1",max:"10",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reward Type"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>F("percentage","new"),className:`px-4 py-2 rounded-md flex items-center ${"percentage"===h.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"}`,children:[(0,s.jsx)(d.gdQ,{className:"mr-2"})," Percentage"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>F("fixed","new"),className:`px-4 py-2 rounded-md flex items-center ${"fixed"===h.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"}`,children:[(0,s.jsx)(d.Tsk,{className:"mr-2"})," Fixed Amount"]})]})]}),"percentage"===h.rewardType?(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Percentage"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"number",name:"percentage",value:h.percentage,onChange:e=>E(e,"new"),min:"0",max:"100",step:"0.1",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pr-10"}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.gdQ,{className:"text-gray-400"})})]})]}):(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fixed Amount"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-500",children:"₱"})}),(0,s.jsx)("input",{type:"number",name:"fixedAmount",value:h.fixedAmount,onChange:e=>E(e,"new"),min:"0",step:"0.01",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)("button",{onClick:T,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Cancel"}),(0,s.jsx)("button",{onClick:()=>S("new"),disabled:j,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.hW,{className:"animate-spin mr-2"})," Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.dIn,{className:"mr-2"})," Save"]})})]})]}),b&&(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Edit Configuration"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product"}),(0,s.jsx)("select",{name:"productId",value:b.productId,onChange:e=>E(e,"editing"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:u.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Level"}),(0,s.jsx)("input",{type:"number",name:"level",value:b.level,onChange:e=>E(e,"editing"),min:"1",max:"10",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reward Type"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>F("percentage","editing"),className:`px-4 py-2 rounded-md flex items-center ${"percentage"===b.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"}`,children:[(0,s.jsx)(d.gdQ,{className:"mr-2"})," Percentage"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>F("fixed","editing"),className:`px-4 py-2 rounded-md flex items-center ${"fixed"===b.rewardType?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 border border-gray-300"}`,children:[(0,s.jsx)(d.Tsk,{className:"mr-2"})," Fixed Amount"]})]})]}),"percentage"===b.rewardType?(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Percentage"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"number",name:"percentage",value:b.percentage,onChange:e=>E(e,"editing"),min:"0",max:"100",step:"0.1",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 pr-10"}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.gdQ,{className:"text-gray-400"})})]})]}):(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fixed Amount"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-500",children:"₱"})}),(0,s.jsx)("input",{type:"number",name:"fixedAmount",value:b.fixedAmount,onChange:e=>E(e,"editing"),min:"0",step:"0.01",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)("button",{onClick:T,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Cancel"}),(0,s.jsx)("button",{onClick:()=>S("editing"),disabled:j,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.hW,{className:"animate-spin mr-2"})," Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.dIn,{className:"mr-2"})," Save"]})})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b",children:(0,s.jsx)("h2",{className:"text-lg font-semibold",children:"All Configurations"})}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reward Type"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Value"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.length>0?c.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.product?.name||_(e.productId)})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Level ",e.level]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${"percentage"===e.rewardType?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:["percentage"===e.rewardType?(0,s.jsx)(d.gdQ,{className:"mr-1"}):(0,s.jsx)(d.Tsk,{className:"mr-1"}),e.rewardType.charAt(0).toUpperCase()+e.rewardType.slice(1)]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:"percentage"===e.rewardType?`${e.percentage}%`:`₱${e.fixedAmount.toFixed(2)}`})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,s.jsx)("button",{onClick:()=>A(e),className:"text-blue-600 hover:text-blue-900 mr-3",disabled:!!b||!!h,children:(0,s.jsx)(d.uO9,{})}),(0,s.jsx)("button",{onClick:()=>L(e.id),className:"text-red-600 hover:text-red-900",disabled:!!b||!!h,children:(0,s.jsx)(d.qbC,{})})]})]},e.id)):(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:5,className:"px-6 py-4 text-center text-gray-500",children:"No rebate configurations found"})})})]})})]})]})}):(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,8414,9567,3877,474,4859,3024],()=>t(70386));module.exports=s})();
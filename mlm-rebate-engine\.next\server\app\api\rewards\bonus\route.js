"use strict";(()=>{var e={};e.id=3470,e.ids=[3470],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},62434:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>m,routeModule:()=>w,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>q});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>l});var o=s(96559),n=s(48088),i=s(37719),u=s(31183),a=s(32190),p=s(19854),d=s(12909),x=s(73967);async function l(e){try{let r=await (0,p.getServerSession)(d.Nh);if(!r||!r.user)return a.NextResponse.json({error:"You must be logged in to process bonus rewards"},{status:401});let s=r.user.email;if(!s)return a.NextResponse.json({error:"User email not found in session"},{status:400});let t=await u.z.user.findUnique({where:{email:s},select:{id:!0,rankId:!0}});if(!t)return a.NextResponse.json({error:"User not found"},{status:404});if(6!==t.rankId)return a.NextResponse.json({error:"You do not have permission to process bonus rewards"},{status:403});let{userId:o,triggerType:n,triggerData:i}=await e.json();if(!o||!n)return a.NextResponse.json({error:"Missing required fields: userId and triggerType"},{status:400});let l=await (0,x.e)(o,n,i||{});return a.NextResponse.json(l)}catch(e){return console.error("Error processing bonus reward:",e),a.NextResponse.json({error:"Failed to process bonus reward"},{status:500})}}async function c(e){try{let r=await (0,p.getServerSession)(d.Nh);if(!r||!r.user)return a.NextResponse.json({error:"You must be logged in to view bonus rewards"},{status:401});let s=r.user.email;if(!s)return a.NextResponse.json({error:"User email not found in session"},{status:400});let t=await u.z.user.findUnique({where:{email:s},select:{id:!0,rankId:!0}});if(!t)return a.NextResponse.json({error:"User not found"},{status:404});if(6!==t.rankId)return a.NextResponse.json({error:"You do not have permission to view bonus rewards"},{status:403});let o=new URL(e.url).searchParams.get("triggerType"),n={};o&&(n.triggerType=o);let i=await u.z.bonusReward.findMany({where:n,orderBy:{createdAt:"desc"}});return a.NextResponse.json({bonusRewards:i})}catch(e){return console.error("Error fetching bonus rewards:",e),a.NextResponse.json({error:"Failed to fetch bonus rewards"},{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/rewards/bonus/route",pathname:"/api/rewards/bonus",filename:"route",bundlePath:"app/api/rewards/bonus/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\rewards\\bonus\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:f,workUnitAsyncStorage:q,serverHooks:g}=w;function m(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:q})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,8044,3112,4079,4935],()=>s(62434));module.exports=t})();
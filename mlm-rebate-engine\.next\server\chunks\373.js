exports.id=373,exports.ids=[373],exports.modules={12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>c,Nh:()=>d,aP:()=>u});var a=r(96330),i=r(13581),o=r(85663),n=r(55511),s=r.n(n);async function c(e){return await o.Ay.hash(e,10)}function u(){let e=s().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new a.PrismaClient;let d={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,i.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new a.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let i=await o.Ay.compare(e.password,r.password);if(console.log("Password valid:",i),!i)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:n,...s}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var a=r(96330);let i=global.prisma||new a.PrismaClient({log:["query"]})},78335:()=>{},95613:(e,t,r)=>{"use strict";r.d(t,{AG:()=>v,DD:()=>u,EC:()=>m,Kc:()=>y,MB:()=>w,UH:()=>g,WY:()=>s,_T:()=>d,d$:()=>i,p_:()=>l,qn:()=>p,vc:()=>c,y9:()=>o});var a=r(31183);async function i(e={}){let t={};if(e.search&&(t.OR=[{name:{contains:e.search,mode:"insensitive"}},{sku:{contains:e.search,mode:"insensitive"}},{description:{contains:e.search,mode:"insensitive"}}]),e.tags&&e.tags.length>0){let r=e.tags.map(e=>({tags:{contains:e,mode:"insensitive"}}));t.OR=[...t.OR||[],...r]}void 0!==e.isActive&&(t.isActive=e.isActive),void 0!==e.minPrice&&(t.price={...t.price,gte:e.minPrice}),void 0!==e.maxPrice&&(t.price={...t.price,lte:e.maxPrice}),void 0!==e.minPv&&(t.pv={...t.pv,gte:e.minPv}),void 0!==e.maxPv&&(t.pv={...t.pv,lte:e.maxPv}),void 0!==e.minInventory&&(t.inventory={...t.inventory,gte:e.minInventory}),void 0!==e.maxInventory&&(t.inventory={...t.inventory,lte:e.maxInventory});let r={};e.sortBy?r[e.sortBy]=e.sortOrder||"asc":r.createdAt="desc";let o=e.page||1,n=e.pageSize||10,s=(o-1)*n,c=await a.z.product.count({where:t}),u=await a.z.product.findMany({where:t,orderBy:r,skip:s,take:n});return{products:u,pagination:{page:o,pageSize:n,totalCount:c,totalPages:Math.ceil(c/n),hasMore:s+u.length<c}}}async function o(e){return await a.z.product.findUnique({where:{id:e}})}async function n(e){return await a.z.product.findUnique({where:{sku:e}})}async function s(e){if(await n(e.sku))throw Error(`Product with SKU ${e.sku} already exists`);return await a.z.$transaction(async t=>{let r=await t.product.create({data:{name:e.name,sku:e.sku,description:e.description,price:e.price,pv:e.pv,binaryValue:e.binaryValue||0,inventory:e.inventory||0,tags:e.tags,image:e.image,isActive:void 0===e.isActive||e.isActive,referralCommissionType:e.referralCommissionType,referralCommissionValue:e.referralCommissionValue,lastUpdatedBy:e.userId,lastUpdatedByName:e.userName}});return await t.productAudit.create({data:{productId:r.id,userId:e.userId,userName:e.userName,action:"create",details:JSON.stringify({name:r.name,sku:r.sku,price:r.price,pv:r.pv})}}),r})}async function c(e,t){let r=await o(e);if(!r)throw Error(`Product with ID ${e} not found`);if(t.sku&&t.sku!==r.sku){let r=await n(t.sku);if(r&&r.id!==e)throw Error(`Product with SKU ${t.sku} already exists`)}return await a.z.$transaction(async a=>{let i=await a.product.update({where:{id:e},data:{name:t.name,sku:t.sku,description:t.description,price:t.price,pv:t.pv,binaryValue:t.binaryValue,inventory:t.inventory,tags:t.tags,image:t.image,isActive:t.isActive,referralCommissionType:t.referralCommissionType,referralCommissionValue:t.referralCommissionValue,lastUpdatedBy:t.userId,lastUpdatedByName:t.userName,updatedAt:new Date}}),o={};return t.name&&t.name!==r.name&&(o.name={from:r.name,to:t.name}),t.sku&&t.sku!==r.sku&&(o.sku={from:r.sku,to:t.sku}),t.price&&t.price!==r.price&&(o.price={from:r.price,to:t.price}),t.pv&&t.pv!==r.pv&&(o.pv={from:r.pv,to:t.pv}),void 0!==t.binaryValue&&t.binaryValue!==r.binaryValue&&(o.binaryValue={from:r.binaryValue,to:t.binaryValue}),void 0!==t.inventory&&t.inventory!==r.inventory&&(o.inventory={from:r.inventory,to:t.inventory}),void 0!==t.isActive&&t.isActive!==r.isActive&&(o.isActive={from:r.isActive,to:t.isActive}),await a.productAudit.create({data:{productId:i.id,userId:t.userId,userName:t.userName,action:"update",details:JSON.stringify(o)}}),i})}async function u(e,t,r){let i=await o(e);if(!i)throw Error(`Product with ID ${e} not found`);let n=await a.z.purchase.count({where:{productId:e}});if(n>0)throw Error(`Cannot delete product with ID ${e} because it has ${n} purchases`);return await a.z.$transaction(async a=>(await a.productAudit.create({data:{productId:e,userId:t,userName:r,action:"delete",details:JSON.stringify({name:i.name,sku:i.sku})}}),await a.product.delete({where:{id:e}})))}async function d(e,t,r,i){let n=await o(e);if(!n)throw Error(`Product with ID ${e} not found`);return await a.z.$transaction(async a=>{let o=await a.product.update({where:{id:e},data:{isActive:t,lastUpdatedBy:r,lastUpdatedByName:i,updatedAt:new Date}});return await a.productAudit.create({data:{productId:o.id,userId:r,userName:i,action:t?"activate":"deactivate",details:JSON.stringify({name:o.name,sku:o.sku,isActive:{from:n.isActive,to:t}})}}),o})}async function l(e,t,r,i){let s=await o(e);if(!s)throw Error(`Product with ID ${e} not found`);if(await n(t))throw Error(`Product with SKU ${t} already exists`);return await a.z.$transaction(async e=>{let a=await e.product.create({data:{name:`${s.name} (Copy)`,sku:t,description:s.description,price:s.price,pv:s.pv,binaryValue:s.binaryValue,inventory:0,tags:s.tags,image:s.image,isActive:!1,referralCommissionType:s.referralCommissionType,referralCommissionValue:s.referralCommissionValue,lastUpdatedBy:r,lastUpdatedByName:i}});return await e.productAudit.create({data:{productId:a.id,userId:r,userName:i,action:"clone",details:JSON.stringify({sourceProductId:s.id,sourceProductSku:s.sku,newSku:a.sku})}}),a})}async function p(e,t=10,r=0){let i=await a.z.productAudit.count({where:{productId:e}});return{logs:await a.z.productAudit.findMany({where:{productId:e},orderBy:{createdAt:"desc"},take:t,skip:r,include:{user:{select:{id:!0,name:!0,email:!0}}}}),total:i}}async function m(e,t=12){let r=new Date,i=r.getFullYear(),o=r.getMonth()+1-t,n=i,s=o;return s<=0&&(n-=Math.floor(Math.abs(s)/12)+1,s=12-Math.abs(s)%12),await a.z.productSalesHistory.findMany({where:{productId:e,OR:[{year:{gt:n}},{year:n,month:{gte:s}}]},orderBy:[{year:"asc"},{month:"asc"}]})}async function w(e,t,r){let a={totalProcessed:e.length,successful:0,failed:0,errors:[],createdProducts:[]};for(let i=0;i<e.length;i++){let o=e[i];try{o.userId=t,o.userName=r;let e=await s(o);a.successful++,a.createdProducts.push(e)}catch(e){a.failed++,a.errors.push({row:i+1,sku:o.sku,error:e instanceof Error?e.message:"Unknown error"})}}return a}async function y(e,t,r){let a=0;for(let i of e)try{let e={...i.data,userId:t,userName:r};await c(i.id,e),a++}catch(e){console.error(`Error updating product ${i.id}:`,e)}return a}async function f(e,t,r){let i=await o(e);if(!i)throw Error(`Product with ID ${e} not found`);let n=await a.z.rebateConfig.findFirst({where:{productId:e,level:r}});if(!n){let e=1===r?.1:2===r?.075:.05*(r<=6);return i.pv*t*e}return"percentage"===n.type?i.pv*t*(n.percentage/100):n.fixedAmount*t}async function v(e,t,r=6){let a=[];for(let i=1;i<=r;i++){let r=await f(e,t,i);a.push({level:i,rebate:r})}return a}async function g(){return[...new Set((await a.z.product.findMany({where:{tags:{not:null}},select:{tags:!0}})).map(e=>e.tags?.split(",").map(e=>e.trim())||[]).flat())].filter(e=>""!==e)}},96487:()=>{}};
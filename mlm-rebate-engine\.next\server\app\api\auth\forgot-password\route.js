(()=>{var e={};e.id=7556,e.ids=[7556],e.modules={2041:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var o={};s.r(o),s.d(o,{POST:()=>d});var t=s(96559),a=s(48088),i=s(37719),n=s(32190),l=s(31183),u=s(12909);async function c(e,r){let s=`${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${r}`;console.log(`
    To: ${e}
    Subject: Reset Your Password - Extreme Life Herbal Products
    
    Hello,
    
    You requested to reset your password for your Extreme Life Herbal Products account.
    
    Please click the link below to reset your password:
    ${s}
    
    This link will expire in 1 hour.
    
    If you did not request a password reset, please ignore this email.
    
    Best regards,
    Extreme Life Herbal Products Team
  `)}async function d(e){try{let{email:r}=await e.json();if(!r)return n.NextResponse.json({error:"Email is required"},{status:400});let s=await l.z.user.findUnique({where:{email:r}});if(!s)return n.NextResponse.json({message:"If your email is registered, you will receive a password reset link"},{status:200});let{token:o,expiresAt:t}=(0,u.aP)();return await l.z.passwordReset.upsert({where:{userId:s.id},update:{token:o,expiresAt:t},create:{userId:s.id,token:o,expiresAt:t}}),await c(s.email,o),n.NextResponse.json({message:"If your email is registered, you will receive a password reset link"},{status:200})}catch(e){return console.error("Error in forgot-password API:",e),n.NextResponse.json({error:"An error occurred while processing your request"},{status:500})}}let p=new t.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/forgot-password/route",pathname:"/api/auth/forgot-password",filename:"route",bundlePath:"app/api/auth/forgot-password/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\auth\\forgot-password\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:m}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,s)=>{"use strict";s.d(r,{Er:()=>l,Nh:()=>c,aP:()=>u});var o=s(96330),t=s(13581),a=s(85663),i=s(55511),n=s.n(i);async function l(e){return await a.Ay.hash(e,10)}function u(){let e=n().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new o.PrismaClient;let c={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,t.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new o.PrismaClient,s=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!s)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",s.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let t=await a.Ay.compare(e.password,s.password);if(console.log("Password valid:",t),!t)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",s.id);let{password:i,...n}=s;return{id:s.id.toString(),email:s.email,name:s.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},13581:(e,r)=>{"use strict";r.A=function(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>t});var o=s(96330);let t=global.prisma||new o.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),o=r.X(0,[4243,580,8044],()=>s(2041));module.exports=o})();
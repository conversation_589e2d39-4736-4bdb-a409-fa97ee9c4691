(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2489],{21495:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(95155),a=t(12115),n=t(12108),l=t(87747),i=t(35695),c=t(29911),d=t(6874),o=t.n(d);function m(e){var s,t,n,l,d,o,m,x;let{userId1:u,userId2:h,timeRange:p="last30days"}=e,[f,j]=(0,a.useState)(null),[g,b]=(0,a.useState)(!0),[y,N]=(0,a.useState)(null),[v,w]=(0,a.useState)(p),S=(0,i.useRouter)();(0,a.useEffect)(()=>{(async()=>{b(!0),N(null);try{let e=new URLSearchParams({userId1:u.toString(),userId2:h.toString(),timeRange:v}),s=await fetch("/api/genealogy/compare?".concat(e.toString()));if(!s.ok)throw Error("Failed to fetch comparison data");let t=await s.json();j(t)}catch(e){N(e instanceof Error?e.message:"An unknown error occurred")}finally{b(!1)}})()},[u,h,v]);let C=e=>{w(e)},M=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),P=e=>new Intl.NumberFormat().format(e),k=e=>"".concat(e>0?"+":"").concat(e.toFixed(2),"%"),O=e=>e>0?(0,r.jsx)(c.uCC,{className:"text-green-500"}):e<0?(0,r.jsx)(c.$TP,{className:"text-red-500"}):(0,r.jsx)(c.VsL,{className:"text-gray-500"}),E=e=>{S.push("/users/".concat(e))},D=e=>{S.push("/genealogy?userId=".concat(e))};return g&&!f?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:[(0,r.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading comparison data..."})]}):y?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-md",children:[(0,r.jsxs)("h3",{className:"text-red-800 font-medium flex items-center",children:[(0,r.jsx)(c.BS8,{className:"mr-2"}),"Error loading comparison data"]}),(0,r.jsx)("p",{className:"text-red-600",children:y})]})}):f?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,r.jsx)(c.yk7,{className:"mr-2 text-blue-500"}),"Genealogy Comparison"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.bfZ,{className:"text-gray-500 mr-2"}),(0,r.jsxs)("select",{value:v,onChange:e=>C(e.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"last90days",children:"Last 90 Days"}),(0,r.jsx)("option",{value:"last6months",children:"Last 6 Months"}),(0,r.jsx)("option",{value:"last12months",children:"Last 12 Months"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-lg",children:f.user1.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:f.user1.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",f.user1.id]})]}),(0,r.jsx)("div",{className:"text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded-full",children:f.user1.rankName})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Downline"}),(0,r.jsx)("div",{className:"font-medium",children:P(f.user1.downlineCount)})]}),f.user1.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,r.jsx)("div",{className:"font-medium",children:M(f.user1.performanceMetrics.personalSales)})]}),f.user1.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,r.jsx)("div",{className:"font-medium",children:M(f.user1.performanceMetrics.teamSales)})]}),f.user1.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Rebates Earned"}),(0,r.jsx)("div",{className:"font-medium",children:M(f.user1.performanceMetrics.rebatesEarned)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>E(f.user1.id),className:"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700",children:"View Profile"}),(0,r.jsx)("button",{onClick:()=>D(f.user1.id),className:"px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700",children:"View Genealogy"})]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-lg",children:f.user2.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:f.user2.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",f.user2.id]})]}),(0,r.jsx)("div",{className:"text-sm px-2 py-1 bg-purple-100 text-purple-800 rounded-full",children:f.user2.rankName})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Downline"}),(0,r.jsx)("div",{className:"font-medium",children:P(f.user2.downlineCount)})]}),f.user2.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Personal Sales"}),(0,r.jsx)("div",{className:"font-medium",children:M(f.user2.performanceMetrics.personalSales)})]}),f.user2.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Team Sales"}),(0,r.jsx)("div",{className:"font-medium",children:M(f.user2.performanceMetrics.teamSales)})]}),f.user2.performanceMetrics&&(0,r.jsxs)("div",{className:"bg-white p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Rebates Earned"}),(0,r.jsx)("div",{className:"font-medium",children:M(f.user2.performanceMetrics.rebatesEarned)})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>E(f.user2.id),className:"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700",children:"View Profile"}),(0,r.jsx)("button",{onClick:()=>D(f.user2.id),className:"px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700",children:"View Genealogy"})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"font-medium mb-3 flex items-center",children:[(0,r.jsx)(c.YYR,{className:"text-blue-500 mr-2"}),"Performance Comparison"]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metric"}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:f.user1.name}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:f.user2.name}),(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Difference"})]})}),(0,r.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(FaUsers,{className:"text-blue-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Downline Count"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:P(f.user1.downlineCount)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:P(f.user2.downlineCount)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[O(f.differences.downlineCount),(0,r.jsxs)("span",{className:"ml-2 text-sm ".concat(f.differences.downlineCount>0?"text-green-600":f.differences.downlineCount<0?"text-red-600":"text-gray-500"),children:[P(Math.abs(f.differences.downlineCount))," (",k(f.differences.downlineCountPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.AsH,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Personal Sales"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:M((null==(s=f.user1.performanceMetrics)?void 0:s.personalSales)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:M((null==(t=f.user2.performanceMetrics)?void 0:t.personalSales)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[O(f.differences.personalSales),(0,r.jsxs)("span",{className:"ml-2 text-sm ".concat(f.differences.personalSales>0?"text-green-600":f.differences.personalSales<0?"text-red-600":"text-gray-500"),children:[M(Math.abs(f.differences.personalSales))," (",k(f.differences.personalSalesPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.AsH,{className:"text-blue-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Team Sales"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:M((null==(n=f.user1.performanceMetrics)?void 0:n.teamSales)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:M((null==(l=f.user2.performanceMetrics)?void 0:l.teamSales)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[O(f.differences.teamSales),(0,r.jsxs)("span",{className:"ml-2 text-sm ".concat(f.differences.teamSales>0?"text-green-600":f.differences.teamSales<0?"text-red-600":"text-gray-500"),children:[M(Math.abs(f.differences.teamSales))," (",k(f.differences.teamSalesPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.lcY,{className:"text-purple-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Rebates Earned"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:M((null==(d=f.user1.performanceMetrics)?void 0:d.rebatesEarned)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:M((null==(o=f.user2.performanceMetrics)?void 0:o.rebatesEarned)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[O(f.differences.rebatesEarned),(0,r.jsxs)("span",{className:"ml-2 text-sm ".concat(f.differences.rebatesEarned>0?"text-green-600":f.differences.rebatesEarned<0?"text-red-600":"text-gray-500"),children:[M(Math.abs(f.differences.rebatesEarned))," (",k(f.differences.rebatesEarnedPercentage),")"]})]})})]}),(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.NPy,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"New Members"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:P((null==(m=f.user1.performanceMetrics)?void 0:m.newTeamMembers)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:P((null==(x=f.user2.performanceMetrics)?void 0:x.newTeamMembers)||0)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[O(f.differences.newMembers),(0,r.jsxs)("span",{className:"ml-2 text-sm ".concat(f.differences.newMembers>0?"text-green-600":f.differences.newMembers<0?"text-red-600":"text-gray-500"),children:[P(Math.abs(f.differences.newMembers))," (",k(f.differences.newMembersPercentage),")"]})]})})]})]})]})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h3",{className:"font-medium mb-3 flex items-center",children:[(0,r.jsx)(FaUsers,{className:"text-blue-500 mr-2"}),"Network Overlap"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-md text-center",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-1",children:["Unique to ",f.user1.name]}),(0,r.jsx)("div",{className:"text-xl font-semibold text-blue-700",children:P(f.differences.uniqueMembers1)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"members"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-3 rounded-md text-center",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Common Members"}),(0,r.jsx)("div",{className:"text-xl font-semibold text-purple-700",children:P(f.differences.commonMembers)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"members"})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-md text-center",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-1",children:["Unique to ",f.user2.name]}),(0,r.jsx)("div",{className:"text-xl font-semibold text-green-700",children:P(f.differences.uniqueMembers2)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"members"})]})]})]})]}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.yk7,{className:"text-gray-400 text-4xl mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Comparison Data"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Unable to load comparison data for the selected users."})]})})}function x(){let{data:e,status:s}=(0,n.useSession)(),t=(0,i.useSearchParams)(),d=t.get("userId1"),x=t.get("userId2"),u=t.get("timeRange")||"last30days",[h,p]=(0,a.useState)(d?parseInt(d):void 0),[f,j]=(0,a.useState)(x?parseInt(x):void 0),[g,b]=(0,a.useState)(u||"last30days"),[y,N]=(0,a.useState)(""),[v,w]=(0,a.useState)(""),[S,C]=(0,a.useState)([]),[M,P]=(0,a.useState)([]),[k,O]=(0,a.useState)(!1),[E,D]=(0,a.useState)(!1),{data:I,isLoading:R}=(0,l.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let t=await fetch("/api/users/me");if(!t.ok)throw Error("Failed to fetch user data");return await t.json()},enabled:"authenticated"===s});!I||h||d||p(I.id);let U=async()=>{if(y.trim()){O(!0);try{let e=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:y,page:1,pageSize:5})});if(!e.ok)throw Error("Failed to search users");let s=await e.json();C(s.users)}catch(e){console.error("Error searching users:",e)}finally{O(!1)}}},T=async()=>{if(v.trim()){D(!0);try{let e=await fetch("/api/genealogy/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:v,page:1,pageSize:5})});if(!e.ok)throw Error("Failed to search users");let s=await e.json();P(s.users)}catch(e){console.error("Error searching users:",e)}finally{D(!1)}}},F=e=>{p(e.id),C([]),N("")},L=e=>{j(e.id),P([]),w("")},q=e=>{b(e)};return"loading"===s||R?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,r.jsx)(c.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,r.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,r.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,r.jsx)(c.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to compare genealogy users."}),(0,r.jsx)(o(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Genealogy User Comparison"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Compare two users to analyze differences and similarities"})]}),(0,r.jsxs)(o(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,r.jsx)(c.QVr,{className:"mr-1"}),"Back to Genealogy"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,r.jsx)(c.NPy,{className:"mr-2 text-blue-500"}),"Select Users to Compare"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"User 1"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID...",value:y,onChange:e=>N(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("button",{onClick:U,disabled:k,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:k?(0,r.jsx)(c.hW,{className:"animate-spin"}):(0,r.jsx)(c.KSO,{})})]}),S.length>0&&(0,r.jsx)("div",{className:"mt-2 border rounded-md divide-y max-h-60 overflow-y-auto",children:S.map(e=>(0,r.jsxs)("div",{className:"p-2 hover:bg-gray-50 cursor-pointer",onClick:()=>F(e),children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]},e.id))}),h&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-blue-50 rounded-md",children:[(0,r.jsx)("div",{className:"font-medium",children:"Selected User 1"}),(0,r.jsxs)("div",{className:"text-sm",children:["ID: ",h]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"User 2"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID...",value:v,onChange:e=>w(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("button",{onClick:T,disabled:E,className:"px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed",children:E?(0,r.jsx)(c.hW,{className:"animate-spin"}):(0,r.jsx)(c.KSO,{})})]}),M.length>0&&(0,r.jsx)("div",{className:"mt-2 border rounded-md divide-y max-h-60 overflow-y-auto",children:M.map(e=>(0,r.jsxs)("div",{className:"p-2 hover:bg-gray-50 cursor-pointer",onClick:()=>L(e),children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.id]})]},e.id))}),f&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-purple-50 rounded-md",children:[(0,r.jsx)("div",{className:"font-medium",children:"Selected User 2"}),(0,r.jsxs)("div",{className:"text-sm",children:["ID: ",f]})]})]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time Range"}),(0,r.jsxs)("select",{value:g,onChange:e=>q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"last90days",children:"Last 90 Days"}),(0,r.jsx)("option",{value:"last6months",children:"Last 6 Months"}),(0,r.jsx)("option",{value:"last12months",children:"Last 12 Months"})]})]})]}),h&&f?(0,r.jsx)(m,{userId1:h,userId2:f,timeRange:g}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.yk7,{className:"text-gray-400 text-4xl mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"Select Users to Compare"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Please select two users to compare their genealogy trees."})]})}),(0,r.jsxs)("div",{className:"mt-6 bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,r.jsx)(c.__w,{className:"mr-2 text-blue-500"}),"About Genealogy User Comparison"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{children:"The Genealogy User Comparison tool allows you to compare two users to identify differences and similarities in their networks. This can be useful for analyzing performance, identifying growth opportunities, and understanding network structures."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Key Metrics Compared"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-blue-700",children:[(0,r.jsx)("li",{children:"Downline Count - Total number of members in each network"}),(0,r.jsx)("li",{children:"Personal Sales - Direct sales made by each user"}),(0,r.jsx)("li",{children:"Team Sales - Sales made by the entire downline"}),(0,r.jsx)("li",{children:"Rebates Earned - Commissions and bonuses earned"}),(0,r.jsx)("li",{children:"New Members - Recent additions to each network"})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-green-800 mb-2",children:"Network Overlap Analysis"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-green-700",children:[(0,r.jsx)("li",{children:"Unique Members - Members exclusive to each network"}),(0,r.jsx)("li",{children:"Common Members - Members present in both networks"}),(0,r.jsx)("li",{children:"Percentage Differences - Relative performance metrics"}),(0,r.jsx)("li",{children:"Growth Patterns - Differences in network expansion"})]})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"How to Use Comparison Data"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Identify Strengths and Weaknesses:"})," Compare performance metrics to identify areas where each user excels or needs improvement."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Learn from Success:"})," Analyze the structure and strategies of the more successful user to apply those lessons to the other."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Find Collaboration Opportunities:"})," Identify common members who could serve as bridges between networks for collaborative efforts."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Set Realistic Goals:"})," Use the comparison data to set achievable growth targets based on proven performance."]})]})]}),(0,r.jsx)("p",{children:"For more detailed analysis, you can adjust the time range to focus on different periods or export the comparison data for further study."})]})]})]})}},35695:(e,s,t)=>{"use strict";var r=t(18999);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},74436:(e,s,t)=>{"use strict";t.d(s,{k5:()=>o});var r=t(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=r.createContext&&r.createContext(a),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function c(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);s&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function d(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?c(Object(t),!0).forEach(function(s){var r,a,n;r=e,a=s,n=t[s],(a=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,s||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(a))in r?Object.defineProperty(r,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function o(e){return s=>r.createElement(m,i({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,t)=>r.createElement(s.tag,d({key:t},s.attr),e(s.child)))}(e.child))}function m(e){var s=s=>{var t,{attr:a,size:n,title:c}=e,o=function(e,s){if(null==e)return{};var t,r,a=function(e,s){if(null==e)return{};var t={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(s.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)t=n[r],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,l),m=n||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,a,o,{className:t,style:d(d({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),e.children)};return void 0!==n?r.createElement(n.Consumer,null,e=>s(e)):s(a)}},89603:(e,s,t)=>{Promise.resolve().then(t.bind(t,21495))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6967,7747,8441,1684,7358],()=>s(89603)),_N_E=e.O()}]);
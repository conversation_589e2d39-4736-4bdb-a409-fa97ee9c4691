"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9465],{39465:(e,s,t)=>{t.r(s),t.d(s,{default:()=>i});var l=t(95155),r=t(12115),a=t(29911);function i(e){let{userId:s,className:t=""}=e,[i,d]=(0,r.useState)(null),[c,n]=(0,r.useState)(!0),[x,o]=(0,r.useState)(null);return((0,r.useEffect)(()=>{(async()=>{n(!0),o(null);try{let e=await fetch("/api/genealogy/statistics?userId=".concat(s));if(!e.ok)throw Error("Failed to fetch statistics");let t=await e.json();d(t)}catch(e){o(e instanceof Error?e.message:"An unknown error occurred")}finally{n(!1)}})()},[s]),c)?(0,l.jsxs)("div",{className:"flex items-center justify-center p-4 ".concat(t),children:[(0,l.jsx)(a.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,l.jsx)("span",{children:"Loading statistics..."})]}):x?(0,l.jsxs)("div",{className:"text-red-500 p-4 ".concat(t),children:["Error: ",x]}):i?(0,l.jsxs)("div",{className:"p-4 ".concat(t),children:[(0,l.jsx)("h3",{className:"font-medium mb-4",children:"Network Statistics"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow border border-gray-200",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 rounded-full bg-blue-100 text-blue-600 mr-4",children:(0,l.jsx)(a.YXz,{})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Total Network Size"}),(0,l.jsx)("div",{className:"text-2xl font-bold",children:i.totalUsers})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow border border-gray-200",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 rounded-full bg-green-100 text-green-600 mr-4",children:(0,l.jsx)(a.M5n,{})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Direct Downline"}),(0,l.jsx)("div",{className:"text-2xl font-bold",children:i.directDownlineCount})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow border border-gray-200",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 rounded-full bg-purple-100 text-purple-600 mr-4",children:(0,l.jsx)(a.v$b,{})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Active Members (30d)"}),(0,l.jsx)("div",{className:"text-2xl font-bold",children:i.activeUsersLast30Days}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:[i.activeUserPercentage.toFixed(1),"% of network"]})]})]})})]}),(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow border border-gray-200",children:[(0,l.jsx)("h4",{className:"text-md font-medium mb-3",children:"Level Distribution"}),(0,l.jsx)("div",{className:"space-y-2",children:Object.entries(i.levelCounts).map(e=>{let[s,t]=e;return(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsxs)("div",{className:"w-24 text-sm",children:["Level ",s,":"]}),(0,l.jsx)("div",{className:"flex-1 h-6 bg-gray-100 rounded-full overflow-hidden",children:(0,l.jsx)("div",{className:"h-full bg-blue-500 rounded-full",style:{width:"".concat(t/i.totalUsers*100,"%")}})}),(0,l.jsx)("div",{className:"w-16 text-right text-sm",children:t})]},s)})})]})]}):(0,l.jsx)("div",{className:"text-gray-500 p-4 ".concat(t),children:"No statistics available"})}}}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3302],{19286:(e,s,t)=>{Promise.resolve().then(t.bind(t,41008))},41008:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(95155),r=t(12115),l=t(12108),i=t(35695),n=t(70357),c=t(29911),d=t(66766),x=t(6874),m=t.n(x);function o(){let{data:e,status:s}=(0,l.useSession)(),t=(0,i.useRouter)(),[x,o]=(0,r.useState)(!0),[h,p]=(0,r.useState)([]),[u,g]=(0,r.useState)({type:"",text:""}),[j,f]=(0,r.useState)("all");(0,r.useEffect)(()=>{"unauthenticated"===s&&t.push("/login")},[s,t]),(0,r.useEffect)(()=>{"authenticated"===s&&y()},[s,j]);let y=async()=>{o(!0),g({type:"",text:""});try{let e="/api/referrals/commissions";"all"!==j&&(e+="?status=".concat(j));let s=await fetch(e);if(!s.ok)throw Error("Failed to fetch referral commissions: ".concat(s.statusText));let t=await s.json();p(t.commissions||[])}catch(e){console.error("Error fetching referral commissions:",e),g({type:"error",text:"Failed to load referral commissions. Please try again."})}finally{o(!1)}},N=e=>new Date(e).toLocaleDateString(),b=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),v=e=>{switch(e){case"pending":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800",children:[(0,a.jsx)(c.w_X,{className:"mr-1"})," Pending"]});case"approved":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:[(0,a.jsx)(c.CMH,{className:"mr-1"})," Approved"]});case"paid":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:[(0,a.jsx)(c.MxO,{className:"mr-1"})," Paid"]});case"rejected":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:[(0,a.jsx)(c.QCr,{className:"mr-1"})," Rejected"]});default:return(0,a.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800",children:e})}};return"loading"===s||x?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(c.hW,{className:"animate-spin text-green-500 mr-2"}),(0,a.jsx)("span",{children:"Loading..."})]})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Referral Commissions"}),u.text&&(0,a.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("error"===u.type?"bg-red-100 text-red-700":"success"===u.type?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:u.text}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(c.MxO,{className:"text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Commissions"}),(0,a.jsx)("div",{className:"text-xl font-bold",children:b(h.reduce((e,s)=>e+s.amount,0))})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-yellow-100 p-3 rounded-full mr-4",children:(0,a.jsx)(c.w_X,{className:"text-yellow-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Pending Commissions"}),(0,a.jsx)("div",{className:"text-xl font-bold",children:b(h.filter(e=>"pending"===e.status||"approved"===e.status).reduce((e,s)=>e+s.amount,0))})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-green-100 p-3 rounded-full mr-4",children:(0,a.jsx)(c.CMH,{className:"text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Paid Commissions"}),(0,a.jsx)("div",{className:"text-xl font-bold",children:b(h.filter(e=>"paid"===e.status).reduce((e,s)=>e+s.amount,0))})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center",children:[(0,a.jsx)("div",{className:"mr-4 mb-2",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Filter by Status:"})}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>f("all"),className:"px-3 py-1 rounded-md text-sm ".concat("all"===j?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"All"}),(0,a.jsx)("button",{type:"button",onClick:()=>f("pending"),className:"px-3 py-1 rounded-md text-sm ".concat("pending"===j?"bg-yellow-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Pending"}),(0,a.jsx)("button",{type:"button",onClick:()=>f("approved"),className:"px-3 py-1 rounded-md text-sm ".concat("approved"===j?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Approved"}),(0,a.jsx)("button",{type:"button",onClick:()=>f("paid"),className:"px-3 py-1 rounded-md text-sm ".concat("paid"===j?"bg-green-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Paid"}),(0,a.jsx)("button",{type:"button",onClick:()=>f("rejected"),className:"px-3 py-1 rounded-md text-sm ".concat("rejected"===j?"bg-red-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Rejected"})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Commission History"})}),(0,a.jsx)("div",{className:"p-4",children:0===h.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{children:"No referral commissions found."}),(0,a.jsxs)("p",{className:"mt-2",children:["Start sharing products to earn commissions! Visit the ",(0,a.jsx)(m(),{href:"/referrals",className:"text-blue-600 hover:underline",children:"Referrals"})," page."]})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Buyer"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Purchase Details"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Commission"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.product.image?(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10 mr-3",children:(0,a.jsx)(d.default,{src:e.product.image,alt:e.product.name,width:40,height:40,className:"rounded-md object-cover"})}):(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md mr-3 flex items-center justify-center",children:(0,a.jsx)(c.AsH,{className:"text-gray-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.product.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:b(e.product.price)})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-8 w-8 bg-gray-200 rounded-full mr-3 flex items-center justify-center",children:(0,a.jsx)(c.x$1,{className:"text-gray-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.buyer.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.buyer.email})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.AsH,{className:"text-gray-500 mr-1"}),(0,a.jsxs)("span",{children:["Quantity: ",e.purchase.quantity]})]}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(c.MxO,{className:"text-gray-500 mr-1"}),(0,a.jsxs)("span",{children:["Total: ",b(e.purchase.totalAmount)]})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,a.jsx)("div",{className:"font-medium text-green-600",children:b(e.amount)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.percentage.toFixed(2),"% commission"]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[v(e.status),"paid"===e.status&&e.paidAt&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Paid on ",N(e.paidAt)]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.bfZ,{className:"text-gray-400 mr-1"}),N(e.createdAt)]})})]},e.id))})]})})})]}),(0,a.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6 text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Earn More Commissions!"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"Share more products with your network to increase your earnings."}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,a.jsx)(m(),{href:"/shop",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Browse Products"}),(0,a.jsx)(m(),{href:"/referrals",className:"inline-block bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:"Manage Referral Links"})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(19286)),_N_E=e.O()}]);
(()=>{var e={};e.id=1834,e.ids=[1834],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,s)=>{"use strict";s.d(r,{Er:()=>u,Nh:()=>d,aP:()=>l});var t=s(96330),o=s(13581),a=s(85663),n=s(55511),i=s.n(n);async function u(e){return await a.Ay.hash(e,10)}function l(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new t.PrismaClient;let d={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new t.PrismaClient,s=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!s)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",s.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await a.Ay.compare(e.password,s.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",s.id);let{password:n,...i}=s;return{id:s.id.toString(),email:s.email,name:s.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,s)=>{"use strict";s.d(r,{z:()=>o});var t=s(96330);let o=global.prisma||new t.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83434:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var t={};s.r(t),s.d(t,{GET:()=>m,PATCH:()=>g});var o=s(96559),a=s(48088),n=s(37719),i=s(32190),u=s(35426),l=s(12909),d=s(31183),c=s(70762);let p=c.z.object({preferredPaymentMethod:c.z.enum(["bank","gcash","paymaya"]),paymentDetails:c.z.record(c.z.string(),c.z.any())});async function m(e,{params:r}){try{let e=await (0,u.getServerSession)(l.Nh);if(!e||!e.user)return i.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let s=parseInt(r.id);if(isNaN(s))return i.NextResponse.json({error:"Invalid user ID"},{status:400});let t=await d.z.user.findUnique({where:{email:e.user.email},select:{id:!0,role:!0}});if(!t)return i.NextResponse.json({error:"User not found"},{status:404});if(t.id!==s&&"admin"!==t.role)return i.NextResponse.json({error:"You do not have permission to access this data"},{status:403});let o=await d.z.user.findUnique({where:{id:s},select:{id:!0,preferredPaymentMethod:!0,paymentDetails:!0}});if(!o)return i.NextResponse.json({error:"User not found"},{status:404});return i.NextResponse.json(o)}catch(e){return console.error("Error fetching payment details:",e),i.NextResponse.json({error:"Failed to fetch payment details"},{status:500})}}async function g(e,{params:r}){try{let s=await (0,u.getServerSession)(l.Nh);if(!s||!s.user)return i.NextResponse.json({error:"You must be logged in to access this endpoint"},{status:401});let t=parseInt(r.id);if(isNaN(t))return i.NextResponse.json({error:"Invalid user ID"},{status:400});let o=await d.z.user.findUnique({where:{email:s.user.email},select:{id:!0,role:!0}});if(!o)return i.NextResponse.json({error:"User not found"},{status:404});if(o.id!==t&&"admin"!==o.role)return i.NextResponse.json({error:"You do not have permission to update this data"},{status:403});let a=await e.json(),n=p.safeParse(a);if(!n.success)return i.NextResponse.json({error:"Invalid request data",details:n.error.format()},{status:400});let{preferredPaymentMethod:c,paymentDetails:m}=n.data;if("bank"===c){if(!m.bankName||!m.accountNumber||!m.accountName)return i.NextResponse.json({error:"Bank details are incomplete"},{status:400})}else if("gcash"===c){if(!m.gcashNumber)return i.NextResponse.json({error:"GCash number is required"},{status:400});if(!/^\d{11}$/.test(m.gcashNumber))return i.NextResponse.json({error:"Invalid GCash number format"},{status:400})}else if("paymaya"===c){if(!m.payMayaNumber)return i.NextResponse.json({error:"PayMaya number is required"},{status:400});if(!/^\d{11}$/.test(m.payMayaNumber))return i.NextResponse.json({error:"Invalid PayMaya number format"},{status:400})}let g=await d.z.user.update({where:{id:t},data:{preferredPaymentMethod:c,paymentDetails:m},select:{id:!0,preferredPaymentMethod:!0,paymentDetails:!0}});return i.NextResponse.json(g)}catch(e){return console.error("Error updating payment details:",e),i.NextResponse.json({error:"Failed to update payment details"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/users/[id]/payment-details/route",pathname:"/api/users/[id]/payment-details",filename:"route",bundlePath:"app/api/users/[id]/payment-details/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\users\\[id]\\payment-details\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:x}=h;function y(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,8044,3112,8381],()=>s(83434));module.exports=t})();
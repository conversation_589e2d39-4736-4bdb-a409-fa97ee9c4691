(()=>{var e={};e.id=8733,e.ids=[8733],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10069:(e,a,t)=>{Promise.resolve().then(t.bind(t,44427))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27406:(e,a)=>{"use strict";a.__esModule=!0,a.default=function(e,a){if(e&&a){var t=Array.isArray(a)?a:a.split(",");if(0===t.length)return!0;var i=e.name||"",n=(e.type||"").toLowerCase(),l=n.replace(/\/.*$/,"");return t.some(function(e){var a=e.trim().toLowerCase();return"."===a.charAt(0)?i.toLowerCase().endsWith(a):a.endsWith("/*")?l===a.replace(/\/.*$/,""):n===a})}return!0}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},37590:(e,a,t)=>{"use strict";t.d(a,{oR:()=>I});var i,n=t(43210);let l={data:""},o=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||l,s=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,r=/\/\*[^]*?\*\/|  +/g,p=/\n+/g,c=(e,a)=>{let t="",i="",n="";for(let l in e){let o=e[l];"@"==l[0]?"i"==l[1]?t=l+" "+o+";":i+="f"==l[1]?c(o,l):l+"{"+c(o,"k"==l[1]?"":a)+"}":"object"==typeof o?i+=c(o,a?a.replace(/([^,])+/g,e=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,a=>/&/.test(a)?a.replace(/&/g,e):e?e+" "+a:a)):l):null!=o&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=c.p?c.p(l,o):l+":"+o+";")}return t+(a&&n?a+"{"+n+"}":n)+i},d={},m=e=>{if("object"==typeof e){let a="";for(let t in e)a+=t+m(e[t]);return a}return e},x=(e,a,t,i,n)=>{let l=m(e),o=d[l]||(d[l]=(e=>{let a=0,t=11;for(;a<e.length;)t=101*t+e.charCodeAt(a++)>>>0;return"go"+t})(l));if(!d[o]){let a=l!==e?e:(e=>{let a,t,i=[{}];for(;a=s.exec(e.replace(r,""));)a[4]?i.shift():a[3]?(t=a[3].replace(p," ").trim(),i.unshift(i[0][t]=i[0][t]||{})):i[0][a[1]]=a[2].replace(p," ").trim();return i[0]})(e);d[o]=c(n?{["@keyframes "+o]:a}:a,t?"":"."+o)}let x=t&&d.g?d.g:null;return t&&(d.g=d[o]),((e,a,t,i)=>{i?a.data=a.data.replace(i,e):-1===a.data.indexOf(e)&&(a.data=t?e+a.data:a.data+e)})(d[o],a,i,x),o},u=(e,a,t)=>e.reduce((e,i,n)=>{let l=a[n];if(l&&l.call){let e=l(t),a=e&&e.props&&e.props.className||/^go/.test(e)&&e;l=a?"."+a:e&&"object"==typeof e?e.props?"":c(e,""):!1===e?"":e}return e+i+(null==l?"":l)},"");function v(e){let a=this||{},t=e.call?e(a.p):e;return x(t.unshift?t.raw?u(t,[].slice.call(arguments,1),a.p):t.reduce((e,t)=>Object.assign(e,t&&t.call?t(a.p):t),{}):t,o(a.target),a.g,a.o,a.k)}v.bind({g:1});let f,g,h,b=v.bind({k:1});function y(e,a){let t=this||{};return function(){let i=arguments;function n(l,o){let s=Object.assign({},l),r=s.className||n.className;t.p=Object.assign({theme:g&&g()},s),t.o=/ *go\d+/.test(r),s.className=v.apply(t,i)+(r?" "+r:""),a&&(s.ref=o);let p=e;return e[0]&&(p=s.as||e,delete s.as),h&&p[0]&&h(s),f(p,s)}return a?a(n):n}}var w=e=>"function"==typeof e,k=(e,a)=>w(e)?e(a):e,N=(()=>{let e=0;return()=>(++e).toString()})(),z=(()=>{let e;return()=>e})(),D=(e,a)=>{switch(a.type){case 0:return{...e,toasts:[a.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case 2:let{toast:t}=a;return D(e,{type:+!!e.toasts.find(e=>e.id===t.id),toast:t});case 3:let{toastId:i}=a;return{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===a.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)};case 5:return{...e,pausedAt:a.time};case 6:let n=a.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+n}))}}},C=[],E={toasts:[],pausedAt:void 0},S=e=>{E=D(E,e),C.forEach(e=>{e(E)})},O={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},F=(e={})=>{let[a,t]=j(E),i=Q(E);H(()=>(i.current!==E&&t(E),C.push(t),()=>{let e=C.indexOf(t);e>-1&&C.splice(e,1)}),[]);let n=a.toasts.map(a=>{var t,i,n;return{...e,...e[a.type],...a,removeDelay:a.removeDelay||(null==(t=e[a.type])?void 0:t.removeDelay)||(null==e?void 0:e.removeDelay),duration:a.duration||(null==(i=e[a.type])?void 0:i.duration)||(null==e?void 0:e.duration)||O[a.type],style:{...e.style,...null==(n=e[a.type])?void 0:n.style,...a.style}}});return{...a,toasts:n}},P=(e,a="blank",t)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:a,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...t,id:(null==t?void 0:t.id)||N()}),A=e=>(a,t)=>{let i=P(a,e,t);return S({type:2,toast:i}),i.id},I=(e,a)=>A("blank")(e,a);I.error=A("error"),I.success=A("success"),I.loading=A("loading"),I.custom=A("custom"),I.dismiss=e=>{S({type:3,toastId:e})},I.remove=e=>S({type:4,toastId:e}),I.promise=(e,a,t)=>{let i=I.loading(a.loading,{...t,...null==t?void 0:t.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let n=a.success?k(a.success,e):void 0;return n?I.success(n,{id:i,...t,...null==t?void 0:t.success}):I.dismiss(i),e}).catch(e=>{let n=a.error?k(a.error,e):void 0;n?I.error(n,{id:i,...t,...null==t?void 0:t.error}):I.dismiss(i)}),e};var R=(e,a)=>{S({type:1,toast:{id:e,height:a}})},q=()=>{S({type:5,time:Date.now()})},_=new Map,T=1e3,$=(e,a=T)=>{if(_.has(e))return;let t=setTimeout(()=>{_.delete(e),S({type:4,toastId:e})},a);_.set(e,t)},M=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,L=b`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,U=b`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=(y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${M} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${L} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${U} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,b`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`),W=(y("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${B} 1s linear infinite;
`,b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`),V=b`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,K=(y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${W} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${V} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,y("div")`
  position: absolute;
`,y("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,b`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`);y("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${K} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,y("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,y("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,i=n.createElement,c.p=void 0,f=i,g=void 0,h=void 0,v`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`},44427:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>ef});var i=t(60687),n=t(43210),l=t(82136),o=t(16189),s=t(68367),r=t(47081),p=t(23877),c=t(87955);function d(e,a,t,i){return new(t||(t=Promise))(function(n,l){function o(e){try{r(i.next(e))}catch(e){l(e)}}function s(e){try{r(i.throw(e))}catch(e){l(e)}}function r(e){var a;e.done?n(e.value):((a=e.value)instanceof t?a:new t(function(e){e(a)})).then(o,s)}r((i=i.apply(e,a||[])).next())})}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;let m=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function x(e,a,t){let i=function(e){let{name:a}=e;if(a&&-1!==a.lastIndexOf(".")&&!e.type){let t=a.split(".").pop().toLowerCase(),i=m.get(t);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:n}=e,l="string"==typeof a?a:"string"==typeof n&&n.length>0?n:`./${e.name}`;return"string"!=typeof i.path&&u(i,"path",l),void 0!==t&&Object.defineProperty(i,"handle",{value:t,writable:!1,configurable:!1,enumerable:!0}),u(i,"relativePath",l),i}function u(e,a,t){Object.defineProperty(e,a,{value:t,writable:!1,configurable:!1,enumerable:!0})}let v=[".DS_Store","Thumbs.db"];function f(e){return"object"==typeof e&&null!==e}function g(e){return e.filter(e=>-1===v.indexOf(e.name))}function h(e){if(null===e)return[];let a=[];for(let t=0;t<e.length;t++){let i=e[t];a.push(i)}return a}function b(e){if("function"!=typeof e.webkitGetAsEntry)return y(e);let a=e.webkitGetAsEntry();return a&&a.isDirectory?k(a):y(e,a)}function y(e,a){return d(this,void 0,void 0,function*(){var t;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let a=yield e.getAsFileSystemHandle();if(null===a)throw Error(`${e} is not a File`);if(void 0!==a){let e=yield a.getFile();return e.handle=a,x(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return x(i,null!=(t=null==a?void 0:a.fullPath)?t:void 0)})}function w(e){return d(this,void 0,void 0,function*(){return e.isDirectory?k(e):function(e){return d(this,void 0,void 0,function*(){return new Promise((a,t)=>{e.file(t=>{a(x(t,e.fullPath))},e=>{t(e)})})})}(e)})}function k(e){let a=e.createReader();return new Promise((e,t)=>{let i=[];!function n(){a.readEntries(a=>d(this,void 0,void 0,function*(){if(a.length){let e=Promise.all(a.map(w));i.push(e),n()}else try{let a=yield Promise.all(i);e(a)}catch(e){t(e)}}),e=>{t(e)})}()})}var N=t(27406);function z(e){return function(e){if(Array.isArray(e))return F(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||O(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,i)}return t}function C(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?D(Object(t),!0).forEach(function(a){E(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):D(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function E(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function S(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l=[],o=!0,s=!1;try{for(n=n.call(e);!(o=(t=n.next()).done)&&(l.push(t.value),!a||l.length!==a);o=!0);}catch(e){s=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(s)throw i}}return l}}(e,a)||O(e,a)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,a){if(e){if("string"==typeof e)return F(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return F(e,a)}}function F(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,i=Array(a);t<a;t++)i[t]=e[t];return i}var P="function"==typeof N?N:N.default,A=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",a=e.split(","),t=a.length>1?"one of ".concat(a.join(", ")):a[0];return{code:"file-invalid-type",message:"File type must be ".concat(t)}},I=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},R=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},q={code:"too-many-files",message:"Too many files"};function _(e,a){var t="application/x-moz-file"===e.type||P(e,a);return[t,t?null:A(a)]}function T(e,a,t){if($(e.size)){if($(a)&&$(t)){if(e.size>t)return[!1,I(t)];if(e.size<a)return[!1,R(a)]}else if($(a)&&e.size<a)return[!1,R(a)];else if($(t)&&e.size>t)return[!1,I(t)]}return[!0,null]}function $(e){return null!=e}function M(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function L(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function U(e){e.preventDefault()}function B(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return a.some(function(a){return!M(e)&&a&&a.apply(void 0,[e].concat(i)),M(e)})}}function W(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function V(e){return/^.*\.[\w]+$/.test(e)}var K=["children"],J=["open"],G=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Y=["refKey","onChange","onClick"];function X(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l=[],o=!0,s=!1;try{for(n=n.call(e);!(o=(t=n.next()).done)&&(l.push(t.value),!a||l.length!==a);o=!0);}catch(e){s=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(s)throw i}}return l}}(e,a)||Z(e,a)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(e,a){if(e){if("string"==typeof e)return ee(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ee(e,a)}}function ee(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,i=Array(a);t<a;t++)i[t]=e[t];return i}function ea(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,i)}return t}function et(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?ea(Object(t),!0).forEach(function(a){ei(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ea(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function ei(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function en(e,a){if(null==e)return{};var t,i,n=function(e,a){if(null==e)return{};var t,i,n={},l=Object.keys(e);for(i=0;i<l.length;i++)t=l[i],a.indexOf(t)>=0||(n[t]=e[t]);return n}(e,a);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(i=0;i<l.length;i++)t=l[i],!(a.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}var el=(0,n.forwardRef)(function(e,a){var t=e.children,i=er(en(e,K)),l=i.open,o=en(i,J);return(0,n.useImperativeHandle)(a,function(){return{open:l}},[l]),n.createElement(n.Fragment,null,t(et(et({},o),{},{open:l})))});el.displayName="Dropzone";var eo={disabled:!1,getFilesFromEvent:function(e){return d(this,void 0,void 0,function*(){var a;if(f(e)&&f(e.dataTransfer))return function(e,a){return d(this,void 0,void 0,function*(){if(e.items){let t=h(e.items).filter(e=>"file"===e.kind);return"drop"!==a?t:g(function e(a){return a.reduce((a,t)=>[...a,...Array.isArray(t)?e(t):[t]],[])}((yield Promise.all(t.map(b)))))}return g(h(e.files).map(e=>x(e)))})}(e.dataTransfer,e.type);if(f(a=e)&&f(a.target))return h(e.target.files).map(e=>x(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return d(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>x(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};el.defaultProps=eo,el.propTypes={children:c.func,accept:c.objectOf(c.arrayOf(c.string)),multiple:c.bool,preventDropOnDocument:c.bool,noClick:c.bool,noKeyboard:c.bool,noDrag:c.bool,noDragEventsBubbling:c.bool,minSize:c.number,maxSize:c.number,maxFiles:c.number,disabled:c.bool,getFilesFromEvent:c.func,onFileDialogCancel:c.func,onFileDialogOpen:c.func,useFsAccessApi:c.bool,autoFocus:c.bool,onDragEnter:c.func,onDragLeave:c.func,onDragOver:c.func,onDrop:c.func,onDropAccepted:c.func,onDropRejected:c.func,onError:c.func,validator:c.func};var es={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function er(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=et(et({},eo),e),t=a.accept,i=a.disabled,l=a.getFilesFromEvent,o=a.maxSize,s=a.minSize,r=a.multiple,p=a.maxFiles,c=a.onDragEnter,d=a.onDragLeave,m=a.onDragOver,x=a.onDrop,u=a.onDropAccepted,v=a.onDropRejected,f=a.onFileDialogCancel,g=a.onFileDialogOpen,h=a.useFsAccessApi,b=a.autoFocus,y=a.preventDropOnDocument,w=a.noClick,k=a.noKeyboard,N=a.noDrag,D=a.noDragEventsBubbling,O=a.onError,F=a.validator,P=(0,n.useMemo)(function(){return $(t)?Object.entries(t).reduce(function(e,a){var t=S(a,2),i=t[0],n=t[1];return[].concat(z(e),[i],z(n))},[]).filter(function(e){return W(e)||V(e)}).join(","):void 0},[t]),A=(0,n.useMemo)(function(){return $(t)?[{description:"Files",accept:Object.entries(t).filter(function(e){var a=S(e,2),t=a[0],i=a[1],n=!0;return W(t)||(console.warn('Skipped "'.concat(t,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(V)||(console.warn('Skipped "'.concat(t,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(e,a){var t=S(a,2),i=t[0],n=t[1];return C(C({},e),{},E({},i,n))},{})}]:t},[t]),I=(0,n.useMemo)(function(){return"function"==typeof g?g:ec},[g]),R=(0,n.useMemo)(function(){return"function"==typeof f?f:ec},[f]),K=(0,n.useRef)(null),J=(0,n.useRef)(null),ea=X((0,n.useReducer)(ep,es),2),el=ea[0],er=ea[1],ed=el.isFocused,em=el.isFileDialogActive,ex=(0,n.useRef)("undefined"!=typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),eu=function(){!ex.current&&em&&setTimeout(function(){J.current&&(J.current.files.length||(er({type:"closeDialog"}),R()))},300)};(0,n.useEffect)(function(){return window.addEventListener("focus",eu,!1),function(){window.removeEventListener("focus",eu,!1)}},[J,em,R,ex]);var ev=(0,n.useRef)([]),ef=function(e){K.current&&K.current.contains(e.target)||(e.preventDefault(),ev.current=[])};(0,n.useEffect)(function(){return y&&(document.addEventListener("dragover",U,!1),document.addEventListener("drop",ef,!1)),function(){y&&(document.removeEventListener("dragover",U),document.removeEventListener("drop",ef))}},[K,y]),(0,n.useEffect)(function(){return!i&&b&&K.current&&K.current.focus(),function(){}},[K,b,i]);var eg=(0,n.useCallback)(function(e){O?O(e):console.error(e)},[O]),eh=(0,n.useCallback)(function(e){var a;e.preventDefault(),e.persist(),eF(e),ev.current=[].concat(function(e){if(Array.isArray(e))return ee(e)}(a=ev.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(a)||Z(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),L(e)&&Promise.resolve(l(e)).then(function(a){if(!M(e)||D){var t,i,n,l,d,m,x,u,v=a.length,f=v>0&&(i=(t={files:a,accept:P,minSize:s,maxSize:o,multiple:r,maxFiles:p,validator:F}).files,n=t.accept,l=t.minSize,d=t.maxSize,m=t.multiple,x=t.maxFiles,u=t.validator,(!!m||!(i.length>1))&&(!m||!(x>=1)||!(i.length>x))&&i.every(function(e){var a=S(_(e,n),1)[0],t=S(T(e,l,d),1)[0],i=u?u(e):null;return a&&t&&!i}));er({isDragAccept:f,isDragReject:v>0&&!f,isDragActive:!0,type:"setDraggedFiles"}),c&&c(e)}}).catch(function(e){return eg(e)})},[l,c,eg,D,P,s,o,r,p,F]),eb=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),eF(e);var a=L(e);if(a&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return a&&m&&m(e),!1},[m,D]),ey=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),eF(e);var a=ev.current.filter(function(e){return K.current&&K.current.contains(e)}),t=a.indexOf(e.target);-1!==t&&a.splice(t,1),ev.current=a,!(a.length>0)&&(er({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),L(e)&&d&&d(e))},[K,d,D]),ej=(0,n.useCallback)(function(e,a){var t=[],i=[];e.forEach(function(e){var a=X(_(e,P),2),n=a[0],l=a[1],r=X(T(e,s,o),2),p=r[0],c=r[1],d=F?F(e):null;if(n&&p&&!d)t.push(e);else{var m=[l,c];d&&(m=m.concat(d)),i.push({file:e,errors:m.filter(function(e){return e})})}}),(!r&&t.length>1||r&&p>=1&&t.length>p)&&(t.forEach(function(e){i.push({file:e,errors:[q]})}),t.splice(0)),er({acceptedFiles:t,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),x&&x(t,i,a),i.length>0&&v&&v(i,a),t.length>0&&u&&u(t,a)},[er,r,P,s,o,p,x,u,v,F]),ew=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),eF(e),ev.current=[],L(e)&&Promise.resolve(l(e)).then(function(a){(!M(e)||D)&&ej(a,e)}).catch(function(e){return eg(e)}),er({type:"reset"})},[l,ej,eg,D]),ek=(0,n.useCallback)(function(){if(ex.current){er({type:"openDialog"}),I(),window.showOpenFilePicker({multiple:r,types:A}).then(function(e){return l(e)}).then(function(e){ej(e,null),er({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(R(e),er({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(ex.current=!1,J.current?(J.current.value=null,J.current.click()):eg(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):eg(e)});return}J.current&&(er({type:"openDialog"}),I(),J.current.value=null,J.current.click())},[er,I,R,h,ej,eg,A,r]),eN=(0,n.useCallback)(function(e){K.current&&K.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ek())},[K,ek]),ez=(0,n.useCallback)(function(){er({type:"focus"})},[]),eD=(0,n.useCallback)(function(){er({type:"blur"})},[]),eC=(0,n.useCallback)(function(){w||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ek,0):ek())},[w,ek]),eE=function(e){return i?null:e},eS=function(e){return k?null:eE(e)},eO=function(e){return N?null:eE(e)},eF=function(e){D&&e.stopPropagation()},eP=(0,n.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.refKey,t=e.role,n=e.onKeyDown,l=e.onFocus,o=e.onBlur,s=e.onClick,r=e.onDragEnter,p=e.onDragOver,c=e.onDragLeave,d=e.onDrop,m=en(e,G);return et(et(ei({onKeyDown:eS(B(n,eN)),onFocus:eS(B(l,ez)),onBlur:eS(B(o,eD)),onClick:eE(B(s,eC)),onDragEnter:eO(B(r,eh)),onDragOver:eO(B(p,eb)),onDragLeave:eO(B(c,ey)),onDrop:eO(B(d,ew)),role:"string"==typeof t&&""!==t?t:"presentation"},void 0===a?"ref":a,K),i||k?{}:{tabIndex:0}),m)}},[K,eN,ez,eD,eC,eh,eb,ey,ew,k,N,i]),eA=(0,n.useCallback)(function(e){e.stopPropagation()},[]),eI=(0,n.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.refKey,t=e.onChange,i=e.onClick,n=en(e,Y);return et(et({},ei({accept:P,multiple:r,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eE(B(t,ew)),onClick:eE(B(i,eA)),tabIndex:-1},void 0===a?"ref":a,J)),n)}},[J,t,r,ew,i]);return et(et({},el),{},{isFocused:ed&&!i,getRootProps:eP,getInputProps:eI,rootRef:K,inputRef:J,open:eE(ek)})}function ep(e,a){switch(a.type){case"focus":return et(et({},e),{},{isFocused:!0});case"blur":return et(et({},e),{},{isFocused:!1});case"openDialog":return et(et({},es),{},{isFileDialogActive:!0});case"closeDialog":return et(et({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return et(et({},e),{},{isDragActive:a.isDragActive,isDragAccept:a.isDragAccept,isDragReject:a.isDragReject});case"setFiles":return et(et({},e),{},{acceptedFiles:a.acceptedFiles,fileRejections:a.fileRejections,isDragReject:a.isDragReject});case"reset":return et({},es);default:return e}}function ec(){}var ed=t(37590);function em({isOpen:e,onClose:a,onImportComplete:t}){let[l,o]=(0,n.useState)(null),[s,r]=(0,n.useState)(!1),[c,d]=(0,n.useState)(!1),[m,x]=(0,n.useState)(!1),[u,v]=(0,n.useState)([]),[f,g]=(0,n.useState)(null),[h,b]=(0,n.useState)("upload"),[y,w]=(0,n.useState)({defaultPassword:"Password123!",skipDuplicates:!0}),k=(0,n.useRef)(null),{getRootProps:N,getInputProps:z,isDragActive:D}=er({onDrop:(0,n.useCallback)(e=>{e.length>0&&o(e[0])},[]),accept:{"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":[".xlsx"],"application/vnd.ms-excel":[".xls"]},maxFiles:1}),C=async()=>{if(l){d(!0);try{let e=new FormData;e.append("file",l),e.append("options",JSON.stringify(y));let a=await fetch("/api/admin/users/import",{method:"POST",body:e});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to validate file")}let t=await a.json();v(t.validationResults),b("validate")}catch(e){console.error("Error validating file:",e),ed.oR.error("Failed to validate file: "+(e instanceof Error?e.message:"Unknown error"))}finally{d(!1)}}},E=async()=>{x(!0);try{let e=u.filter(e=>e.isValid),a=await fetch("/api/admin/users/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({validatedData:e,options:y})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to import users")}let i=await a.json();g(i),b("summary"),t(),ed.oR.success(`Successfully imported ${i.successful} users`)}catch(e){console.error("Error importing users:",e),ed.oR.error("Failed to import users: "+(e instanceof Error?e.message:"Unknown error"))}finally{x(!1)}},S=async()=>{try{let e=await fetch("/api/admin/users/import",{method:"GET"});if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to download template")}let a=await e.blob(),t=window.URL.createObjectURL(a),i=document.createElement("a");i.style.display="none",i.href=t,i.download="user_import_template.xlsx",document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(t),document.body.removeChild(i)}catch(e){console.error("Error downloading template:",e),ed.oR.error("Failed to download template: "+(e instanceof Error?e.message:"Unknown error"))}},O=()=>{o(null),v([]),g(null),b("upload")},F=()=>{O(),a()};return e?(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"Import Users"}),(0,i.jsx)("button",{onClick:F,className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)(p.QCr,{})})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[(0,i.jsxs)("div",{className:`flex items-center ${"upload"===h?"text-blue-600":"text-gray-500"}`,children:[(0,i.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"upload"===h?"bg-blue-100":"bg-gray-100"}`,children:"1"}),(0,i.jsx)("span",{className:"ml-2",children:"Upload"})]}),(0,i.jsx)("div",{className:"w-12 h-1 mx-2 bg-gray-200"}),(0,i.jsxs)("div",{className:`flex items-center ${"validate"===h?"text-blue-600":"text-gray-500"}`,children:[(0,i.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"validate"===h?"bg-blue-100":"bg-gray-100"}`,children:"2"}),(0,i.jsx)("span",{className:"ml-2",children:"Validate"})]}),(0,i.jsx)("div",{className:"w-12 h-1 mx-2 bg-gray-200"}),(0,i.jsxs)("div",{className:`flex items-center ${"summary"===h?"text-blue-600":"text-gray-500"}`,children:[(0,i.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"summary"===h?"bg-blue-100":"bg-gray-100"}`,children:"3"}),(0,i.jsx)("span",{className:"ml-2",children:"Complete"})]})]}),"upload"===h&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)(p.__w,{className:"text-blue-500 mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:"Upload an Excel file (.xlsx) containing user data. You can download a template to get started."})]}),(0,i.jsx)("div",{className:"flex justify-end mb-4",children:(0,i.jsxs)("button",{onClick:S,className:"flex items-center text-sm text-blue-600 hover:text-blue-800",children:[(0,i.jsx)(p.WCW,{className:"mr-1"}),"Download Template"]})}),(0,i.jsxs)("div",{...N(),className:`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${D?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400"}`,children:[(0,i.jsx)("input",{...z(),ref:k}),(0,i.jsx)(p.Ru,{className:"mx-auto text-4xl text-green-500 mb-3"}),l?(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-lg font-medium text-gray-800",children:l.name}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:[(l.size/1024).toFixed(2)," KB"]}),(0,i.jsx)("button",{onClick:e=>{e.stopPropagation(),o(null)},className:"mt-2 text-sm text-red-600 hover:text-red-800",children:"Remove"})]}):(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-lg font-medium text-gray-800",children:"Drag & drop an Excel file here"}),(0,i.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"or click to select a file"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Supported formats: .xlsx, .xls"})]})]})]}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Import Options"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Default Password"}),(0,i.jsx)("input",{type:"text",value:y.defaultPassword,onChange:e=>w({...y,defaultPassword:e.target.value}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter default password for new users"}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"This password will be assigned to all imported users."})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"skipDuplicates",checked:y.skipDuplicates,onChange:e=>w({...y,skipDuplicates:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsx)("label",{htmlFor:"skipDuplicates",className:"ml-2 block text-sm text-gray-700",children:"Skip duplicate users (based on email)"})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{onClick:F,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,i.jsx)("button",{onClick:C,disabled:!l||c,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.hW,{className:"animate-spin mr-2"}),"Validating..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.HVe,{className:"mr-2"}),"Validate File"]})})]})]}),"validate"===h&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("h4",{className:"text-lg font-medium text-gray-800",children:"Validation Results"}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500 mr-1"}),(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:["Valid: ",u.filter(e=>e.isValid).length]})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500 mr-1"}),(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:["Invalid: ",u.filter(e=>!e.isValid).length]})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-500 mr-1"}),(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:["Total: ",u.length]})]})]})]}),0===u.length?(0,i.jsx)("div",{className:"bg-yellow-50 p-4 rounded-md",children:(0,i.jsxs)("div",{className:"flex",children:[(0,i.jsx)(p.BS8,{className:"text-yellow-400 mt-0.5 mr-2"}),(0,i.jsx)("p",{className:"text-sm text-yellow-700",children:"No data found in the uploaded file. Please check the file format and try again."})]})}):(0,i.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Row"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Member ID"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Upline"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errors"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map((e,a)=>(0,i.jsxs)("tr",{className:e.isValid?"":"bg-red-50",children:[(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.row}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.isValid?(0,i.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:[(0,i.jsx)(p.CMH,{className:"mr-1"})," Valid"]}):(0,i.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:[(0,i.jsx)(p.QCr,{className:"mr-1"})," Invalid"]})}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.data?.memberId||"-"}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.data?.name||"-"}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.data?.email||"-"}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.data?.uplineId||"-"}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-red-500",children:e.errors.join(", ")})]},a))})]})})})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{onClick:O,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Back"}),(0,i.jsx)("button",{onClick:E,disabled:0===u.filter(e=>e.isValid).length||m,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:m?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.hW,{className:"animate-spin mr-2"}),"Importing..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.CMH,{className:"mr-2"}),"Confirm Import"]})})]})]}),"summary"===h&&f&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-4 mb-6",children:(0,i.jsxs)("div",{className:"flex",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)(p.CMH,{className:"h-5 w-5 text-green-400"})}),(0,i.jsxs)("div",{className:"ml-3",children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Import Completed Successfully"}),(0,i.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,i.jsxs)("p",{children:[f.successful," users were successfully imported.",f.failed>0&&` ${f.failed} users failed to import.`,f.duplicates>0&&` ${f.duplicates} duplicates were skipped.`]})})]})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,i.jsxs)("div",{className:"bg-white border rounded-md p-4",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-600",children:f.successful}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:"Successfully Imported"})]}),(0,i.jsxs)("div",{className:"bg-white border rounded-md p-4",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-red-600",children:f.failed}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:"Failed to Import"})]}),(0,i.jsxs)("div",{className:"bg-white border rounded-md p-4",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:f.duplicates}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:"Duplicates Skipped"})]})]}),f.importedUsers.length>0&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Imported Users"}),(0,i.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Member ID"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Upline ID"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.importedUsers.map(e=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.memberId}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.name}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.uplineId||"-"})]},e.id))})]})})})]}),f.errors.length>0&&(0,i.jsxs)("div",{className:"mt-6",children:[(0,i.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Errors"}),(0,i.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Row"}),(0,i.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errors"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.errors.map((e,a)=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.row}),(0,i.jsx)("td",{className:"px-6 py-4 text-sm text-red-500",children:e.errors.join(", ")})]},a))})]})})})]})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{onClick:F,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Close"}),(0,i.jsx)("button",{onClick:O,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Import More Users"})]})]})]})]})}):null}function ex({isOpen:e,onClose:a}){let[t,l]=(0,n.useState)(!1),[o,s]=(0,n.useState)([]),[r,c]=(0,n.useState)(!1),[d,m]=(0,n.useState)({includeRank:!0,includeDownlineCount:!0,includeJoinDate:!0,includeEarnings:!1,activeOnly:!1}),x=async()=>{l(!0);try{let e=document.createElement("form");e.method="POST",e.action="/api/admin/users/export",e.target="_blank";let t=document.createElement("input");t.type="hidden",t.name="options",t.value=JSON.stringify(d),e.appendChild(t),document.body.appendChild(e),e.submit(),document.body.removeChild(e),ed.oR.success("Export started. Your download will begin shortly."),a()}catch(e){console.error("Error exporting users:",e),ed.oR.error("Failed to export users: "+(e instanceof Error?e.message:"Unknown error"))}finally{l(!1)}};return e?(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"Export Users"}),(0,i.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)(p.QCr,{})})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Export Options"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"includeRank",checked:d.includeRank,onChange:e=>m({...d,includeRank:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsx)("label",{htmlFor:"includeRank",className:"ml-2 block text-sm text-gray-700",children:"Include Rank"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"includeDownlineCount",checked:d.includeDownlineCount,onChange:e=>m({...d,includeDownlineCount:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsx)("label",{htmlFor:"includeDownlineCount",className:"ml-2 block text-sm text-gray-700",children:"Include Downline Count"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"includeJoinDate",checked:d.includeJoinDate,onChange:e=>m({...d,includeJoinDate:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsx)("label",{htmlFor:"includeJoinDate",className:"ml-2 block text-sm text-gray-700",children:"Include Join Date"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"includeEarnings",checked:d.includeEarnings,onChange:e=>m({...d,includeEarnings:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsx)("label",{htmlFor:"includeEarnings",className:"ml-2 block text-sm text-gray-700",children:"Include Earnings"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"activeOnly",checked:d.activeOnly,onChange:e=>m({...d,activeOnly:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,i.jsx)("label",{htmlFor:"activeOnly",className:"ml-2 block text-sm text-gray-700",children:"Active Users Only"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Rank"}),(0,i.jsxs)("select",{value:d.rankFilter||"",onChange:e=>m({...d,rankFilter:e.target.value?parseInt(e.target.value):void 0}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"",children:"All Ranks"}),r?(0,i.jsx)("option",{disabled:!0,children:"Loading ranks..."}):o.map(e=>(0,i.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Join Date From"}),(0,i.jsx)("input",{type:"date",value:d.dateRangeStart||"",onChange:e=>m({...d,dateRangeStart:e.target.value||void 0}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Join Date To"}),(0,i.jsx)("input",{type:"date",value:d.dateRangeEnd||"",onChange:e=>m({...d,dateRangeEnd:e.target.value||void 0}),className:"border rounded-md px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{onClick:a,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,i.jsx)("button",{onClick:x,disabled:t,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:t?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.hW,{className:"animate-spin mr-2"}),"Exporting..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.Ru,{className:"mr-2"}),"Export to Excel"]})})]})]})]})}):null}function eu({limit:e=10}){let[a,t]=(0,n.useState)([]),[l,o]=(0,n.useState)(!0),[s,r]=(0,n.useState)(null),c=e=>new Date(e).toLocaleString(),d=e=>{try{return JSON.parse(e)}catch(e){return null}},m=e=>{switch(e){case"import":return(0,i.jsx)(p.PiR,{className:"text-blue-500"});case"bulk_import":return(0,i.jsx)(p.PiR,{className:"text-green-500"});case"export":return(0,i.jsx)(p.Mbn,{className:"text-purple-500"});default:return(0,i.jsx)(p.OKX,{className:"text-gray-500"})}},x=e=>{let a=d(e.details);switch(e.action){case"import":return"Imported a single user";case"bulk_import":if(a)return`Imported ${a.successful} users (${a.totalProcessed} processed, ${a.failed} failed, ${a.duplicates} duplicates)`;return"Bulk imported users";case"export":return"Exported user data";default:return e.action}};return l?(0,i.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,i.jsx)(p.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading audit logs..."})]}):s?(0,i.jsx)("div",{className:"bg-red-50 p-4 rounded-md",children:(0,i.jsx)("p",{className:"text-red-700",children:s})}):0===a.length?(0,i.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(0,i.jsx)("p",{className:"text-gray-700",children:"No import/export history found."})}):(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,i.jsxs)("div",{className:"px-4 py-5 sm:px-6 border-b",children:[(0,i.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Import/Export History"}),(0,i.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Recent user import and export activities."})]}),(0,i.jsx)("ul",{className:"divide-y divide-gray-200",children:a.map(e=>(0,i.jsx)("li",{className:"px-4 py-4 sm:px-6 hover:bg-gray-50",children:(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"mr-4",children:m(e.action)}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-900",children:x(e)}),(0,i.jsxs)("div",{className:"flex mt-1",children:[(0,i.jsxs)("div",{className:"flex items-center text-xs text-gray-500 mr-4",children:[(0,i.jsx)(p.x$1,{className:"mr-1"}),e.user.name]}),(0,i.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,i.jsx)(p.bfZ,{className:"mr-1"}),c(e.createdAt)]})]})]})]})})},e.id))})]})}let ev=()=>{let[e,a]=(0,n.useState)([]),[t,l]=(0,n.useState)(!0),[o,s]=(0,n.useState)([]),[r,c]=(0,n.useState)({page:1,pageSize:10,totalItems:0,totalPages:0}),[d,m]=(0,n.useState)(""),[x,u]=(0,n.useState)(""),[v,f]=(0,n.useState)(!1),[g,h]=(0,n.useState)(null),[b,y]=(0,n.useState)(!1),[w,k]=(0,n.useState)(!1),[N,z]=(0,n.useState)({name:"",email:"",phone:"",rankId:"",uplineId:""}),[D,C]=(0,n.useState)(!1),[E,S]=(0,n.useState)(!1),[O,F]=(0,n.useState)(!1),[P,A]=(0,n.useState)({type:"",text:""});(0,n.useEffect)(()=>{I(),R()},[r.page,r.pageSize,d,x]);let I=async()=>{l(!0);try{let e=new URLSearchParams;e.append("page",r.page.toString()),e.append("pageSize",r.pageSize.toString()),d&&e.append("search",d),x&&e.append("rankId",x);let t=await fetch(`/api/users?${e.toString()}`);if(!t.ok)throw Error(`Failed to fetch users: ${t.statusText}`);let i=await t.json();a(i.users),c(i.pagination)}catch(e){console.error("Error fetching users:",e),A({type:"error",text:"Failed to fetch users. Please try again."})}finally{l(!1)}},R=async()=>{try{let e=await fetch("/api/ranks");if(!e.ok)throw Error(`Failed to fetch ranks: ${e.statusText}`);let a=await e.json();s(a)}catch(e){console.error("Error fetching ranks:",e)}},q=e=>{e>0&&e<=r.totalPages&&c(a=>({...a,page:e}))},_=e=>{h(e),y(!0),k(!1)},T=e=>{h(e),z({name:e.name,email:e.email,phone:e.phone||"",rankId:e.rankId.toString(),uplineId:e.uplineId?e.uplineId.toString():""}),k(!0),y(!1)},$=e=>{let{name:a,value:t}=e.target;z(e=>({...e,[a]:t}))},M=async e=>{if(e.preventDefault(),g){l(!0),A({type:"",text:""});try{let e=await fetch(`/api/users/${g.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:N.name,phone:N.phone||null,rankId:parseInt(N.rankId),uplineId:N.uplineId?parseInt(N.uplineId):null})});if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to update user")}A({type:"success",text:"User updated successfully"}),I(),k(!1)}catch(e){A({type:"error",text:e.message||"An error occurred while updating the user"})}finally{l(!1)}}},L=async e=>{if(confirm("Are you sure you want to reset this user's wallet balance to 0?")){l(!0),A({type:"",text:""});try{let a=await fetch(`/api/users/${e}/wallet/reset`,{method:"POST"});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to reset wallet balance")}A({type:"success",text:"Wallet balance reset successfully"}),I()}catch(e){A({type:"error",text:e.message||"An error occurred while resetting wallet balance"})}finally{l(!1)}}},U=()=>{F(!O)};return(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,i.jsxs)("div",{className:"p-6 border-b",children:[(0,i.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,i.jsx)("form",{onSubmit:e=>{e.preventDefault(),c(e=>({...e,page:1}))},className:"flex-1",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("input",{type:"text",placeholder:"Search by name or email",value:d,onChange:e=>{m(e.target.value)},className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)(p.KSO,{className:"text-gray-400"})}),(0,i.jsx)("button",{type:"submit",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-blue-500 hover:text-blue-700",children:"Search"})]})}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("button",{onClick:()=>{C(!0)},className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[(0,i.jsx)(p.PiR,{className:"mr-2"}),"Import"]}),(0,i.jsxs)("button",{onClick:()=>{S(!0)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[(0,i.jsx)(p.Mbn,{className:"mr-2"}),"Export"]}),(0,i.jsxs)("button",{onClick:()=>f(!v),className:"flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:[(0,i.jsx)(p.YsJ,{className:"mr-2"}),"Filters",v?(0,i.jsx)(p.Ucs,{className:"ml-2"}):(0,i.jsx)(p.Vr3,{className:"ml-2"})]}),(0,i.jsxs)("button",{onClick:U,className:"flex items-center px-4 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200",children:[(0,i.jsx)(p.OKX,{className:"mr-2"}),"History"]})]})]}),v&&(0,i.jsx)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,i.jsxs)("select",{value:x,onChange:e=>{u(e.target.value),c(e=>({...e,page:1}))},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,i.jsx)("option",{value:"",children:"All Ranks"}),o.map(e=>(0,i.jsx)("option",{value:e.id.toString(),children:e.name},e.id))]})]})})})]}),P.text&&(0,i.jsx)("div",{className:`mx-6 mt-4 p-4 rounded-md ${"success"===P.type?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,children:P.text}),(0,i.jsx)("div",{className:"overflow-x-auto",children:t&&0===e.length?(0,i.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,i.jsx)(p.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,i.jsx)("span",{children:"Loading users..."})]}):e.length>0?(0,i.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,i.jsx)("thead",{className:"bg-gray-50",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Downline"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,i.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,i.jsxs)("tr",{children:[(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:e.profileImage?(0,i.jsx)("img",{className:"h-10 w-10 rounded-full",src:e.profileImage,alt:e.name}):(0,i.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-gray-500 font-medium",children:e.name.charAt(0).toUpperCase()})})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:e.phone||"No phone"})]})]})}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:e.rank.name})}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e._count.downline}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.walletBalance.toFixed(2)]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("button",{onClick:()=>_(e),className:"text-blue-600 hover:text-blue-900",title:"View Details",children:(0,i.jsx)(p.Ny1,{})}),(0,i.jsx)("button",{onClick:()=>T(e),className:"text-green-600 hover:text-green-900",title:"Edit User",children:(0,i.jsx)(p.uO9,{})}),(0,i.jsx)("button",{onClick:()=>L(e.id),className:"text-red-600 hover:text-red-900",title:"Reset Wallet Balance",children:(0,i.jsx)(p.aBc,{})})]})})]},e.id))})]}):(0,i.jsx)("div",{className:"text-center py-12",children:(0,i.jsx)("p",{className:"text-gray-500",children:"No users found."})})}),e.length>0&&(0,i.jsxs)("div",{className:"px-6 py-4 flex items-center justify-between border-t",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-sm text-gray-700 mr-2",children:"Rows per page:"}),(0,i.jsxs)("select",{value:r.pageSize,onChange:e=>{let a=parseInt(e.target.value);c(e=>({...e,pageSize:a,page:1}))},className:"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,i.jsx)("option",{value:"10",children:"10"}),(0,i.jsx)("option",{value:"25",children:"25"}),(0,i.jsx)("option",{value:"50",children:"50"}),(0,i.jsx)("option",{value:"100",children:"100"})]})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsxs)("span",{className:"text-sm text-gray-700 mr-4",children:[r.page," of ",r.totalPages," pages (",r.totalItems," total users)"]}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("button",{onClick:()=>q(1),disabled:1===r.page,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"First"}),(0,i.jsx)("button",{onClick:()=>q(r.page-1),disabled:1===r.page,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,i.jsx)("button",{onClick:()=>q(r.page+1),disabled:r.page===r.totalPages,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"}),(0,i.jsx)("button",{onClick:()=>q(r.totalPages),disabled:r.page===r.totalPages,className:"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Last"})]})]})]}),b&&g&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"User Details"}),(0,i.jsx)("button",{onClick:()=>y(!1),className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)(p.QCr,{})})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsx)("div",{className:"h-20 w-20 flex-shrink-0",children:g.profileImage?(0,i.jsx)("img",{className:"h-20 w-20 rounded-full",src:g.profileImage,alt:g.name}):(0,i.jsx)("div",{className:"h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-gray-500 text-2xl font-medium",children:g.name.charAt(0).toUpperCase()})})}),(0,i.jsxs)("div",{className:"ml-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold",children:g.name}),(0,i.jsx)("p",{className:"text-gray-500",children:g.email}),(0,i.jsx)("div",{className:"mt-1",children:(0,i.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:g.rank.name})})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Phone"}),(0,i.jsx)("p",{className:"text-gray-900",children:g.phone||"Not provided"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Wallet Balance"}),(0,i.jsxs)("p",{className:"text-gray-900",children:["₱",g.walletBalance.toFixed(2)]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Upline"}),(0,i.jsx)("p",{className:"text-gray-900",children:g.upline?`${g.upline.name} (${g.upline.email})`:"None"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Downline Count"}),(0,i.jsx)("p",{className:"text-gray-900",children:g._count.downline})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-1",children:"Member Since"}),(0,i.jsx)("p",{className:"text-gray-900",children:new Date(g.createdAt).toLocaleDateString()})]})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{onClick:()=>{y(!1),T(g)},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Edit User"}),(0,i.jsx)("button",{onClick:()=>y(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Close"})]})]})]})}),w&&g&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"Edit User"}),(0,i.jsx)("button",{onClick:()=>k(!1),className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)(p.QCr,{})})]}),(0,i.jsxs)("form",{onSubmit:M,className:"p-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,i.jsx)("input",{type:"text",name:"name",value:N.name,onChange:$,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,i.jsx)("input",{type:"email",name:"email",value:N.email,disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"}),(0,i.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Email cannot be changed"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),(0,i.jsx)("input",{type:"tel",name:"phone",value:N.phone,onChange:$,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,i.jsx)("select",{name:"rankId",value:N.rankId,onChange:$,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0,children:o.map(e=>(0,i.jsx)("option",{value:e.id.toString(),children:e.name},e.id))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Upline ID"}),(0,i.jsx)("input",{type:"text",name:"uplineId",value:N.uplineId,onChange:$,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Leave empty for no upline"})]})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{type:"submit",disabled:t,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300",children:t?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.hW,{className:"animate-spin inline mr-2"}),"Saving..."]}):"Save Changes"}),(0,i.jsx)("button",{type:"button",onClick:()=>k(!1),className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"})]})]})]})}),(0,i.jsx)(em,{isOpen:D,onClose:()=>{C(!1)},onImportComplete:()=>{I()}}),(0,i.jsx)(ex,{isOpen:E,onClose:()=>{S(!1)}}),O&&(0,i.jsxs)("div",{className:"mt-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:"Import/Export History"}),(0,i.jsx)("button",{onClick:U,className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)(p.QCr,{})})]}),(0,i.jsx)(eu,{limit:10})]})]})};function ef(){let{data:e,status:a}=(0,l.useSession)();(0,o.useRouter)();let[t,c]=(0,n.useState)(!0),[d,m]=(0,n.useState)(!1);return"loading"===a||t?(0,i.jsx)(r.A,{children:(0,i.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,i.jsx)("div",{className:"text-xl",children:"Loading..."})})}):d?(0,i.jsx)(r.A,{children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,i.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,i.jsx)(p.YXz,{className:"mr-2 text-blue-500"})," User Management"]})}),(0,i.jsx)(ev,{})]})}):(0,i.jsx)(s.A,{children:(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,i.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}},52031:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});let i=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\users\\page.tsx","default")},53912:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>m,tree:()=>p});var i=t(65239),n=t(48088),l=t(88170),o=t.n(l),s=t(30893),r={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(r[e]=()=>s[e]);t.d(a,r);let p={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52031)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\users\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\users\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68213:(e,a,t)=>{Promise.resolve().then(t.bind(t,52031))},79551:e=>{"use strict";e.exports=require("url")},84031:(e,a,t)=>{"use strict";var i=t(34452);function n(){}function l(){}l.resetWarningCache=n,e.exports=function(){function e(e,a,t,n,l,o){if(o!==i){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function a(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:a,element:e,elementType:e,instanceOf:a,node:e,objectOf:a,oneOf:a,oneOfType:a,shape:a,exact:a,checkPropTypes:l,resetWarningCache:n};return t.PropTypes=t,t}},87955:(e,a,t)=>{e.exports=t(84031)()}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),i=a.X(0,[4243,8414,9567,3877,474,4859,3024,7081],()=>t(53912));module.exports=i})();
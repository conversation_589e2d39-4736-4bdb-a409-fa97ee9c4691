(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4674],{5092:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(95155),r=t(12115),l=t(12108),n=t(35695),d=t(70357),i=t(29911);let c=()=>{let[e,s]=(0,r.useState)(!1),[t,l]=(0,r.useState)(null),[n,d]=(0,r.useState)("development"),[c,o]=(0,r.useState)(!0),[m,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(""),[g,b]=(0,r.useState)(""),[p,f]=(0,r.useState)(!1),[j,y]=(0,r.useState)(30),[N,v]=(0,r.useState)(1),[w,k]=(0,r.useState)(20),[C,S]=(0,r.useState)(5),[T,R]=(0,r.useState)(4),[F,D]=(0,r.useState)(10),[E,U]=(0,r.useState)(!0),[_,I]=(0,r.useState)(!0),[A,P]=(0,r.useState)(!1),B=async()=>{s(!0),b("");try{let e=await fetch("/api/admin/test-users?environment=".concat(n));if(!e.ok)throw Error("Failed to fetch test users: ".concat(e.statusText));let s=await e.json();l(s)}catch(e){b(e.message||"Failed to fetch test users")}finally{s(!1)}},G=async()=>{s(!0),b(""),h("");try{let e=await fetch("/api/admin/test-users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({environment:n,userCount:j,adminCount:N,distributorCount:w,rankedDistributorCount:C,viewerCount:T,maxLevels:F,generatePurchases:E,generateRebates:_})});if(!e.ok)throw Error("Failed to generate test users: ".concat(e.statusText));let s=await e.json();h(s.message),await B()}catch(e){b(e.message||"Failed to generate test users")}finally{s(!1)}},M=async()=>{s(!0),b(""),h("");try{let e=await fetch("/api/admin/test-users",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({environment:n,retainKeyTesters:c,dryRun:m})});if(!e.ok)throw Error("Failed to delete test users: ".concat(e.statusText));let s=await e.json();h("".concat(m?"[DRY RUN] ":"").concat(s.message,": ").concat(s.deleted," deleted, ").concat(s.retained," retained")),await B(),f(!1)}catch(e){b(e.message||"Failed to delete test users")}finally{s(!1)}};return(0,r.useEffect)(()=>{B()},[n]),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold flex items-center",children:[(0,a.jsx)(i.YXz,{className:"mr-2 text-blue-500"})," Test User Manager"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("select",{value:n,onChange:e=>d(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",disabled:e,children:[(0,a.jsx)("option",{value:"development",children:"Development"}),(0,a.jsx)("option",{value:"staging",children:"Staging"})]}),(0,a.jsx)("button",{onClick:()=>B(),className:"px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",disabled:e,children:"Refresh"})]})]}),u&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-green-100 text-green-700 rounded-md",children:u}),g&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:g}),t&&(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Users"}),(0,a.jsx)("div",{className:"text-2xl font-semibold",children:t.totalCount})]}),(0,a.jsxs)("div",{className:"bg-purple-50 p-4 rounded-md",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Admins"}),(0,a.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.admin.length})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Ranked Distributors"}),(0,a.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.ranked_distributor.length})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Distributors"}),(0,a.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.distributor.length})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Viewers"}),(0,a.jsx)("div",{className:"text-2xl font-semibold",children:t.usersByRole.viewer.length})]})]})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Generate Test Users"}),(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[(0,a.jsx)("button",{onClick:()=>P(!A),className:"text-blue-500 text-sm underline mr-2",children:A?"Hide Settings":"Show Settings"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[j," users, ",F," levels deep"]})]}),A&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Users"}),(0,a.jsx)("input",{type:"number",value:j,onChange:e=>y(parseInt(e.target.value)),min:"1",max:"100",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Levels"}),(0,a.jsx)("input",{type:"number",value:F,onChange:e=>D(parseInt(e.target.value)),min:"1",max:"10",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Admins"}),(0,a.jsx)("input",{type:"number",value:N,onChange:e=>v(parseInt(e.target.value)),min:"0",max:"5",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ranked Distributors"}),(0,a.jsx)("input",{type:"number",value:C,onChange:e=>S(parseInt(e.target.value)),min:"0",max:"20",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Distributors"}),(0,a.jsx)("input",{type:"number",value:w,onChange:e=>k(parseInt(e.target.value)),min:"0",max:"50",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Viewers"}),(0,a.jsx)("input",{type:"number",value:T,onChange:e=>R(parseInt(e.target.value)),min:"0",max:"20",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"generatePurchases",checked:E,onChange:e=>U(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"generatePurchases",className:"ml-2 block text-sm text-gray-700",children:"Generate Purchases"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"generateRebates",checked:_,onChange:e=>I(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"generateRebates",className:"ml-2 block text-sm text-gray-700",children:"Generate Rebates"})]})]}),(0,a.jsx)("button",{onClick:G,disabled:e,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.hW,{className:"animate-spin mr-2"})," Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.NPy,{className:"mr-2"})," Generate Test Users"]})})]})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Clean Up Test Users"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[(0,a.jsx)("input",{type:"checkbox",id:"retainKeyTesters",checked:c,onChange:e=>o(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"retainKeyTesters",className:"ml-2 block text-sm text-gray-700",children:"Retain Key Testers (keep_for_dev = true)"})]}),(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[(0,a.jsx)("input",{type:"checkbox",id:"dryRun",checked:m,onChange:e=>x(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"dryRun",className:"ml-2 block text-sm text-gray-700",children:"Dry Run (preview only, no deletion)"})]})]}),p?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm text-red-600 font-medium",children:["Are you sure you want to delete ",(null==t?void 0:t.totalCount)||0," test users?"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:M,disabled:e,className:"flex-1 flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.hW,{className:"animate-spin mr-2"})," Deleting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.CMH,{className:"mr-2"})," Confirm"]})}),(0,a.jsxs)("button",{onClick:()=>f(!1),disabled:e,className:"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:[(0,a.jsx)(i.QCr,{className:"mr-2"})," Cancel"]})]})]}):(0,a.jsxs)("button",{onClick:()=>f(!0),disabled:e||!t||0===t.totalCount,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)(i.Dby,{className:"mr-2"})," Delete Test Users"]})]})})]}),t&&t.users.length>0&&(0,a.jsxs)("div",{className:"overflow-x-auto",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Test Users"}),(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Upline"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Balance"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Keep"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.users.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("admin"===e.metadata.role?"bg-purple-100 text-purple-800":"ranked_distributor"===e.metadata.role?"bg-yellow-100 text-yellow-800":"distributor"===e.metadata.role?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.metadata.role})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.uplineId||"-"}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",e.walletBalance.toFixed(2)]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.metadata.keepForDev?(0,a.jsx)(i.CMH,{className:"text-green-500"}):(0,a.jsx)(i.QCr,{className:"text-red-500"})})]},e.id))})]})]})]})};function o(){let{data:e,status:s}=(0,l.useSession)(),t=(0,n.useRouter)(),[o,m]=(0,r.useState)(!0),[x,u]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{"unauthenticated"===s&&t.push("/login")},[s,t]),(0,r.useEffect)(()=>{"authenticated"===s&&(async()=>{try{let e=await fetch("/api/users/me"),s=await e.json();u(6===s.rankId),m(!1)}catch(e){console.error("Error checking admin status:",e),m(!1)}})()},[s]),"loading"===s||o)?(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})})}):x?(0,a.jsx)(d.A,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,a.jsxs)("h1",{className:"text-2xl font-semibold flex items-center",children:[(0,a.jsx)(i.YXz,{className:"mr-2 text-blue-500"})," Test User Management"]})}),(0,a.jsx)(c,{})]})}):(0,a.jsx)(d.A,{children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"You do not have permission to access this page. Please contact an administrator."})]})})})}},38126:(e,s,t)=>{Promise.resolve().then(t.bind(t,5092))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,6766,5557,1694,357,8441,1684,7358],()=>s(38126)),_N_E=e.O()}]);
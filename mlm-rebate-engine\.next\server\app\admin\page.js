(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={680:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17507:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31579:(e,s,t)=>{Promise.resolve().then(t.bind(t,42334))},33873:e=>{"use strict";e.exports=require("path")},42334:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var r=t(60687),a=t(43210),l=t(82136),n=t(16189),i=t(68367),o=t(47081),d=t(85814),c=t.n(d),m=t(23877);let x=({title:e,value:s,icon:t,borderColor:a,iconBgColor:l,iconColor:n,percentageChange:i,footer:o})=>{let d="number"==typeof s?s.toLocaleString():s,c=i&&i>0,x=i&&i<0;return(0,r.jsx)("div",{className:`bg-white rounded-lg shadow p-6 border-l-4 ${a}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 font-medium",children:e}),(0,r.jsxs)("div",{className:"flex items-baseline",children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:d}),void 0!==i&&(0,r.jsxs)("span",{className:`ml-2 text-xs font-medium flex items-center
                  ${c?"text-green-500":x?"text-red-500":"text-gray-500"}`,children:[c?(0,r.jsx)(m.uCC,{className:"mr-1"}):x?(0,r.jsx)(m.$TP,{className:"mr-1"}):null,Math.abs(i),"%"]})]}),o&&(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:o})]}),(0,r.jsx)("div",{className:`p-3 rounded-full ${l} ${n}`,children:t})]})})},u=({distributors:e,showEarnings:s=!1,showReferrals:t=!1,title:a="Top Distributors",viewAllLink:l="/admin/reports"})=>e&&0!==e.length?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:a}),(0,r.jsxs)(c(),{href:l,className:"text-blue-600 hover:text-blue-800 text-sm flex items-center",children:["View All ",(0,r.jsx)(m.EQc,{className:"ml-1",size:12})]})]}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Distributor"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),s&&(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Earnings"}),(0,r.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:t?"Direct Referrals":"Downline"})]})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 font-medium",children:e.name.charAt(0)}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",e.id]})]})]})}),(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:e.rank})}),s&&(0,r.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-500",children:["₱",e.earnings?.toLocaleString()||0]}),(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-500",children:t?e.directReferrals||0:e.downlineCount})]},e.id))})]})})})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:a})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No distributor data available"})})]}),h=({users:e,purchases:s,title:t="Recent Activity"})=>{let[l,n]=(0,a.useState)("users"),i="users"===l&&e&&e.length>0||"purchases"===l&&s&&s.length>0;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:t}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:`px-2 py-1 text-xs rounded-md ${"users"===l?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"}`,onClick:()=>n("users"),children:"Users"}),(0,r.jsx)("button",{className:`px-2 py-1 text-xs rounded-md ${"purchases"===l?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"}`,onClick:()=>n("purchases"),children:"Purchases"})]})]}),(0,r.jsx)("div",{className:"p-6",children:i?(0,r.jsxs)("div",{className:"space-y-4",children:["users"===l&&e.map(e=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center text-green-500",children:(0,r.jsx)(m.NPy,{})}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["New ",e.rank," member joined"]})]})]},e.id)),"purchases"===l&&s.map(e=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-500",children:(0,r.jsx)(m.AsH,{})}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.date).toLocaleDateString()})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Purchased ",e.productName," for ₱",e.amount.toLocaleString()]})]})]},e.id))]}):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:["No ",l," activity available"]})})]})},g=({onFilterChange:e,ranks:s=[]})=>{let[t,l]=(0,a.useState)(!1),[n,i]=(0,a.useState)({dateRange:"last30days",rankId:"",sponsorId:"",searchTerm:""}),o=s.length>0?s:[{id:1,name:"Starter"},{id:2,name:"Bronze"},{id:3,name:"Silver"},{id:4,name:"Gold"},{id:5,name:"Platinum"},{id:6,name:"Diamond"}],d=(s,t)=>{let r={...n,[s]:t};i(r),e&&e(r)},c=()=>{let s={dateRange:"last30days",rankId:"",sponsorId:"",searchTerm:""};i(s),e&&e(s)},x=n.rankId||n.sponsorId||n.searchTerm||"last30days"!==n.dateRange;return(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, or ID",value:n.searchTerm,onChange:e=>d("searchTerm",e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(m.KSO,{className:"text-gray-400"})}),n.searchTerm&&(0,r.jsx)("button",{onClick:()=>d("searchTerm",""),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:(0,r.jsx)(m.QCr,{})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("button",{className:"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",onClick:()=>l(!t),children:[(0,r.jsx)(m.bfZ,{className:"mr-2"}),(()=>{switch(n.dateRange){case"today":return"Today";case"yesterday":return"Yesterday";case"last7days":return"Last 7 Days";case"last30days":default:return"Last 30 Days";case"thisMonth":return"This Month";case"lastMonth":return"Last Month";case"thisYear":return"This Year";case"lastYear":return"Last Year";case"custom":return"Custom Range"}})(),(0,r.jsx)(m.Vr3,{className:"ml-2"})]})}),(0,r.jsxs)("button",{onClick:()=>l(!t),className:`flex items-center px-4 py-2 rounded-md ${x?"bg-blue-100 text-blue-700 hover:bg-blue-200":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[(0,r.jsx)(m.YsJ,{className:"mr-2"}),"Filters",t?(0,r.jsx)(m.Ucs,{className:"ml-2"}):(0,r.jsx)(m.Vr3,{className:"ml-2"}),x&&(0,r.jsx)("span",{className:"ml-2 px-1.5 py-0.5 text-xs bg-blue-500 text-white rounded-full",children:+!!n.rankId+ +!!n.sponsorId+ +("last30days"!==n.dateRange)})]}),x&&(0,r.jsx)("button",{onClick:c,className:"px-3 py-2 text-sm text-red-600 hover:text-red-800",children:"Clear"})]})]}),t&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),(0,r.jsxs)("select",{value:n.dateRange,onChange:e=>d("dateRange",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"today",children:"Today"}),(0,r.jsx)("option",{value:"yesterday",children:"Yesterday"}),(0,r.jsx)("option",{value:"last7days",children:"Last 7 Days"}),(0,r.jsx)("option",{value:"last30days",children:"Last 30 Days"}),(0,r.jsx)("option",{value:"thisMonth",children:"This Month"}),(0,r.jsx)("option",{value:"lastMonth",children:"Last Month"}),(0,r.jsx)("option",{value:"thisYear",children:"This Year"}),(0,r.jsx)("option",{value:"lastYear",children:"Last Year"}),(0,r.jsx)("option",{value:"custom",children:"Custom Range"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rank"}),(0,r.jsxs)("select",{value:n.rankId,onChange:e=>d("rankId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Ranks"}),o.map(e=>(0,r.jsx)("option",{value:e.id.toString(),children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sponsor ID"}),(0,r.jsx)("input",{type:"text",placeholder:"Enter sponsor ID",value:n.sponsorId,onChange:e=>d("sponsorId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),"custom"===n.dateRange&&(0,r.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,r.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,r.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex justify-end",children:[(0,r.jsx)("button",{onClick:c,className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-100 mr-2",children:"Reset"}),(0,r.jsx)("button",{onClick:()=>l(!1),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Apply Filters"})]})]})]})},p=({node:e,maxLevel:s})=>{let[t,l]=(0,a.useState)(e.level<1),n=e.children&&e.children.length>0,i=n&&e.level<s,o=e.personalVolume/e.groupVolume*100;return(0,r.jsxs)("div",{className:`mb-2 ${e.level>0?"ml-6":""}`,children:[(0,r.jsx)("div",{className:`p-3 rounded-lg border ${e.isActive?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:(0,r.jsxs)("div",{className:"flex items-center",children:[i?(0,r.jsx)("button",{onClick:()=>l(!t),className:"mr-2 text-gray-500 hover:text-gray-700",children:t?(0,r.jsx)(m.Vr3,{}):(0,r.jsx)(m.X6T,{})}):(0,r.jsx)("span",{className:"mr-2 w-4"}),(0,r.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${e.isActive?"bg-green-100 text-green-600":"bg-red-100 text-red-600"}`,children:e.isActive?(0,r.jsx)(m.A7C,{}):(0,r.jsx)(m._Hm,{})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex flex-wrap justify-between items-center",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Level ",e.level]})]}),(0,r.jsxs)("div",{className:"mt-2 grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Personal Volume"}),(0,r.jsxs)("div",{className:"font-medium",children:["₱",e.personalVolume.toLocaleString()]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Group Volume"}),(0,r.jsxs)("div",{className:"font-medium",children:["₱",e.groupVolume.toLocaleString()]})]})]}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,r.jsx)("span",{children:"PV Contribution"}),(0,r.jsxs)("span",{children:[Math.round(o),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full ${e.isActive?"bg-green-500":"bg-red-500"}`,style:{width:`${Math.min(o,100)}%`}})})]})]})]})}),t&&n&&(0,r.jsx)("div",{className:"mt-2 border-l-2 border-gray-200 pl-2",children:e.children?.map(e=>(0,r.jsx)(p,{node:e,maxLevel:s},e.id))})]})},b=({rootMember:e,maxLevel:s=6,title:t="Group Volume Tracker"})=>{let a={id:1,name:"John Doe",personalVolume:1200,groupVolume:25e3,isActive:!0,level:0,children:[{id:2,name:"Alice Smith",personalVolume:800,groupVolume:12e3,isActive:!0,level:1,children:[{id:5,name:"Bob Johnson",personalVolume:500,groupVolume:3e3,isActive:!0,level:2,children:[{id:9,name:"Charlie Brown",personalVolume:300,groupVolume:300,isActive:!0,level:3}]},{id:6,name:"Diana Prince",personalVolume:700,groupVolume:700,isActive:!0,level:2}]},{id:3,name:"Mark Wilson",personalVolume:600,groupVolume:8e3,isActive:!0,level:1,children:[{id:7,name:"Eve Adams",personalVolume:400,groupVolume:2400,isActive:!1,level:2,children:[{id:10,name:"Frank Miller",personalVolume:200,groupVolume:200,isActive:!0,level:3}]}]},{id:4,name:"Sarah Lee",personalVolume:900,groupVolume:3200,isActive:!0,level:1,children:[{id:8,name:"George Davis",personalVolume:350,groupVolume:350,isActive:!0,level:2}]}]};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b",children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:t})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("span",{className:"w-3 h-3 rounded-full bg-green-500 mr-1"}),(0,r.jsx)("span",{children:"Active"})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("span",{className:"w-3 h-3 rounded-full bg-red-500 mr-1"}),(0,r.jsx)("span",{children:"Inactive"})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm ml-4",children:[(0,r.jsx)("span",{className:"font-medium mr-1",children:"PV:"}),(0,r.jsx)("span",{children:"Personal Volume"})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("span",{className:"font-medium mr-1",children:"GV:"}),(0,r.jsx)("span",{children:"Group Volume"})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)(p,{node:e||a,maxLevel:s})})]})]})};var j=t(43324),v=t(29947);j.t1.register(j.PP,j.kc,j.FN,j.No,j.hE,j.m_,j.s$,j.dN);let f=({title:e="Monthly Sales",currentYearData:s=[],previousYearData:t=[],labels:l=[]})=>{let[n,i]=(0,a.useState)("current"),o=l.length>0?l:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],d=s.length>0?s:[12500,15e3,18e3,16500,21e3,22500,25e3,23e3,27e3,28500,3e4,32500],c=t.length>0?t:[1e4,12e3,14500,13e3,17500,19e3,21500,2e4,23500,25e3,26500,28e3];return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:e}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:`px-3 py-1 text-sm rounded-md ${"current"===n?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"}`,onClick:()=>i("current"),children:"This Year"}),(0,r.jsx)("button",{className:`px-3 py-1 text-sm rounded-md ${"previous"===n?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"}`,onClick:()=>i("previous"),children:"Last Year"})]})]}),(0,r.jsx)("div",{className:"h-80",children:(0,r.jsx)(v.N1,{data:{labels:o,datasets:[{label:"current"===n?"This Year":"Last Year",data:"current"===n?d:c,borderColor:"rgb(79, 70, 229)",backgroundColor:"rgba(79, 70, 229, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"rgb(79, 70, 229)",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(255, 255, 255, 0.9)",titleColor:"#1f2937",bodyColor:"#4b5563",borderColor:"#e5e7eb",borderWidth:1,padding:12,boxPadding:6,usePointStyle:!0,callbacks:{label:function(e){let s=e.dataset.label||"";return s&&(s+=": "),null!==e.parsed.y&&(s+=new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e.parsed.y)),s}}}},scales:{x:{grid:{display:!1},ticks:{color:"#6b7280"}},y:{grid:{color:"rgba(243, 244, 246, 1)"},ticks:{color:"#6b7280",callback:function(e){return"₱"+e.toLocaleString()}},beginAtZero:!0}}}})})]})};j.t1.register(j.Bs,j.m_,j.s$);let y=({title:e="Member Distribution",data:s=[]})=>{let t=s.length>0?s:[{label:"Starter",value:120,color:"#94a3b8"},{label:"Bronze",value:85,color:"#ca8a04"},{label:"Silver",value:65,color:"#94a3b8"},{label:"Gold",value:40,color:"#eab308"},{label:"Platinum",value:25,color:"#0ea5e9"},{label:"Diamond",value:10,color:"#6366f1"}],a={labels:t.map(e=>e.label),datasets:[{data:t.map(e=>e.value),backgroundColor:t.map(e=>e.color),borderColor:t.map(()=>"#ffffff"),borderWidth:2,hoverOffset:15}]};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:e}),(0,r.jsx)("div",{className:"h-80 relative",children:(0,r.jsx)(v.Fq,{data:a,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{boxWidth:15,padding:15,font:{size:12},generateLabels:function(e){let s=e.data;return s.labels.length&&s.datasets.length?s.labels.map((e,t)=>{let r=s.datasets[0],a=r.data[t],l=Math.round(a/r.data.reduce((e,s)=>e+s,0)*100);return{text:`${e}: ${l}% (${a})`,fillStyle:r.backgroundColor[t],strokeStyle:r.borderColor[t],lineWidth:r.borderWidth,hidden:!1,index:t}}):[]}}},tooltip:{backgroundColor:"rgba(255, 255, 255, 0.9)",titleColor:"#1f2937",bodyColor:"#4b5563",borderColor:"#e5e7eb",borderWidth:1,padding:12,boxPadding:6,usePointStyle:!0,callbacks:{label:function(e){let s=e.label||"",t=e.raw||0,r=Math.round(t/e.dataset.data.reduce((e,s)=>e+s,0)*100);return`${s}: ${r}% (${t} members)`}}}}}})})]})};j.t1.register(j.PP,j.kc,j.E8,j.hE,j.m_,j.s$);let N=({title:e="Rebates by Rank",data:s=[]})=>{let t=s.length>0?s:[{rank:"Starter",amount:25e3,color:"rgba(148, 163, 184, 0.8)"},{rank:"Bronze",amount:45e3,color:"rgba(202, 138, 4, 0.8)"},{rank:"Silver",amount:75e3,color:"rgba(148, 163, 184, 0.8)"},{rank:"Gold",amount:12e4,color:"rgba(234, 179, 8, 0.8)"},{rank:"Platinum",amount:18e4,color:"rgba(14, 165, 233, 0.8)"},{rank:"Diamond",amount:25e4,color:"rgba(99, 102, 241, 0.8)"}],a={labels:t.map(e=>e.rank),datasets:[{label:"Rebate Amount",data:t.map(e=>e.amount),backgroundColor:t.map(e=>e.color),borderColor:t.map(e=>e.color.replace("0.8","1")),borderWidth:1,borderRadius:4,barThickness:30}]};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:e}),(0,r.jsx)("div",{className:"h-80",children:(0,r.jsx)(v.yP,{data:a,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(255, 255, 255, 0.9)",titleColor:"#1f2937",bodyColor:"#4b5563",borderColor:"#e5e7eb",borderWidth:1,padding:12,boxPadding:6,usePointStyle:!0,callbacks:{label:function(e){let s=e.dataset.label||"";return s&&(s+=": "),null!==e.parsed.y&&(s+=new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:0,maximumFractionDigits:0}).format(e.parsed.y)),s}}}},scales:{x:{grid:{display:!1},ticks:{color:"#6b7280"}},y:{grid:{color:"rgba(243, 244, 246, 1)"},ticks:{color:"#6b7280",callback:function(e){return"₱"+e.toLocaleString()}},beginAtZero:!0}}}})})]})},w=({title:e,description:s,icon:t,href:a,iconBgColor:l,iconColor:n})=>(0,r.jsxs)(c(),{href:a,className:"bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:`p-3 rounded-full ${l} ${n} mr-4`,children:t}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:e})]}),(0,r.jsx)("p",{className:"text-gray-500 text-sm mb-4",children:s})]});function C(){let{data:e,status:s}=(0,l.useSession)();(0,n.useRouter)();let[t,d]=(0,a.useState)(!0),[p,j]=(0,a.useState)(!1),[v,C]=(0,a.useState)({totalUsers:0,totalProducts:0,totalPurchases:0,totalRebates:0,pendingRebates:0,processedRebates:0,totalRebateAmount:0}),[k,A]=(0,a.useState)({users:[],purchases:[]}),[P,R]=(0,a.useState)({products:[],distributors:[]}),[S,D]=(0,a.useState)(""),L=async()=>{d(!0),D("");try{let e=await fetch("/api/admin/stats");if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch admin statistics")}let s=await e.json();C(s.stats),A(s.recentActivity),R(s.topPerformers),d(!1)}catch(e){console.error("Error fetching admin stats:",e),D(e.message||"Failed to load admin dashboard data"),d(!1)}};return"loading"===s||t?(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-64",children:[(0,r.jsx)(m.hW,{className:"animate-spin text-blue-500 text-4xl mb-4"}),(0,r.jsx)("div",{className:"text-xl",children:"Loading Admin Dashboard..."})]})}):p?S?(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(m.BS8,{className:"text-red-500 text-5xl mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Error Loading Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:S}),(0,r.jsx)("button",{onClick:L,className:"inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Try Again"})]})})}):(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"Extreme Life Herbal Product Rewards - Admin"}),(0,r.jsxs)("div",{className:"mt-4 md:mt-0 flex items-center space-x-3",children:[(0,r.jsxs)("button",{onClick:L,className:"flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200",disabled:t,children:[(0,r.jsx)(m.hW,{className:`mr-2 ${t?"animate-spin":"hidden"}`}),(0,r.jsx)(m.DIg,{className:`mr-2 ${!t?"":"hidden"}`}),"Refresh Data"]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("button",{className:"flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",onClick:()=>{alert("Test mode toggle would be implemented here")},children:[(0,r.jsx)(m.vWM,{className:"mr-2"}),"Live Data",(0,r.jsx)(m.Vr3,{className:"ml-2"})]})})]})]}),(0,r.jsx)(g,{onFilterChange:e=>{console.log("Filters changed:",e)}}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)(x,{title:"Total Members",value:v.totalUsers,icon:(0,r.jsx)(m.YXz,{className:"h-6 w-6"}),borderColor:"border-blue-500",iconBgColor:"bg-blue-100",iconColor:"text-blue-500",percentageChange:12,footer:"From previous period"}),(0,r.jsx)(x,{title:"Active Products",value:v.totalProducts,icon:(0,r.jsx)(m.AsH,{className:"h-6 w-6"}),borderColor:"border-green-500",iconBgColor:"bg-green-100",iconColor:"text-green-500",percentageChange:5,footer:"From previous period"}),(0,r.jsx)(x,{title:"Monthly Sales",value:`₱${(1200*v.totalPurchases).toLocaleString()}`,icon:(0,r.jsx)(m.MxO,{className:"h-6 w-6"}),borderColor:"border-purple-500",iconBgColor:"bg-purple-100",iconColor:"text-purple-500",percentageChange:-3,footer:"Compared to last month"}),(0,r.jsx)(x,{title:"Total Rebates",value:`₱${v.totalRebateAmount.toLocaleString()}`,icon:(0,r.jsx)(m.YYR,{className:"h-6 w-6"}),borderColor:"border-orange-500",iconBgColor:"bg-orange-100",iconColor:"text-orange-500",percentageChange:8,footer:`${v.pendingRebates} pending / ${v.processedRebates} processed`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)(f,{})}),(0,r.jsx)(y,{})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,r.jsx)(N,{}),(0,r.jsx)(b,{})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,r.jsx)(u,{distributors:P.distributors||[],title:"Top Distributors",viewAllLink:"/admin/reports"}),(0,r.jsx)(h,{users:k.users||[],purchases:k.purchases||[],title:"Recent Activity"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(w,{title:"User Management",description:"Manage users, ranks, and genealogy structure.",icon:(0,r.jsx)(m.YXz,{className:"h-6 w-6"}),href:"/admin/users",iconBgColor:"bg-blue-100",iconColor:"text-blue-500"}),(0,r.jsx)(w,{title:"Product Management",description:"Add, edit, and manage products in the system.",icon:(0,r.jsx)(m.AsH,{className:"h-6 w-6"}),href:"/admin/products",iconBgColor:"bg-green-100",iconColor:"text-green-500"}),(0,r.jsx)(w,{title:"Rebate Management",description:"Process rebates and manage wallet transactions.",icon:(0,r.jsx)(m.lcY,{className:"h-6 w-6"}),href:"/admin/rebates",iconBgColor:"bg-purple-100",iconColor:"text-purple-500"}),(0,r.jsx)(w,{title:"Reports",description:"View sales reports, rebate reports, and user statistics.",icon:(0,r.jsx)(m.YYR,{className:"h-6 w-6"}),href:"/admin/reports",iconBgColor:"bg-orange-100",iconColor:"text-orange-500"}),(0,r.jsx)(w,{title:"Test Data Generator",description:"Generate test data for different user scenarios.",icon:(0,r.jsx)(m.kkc,{className:"h-6 w-6"}),href:"/admin/test-data",iconBgColor:"bg-indigo-100",iconColor:"text-indigo-500"})]})]})}):(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(m.BS8,{className:"text-yellow-500 text-5xl mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-red-600 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"You do not have permission to access the admin panel. Admin access is restricted to Diamond rank members only."}),(0,r.jsx)(c(),{href:"/dashboard",className:"inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Return to Dashboard"})]})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,8414,9567,3877,474,4859,9947,3024,7081],()=>t(680));module.exports=r})();
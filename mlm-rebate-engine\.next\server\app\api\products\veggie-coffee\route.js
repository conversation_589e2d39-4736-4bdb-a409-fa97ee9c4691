(()=>{var e={};e.id=1887,e.ids=[1887],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>c,Nh:()=>l,aP:()=>u});var a=r(96330),o=r(13581),i=r(85663),s=r(55511),n=r.n(s);async function c(e){return await i.Ay.hash(e,10)}function u(){let e=n().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new a.PrismaClient;let l={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new a.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await i.Ay.compare(e.password,r.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:s,...n}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var o=r(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var n=o?Object.getOwnPropertyDescriptor(e,i):null;n&&(n.get||n.set)?Object.defineProperty(a,i,n):a[i]=e[i]}return a.default=e,r&&r.set(e,a),a}(r(35426));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var a=r(96330);let o=global.prisma||new a.PrismaClient({log:["query"]})},38415:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{GET:()=>d,POST:()=>f});var o=r(96559),i=r(48088),s=r(37719),n=r(32190),c=r(19854),u=r(12909),l=r(31183);async function d(e){try{let e=await l.z.product.findFirst({where:{sku:"VEGGIE-COFFEE-124"},include:{category:!0,productImages:!0,productVariants:!0}});if(!e)return n.NextResponse.json({error:"Veggie Coffee product not found"},{status:404});let t=await l.z.productReview.findMany({where:{productId:e.id},include:{user:{select:{id:!0,name:!0,image:!0}}},orderBy:{createdAt:"desc"},take:10}),r=await l.z.product.findMany({where:{categoryId:e.categoryId,id:{not:e.id}},take:4});return n.NextResponse.json({product:e,reviews:t,relatedProducts:r})}catch(e){return console.error("Error fetching Veggie Coffee product:",e),n.NextResponse.json({error:"Failed to fetch Veggie Coffee product"},{status:500})}}async function f(e){try{let e,t=await (0,c.getServerSession)(u.Nh);if(!t||!t.user||"ADMIN"!==t.user.role)return n.NextResponse.json({error:"You must be an admin to create or update products"},{status:401});let r=await l.z.product.findFirst({where:{sku:"VEGGIE-COFFEE-124"}}),a=await l.z.productCategory.findFirst({where:{name:"Health Supplements"}});a||(a=await l.z.productCategory.create({data:{name:"Health Supplements",description:"Natural health supplements and wellness products",slug:"health-supplements"}}));let o={name:"Veggie Coffee 124 in 1",description:`Veggie Coffee 124 in 1 is a unique blend of 124 natural ingredients that provide a coffee-like taste without any caffeine. This healthy alternative to regular coffee offers numerous health benefits while satisfying your coffee cravings.

Key Benefits:
- Caffeine-free coffee alternative with natural flavor
- Contains 124 natural ingredients for comprehensive nutrition
- Supports detoxification when taken before meals
- Helps maintain good health when taken during meals
- Aids in weight management when taken after meals
- 100% natural and plant-based ingredients
- No artificial flavors, colors, or preservatives

Veggie Coffee 124 in 1 is perfect for those who love the taste of coffee but want to avoid caffeine and gain additional health benefits. Each sachet contains a carefully selected blend of vegetables, fruits, herbs, and other natural ingredients that work synergistically to support your overall health.

How to Use:
- For detoxification: Take one sachet before meals
- To maintain good health: Take one sachet during meals
- For weight management: Take one sachet after meals

Simply mix one sachet with hot water, stir well, and enjoy your healthy, delicious cup of Veggie Coffee.

Size: Box of 10 sachets (15g each)`,shortDescription:"A caffeine-free coffee alternative with 124 natural ingredients that support detoxification, health maintenance, and weight management.",sku:"VEGGIE-COFFEE-124",price:980,salePrice:850,cost:450,pointValue:40,stock:150,weight:150,dimensions:"15x10x5cm",featured:!0,categoryId:a.id,status:"ACTIVE",tags:["coffee alternative","caffeine-free","detox","weight management","natural","veggie"]};r?(e=await l.z.product.update({where:{id:r.id},data:o}),await l.z.productVariant.deleteMany({where:{productId:e.id}})):e=await l.z.product.create({data:o}),await l.z.productVariant.create({data:{productId:e.id,name:"10 Sachets Box",sku:"VEGGIE-COFFEE-124-10",price:980,salePrice:850,stock:100,isDefault:!0}}),await l.z.productVariant.create({data:{productId:e.id,name:"30 Sachets Box",sku:"VEGGIE-COFFEE-124-30",price:2800,salePrice:2500,stock:50,isDefault:!1}});let i=["/images/products/veggie-coffee/veggie-coffee-main.jpg","/images/products/veggie-coffee/veggie-coffee-lifestyle.jpg","/images/products/veggie-coffee/veggie-coffee-benefits.jpg"];await l.z.productImage.deleteMany({where:{productId:e.id}});for(let t=0;t<i.length;t++)await l.z.productImage.create({data:{productId:e.id,url:i[t],sortOrder:t,isDefault:0===t}});let s=await l.z.productReview.count({where:{productId:e.id}});if(0===s){let t=await l.z.user.findMany({take:5,orderBy:{id:"asc"}});if(t.length>0)for(let r of[{rating:5,title:"Great coffee alternative!",content:"I've been looking for a caffeine-free coffee alternative for a long time, and this is perfect! It tastes great and has so many health benefits.",userId:t[0].id},{rating:4,title:"Tastes surprisingly good",content:"I was skeptical at first, but this veggie coffee actually tastes quite good. I've been taking it after meals and have noticed some weight management benefits.",userId:t[1].id},{rating:5,title:"Amazing product",content:"I love that this has 124 natural ingredients. I feel healthier already after just two weeks of using it. Will definitely buy again!",userId:t[2].id},{rating:4,title:"Good for detox",content:"I've been taking this before meals as recommended for detoxification, and I can feel the difference. My digestion has improved significantly.",userId:t[3].id},{rating:5,title:"Perfect replacement for coffee",content:"As someone who had to give up caffeine for health reasons, this product has been a lifesaver. It satisfies my coffee cravings without the negative effects.",userId:t[4].id}])await l.z.productReview.create({data:{productId:e.id,userId:r.userId,rating:r.rating,title:r.title,content:r.content}})}return n.NextResponse.json({success:!0,product:e})}catch(e){return console.error("Error creating/updating Veggie Coffee product:",e),n.NextResponse.json({error:"Failed to create/update Veggie Coffee product"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/products/veggie-coffee/route",pathname:"/api/products/veggie-coffee",filename:"route",bundlePath:"app/api/products/veggie-coffee/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\veggie-coffee\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:w}=p;function m(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8044,3112],()=>r(38415));module.exports=a})();
"use strict";(()=>{var e={};e.id=5309,e.ids=[5309],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},18019:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>b,serverHooks:()=>M,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>h});var n={};t.r(n),t.d(n,{GET:()=>g,POST:()=>v});var s=t(96559),o=t(48088),a=t(37719),i=t(31183),u=t(32190),p=t(19854),l=t(12909),c=t(27942),m=t(77352),d=t(70762);let f=d.z.object({mlmStructure:d.z.enum(["binary","unilevel"]).optional(),pvCalculation:d.z.enum(["percentage","fixed"]).optional(),performanceBonusEnabled:d.z.boolean().optional(),monthlyCutoffDay:d.z.number().int().min(1).max(31).optional(),binaryMaxDepth:d.z.number().int().min(1).max(10).optional(),unilevelMaxDepth:d.z.number().int().min(1).max(10).optional()}),x=d.z.object({name:d.z.string().min(1),minSales:d.z.number().positive(),maxSales:d.z.number().positive().nullable(),bonusType:d.z.enum(["percentage","fixed"]),percentage:d.z.number().min(0).max(100).optional(),fixedAmount:d.z.number().min(0).optional(),active:d.z.boolean().optional()});async function g(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to view MLM configuration"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let n=await i.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!n)return u.NextResponse.json({error:"User not found"},{status:404});if(!(n.rankId>=6))return u.NextResponse.json({error:"You do not have permission to view MLM configuration"},{status:403});let s=new URL(e.url),o="true"===s.searchParams.get("includePerformanceTiers"),a=await (0,c.Xr)(),m=null;return o&&(m=await (0,c.DW)(!1)),u.NextResponse.json({config:a,performanceTiers:m})}catch(e){return console.error("Error fetching MLM configuration:",e),u.NextResponse.json({error:"Failed to fetch MLM configuration"},{status:500})}}async function v(e){try{let r=await (0,p.getServerSession)(l.Nh);if(!r||!r.user)return u.NextResponse.json({error:"You must be logged in to update MLM configuration"},{status:401});let t=r.user.email;if(!t)return u.NextResponse.json({error:"User email not found in session"},{status:400});let n=await i.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!n)return u.NextResponse.json({error:"User not found"},{status:404});if(!(n.rankId>=6))return u.NextResponse.json({error:"You do not have permission to update MLM configuration"},{status:403});let s=await e.json();switch(s.action||"updateConfig"){case"updateConfig":let o=f.safeParse(s.config);if(!o.success)return u.NextResponse.json({error:o.error.errors},{status:400});let a=o.data;a.mlmStructure&&await (0,m.EA)(a.mlmStructure);let d=await (0,c.cs)(a);return u.NextResponse.json({config:d,message:"MLM configuration updated successfully"});case"createPerformanceTier":let g=x.safeParse(s.tier);if(!g.success)return u.NextResponse.json({error:g.error.errors},{status:400});let v=g.data,b=await (0,c.QX)({name:v.name,minSales:v.minSales,maxSales:v.maxSales,bonusType:v.bonusType,percentage:v.percentage||0,fixedAmount:v.fixedAmount||0,active:void 0===v.active||v.active});return u.NextResponse.json({tier:b,message:"Performance bonus tier created successfully"});case"updatePerformanceTier":let j=s.tierId;if(!j||"number"!=typeof j)return u.NextResponse.json({error:"Invalid tier ID"},{status:400});let h=x.partial().safeParse(s.tier);if(!h.success)return u.NextResponse.json({error:h.error.errors},{status:400});let M=h.data,R=await (0,c.J0)(j,M);return u.NextResponse.json({tier:R,message:"Performance bonus tier updated successfully"});default:return u.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("Error updating MLM configuration:",e),u.NextResponse.json({error:"Failed to update MLM configuration"},{status:500})}}let b=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/mlm-config/route",pathname:"/api/mlm-config",filename:"route",bundlePath:"app/api/mlm-config/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\mlm-config\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:j,workUnitAsyncStorage:h,serverHooks:M}=b;function R(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:h})}},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4243,580,8044,3112,8381,2610,7352],()=>t(18019));module.exports=n})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8563],{25177:(e,s,t)=>{Promise.resolve().then(t.bind(t,72489))},72489:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(95155),l=t(12115),i=t(12108),n=t(87747),r=t(29911),d=t(6874),c=t.n(d);let o=(0,t(55028).default)(()=>Promise.all([t.e(1294),t.e(8702),t.e(6113),t.e(6808),t.e(2406),t.e(3131)]).then(t.bind(t,35512)),{loadableGenerated:{webpack:()=>[35512]},ssr:!1,loading:()=>(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading interactive visualization..."})]})});function u(){let{data:e,status:s}=(0,i.useSession)(),[t,d]=(0,l.useState)(void 0),[u,h]=(0,l.useState)(!1),{data:x,isLoading:m}=(0,n.I)({queryKey:["user"],queryFn:async()=>{var s;if(!(null==e||null==(s=e.user)?void 0:s.email))return null;let t=await fetch("/api/users/me");if(!t.ok)throw Error("Failed to fetch user data");return await t.json()},enabled:"authenticated"===s});x&&!t&&d(x.id);let g=async e=>{console.log("Saving changes:",e),alert("Changes saved successfully!")};return"loading"===s||m?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading user data..."})]})}):"unauthenticated"===s?(0,a.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-96",children:[(0,a.jsx)(r.BS8,{className:"text-yellow-500 text-4xl mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold mb-2",children:"Authentication Required"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to view your genealogy tree."}),(0,a.jsx)(c(),{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Sign In"})]})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Interactive Genealogy Tree"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Customize and interact with your genealogy tree"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{onClick:()=>h(!u),className:"px-4 py-2 rounded-md flex items-center ".concat(u?"bg-green-600 text-white hover:bg-green-700":"bg-blue-600 text-white hover:bg-blue-700"),children:[(0,a.jsx)(r.uO9,{className:"mr-2"}),u?"Exit Edit Mode":"Enter Edit Mode"]}),(0,a.jsxs)(c(),{href:"/genealogy",className:"flex items-center text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(r.QVr,{className:"mr-1"}),"Back to Genealogy"]})]})]}),u&&(0,a.jsxs)("div",{className:"mb-6 bg-yellow-50 border border-yellow-300 p-4 rounded-md",children:[(0,a.jsxs)("h3",{className:"text-yellow-800 font-medium flex items-center mb-2",children:[(0,a.jsx)(r.__w,{className:"mr-2"}),"Edit Mode Enabled"]}),(0,a.jsx)("p",{className:"text-yellow-700 mb-2",children:"You can now drag and drop users to reorganize your genealogy tree. Changes will not be permanent until you save them."}),(0,a.jsxs)("ul",{className:"list-disc list-inside text-yellow-700 space-y-1",children:[(0,a.jsx)("li",{children:"Drag a user to a new parent to change their upline"}),(0,a.jsx)("li",{children:"Click the edit button on a user to modify their details"}),(0,a.jsx)("li",{children:"Click the add button to add a new user under a parent"}),(0,a.jsx)("li",{children:"Click the delete button to remove a user (and their downline)"})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:t?(0,a.jsx)(o,{userId:t,maxLevel:3,initialLayout:"vertical",allowEditing:u,onSaveChanges:g}):(0,a.jsxs)("div",{className:"flex items-center justify-center h-96",children:[(0,a.jsx)(r.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading genealogy data..."})]})}),(0,a.jsxs)("div",{className:"mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-blue-800 mb-2",children:"About Interactive Genealogy"}),(0,a.jsx)("p",{className:"text-blue-700 mb-2",children:"This interactive genealogy tree provides advanced customization and interaction features:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"Customize the appearance with different themes and layouts"}),(0,a.jsx)("li",{children:"Adjust node spacing, connection styles, and other visual properties"}),(0,a.jsx)("li",{children:"Drag and drop users to reorganize your network (in edit mode)"}),(0,a.jsx)("li",{children:"Add, edit, or remove users directly from the visualization"}),(0,a.jsx)("li",{children:"Save your changes to update your actual genealogy structure"})]}),(0,a.jsx)("p",{className:"text-blue-700 mt-2",children:"Use the settings panel to customize the visualization to your preferences."})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,5557,6967,7747,8441,1684,7358],()=>s(25177)),_N_E=e.O()}]);
"use strict";exports.id=7352,exports.ids=[7352],exports.modules={27942:(e,t,a)=>{a.d(t,{$v:()=>f,A4:()=>l,DW:()=>i,J0:()=>s,QX:()=>u,Xr:()=>r,Xu:()=>c,_8:()=>d,cs:()=>o,cv:()=>m,qd:()=>y});var n=a(31183);async function r(){let e=(await n.z.systemConfig.findMany()).reduce((e,t)=>(e[t.key]=t.value,e),{});return{mlmStructure:e.mlm_structure||"binary",pvCalculation:e.pv_calculation||"percentage",performanceBonusEnabled:"true"===e.performance_bonus_enabled,monthlyCutoffDay:parseInt(e.monthly_cutoff_day||"25"),binaryMaxDepth:parseInt(e.binary_max_depth||"6"),unilevelMaxDepth:parseInt(e.unilevel_max_depth||"6")}}async function o(e){let t=[];for(let a of(void 0!==e.mlmStructure&&t.push({key:"mlm_structure",value:e.mlmStructure}),void 0!==e.pvCalculation&&t.push({key:"pv_calculation",value:e.pvCalculation}),void 0!==e.performanceBonusEnabled&&t.push({key:"performance_bonus_enabled",value:e.performanceBonusEnabled.toString()}),void 0!==e.monthlyCutoffDay&&t.push({key:"monthly_cutoff_day",value:e.monthlyCutoffDay.toString()}),void 0!==e.binaryMaxDepth&&t.push({key:"binary_max_depth",value:e.binaryMaxDepth.toString()}),void 0!==e.unilevelMaxDepth&&t.push({key:"unilevel_max_depth",value:e.unilevelMaxDepth.toString()}),t))await n.z.systemConfig.update({where:{key:a.key},data:{value:a.value,updatedAt:new Date}});return await r()}async function i(e=!0){return await n.z.performanceBonusTier.findMany({where:e?{active:!0}:void 0,orderBy:{minSales:"asc"}})}async function u(e){return await n.z.performanceBonusTier.create({data:e})}async function s(e,t){return await n.z.performanceBonusTier.update({where:{id:e},data:{...t,updatedAt:new Date}})}async function l(e,t){let a={};return void 0!==e&&(a.year=e),void 0!==t&&(a.month=t),await n.z.monthlyCutoff.findMany({where:a,orderBy:[{year:"desc"},{month:"desc"}]})}async function c(e){if(await n.z.monthlyCutoff.findUnique({where:{year_month:{year:e.year,month:e.month}}}))throw Error(`A cutoff already exists for ${e.year}-${e.month}`);return await n.z.monthlyCutoff.create({data:{year:e.year,month:e.month,cutoffDay:e.cutoffDay,notes:e.notes,status:"pending"}})}async function d(e,t){return await n.z.monthlyCutoff.update({where:{id:e},data:{...t,updatedAt:new Date}})}async function m(){let e=new Date,t=e.getFullYear(),a=e.getMonth()+1,r=await n.z.monthlyCutoff.findUnique({where:{year_month:{year:t,month:a}}});return r||(r=await n.z.monthlyCutoff.findFirst({where:{OR:[{year:t,month:{gt:a}},{year:{gt:t}}]},orderBy:[{year:"asc"},{month:"asc"}]}))}async function f(e,t){return"fixed"===(await r()).pvCalculation?t:.5*e}async function y(e){if(!(await r()).performanceBonusEnabled)return 0;let t=(await i(!0)).find(t=>e>=t.minSales&&(null===t.maxSales||e<=t.maxSales));return t?"percentage"===t.bonusType?e*(t.percentage/100):t.fixedAmount:0}},77352:(e,t,a)=>{a.d(t,{EA:()=>h,vm:()=>p});var n=a(31183),r=a(27942),o=a(47697);async function i(e,t=6){let a=await n.z.user.findUnique({where:{id:e},include:{rank:!0}});if(!a)throw Error(`User with ID ${e} not found`);return await u(a,0,t)}async function u(e,t,a){if(t>a)return{user:e,level:t,children:[]};let r={user:e,level:t,children:[]},o=await n.z.user.findMany({where:{uplineId:e.id},include:{rank:!0}});if(o.length>0)for(let e of o){let n=await u(e,t+1,a);r.children.push(n)}return r}async function s(e,t=6){let a=await i(e,t),n=[];return!function e(t){for(let a of(n.push({user:t.user,level:t.level}),t.children))e(a)}(a),n}async function l(e,t,a){return(await n.z.purchase.findMany({where:{userId:e,createdAt:{gte:t,lte:a},status:"completed"},select:{totalPV:!0}})).reduce((e,t)=>e+t.totalPV,0)}async function c(e,t,a){let r=(await s(e)).map(e=>e.user.id);return(await n.z.purchase.findMany({where:{userId:{in:r},createdAt:{gte:t,lte:a},status:"completed"},select:{totalPV:!0}})).reduce((e,t)=>e+t.totalPV,0)}async function d(e,t,a){let o=await (0,r.Xr)(),i=await n.z.commissionRate.findMany({where:{active:!0}}),u=i.find(e=>"direct_referral"===e.type),l=i.filter(e=>"level_commission"===e.type).reduce((e,t)=>(null!==t.level&&(e[t.level]=t),e),{}),c={directReferralBonus:0,levelCommissions:0,performanceBonus:0,totalCommission:0,breakdown:{directReferral:{amount:0,details:[]},levelCommissions:{amount:0,byLevel:{},details:[]},performanceBonus:{amount:0,details:null}}},d=await n.z.user.findMany({where:{uplineId:e,createdAt:{gte:t,lte:a}},select:{id:!0,name:!0,email:!0,createdAt:!0}});if(u){let e="fixed"===u.rewardType?u.fixedAmount:0;c.directReferralBonus=e*d.length,c.breakdown.directReferral={amount:c.directReferralBonus,details:d.map(t=>({userId:t.id,name:t.name,email:t.email,joinDate:t.createdAt,bonus:e}))}}let m=(await s(e,o.unilevelMaxDepth)).reduce((e,t)=>{let a=t.level;return e[a]||(e[a]=[]),e[a].push(t.user),e},{}),f=0,y={},h=[];for(let e=1;e<=o.unilevelMaxDepth;e++){let r=m[e]||[],o=l[e];if(o&&r.length>0){let i=r.map(e=>e.id),u=await n.z.purchase.findMany({where:{userId:{in:i},createdAt:{gte:t,lte:a},status:"completed"},include:{user:!0,product:!0}}),s=0;for(let t of u){let a=0;s+=a="percentage"===o.rewardType?t.totalPV*(o.percentage/100):o.fixedAmount,h.push({level:e,purchaseId:t.id,userId:t.userId,userName:t.user.name,productName:t.product.name,pv:t.totalPV,commissionAmount:a})}f+=s,y[e]=s}}if(c.levelCommissions=f,c.breakdown.levelCommissions={amount:f,byLevel:y,details:h},o.performanceBonusEnabled){let o=(await n.z.purchase.aggregate({where:{userId:e,createdAt:{gte:t,lte:a},status:"completed"},_sum:{totalAmount:!0}}))._sum.totalAmount||0,i=await (0,r.qd)(o);c.performanceBonus=i,c.breakdown.performanceBonus={amount:i,details:{totalSales:o,bonusAmount:i}}}return c.totalCommission=c.directReferralBonus+c.levelCommissions+c.performanceBonus,c}async function m(e,t,a,r){return await n.z.monthlyPerformance.upsert({where:{userId_year_month:{userId:e,year:t,month:a}},update:r,create:{userId:e,year:t,month:a,...r,leftLegPV:0,rightLegPV:0,groupVolumeBonus:0}})}async function f(e,t,a){let n=new Date(t,a-1,1),r=new Date(t,a,0,23,59,59,999),o=await d(e,n,r),i=await l(e,n,r),u=await c(e,n,r);return await m(e,t,a,{personalPV:i,totalGroupPV:u,directReferralBonus:o.directReferralBonus,levelCommissions:o.levelCommissions,performanceBonus:o.performanceBonus,totalEarnings:o.totalCommission}),o}async function y(e,t,a){return"binary"===(await (0,r.Xr)()).mlmStructure?await o.Qx(e,t,a):await f(e,t,a)}async function h(e){return await n.z.systemConfig.update({where:{key:"mlm_structure"},data:{value:e,updatedAt:new Date}}),await (0,r.Xr)()}async function p(e,t){try{let a=await n.z.monthlyCutoff.findUnique({where:{year_month:{year:e,month:t}}});if(!a)throw Error(`No cutoff record found for ${e}-${t}`);await n.z.monthlyCutoff.update({where:{id:a.id},data:{status:"processing",updatedAt:new Date}});let r=await n.z.user.findMany({select:{id:!0,name:!0,email:!0}}),o=[];for(let a of r)try{let n=await y(a.id,e,t);o.push({userId:a.id,name:a.name,email:a.email,earnings:n,success:!0})}catch(e){console.error(`Error processing user ${a.id}:`,e),o.push({userId:a.id,name:a.name,email:a.email,error:e instanceof Error?e.message:"Unknown error",success:!1})}return await n.z.monthlyCutoff.update({where:{id:a.id},data:{status:"completed",processedAt:new Date,updatedAt:new Date,notes:`Processed ${o.length} users. ${o.filter(e=>e.success).length} succeeded, ${o.filter(e=>!e.success).length} failed.`}}),{cutoff:a,results:o,summary:{totalUsers:o.length,succeeded:o.filter(e=>e.success).length,failed:o.filter(e=>!e.success).length}}}catch(a){throw console.error(`Error processing monthly commissions for ${e}-${t}:`,a),await n.z.monthlyCutoff.update({where:{year_month:{year:e,month:t}},data:{status:"failed",updatedAt:new Date,notes:`Error: ${a instanceof Error?a.message:"Unknown error"}`}}),a}}}};
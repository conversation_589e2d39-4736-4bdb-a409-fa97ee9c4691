(()=>{var e={};e.id=762,e.ids=[762],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{Er:()=>l,Nh:()=>d,aP:()=>u});var a=t(96330),o=t(13581),s=t(85663),i=t(55511),n=t.n(i);async function l(e){return await s.Ay.hash(e,10)}function u(){let e=n().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new a.PrismaClient;let d={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new a.PrismaClient,t=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!t)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",t.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await s.Ay.compare(e.password,t.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",t.id);let{password:i,...n}=t;return{id:t.id.toString(),email:t.email,name:t.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var o=t(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(r);if(t&&t.has(e))return t.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var n=o?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(a,s,n):a[s]=e[s]}return a.default=e,t&&t.set(e,a),a}(t(35426));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});var a=t(96330);let o=global.prisma||new a.PrismaClient({log:["query"]})},41713:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{GET:()=>c,POST:()=>p});var o=t(96559),s=t(48088),i=t(37719),n=t(32190),l=t(19854),u=t(12909),d=t(31183);async function c(e){try{let e=await d.z.product.findFirst({where:{sku:"BIOGEN-SHIELD-SOAP"},include:{category:!0,productImages:!0,productVariants:!0}});if(!e)return n.NextResponse.json({error:"Biogen Shield Herbal Care Soap product not found"},{status:404});let r=await d.z.productReview.findMany({where:{productId:e.id},include:{user:{select:{id:!0,name:!0,image:!0}}},orderBy:{createdAt:"desc"},take:10}),t=await d.z.product.findMany({where:{categoryId:e.categoryId,id:{not:e.id}},take:4});return n.NextResponse.json({product:e,reviews:r,relatedProducts:t})}catch(e){return console.error("Error fetching Biogen Shield Herbal Care Soap product:",e),n.NextResponse.json({error:"Failed to fetch Biogen Shield Herbal Care Soap product"},{status:500})}}async function p(e){try{let e,r=await (0,l.getServerSession)(u.Nh);if(!r||!r.user||"ADMIN"!==r.user.role)return n.NextResponse.json({error:"You must be an admin to create or update products"},{status:401});let t=await d.z.product.findFirst({where:{sku:"BIOGEN-SHIELD-SOAP"}}),a=await d.z.productCategory.findFirst({where:{name:"Personal Care"}});a||(a=await d.z.productCategory.create({data:{name:"Personal Care",description:"Personal care and hygiene products for daily use",slug:"personal-care"}}));let o={name:"Biogen Shield Herbal Care Soap",description:`Biogen Shield Herbal Care Soap is a premium herbal soap formulated with natural ingredients to provide multiple benefits for your skin. This specially crafted soap offers a comprehensive solution for various skin concerns while maintaining the skin's natural balance.

Key Benefits:
- Whitens, renews, and nourishes skin
- Effectively removes body odor
- Acts as a natural deodorizer
- Suitable for feminine wash
- Anti-bacterial properties to protect against germs
- Made with natural herbal ingredients
- Gentle enough for daily use
- Free from harsh chemicals
- Suitable for all skin types

Biogen Shield Herbal Care Soap combines the power of natural herbs with modern skincare science to deliver a soap that not only cleanses but also improves your skin's overall health and appearance. The unique formulation helps to remove impurities while maintaining your skin's natural moisture.

The soap's anti-bacterial properties help protect against harmful germs while its natural deodorizing effect keeps you feeling fresh throughout the day. Regular use helps to gradually lighten and even out skin tone, giving you a more radiant complexion.

Directions for Use:
- For body: Lather on wet skin, massage gently, and rinse thoroughly.
- For feminine wash: Use as directed by a healthcare professional.
- For best results, use daily.

Size: 135g per bar`,shortDescription:"A premium herbal soap that whitens, renews, and nourishes skin while providing anti-bacterial protection and deodorizing benefits.",sku:"BIOGEN-SHIELD-SOAP",price:120,salePrice:99,cost:60,pointValue:10,stock:200,weight:135,dimensions:"8x5x2cm",featured:!0,categoryId:a.id,status:"ACTIVE",tags:["soap","herbal","personal care","anti-bacterial","whitening","deodorizer"]};t?(e=await d.z.product.update({where:{id:t.id},data:o}),await d.z.productVariant.deleteMany({where:{productId:e.id}})):e=await d.z.product.create({data:o}),await d.z.productVariant.create({data:{productId:e.id,name:"Single Bar",sku:"BIOGEN-SHIELD-SOAP-SINGLE",price:120,salePrice:99,stock:100,isDefault:!0}}),await d.z.productVariant.create({data:{productId:e.id,name:"3-Pack",sku:"BIOGEN-SHIELD-SOAP-3PACK",price:330,salePrice:280,stock:70,isDefault:!1}}),await d.z.productVariant.create({data:{productId:e.id,name:"6-Pack",sku:"BIOGEN-SHIELD-SOAP-6PACK",price:650,salePrice:550,stock:30,isDefault:!1}});let s=["/images/products/shield-soap/shield-soap-main.jpg","/images/products/shield-soap/shield-soap-benefits.jpg","/images/products/shield-soap/shield-soap-packaging.jpg"];await d.z.productImage.deleteMany({where:{productId:e.id}});for(let r=0;r<s.length;r++)await d.z.productImage.create({data:{productId:e.id,url:s[r],sortOrder:r,isDefault:0===r}});let i=await d.z.productReview.count({where:{productId:e.id}});if(0===i){let r=await d.z.user.findMany({take:5,orderBy:{id:"asc"}});if(r.length>0)for(let t of[{rating:5,title:"Amazing for my sensitive skin!",content:"I've tried many soaps that claim to be gentle, but this is the first one that actually delivers. My skin feels soft and clean without any irritation.",userId:r[0].id},{rating:4,title:"Great deodorizing effect",content:"I work out a lot and this soap has been excellent at keeping body odor away. The scent is pleasant but not overpowering.",userId:r[1].id},{rating:5,title:"Noticeable skin improvement",content:"After using this soap for a month, I've noticed my skin tone is more even and some dark spots have faded. Will definitely buy again!",userId:r[2].id},{rating:4,title:"Good for the whole family",content:"We've switched our entire family to this soap. It's gentle enough for the kids but effective for adults too. The anti-bacterial properties give me peace of mind.",userId:r[3].id},{rating:5,title:"Perfect for feminine use",content:"I was looking for a gentle soap for feminine use and this has been perfect. No irritation and keeps me feeling fresh all day.",userId:r[4].id}])await d.z.productReview.create({data:{productId:e.id,userId:t.userId,rating:t.rating,title:t.title,content:t.content}})}return n.NextResponse.json({success:!0,product:e})}catch(e){return console.error("Error creating/updating Biogen Shield Herbal Care Soap product:",e),n.NextResponse.json({error:"Failed to create/update Biogen Shield Herbal Care Soap product"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/products/shield-soap/route",pathname:"/api/products/shield-soap",filename:"route",bundlePath:"app/api/products/shield-soap/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\shield-soap\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:m}=h;function w(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4243,580,8044,3112],()=>t(41713));module.exports=a})();
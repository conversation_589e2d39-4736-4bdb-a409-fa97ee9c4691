"use strict";exports.id=632,exports.ids=[632],exports.modules={12269:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},19854:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var n=r(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var o=n?Object.getOwnPropertyDescriptor(e,s):null;o&&(o.get||o.set)?Object.defineProperty(a,s,o):a[s]=e[s]}return a.default=e,r&&r.set(e,a),a}(r(35426));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},81437:(e,t,r)=>{let a;r.d(t,{LF:()=>function e(t,r){m();var s,i,o,c,l,f,h,u=r||{};if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer)return e(new Uint8Array(t),((u=eW(u)).type="array",u));"undefined"!=typeof Uint8Array&&t instanceof Uint8Array&&!u.type&&(u.type="undefined"!=typeof Deno?"buffer":"array");var d=t,p=[0,0,0,0],g=!1;if(u.cellStyles&&(u.cellNF=!0,u.sheetStubs=!0),sF={},u.dateNF&&(sF.dateNF=u.dateNF),u.type||(u.type=k&&Buffer.isBuffer(t)?"buffer":"base64"),"file"==u.type&&(u.type=k?"buffer":"binary",d=function(e){if(void 0!==a)return a.readFileSync(e);if("undefined"!=typeof Deno)return Deno.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("Cannot access file "+e)}(t),"undefined"==typeof Uint8Array||k||(u.type="array")),"string"==u.type&&(g=!0,u.type="binary",u.codepage=65001,d=t.match(/[^\x00-\x7F]/)?tv(t):t),"array"==u.type&&"undefined"!=typeof Uint8Array&&t instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var v=new Uint8Array(new ArrayBuffer(3));if(v.foo="bar",!v.foo)return(u=eW(u)).type="array",e(I(d),u)}switch((p=i3(d,u))[0]){case 208:if(207===p[1]&&17===p[2]&&224===p[3]&&161===p[4]&&177===p[5]&&26===p[6]&&225===p[7])return o=ey.read(d,u),c=u,ey.find(o,"EncryptedPackage")?function(e,t){var r=t||{},a="Workbook",n=ey.find(e,a);try{if(a="/!DataSpaces/Version",!(n=ey.find(e,a))||!n.content||(s=n.content,(i={}).id=s.read_shift(0,"lpp4"),i.R=nT(s,4),i.U=nT(s,4),i.W=nT(s,4),a="/!DataSpaces/DataSpaceMap",!(n=ey.find(e,a))||!n.content))throw Error("ECMA-376 Encrypted file missing "+a);var s,i,o=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(function(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}(e));return t}(n.content);if(1!==o.length||1!==o[0].comps.length||0!==o[0].comps[0].t||"StrongEncryptionDataSpace"!==o[0].name||"EncryptedPackage"!==o[0].comps[0].v)throw Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);var c=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}(n.content);if(1!=c.length||"StrongEncryptionTransform"!=c[0])throw Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);!function(e){var t,r=(t={},e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=nT(e,4),t.U=nT(e,4),t.W=nT(e,4),t);if(r.ename=e.read_shift(0,"8lpp4"),r.blksz=e.read_shift(4),r.cmode=e.read_shift(4),4!=e.read_shift(4))throw Error("Bad !Primary record")}(n.content)}catch(e){}if(a="/EncryptionInfo",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);var l=function(e){var t,r,a,n,s=nT(e);switch(s.Minor){case 2:return[s.Minor,function(e){if((63&e.read_shift(4))!=36)throw Error("EncryptionInfo mismatch");var t=e.read_shift(4);return{t:"Std",h:nE(e,t),v:nS(e,e.length-e.l)}}(e,s)];case 3:return[s.Minor,function(){throw Error("File is password-protected: ECMA-376 Extensible")}(e,s)];case 4:return[s.Minor,(t=e,r=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"],t.l+=4,a=t.read_shift(t.length-t.l,"utf8"),n={},a.replace(e6,function(e){var t=e9(e);switch(te(t[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":r.forEach(function(e){n[e]=t[e]});break;case"<dataIntegrity":n.encryptedHmacKey=t.encryptedHmacKey,n.encryptedHmacValue=t.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":n.encs=[];break;case"<keyEncryptor":n.uri=t.uri;break;case"<encryptedKey":n.encs.push(t);break;default:throw t[0]}}),n)]}throw Error("ECMA-376 Encrypted file unrecognized Version: "+s.Minor)}(n.content);if(a="/EncryptedPackage",!(n=ey.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);if(4==l[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(l[1],n.content,r.password||"",r);if(2==l[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(l[1],n.content,r.password||"",r);throw Error("File is password-protected")}(o,c):iw(o,c);break;case 9:if(p[1]<=8)return iw(d,u);break;case 60:return id(d,u);case 73:if(73===p[1]&&42===p[2]&&0===p[3])throw Error("TIFF Image File is not a spreadsheet");if(68===p[1]){var b=d,w=u,T=w||{},E=!!T.WTF;T.WTF=!0;try{var S=nr.to_workbook(b,T);return T.WTF=E,S}catch(e){if(T.WTF=E,!e.message.match(/SYLK bad record ID/)&&E)throw e;return ns.to_workbook(b,w)}}break;case 84:if(65===p[1]&&66===p[2]&&76===p[3])return na.to_workbook(d,u);break;case 80:return 75===p[1]&&p[2]<9&&p[3]<9?(s=d,(i=u||{}).type||(i.type=k&&Buffer.isBuffer(s)?"buffer":"base64"),function(e,t){if(ew(),i0(t=t||{}),eY(e,"META-INF/manifest.xml")||eY(e,"objectdata.xml")){var r=e,a=t;a=a||{},eY(r,"META-INF/manifest.xml")&&function(e,t){for(var r,a,n=tO(e);r=tR.exec(n);)switch(r[3]){case"manifest":break;case"file-entry":if("/"==(a=e9(r[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==a.type)throw Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw r}}(eJ(r,"META-INF/manifest.xml"),a);var n=eq(r,"content.xml");if(!n)throw Error("Missing content.xml in ODS / UOF file");var s=iI(tg(n),a);return eY(r,"meta.xml")&&(s.Props=at(eJ(r,"meta.xml"))),s}if(eY(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw Error("NUMBERS file parsing requires Uint8Array support");if(e.FileIndex)return iQ(e);var i,o,c,l,f,h,u,d,p,m,g,v,b,w,T=ey.utils.cfb_new();return eZ(e).forEach(function(t){eQ(T,t,function e(t,r,a){if(!a)return eX(eK(t,r));if(!r)return null;try{return e(t,r)}catch(e){return null}}(e,t))}),iQ(T)}if(!eY(e,"[Content_Types].xml")){if(eY(e,"index.xml.gz"))throw Error("Unsupported NUMBERS 08 file");if(eY(e,"index.xml"))throw Error("Unsupported NUMBERS 09 file");throw Error("Unsupported ZIP file")}var E=eZ(e),S=function(e){var t=r1();if(!e||!e.match)return t;var r={};if((e.match(e6)||[]).forEach(function(e){var a=e9(e);switch(a[0].replace(e8,"<")){case"<?xml":break;case"<Types":t.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[a.Extension]=a.ContentType;break;case"<Override":void 0!==t[rZ[a.ContentType]]&&t[rZ[a.ContentType]].push(a.PartName)}}),t.xmlns!==tI.CT)throw Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}(eq(e,"[Content_Types].xml")),y=!1;if(0===S.workbooks.length&&eJ(e,v="xl/workbook.xml",!0)&&S.workbooks.push(v),0===S.workbooks.length){if(!eJ(e,v="xl/workbook.bin",!0))throw Error("Could not find workbook");S.workbooks.push(v),y=!0}"bin"==S.workbooks[0].slice(-3)&&(y=!0);var k={},x={};if(!t.bookSheets&&!t.bookProps){if(sD=[],S.sst)try{sD=function(e,t,r){if(".bin"===t.slice(-4)){var a,n;return a=[],n=!1,rs(e,function(e,t,s){switch(s){case 159:a.Count=e[0],a.Unique=e[1];break;case 19:a.push(e);break;case 160:return!0;case 35:n=!0;break;case 36:n=!1;break;default:if(t.T,!n||r.WTF)throw Error("Unexpected record 0x"+s.toString(16))}}),a}return function(e,t){var r=[],a="";if(!e)return r;var n=e.match(nd);if(n){a=n[2].replace(np,"").split(nm);for(var s=0;s!=a.length;++s){var i=nu(a[s].trim(),t);null!=i&&(r[r.length]=i)}r.Count=(n=e9(n[1])).count,r.Unique=n.uniqueCount}return r}(e,r)}(eJ(e,i4(S.sst)),S.sst,t)}catch(e){if(t.WTF)throw e}t.cellStyles&&S.themes.length&&(i=eq(e,S.themes[0].replace(/^\//,""),!0)||"",S.themes[0],k=n2(i,t)),S.style&&(x=function(e,t,r,a){if(".bin"===t.slice(-4)){var n={};for(var s in n.NumberFmt=[],z)n.NumberFmt[s]=z[s];n.CellXf=[],n.Fonts=[];var i=[],o=!1;return rs(e,function(e,t,s){switch(s){case 44:n.NumberFmt[e[0]]=e[1],ev(e[1],e[0]);break;case 43:n.Fonts.push(e),null!=e.color.theme&&r&&r.themeElements&&r.themeElements.clrScheme&&(e.color.rgb=nO(r.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==i[i.length-1]&&n.CellXf.push(e);break;case 35:o=!0;break;case 36:o=!1;break;case 37:i.push(s),o=!0;break;case 38:i.pop(),o=!1;break;default:if(t.T>0)i.push(s);else if(t.T<0)i.pop();else if(!o||a.WTF&&37!=i[i.length-1])throw Error("Unexpected record 0x"+s.toString(16))}}),n}return nV(e,r,a)}(eJ(e,i4(S.style)),S.style,k,t))}S.links.map(function(r){try{var a=r3(eq(e,r4(i4(r))),r),n=eJ(e,i4(r)),s=0,i=r,o=t;if(".bin"===i.slice(-4)){if(!n)return n;var c=o||{},l=!1;return void rs(n,function(e,t,r){switch(r){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:l=!0;break;case 36:l=!1;break;default:if(t.T);else if(!l||c.WTF)throw Error("Unexpected record 0x"+r.toString(16))}},c)}return}catch(e){}});var _=function(e,t,r){if(".bin"===t.slice(-4)){var a,n,s,i,o,c;return n={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},s=[],i=!1,(a=r)||(a={}),a.biff=12,o=[],(c=[[]]).SheetNames=[],c.XTI=[],iT[16]={n:"BrtFRTArchID$",f:io},rs(e,function(e,t,r){switch(r){case 156:c.SheetNames.push(e.name),n.Sheets.push(e);break;case 153:n.WBProps=e;break;case 39:null!=e.Sheet&&(a.SID=e.Sheet),e.Ref=sx(e.Ptg,null,null,c,a),delete a.SID,delete e.Ptg,o.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:c[0].length?c.push([r,e]):c[0]=[r,e],c[c.length-1].XTI=[];break;case 362:0===c.length&&(c[0]=[],c[0].XTI=[]),c[c.length-1].XTI=c[c.length-1].XTI.concat(e),c.XTI=c.XTI.concat(e);break;case 35:case 37:s.push(r),i=!0;break;case 36:case 38:s.pop(),i=!1;break;default:if(t.T);else if(!i||a.WTF&&37!=s[s.length-1]&&35!=s[s.length-1])throw Error("Unexpected record 0x"+r.toString(16))}},a),it(n),n.Names=o,n.supbooks=c,n}return function(e,t){if(!e)throw Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(e6,function(o,c){var l=e9(o);switch(te(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":o.match(is)&&(n="xmlns"+o.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":s5.forEach(function(e){if(null!=l[e[0]])switch(e[2]){case"bool":r.WBProps[e[0]]=th(l[e[0]]);break;case"int":r.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:r.WBProps[e[0]]=l[e[0]]}}),l.codeName&&(r.WBProps.CodeName=tg(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=ta(tg(l.name)),delete l[0],r.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=tg(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),th(l.hidden||"0")&&(s.Hidden=!0),i=c+o.length;break;case"</definedName>":s.Ref=ta(tg(e.slice(i,c))),r.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],r.CalcPr=l;break;default:if(!a&&t.WTF)throw Error("unrecognized "+l[0]+" in workbook")}return o}),-1===tN.indexOf(r.xmlns))throw Error("Unknown Namespace: "+r.xmlns);return it(r),r}(e,r)}(eJ(e,i4(S.workbooks[0])),S.workbooks[0],t),A={},C="";S.coreprops.length&&((C=eJ(e,i4(S.coreprops[0]),!0))&&(A=at(C)),0!==S.extprops.length)&&(C=eJ(e,i4(S.extprops[0]),!0))&&(c=C,l=A,f=t,h={},l||(l={}),c=tg(c),an.forEach(function(e){var t=(c.match(tb(e[0]))||[])[1];switch(e[2]){case"string":t&&(l[e[1]]=ta(t));break;case"bool":l[e[1]]="true"===t;break;case"raw":var r=c.match(RegExp("<"+e[0]+"[^>]*>([\\s\\S]*?)</"+e[0]+">"));r&&r.length>0&&(h[e[1]]=r[1])}}),h.HeadingPairs&&h.TitlesOfParts&&ai(h.HeadingPairs,h.TitlesOfParts,l,f));var O={};(!t.bookSheets||t.bookProps)&&0!==S.custprops.length&&(C=eq(e,i4(S.custprops[0]),!0))&&(O=function(e,t){var r={},a="",n=e.match(ac);if(n)for(var s=0;s!=n.length;++s){var i=n[s],o=e9(i);switch(o[0]){case"<?xml":case"<Properties":break;case"<property":a=ta(o.name);break;case"</property>":a=null;break;default:if(0===i.indexOf("<vt:")){var c=i.split(">"),l=c[0].slice(4),f=c[1];switch(l){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":r[a]=ta(f);break;case"bool":r[a]=th(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(f);break;case"filetime":case"date":r[a]=eU(f);break;default:if("/"==l.slice(-1))break;t.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",i,l,c)}}else if("</"===i.slice(0,2));else if(t.WTF)throw Error(i)}}return r}(C,t));var R={};if((t.bookSheets||t.bookProps)&&(_.Sheets?g=_.Sheets.map(function(e){return e.name}):A.Worksheets&&A.SheetNames.length>0&&(g=A.SheetNames),t.bookProps&&(R.Props=A,R.Custprops=O),t.bookSheets&&void 0!==g&&(R.SheetNames=g),t.bookSheets?R.SheetNames:t.bookProps))return R;g={};var I={};t.bookDeps&&S.calcchain&&(I=function(e,t,r){if(".bin"===t.slice(-4)){var a;return a=[],rs(e,function(e,t,r){if(63===r)a.push(e);else if(t.T);else if(1)throw Error("Unexpected record 0x"+r.toString(16))}),a}var n=[];if(!e)return n;var s=1;return(e.match(e6)||[]).forEach(function(e){var t=e9(e);switch(t[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete t[0],t.i?s=t.i:t.i=s,n.push(t)}}),n}(eJ(e,i4(S.calcchain)),S.calcchain,t));var N=0,D={},F=_.Sheets;A.Worksheets=F.length,A.SheetNames=[];for(var P=0;P!=F.length;++P)A.SheetNames[P]=F[P].name;var L=y?"bin":"xml",M=S.workbooks[0].lastIndexOf("/"),U=(S.workbooks[0].slice(0,M+1)+"_rels/"+S.workbooks[0].slice(M+1)+".rels").replace(/^\//,"");eY(e,U)||(U="xl/_rels/workbook."+L+".rels");var B=r3(eq(e,U,!0),U.replace(/_rels.*/,"s5s"));(S.metadata||[]).length>=1&&(t.xlmeta=function(e,t,r){if(".bin"===t.slice(-4)){var a,n,s,i,o;return a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,o=2,rs(e,function(e,t,r){switch(r){case 335:a.Types.push({name:e.name});break;case 51:e.forEach(function(e){1==o?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==o&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})});break;case 337:o=+!!e;break;case 338:o=2;break;case 35:s.push(r),i=!0;break;case 36:s.pop(),i=!1;break;default:if(t.T);else if(!i||n.WTF&&35!=s[s.length-1])throw Error("Unexpected record 0x"+r.toString(16))}}),a}var c,l={Types:[],Cell:[],Value:[]};if(!e)return l;var f=!1,h=2;return e.replace(e6,function(e){var t=e9(e);switch(te(t[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":l.Types.push({name:t.name});break;case"<futureMetadata":for(var a=0;a<l.Types.length;++a)l.Types[a].name==t.name&&(c=l.Types[a]);break;case"<rc":1==h?l.Cell.push({type:l.Types[t.t-1].name,index:+t.v}):0==h&&l.Value.push({type:l.Types[t.t-1].name,index:+t.v});break;case"<cellMetadata":h=1;break;case"</cellMetadata>":case"</valueMetadata>":h=2;break;case"<valueMetadata":h=0;break;case"<ext":f=!0;break;case"</ext>":f=!1;break;case"<rvb":if(!c)break;c.offsets||(c.offsets=[]),c.offsets.push(+t.i);break;default:if(!f&&r.WTF)throw Error("unrecognized "+t[0]+" in metadata")}return e}),l}(eJ(e,i4(S.metadata[0])),S.metadata[0],t)),(S.people||[]).length>=1&&(t.people=(u=eJ(e,i4(S.people[0])),d=t,p=[],m=!1,u.replace(e6,function(e){var t=e9(e);switch(te(t[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":p.push({name:t.displayname,id:t.id});break;case"<ext":m=!0;break;case"</ext>":m=!1;break;default:if(!m&&d.WTF)throw Error("unrecognized "+t[0]+" in threaded comments")}return e}),p)),B&&(B=function(e,t){if(!e)return 0;try{e=t.map(function(t){var r;return t.id||(t.id=t.strRelID),[t.name,e["!id"][t.id].Target,(r=e["!id"][t.id].Type,r2.WS.indexOf(r)>-1?"sheet":r2.CS&&r==r2.CS?"chart":r2.DS&&r==r2.DS?"dialog":r2.MS&&r==r2.MS?"macro":r&&r.length?r:"sheet")]})}catch(e){return null}return e&&0!==e.length?e:null}(B,_.Sheets));var W=+!!eJ(e,"xl/worksheets/sheet.xml",!0);for(N=0;N!=A.Worksheets;++N){var H="sheet";if(B&&B[N]?(eY(e,b="xl/"+B[N][1].replace(/[\/]?xl\//,""))||(b=B[N][1]),eY(e,b)||(b=U.replace(/_rels\/.*$/,"")+B[N][1]),H=B[N][2]):b=(b="xl/worksheets/sheet"+(N+1-W)+"."+L).replace(/sheet0\./,"sheet."),w=b.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&null!=t.sheets)switch(typeof t.sheets){case"number":if(N!=t.sheets)continue;break;case"string":if(A.SheetNames[N].toLowerCase()!=t.sheets.toLowerCase())continue;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var V=!1,G=0;G!=t.sheets.length;++G)"number"==typeof t.sheets[G]&&t.sheets[G]==N&&(V=1),"string"==typeof t.sheets[G]&&t.sheets[G].toLowerCase()==A.SheetNames[N].toLowerCase()&&(V=1);if(!V)continue}}!function(e,t,r,a,n,s,i,o,c,l,f,h){try{s[a]=r3(eq(e,r,!0),t);var u,d,p,m,g=eJ(e,t);switch(o){case"sheet":u=s[a],m=".bin"===t.slice(-4)?function(e,t,r,a,n,s,i){if(!e)return e;var o,c,l,f,h,u,d,p,m,g,v,b,w=t||{};a||(a={"!id":{}});var T=w.dense?[]:{},E={s:{r:2e6,c:2e6},e:{r:0,c:0}},S=[],y=!1,k=!1,x=[];w.biff=12,w["!row"]=0;var _=0,A=!1,C=[],O={},R=w.supbooks||n.supbooks||[[]];if(R.sharedf=O,R.arrayf=C,R.SheetNames=n.SheetNames||n.Sheets.map(function(e){return e.name}),!w.supbooks&&(w.supbooks=R,n.Names))for(var I=0;I<n.Names.length;++I)R[0][I+1]=n.Names[I];var N=[],D=[],F=!1;if(iT[16]={n:"BrtShortReal",f:s4},rs(e,function(e,t,I){if(!k)switch(I){case 148:o=e;break;case 0:c=e,w.sheetRows&&w.sheetRows<=c.r&&(k=!0),m=rd(h=c.r),w["!row"]=c.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=nU(e.hpt)),D[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(l={t:e[2]},e[2]){case"n":l.v=e[1];break;case"s":p=sD[e[1]],l.v=p.t,l.r=p.r;break;case"b":l.v=!!e[1];break;case"e":l.v=e[1],!1!==w.cellText&&(l.w=rJ[l.v]);break;case"str":l.t="s",l.v=e[1];break;case"is":l.t="s",l.v=e[1].t}if((f=i.CellXf[e[0].iStyleRef])&&sW(l,f.numFmtId,null,w,s,i),u=-1==e[0].c?u+1:e[0].c,w.dense?(T[h]||(T[h]=[]),T[h][u]=l):T[rm(u)+m]=l,w.cellFormula){for(_=0,A=!1;_<C.length;++_){var P=C[_];c.r>=P[0].s.r&&c.r<=P[0].e.r&&u>=P[0].s.c&&u<=P[0].e.c&&(l.F=rw(P[0]),A=!0)}!A&&e.length>3&&(l.f=e[3])}if(E.s.r>c.r&&(E.s.r=c.r),E.s.c>u&&(E.s.c=u),E.e.r<c.r&&(E.e.r=c.r),E.e.c<u&&(E.e.c=u),w.cellDates&&f&&"n"==l.t&&ed(z[f.numFmtId])){var L=Y(l.v);L&&(l.t="d",l.v=new Date(L.y,L.m-1,L.d,L.H,L.M,L.S,L.u))}v&&("XLDAPR"==v.type&&(l.D=!0),v=void 0),b&&(b=void 0);break;case 1:case 12:if(!w.sheetStubs||y)break;l={t:"z",v:void 0},u=-1==e[0].c?u+1:e[0].c,w.dense?(T[h]||(T[h]=[]),T[h][u]=l):T[rm(u)+m]=l,E.s.r>c.r&&(E.s.r=c.r),E.s.c>u&&(E.s.c=u),E.e.r<c.r&&(E.e.r=c.r),E.e.c<u&&(E.e.c=u),v&&("XLDAPR"==v.type&&(l.D=!0),v=void 0),b&&(b=void 0);break;case 176:x.push(e);break;case 49:v=((w.xlmeta||{}).Cell||[])[e-1];break;case 494:var M=a["!id"][e.relId];for(M?(e.Target=M.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=M):""==e.relId&&(e.Target="#"+e.loc),h=e.rfx.s.r;h<=e.rfx.e.r;++h)for(u=e.rfx.s.c;u<=e.rfx.e.c;++u)w.dense?(T[h]||(T[h]=[]),T[h][u]||(T[h][u]={t:"z",v:void 0}),T[h][u].l=e):(T[d=rv({c:u,r:h})]||(T[d]={t:"z",v:void 0}),T[d].l=e);break;case 426:if(!w.cellFormula)break;C.push(e),(g=w.dense?T[h][u]:T[rm(u)+m]).f=sx(e[1],E,{r:c.r,c:u},R,w),g.F=rw(e[0]);break;case 427:if(!w.cellFormula)break;O[rv(e[0].s)]=e[1],(g=w.dense?T[h][u]:T[rm(u)+m]).f=sx(e[1],E,{r:c.r,c:u},R,w);break;case 60:if(!w.cellStyles)break;for(;e.e>=e.s;)N[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},F||(F=!0,nP(e.w/256)),nL(N[e.e+1]);break;case 161:T["!autofilter"]={ref:rw(e)};break;case 476:T["!margins"]=e;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name),(e.above||e.left)&&(T["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:y=!0;break;case 36:y=!1;break;case 37:S.push(I),y=!0;break;case 38:S.pop(),y=!1;break;default:if(t.T);else if(!y||w.WTF)throw Error("Unexpected record 0x"+I.toString(16))}},w),delete w.supbooks,delete w["!row"],!T["!ref"]&&(E.s.r<2e6||o&&(o.e.r>0||o.e.c>0||o.s.r>0||o.s.c>0))&&(T["!ref"]=rw(o||E)),w.sheetRows&&T["!ref"]){var P=rT(T["!ref"]);w.sheetRows<=+P.e.r&&(P.e.r=w.sheetRows-1,P.e.r>E.e.r&&(P.e.r=E.e.r),P.e.r<P.s.r&&(P.s.r=P.e.r),P.e.c>E.e.c&&(P.e.c=E.e.c),P.e.c<P.s.c&&(P.s.c=P.e.c),T["!fullref"]=T["!ref"],T["!ref"]=rw(P))}return x.length>0&&(T["!merges"]=x),N.length>0&&(T["!cols"]=N),D.length>0&&(T["!rows"]=D),T}(g,c,n,u,l,f,h):function(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var o=t.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(sV);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(sY);u?sq(u[0],o,n,r):(u=l.match(sK))&&(p=u[0],m=u[1],g=o,v=n,b=r,sq(p.slice(0,p.indexOf(">")),g,v,b));var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p,m,g,v,b,w,T=l.slice(d,d+50).match(sG);T&&(w=rT(T[1])).s.r<=w.e.r&&w.s.c<=w.e.c&&w.s.r>=0&&w.s.c>=0&&(o["!ref"]=rw(w))}var E=l.match(sJ);E&&E[1]&&function(e,t){t.Views||(t.Views=[{}]),(e.match(s1)||[]).forEach(function(e,r){var a=e9(e);t.Views[r]||(t.Views[r]={}),+a.zoomScale&&(t.Views[r].zoom=+a.zoomScale),th(a.rightToLeft)&&(t.Views[r].RTL=!0)})}(E[1],n);var S=[];if(t.cellStyles){var y=l.match(sj);y&&function(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=e9(t[a],!0);n.hidden&&(n.hidden=th(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,nP(n.width)),nL(n);s<=i;)e[s++]=eW(n)}}(S,y)}h&&s0(h[1],o,t,c,s,i);var k=f.match(s$);k&&(o["!autofilter"]={ref:(k[0].match(/ref="([^"]*)"/)||[])[1]});var x=[],_=f.match(sH);if(_)for(d=0;d!=_.length;++d)x[d]=rT(_[d].slice(_[d].indexOf('"')+1));var A=f.match(sz);A&&function(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var s=e9(tg(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+ta(s.location))):(s.Target="#"+ta(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var o=rT(s.ref),c=o.s.r;c<=o.e.r;++c)for(var l=o.s.c;l<=o.e.c;++l){var f=rv({c:l,r:c});a?(e[c]||(e[c]=[]),e[c][l]||(e[c][l]={t:"z",v:void 0}),e[c][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(o,A,a);var C=f.match(sX);if(C&&(o["!margins"]=function(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}(e9(C[0]))),!o["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(o["!ref"]=rw(c)),t.sheetRows>0&&o["!ref"]){var O=rT(o["!ref"]);t.sheetRows<=+O.e.r&&(O.e.r=t.sheetRows-1,O.e.r>c.e.r&&(O.e.r=c.e.r),O.e.r<O.s.r&&(O.s.r=O.e.r),O.e.c>c.e.c&&(O.e.c=c.e.c),O.e.c<O.s.c&&(O.s.c=O.e.c),o["!fullref"]=o["!ref"],o["!ref"]=rw(O))}return S.length>0&&(o["!cols"]=S),x.length>0&&(o["!merges"]=x),o}(g,c,n,u,l,f,h);break;case"chart":if(!(m=function(e,t,r,a,n,s,i,o){if(".bin"===t.slice(-4)){var c=n;if(!e)return e;c||(c={"!id":{}});var l={"!type":"chart","!drawel":null,"!rel":""},f=[],h=!1;return rs(e,function(e,t,n){switch(n){case 550:l["!rel"]=e;break;case 651:s.Sheets[r]||(s.Sheets[r]={}),e.name&&(s.Sheets[r].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:h=!0;break;case 36:h=!1;break;case 37:f.push(n);break;case 38:f.pop();break;default:if(t.T>0)f.push(n);else if(t.T<0)f.pop();else if(!h||a.WTF)throw Error("Unexpected record 0x"+n.toString(16))}},a),c["!id"][l["!rel"]]&&(l["!drawel"]=c["!id"][l["!rel"]]),l}var u=n;if(!e)return e;u||(u={"!id":{}});var d,p={"!type":"chart","!drawel":null,"!rel":""},m=e.match(sY);return m&&sq(m[0],p,s,r),(d=e.match(/drawing r:id="(.*?)"/))&&(p["!rel"]=d[1]),u["!id"][p["!rel"]]&&(p["!drawel"]=u["!id"][p["!rel"]]),p}(g,t,n,c,s[a],l,0,0))||!m["!drawel"])break;var v=e2(m["!drawel"].Target,t),b=r4(v),w=function(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}(eq(e,v,!0),r3(eq(e,b,!0),v)),T=e2(w,v),E=r4(T);m=function(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var o=0,c=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(e){var t,r,a,n,s=(r=[],a=e.match(/^<c:numCache>/),(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(e){var t=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);t&&(r[+t[1]]=a?+t[2]:t[2])}),n=ta((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]),(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(e){t=e.replace(/<.*?>/g,"")}),[r,n,t]);f.s.r=f.s.c=0,f.e.c=o,l=rm(o),s[0].forEach(function(e,t){i[l+rd(t)]={t:"n",v:e,z:s[1]},c=t}),f.e.r<c&&(f.e.r=c),++o}),o>0&&(i["!ref"]=rw(f)),i}(eq(e,T,!0),0,0,r3(eq(e,E,!0),T),0,m);break;case"macro":s[a],t.slice(-4),m={"!type":"macro"};break;case"dialog":s[a],t.slice(-4),m={"!type":"dialog"};break;default:throw Error("Unrecognized sheet type "+o)}i[a]=m;var S=[];s&&s[a]&&ex(s[a]).forEach(function(r){var n,i,o,l,f,h="";if(s[a][r].Type==r2.CMNT){h=e2(s[a][r].Target,t);var u=function(e,t,r){if(".bin"===t.slice(-4)){var a,n,s,i;return a=[],n=[],s={},i=!1,rs(e,function(e,t,o){switch(o){case 632:n.push(e);break;case 635:s=e;break;case 637:s.t=e.t,s.h=e.h,s.r=e.r;break;case 636:if(s.author=n[s.iauthor],delete s.iauthor,r.sheetRows&&s.rfx&&r.sheetRows<=s.rfx.r)break;s.t||(s.t=""),delete s.rfx,a.push(s);break;case 3072:case 37:case 38:break;case 35:i=!0;break;case 36:i=!1;break;default:if(t.T);else if(!i||r.WTF)throw Error("Unexpected record 0x"+o.toString(16))}}),a}if(e.match(/<(?:\w+:)?comments *\/>/))return[];var o=[],c=[],l=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);l&&l[1]&&l[1].split(/<\/\w*:?author>/).forEach(function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?author[^>]*>(.*)/);t&&o.push(t[1])}});var f=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return f&&f[1]&&f[1].split(/<\/\w*:?comment>/).forEach(function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?comment[^>]*>/);if(t){var a=e9(t[0]),n={author:a.authorId&&o[a.authorId]||"sheetjsghost",ref:a.ref,guid:a.guid},s=rg(a.ref);if(!r.sheetRows||!(r.sheetRows<=s.r)){var i=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!i&&!!i[1]&&nu(i[1])||{r:"",t:"",h:""};n.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),n.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),r.cellHTML&&(n.h=l.h),c.push(n)}}}}),c}(eJ(e,h,!0),h,c);if(!u||!u.length)return;n8(m,u,!1)}s[a][r].Type==r2.TCMNT&&(h=e2(s[a][r].Target,t),S=S.concat((n=eJ(e,h,!0),i=[],o=!1,l={},f=0,n.replace(e6,function(e,t){var r=e9(e);switch(te(r[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":l={author:r.personId,guid:r.id,ref:r.ref,T:1};break;case"</threadedComment>":null!=l.t&&i.push(l);break;case"<text>":case"<text":f=t+e.length;break;case"</text>":l.t=n.slice(f,t).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":o=!0;break;case"</mentions>":case"</ext>":o=!1;break;default:if(!o&&c.WTF)throw Error("unrecognized "+r[0]+" in threaded comments")}return e}),i)))}),S&&S.length&&n8(m,S,!0,c.people||[])}catch(e){if(c.WTF)throw e}}(e,b,w,A.SheetNames[N],N,D,g,H,t,_,k,x)}return R={Directory:S,Workbook:_,Props:A,Custprops:O,Deps:I,Sheets:g,SheetNames:A.SheetNames,Strings:sD,Styles:x,Themes:k,SSF:eW(z)},t&&t.bookFiles&&(e.files?(R.keys=E,R.files=e.files):(R.keys=[],R.files={},e.FullPaths.forEach(function(t,r){t=t.replace(/^Root Entry[\/]/,""),R.keys.push(t),R.files[t]=e.FileIndex[r]}))),t&&t.bookVBA&&(S.vba.length>0?R.vbaraw=eJ(e,i4(S.vba[0]),!0):S.defaults&&"application/vnd.ms-office.vbaProject"===S.defaults.bin&&(R.vbaraw=eJ(e,"xl/vbaProject.bin",!0))),R}(e0(s,i),i)):i6(t,d,u,g);case 239:return 60===p[3]?id(d,u):i6(t,d,u,g);case 255:if(254===p[1])return l=d,f=u,h=l,"base64"==f.type&&(h=y(h)),h=n.utils.decode(1200,h.slice(2),"str"),f.type="binary",i5(h,f);if(0===p[1]&&2===p[2]&&0===p[3])return ni.to_workbook(d,u);break;case 0:if(0===p[1]&&(p[2]>=2&&0===p[3]||0===p[2]&&(8===p[3]||9===p[3])))return ni.to_workbook(d,u);break;case 3:case 131:case 139:case 140:return nt.to_workbook(d,u);case 123:if(92===p[1]&&114===p[2]&&116===p[3])return nA.to_workbook(d,u);break;case 10:case 13:case 32:var x=d,_=u,A="",C=i3(x,_);switch(_.type){case"base64":A=y(x);break;case"binary":A=x;break;case"buffer":A=x.toString("binary");break;case"array":A=eB(x);break;default:throw Error("Unrecognized type "+_.type)}return 239==C[0]&&187==C[1]&&191==C[2]&&(A=tg(A)),_.type="binary",i5(A,_);case 137:if(80===p[1]&&78===p[2]&&71===p[3])throw Error("PNG Image File is not a spreadsheet")}return ne.indexOf(p[0])>-1&&p[2]<=12&&p[3]<=31?nt.to_workbook(d,u):i6(t,d,u,g)},M9:()=>function e(t,r){m(),function(e){if(!e||!e.SheetNames||!e.Sheets)throw Error("Invalid Workbook");if(!e.SheetNames.length)throw Error("Workbook is empty");var t,r,a=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=!!e.vbaraw,t.forEach(function(e,n){ia(e);for(var s=0;s<n;++s)if(e==t[s])throw Error("Duplicate Sheet Name: "+e);if(r){var i=a&&a[n]&&a[n].CodeName||e;if(95==i.charCodeAt(0)&&i.length>22)throw Error("Bad Code Name: Worksheet"+i)}});for(var n=0;n<e.SheetNames.length;++n)!function(e,t,r){if(e&&e["!ref"]){var a=rT(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw Error("Bad range ("+r+"): "+e["!ref"])}}(e.Sheets[e.SheetNames[n]],e.SheetNames[n],n)}(t);var a,n,s=eW(r||{});if(s.cellStyles&&(s.cellNF=!0,s.sheetStubs=!0),"array"==s.type){s.type="binary";var i=e(t,s);return s.type="array",O(i)}var o=0;if(s.sheet&&(o="number"==typeof s.sheet?s.sheet:t.SheetNames.indexOf(s.sheet),!t.SheetNames[o]))throw Error("Sheet not found: "+s.sheet+" : "+typeof s.sheet);switch(s.bookType||"xlsb"){case"xml":case"xlml":return i7(function(e,t){t||(t={}),e.SSF||(e.SSF=eW(z)),e.SSF&&(ew(),eb(e.SSF),t.revssf=eC(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],sB(t.cellXfs,{},{revssf:{General:0}}));var r,a,n,s,i,o,c,l,f,h,u=[];u.push((r=t,a=[],e.Props&&a.push((n=e.Props,s=[],ex(af).map(function(e){for(var t=0;t<r9.length;++t)if(r9[t][1]==e)return r9[t];for(t=0;t<an.length;++t)if(an[t][1]==e)return an[t];throw e}).forEach(function(e){if(null!=n[e[1]]){var t=r&&r.Props&&null!=r.Props[e[1]]?r.Props[e[1]]:n[e[1]];"date"===e[2]&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof t?t=String(t):!0===t||!1===t?t=t?"1":"0":t instanceof Date&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"")),s.push(tx(af[e[1]]||e[1],t))}}),tA("DocumentProperties",s.join(""),{xmlns:tD.o}))),e.Custprops&&a.push((i=e.Props,o=e.Custprops,c=["Worksheets","SheetNames"],l="CustomDocumentProperties",f=[],i&&ex(i).forEach(function(e){if(Object.prototype.hasOwnProperty.call(i,e)){for(var t=0;t<r9.length;++t)if(e==r9[t][1])return;for(t=0;t<an.length;++t)if(e==an[t][1])return;for(t=0;t<c.length;++t)if(e==c[t])return;var r=i[e],a="string";"number"==typeof r?(a="float",r=String(r)):!0===r||!1===r?(a="boolean",r=r?"1":"0"):r=String(r),f.push(tA(to(e),r,{"dt:dt":a}))}}),o&&ex(o).forEach(function(e){if(Object.prototype.hasOwnProperty.call(o,e)&&!(i&&Object.prototype.hasOwnProperty.call(i,e))){var t=o[e],r="string";"number"==typeof t?(r="float",t=String(t)):!0===t||!1===t?(r="boolean",t=t?"1":"0"):t instanceof Date?(r="dateTime.tz",t=t.toISOString()):t=String(t),f.push(tA(to(e),t,{"dt:dt":r}))}}),"<"+l+' xmlns="'+tD.o+'">'+f.join("")+"</"+l+">")),a.join(""))),u.push(""),u.push(""),u.push("");for(var d=0;d<e.SheetNames.length;++d)u.push(tA("Worksheet",function(e,t,r){var a=[],n=r.SheetNames[e],s=r.Sheets[n],i=s?function(e,t,r,a){if(!e||!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(ip(o)))}return s.join("")}(s,0,e,r):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),(i=s?function(e,t,r,a){if(!e["!ref"])return"";var n=rT(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach(function(e,t){nL(e);var r=!!e.width,a=sM(t,e),n={"ss:Index":t+1};r&&(n["ss:Width"]=nI(a.width)),e.hidden&&(n["ss:Hidden"]="1"),o.push(tA("Column",null,n))});for(var c=Array.isArray(e),l=n.s.r;l<=n.e.r;++l){for(var f=[function(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=nU(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}(l,(e["!rows"]||[])[l])],h=n.s.c;h<=n.e.c;++h){var u=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>h)&&!(s[i].s.r>l)&&!(s[i].e.c<h)&&!(s[i].e.r<l)){(s[i].s.c!=h||s[i].s.r!=l)&&(u=!0);break}if(!u){var d={r:l,c:h},p=rv(d),m=c?(e[l]||[])[h]:e[p];f.push(function(e,t,r,a,n,s,i){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+ti(sr(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var c=rg(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(c.r==i.r?"":"["+(c.r-i.r)+"]")+"C"+(c.c==i.c?"":"["+(c.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=ti(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=ti(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],f=0;f!=l.length;++f)l[f].s.c==i.c&&l[f].s.r==i.r&&(l[f].e.c>l[f].s.c&&(o["ss:MergeAcross"]=l[f].e.c-l[f].s.c),l[f].e.r>l[f].s.r&&(o["ss:MergeDown"]=l[f].e.r-l[f].s.r));var h="",u="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=rJ[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||z[14]);break;case"s":h="String",u=((e.v||"")+"").replace(tn,function(e){return tr[e]}).replace(tc,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var d=sB(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map(function(e){var t=tA("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return tA("Comment",t,{"ss:Author":e.a})}).join("")),tA("Cell",m,o)}(m,p,e,t,0,0,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}(s,t,0,0):"").length>0&&a.push("<Table>"+i+"</Table>"),a.push(function(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(tA("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(tA("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(tA("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(tA("Visible",1==a.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!a.Workbook.Sheets[s]||a.Workbook.Sheets[s].Hidden);++s);s==r&&n.push("<Selected/>")}return(((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(tx("ProtectContents","True")),e["!protect"].objects&&n.push(tx("ProtectObjects","True")),e["!protect"].scenarios&&n.push(tx("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||n.push(tx("EnableSelection","UnlockedCells")):n.push(tx("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(t){e["!protect"][t[0]]&&n.push("<"+t[1]+"/>")})),0==n.length)?"":tA("WorksheetOptions",n.join(""),{xmlns:tD.x})}(s,0,e,r)),a.join("")}(d,t,e),{"ss:Name":ti(e.SheetNames[d])}));return u[2]=(h=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'],t.cellXfs.forEach(function(e,t){var r=[];r.push(tA("NumberFormat",null,{"ss:Format":ti(z[e.numFmtId])})),h.push(tA("Style",r.join(""),{"ss:ID":"s"+(21+t)}))}),tA("Styles",h.join(""))),u[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];null==n.Sheet&&(n.Name.match(/^_xlfn\./)||r.push(ip(n)))}return tA("Names",r.join(""))}(e,t),e4+tA("Workbook",u.join(""),{xmlns:tD.ss,"xmlns:o":tD.o,"xmlns:x":tD.x,"xmlns:ss":tD.ss,"xmlns:dt":tD.dt,"xmlns:html":tD.html})}(t,s),s);case"slk":case"sylk":return i7(nr.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"htm":case"html":return i7(i_(t.Sheets[t.SheetNames[o]],s),s);case"txt":var l=oa(t.Sheets[t.SheetNames[o]],s);switch(s.type){case"base64":return S(l);case"binary":case"string":return l;case"file":return ek(s.file,l,"binary");case"buffer":if(k)return x(l,"binary");return l.split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+s.type);case"csv":return i7(or(t.Sheets[t.SheetNames[o]],s),s,"\uFEFF");case"dif":return i7(na.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"dbf":return i9(nt.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"prn":return i7(ns.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"rtf":return i7(nA.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"eth":return i7(nn.from_sheet(t.Sheets[t.SheetNames[o]],s),s);case"fods":return i7(iF(t,s),s);case"wk1":return i9(ni.sheet_to_wk1(t.Sheets[t.SheetNames[o]],s),s);case"wk3":return i9(ni.book_to_wk3(t,s),s);case"biff2":s.biff||(s.biff=2);case"biff3":s.biff||(s.biff=3);case"biff4":return s.biff||(s.biff=4),i9(ik(t,s),s);case"biff5":s.biff||(s.biff=5);case"biff8":case"xla":case"xls":return s.biff||(s.biff=8),i8(function(e,t){var r,a=t||{},n=ey.utils.cfb_new({root:"R"}),s="/Workbook";switch(a.bookType||"xls"){case"xls":a.bookType="biff8";case"xla":a.bookType||(a.bookType="xla");case"biff8":s="/Workbook",a.biff=8;break;case"biff5":s="/Book",a.biff=5;break;default:throw Error("invalid type "+a.bookType+" for XLS CFB")}return ey.utils.cfb_add(n,s,ik(e,a)),8==a.biff&&(e.Props||e.Custprops)&&function(e,t){var r,a=[],n=[],s=[],i=0,o=e_(rj,"n"),c=e_(r$,"n");if(e.Props)for(i=0,r=ex(e.Props);i<r.length;++i)(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(i=0,r=ex(e.Custprops);i<r.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},r[i])||(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Custprops[r[i]]]);var l=[];for(i=0;i<s.length;++i)aE.indexOf(s[i][0])>-1||as.indexOf(s[i][0])>-1||null!=s[i][1]&&l.push(s[i]);n.length&&ey.utils.cfb_add(t,"/\x05SummaryInformation",ak(n,ib.SI,c,r$)),(a.length||l.length)&&ey.utils.cfb_add(t,"/\x05DocumentSummaryInformation",ak(a,ib.DSI,o,rj,l.length?l:null,ib.UDI))}(e,n),8==a.biff&&e.vbaraw&&(r=ey.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})).FullPaths.forEach(function(e,t){if(0!=t){var a=e.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==a.slice(-1)&&ey.utils.cfb_add(n,a,r.FileIndex[t].content)}}),n}(t,a=s||{}),a);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r={},a=k?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw Error("Unrecognized type "+t.type)}var n=e.FullPaths?ey.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof n){if("binary"==t.type||"base64"==t.type)return n;n=new Uint8Array(O(n))}return t.password&&"undefined"!=typeof encrypt_agile?i8(encrypt_agile(n,t.password),t):"file"===t.type?ek(t.file,n):"string"==t.type?tg(n):n}("ods"==(n=eW(s||{})).bookType?iF(t,n):"numbers"==n.bookType?function(e,t){if(!t||!t.numbers)throw Error("Must pass a `numbers` option -- check the README");var r,a=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=rb(a["!ref"]);n.s.r=n.s.c=0;var s=!1;n.e.c>9&&(s=!0,n.e.c=9),n.e.r>49&&(s=!0,n.e.r=49),s&&console.error("The Numbers writer is currently limited to ".concat(rw(n)));var i=oe(a,{range:n,header:1}),o=["~Sh33tJ5~"];i.forEach(function(e){return e.forEach(function(e){"string"==typeof e&&o.push(e)})});var c={},l=[],f=ey.read(t.numbers,{type:"base64"});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&ij(iX(t.content)).forEach(function(e){l.push(e.id),c[e.id]={deps:[],location:r,type:iH(e.messages[0].meta[1][0].data)}})}),l.sort(function(e,t){return e-t});var h=l.filter(function(e){return e>1}).map(function(e){return[e,iW(e)]});f.FileIndex.map(function(e,t){return[e,f.FullPaths[t]]}).forEach(function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&ij(iX(t.content)).forEach(function(e){e.messages.forEach(function(t){h.forEach(function(t){e.messages.some(function(e){return 11006!=iH(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}(e.data,t[1])})&&c[t[0]].deps.push(e.id)})})})});for(var u=ey.find(f,c[1].location),d=ij(iX(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(r=m)}var g=iq(iV(r.messages[0].data)[1][0].data);for(p=0,d=ij(iX((u=ey.find(f,c[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=iq(iV(r.messages[0].data)[2][0].data),d=ij(iX((u=ey.find(f,c[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=iq(iV(r.messages[0].data)[2][0].data),d=ij(iX((u=ey.find(f,c[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);var v=iV(r.messages[0].data);v[6][0].data=iW(n.e.r+1),v[7][0].data=iW(n.e.c+1);for(var b=iq(v[46][0].data),w=ey.find(f,c[b].location),T=ij(iX(w.content)),E=0;E<T.length&&T[E].id!=b;++E);if(T[E].id!=b)throw"Bad ColumnRowUIDMapArchive";var S=iV(T[E].messages[0].data);S[1]=[],S[2]=[],S[3]=[];for(var y=0;y<=n.e.c;++y){var k=[];k[1]=k[2]=[{type:0,data:iW(y+420690)}],S[1].push({type:2,data:iz(k)}),S[2].push({type:0,data:iW(y)}),S[3].push({type:0,data:iW(y)})}S[4]=[],S[5]=[],S[6]=[];for(var x=0;x<=n.e.r;++x)(k=[])[1]=k[2]=[{type:0,data:iW(x+726270)}],S[4].push({type:2,data:iz(k)}),S[5].push({type:0,data:iW(x)}),S[6].push({type:0,data:iW(x)});T[E].messages[0].data=iz(S),w.content=iY(i$(T)),w.size=w.content.length,delete v[46];var _=iV(v[4][0].data);_[7][0].data=iW(n.e.r+1);var A=iq(iV(_[1][0].data)[2][0].data);if((T=ij(iX((w=ey.find(f,c[A].location)).content)))[0].id!=A)throw"Bad HeaderStorageBucket";var O=iV(T[0].messages[0].data);for(x=0;x<i.length;++x){var R=iV(O[2][0].data);R[1][0].data=iW(x),R[4][0].data=iW(i[x].length),O[2][x]={type:O[2][0].type,data:iz(R)}}T[0].messages[0].data=iz(O),w.content=iY(i$(T)),w.size=w.content.length;var I=iq(_[2][0].data);if((T=ij(iX((w=ey.find(f,c[I].location)).content)))[0].id!=I)throw"Bad HeaderStorageBucket";for(y=0,O=iV(T[0].messages[0].data);y<=n.e.c;++y)(R=iV(O[2][0].data))[1][0].data=iW(y),R[4][0].data=iW(n.e.r+1),O[2][y]={type:O[2][0].type,data:iz(R)};T[0].messages[0].data=iz(O),w.content=iY(i$(T)),w.size=w.content.length;var N=iq(_[4][0].data);!function(){for(var e,t=ey.find(f,c[N].location),r=ij(iX(t.content)),a=0;a<r.length;++a){var n=r[a];n.id==N&&(e=n)}var s=iV(e.messages[0].data);s[3]=[];var i=[];o.forEach(function(e,t){i[1]=[{type:0,data:iW(t)}],i[2]=[{type:0,data:iW(1)}],i[3]=[{type:2,data:"undefined"!=typeof TextEncoder?new TextEncoder().encode(e):C(tv(e))}],s[3].push({type:2,data:iz(i)})}),e.messages[0].data=iz(s),t.content=iY(i$(r)),t.size=t.content.length}();var D=iV(_[3][0].data),F=D[1][0];delete D[2];var P=iV(F.data),L=iq(P[2][0].data);!function(){for(var e,t=ey.find(f,c[L].location),r=ij(iX(t.content)),a=0;a<r.length;++a){var s=r[a];s.id==L&&(e=s)}var l=iV(e.messages[0].data);delete l[6],delete D[7];var h=new Uint8Array(l[5][0].data);l[5]=[];for(var u=0,d=0;d<=n.e.r;++d){var p=iV(h);u+=function(e,t,r){if(!(null==(a=e[6])?void 0:a[0])||!(null==(n=e[7])?void 0:n[0]))throw"Mutation only works on post-BNC storages!";if((null==(i=null==(s=e[8])?void 0:s[0])?void 0:i.data)&&iH(e[8][0].data)>0)throw"Math only works with normal offsets";for(var a,n,s,i,o,c,l=0,f=iP(e[7][0].data),h=0,u=[],d=iP(e[4][0].data),p=0,m=[],g=0;g<t.length;++g){if(null==t[g]){f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535);continue}switch(f.setUint16(2*g,h,!0),d.setUint16(2*g,p,!0),typeof t[g]){case"string":o=iK({t:"s",v:t[g]},r),c=iJ({t:"s",v:t[g]},r);break;case"number":o=iK({t:"n",v:t[g]},r),c=iJ({t:"n",v:t[g]},r);break;case"boolean":o=iK({t:"b",v:t[g]},r),c=iJ({t:"b",v:t[g]},r);break;default:throw Error("Unsupported value "+t[g])}u.push(o),h+=o.length,m.push(c),p+=c.length,++l}for(e[2][0].data=iW(l);g<e[7][0].data.length/2;++g)f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535,!0);return e[6][0].data=iM(u),e[3][0].data=iM(m),l}(p,i[d],o),p[1][0].data=iW(d),l[5].push({data:iz(p),type:2})}l[1]=[{type:0,data:iW(n.e.c+1)}],l[2]=[{type:0,data:iW(n.e.r+1)}],l[3]=[{type:0,data:iW(u)}],l[4]=[{type:0,data:iW(n.e.r+1)}],e.messages[0].data=iz(l),t.content=iY(i$(r)),t.size=t.content.length}(),F.data=iz(P),_[3][0].data=iz(D),v[4][0].data=iz(_),r.messages[0].data=iz(v);var M=iY(i$(d));return u.content=M,u.size=u.content.length,f}(t,n):"xlsb"==n.bookType?function(e,t){n5=1024,e&&!e.SSF&&(e.SSF=eW(z)),e&&e.SSF&&(ew(),eb(e.SSF),t.revssf=eC(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,sP?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,a,n,s,i,o,l,f="xlsb"==t.bookType?"bin":"xml",h=n9.indexOf(t.bookType)>-1,u=r1();i2(t=t||{});var d=e1(),p="",m=0;if(t.cellXfs=[],sB(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eQ(d,p="docProps/core.xml",aa(e.Props,t)),u.coreprops.push(p),r6(t.rels,2,p,r2.CORE_PROPS),p="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var g=[],v=0;v<e.SheetNames.length;++v)2!=(e.Workbook.Sheets[v]||{}).Hidden&&g.push(e.SheetNames[v]);e.Props.SheetNames=g}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,eQ(d,p,ao(e.Props,t)),u.extprops.push(p),r6(t.rels,3,p,r2.EXT_PROPS),e.Custprops!==e.Props&&ex(e.Custprops||{}).length>0&&(eQ(d,p="docProps/custom.xml",al(e.Custprops,t)),u.custprops.push(p),r6(t.rels,4,p,r2.CUST_PROPS)),m=1;m<=e.SheetNames.length;++m){var b={"!id":{}},w=e.Sheets[e.SheetNames[m-1]];if((w||{})["!type"],eQ(d,p="xl/worksheets/sheet"+m+"."+f,(T=m-1,E=p,S=t,(".bin"===E.slice(-4)?function(e,t,r,a){var n,s,i,o,c,l,f,h,u,d=ri(),p=r.SheetNames[e],m=r.Sheets[p]||{},g=p;try{r&&r.Workbook&&(g=r.Workbook.Sheets[e].CodeName||g)}catch(e){}var v=rT(m["!ref"]||"A1");if(v.e.c>16383||v.e.r>1048575){if(t.WTF)throw Error("Range "+(m["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");v.e.c=Math.min(v.e.c,16383),v.e.r=Math.min(v.e.c,1048575)}m["!links"]=[],m["!comments"]=[],ro(d,129),(r.vbaraw||m["!outline"])&&ro(d,147,function(e,t,r){null==r&&(r=rn(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return rV({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),rC(e,r),r.slice(0,r.l)}(g,m["!outline"])),ro(d,148,rB(v)),n=r.Workbook,ro(d,133),ro(d,137,(null==s&&(s=rn(30)),i=924,(((n||{}).Views||[])[0]||{}).RTL&&(i|=32),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(1,0),s.write_shift(1,0),s.write_shift(2,0),s.write_shift(2,100),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s)),ro(d,138),ro(d,134),m&&m["!cols"]&&(ro(d,390),m["!cols"].forEach(function(e,t){var r,a,n;e&&ro(d,60,(null==r&&(r=rn(18)),a=sM(t,e),r.write_shift(-4,t),r.write_shift(-4,t),r.write_shift(4,256*(a.width||10)),r.write_shift(4,0),n=0,e.hidden&&(n|=1),"number"==typeof a.width&&(n|=2),e.level&&(n|=e.level<<8),r.write_shift(2,n),r))}),ro(d,391)),function(e,t,r,a){var n,s=rT(t["!ref"]||"A1"),i="",o=[];ro(e,145);var c=Array.isArray(t),l=s.e.r;t["!rows"]&&(l=Math.max(s.e.r,t["!rows"].length-1));for(var f=s.s.r;f<=l;++f){i=rd(f),function(e,t,r,a){var n=function(e,t,r){var a=rn(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=20*nM(n.hpx):n.hpt&&(s=20*n.hpt),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,c=a.l;a.l+=4;for(var l={r:e,c:0},f=0;f<16;++f)if(!(t.s.c>f+1<<10)&&!(t.e.c<f<<10)){for(var h=-1,u=-1,d=f<<10;d<f+1<<10;++d)l.c=d,(Array.isArray(r)?(r[l.r]||[])[l.c]:r[rv(l)])&&(h<0&&(h=d),u=d);h<0||(++o,a.write_shift(4,h),a.write_shift(4,u))}var p=a.l;return a.l=c,a.write_shift(4,o),a.l=p,a.length>a.l?a.slice(0,a.l):a}(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&ro(e,0,n)}(e,t,s,f);var h=!1;if(f<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){f===s.s.r&&(o[u]=rm(u)),n=o[u]+i;var d=c?(t[f]||[])[u]:t[n];if(!d){h=!1;continue}h=function(e,t,r,a,n,s,i){if(void 0===t.v)return!1;var o,c,l,f,h,u,d,p,m,g,v,b,w,T,E,S,y,k,x,_,A,C,O,R,I="";switch(t.t){case"b":I=t.v?"1":"0";break;case"d":(t=eW(t)).z=t.z||z[14],t.v=eR(eU(t.v)),t.t="n";break;case"n":case"e":I=""+t.v;break;default:I=t.v}var N={r:r,c:a};switch(N.s=sB(n.cellXfs,t,n),t.l&&s["!links"].push([rv(N),t.l]),t.c&&s["!comments"].push([rv(N),t.c]),t.t){case"s":case"str":return n.bookSST?(I=sL(n.Strings,t.v,n.revStrings),N.t="s",N.v=I,i)?ro(e,18,(null==o&&(o=rn(8)),rD(N,o),o.write_shift(4,N.v),o)):ro(e,7,(null==c&&(c=rn(12)),rI(N,c),c.write_shift(4,N.v),c)):(N.t="str",i)?ro(e,17,(l=t,null==f&&(f=rn(8+4*l.v.length)),rD(N,f),rC(l.v,f),f.length>f.l?f.slice(0,f.l):f)):ro(e,6,(h=t,null==u&&(u=rn(12+4*h.v.length)),rI(N,u),rC(h.v,u),u.length>u.l?u.slice(0,u.l):u)),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?i?ro(e,13,(d=t,null==p&&(p=rn(8)),rD(N,p),rM(d.v,p),p)):ro(e,2,(m=t,null==g&&(g=rn(12)),rI(N,g),rM(m.v,g),g)):i?ro(e,16,(v=t,null==b&&(b=rn(12)),rD(N,b),rH(v.v,b),b)):ro(e,5,(w=t,null==T&&(T=rn(16)),rI(N,T),rH(w.v,T),T)),!0;case"b":return(N.t="b",i)?ro(e,15,(E=t,null==S&&(S=rn(5)),rD(N,S),S.write_shift(1,+!!E.v),S)):ro(e,4,(y=t,null==k&&(k=rn(9)),rI(N,k),k.write_shift(1,+!!y.v),k)),!0;case"e":return(N.t="e",i)?ro(e,14,(x=t,null==_&&(_=rn(8)),rD(N,_),_.write_shift(1,x.v),_.write_shift(2,0),_.write_shift(1,0),_)):ro(e,3,(A=t,null==C&&(C=rn(9)),rI(N,C),C.write_shift(1,A.v),C)),!0}return i?ro(e,12,(null==O&&(O=rn(4)),rD(N,O))):ro(e,1,(null==R&&(R=rn(8)),rI(N,R))),!0}(e,d,f,u,a,t,h)}}ro(e,146)}(d,m,0,t,r);m["!protect"]&&ro(d,535,(o=m["!protect"],null==c&&(c=rn(66)),c.write_shift(2,o.password?ny(o.password):0),c.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(e){e[1]?c.write_shift(4,+(null!=o[e[0]]&&!o[e[0]])):c.write_shift(4,null!=o[e[0]]&&o[e[0]]?0:1)}),c)),!function(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],s="string"==typeof n.ref?n.ref:rw(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=rb(s);o.s.r==o.e.r&&(o.e.r=rb(t["!ref"]).e.r,s=rw(o));for(var c=0;c<i.length;++c){var l=i[c];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==a){l.Ref="'"+r.SheetNames[a]+"'!"+s;break}}c==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+s}),ro(e,161,rB(rT(s))),ro(e,162)}}(d,m,r,e);m&&m["!merges"]&&(ro(d,177,(l=m["!merges"].length,null==f&&(f=rn(4)),f.write_shift(4,l),f)),m["!merges"].forEach(function(e){ro(d,176,rB(e))}),ro(d,178)),m["!links"].forEach(function(e){if(e[1].Target){var t,r,n=r6(a,-1,e[1].Target.replace(/#.*$/,""),r2.HLINK);ro(d,494,(t=rn(50+4*(e[1].Target.length+(e[1].Tooltip||"").length)),rB({s:rg(e[0]),e:rg(e[0])},t),rP("rId"+n,t),rC((-1==(r=e[1].Target.indexOf("#"))?"":e[1].Target.slice(r+1))||"",t),rC(e[1].Tooltip||"",t),rC("",t),t.slice(0,t.l)))}}),delete m["!links"],m["!margins"]&&ro(d,476,(h=m["!margins"],null==u&&(u=rn(48)),sU(h),s3.forEach(function(e){rH(h[e],u)}),u)),(!t||t.ignoreEC||void 0==t.ignoreEC)&&function(e,t){if(t&&t["!ref"]){var r,a;ro(e,648),ro(e,649,(r=rT(t["!ref"]),(a=rn(24)).write_shift(4,4),a.write_shift(4,1),rB(r,a),a)),ro(e,650)}}(d,m);if(m["!comments"].length>0){var b=r6(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",r2.VML);ro(d,551,rP("rId"+b)),m["!legacy"]=b}return ro(d,130),d.end()}:s2)(T,S,e,b))),u.sheets.push(p),r6(t.wbrels,-1,"worksheets/sheet"+m+"."+f,r2.WS[0]),w){var T,E,S,y,k,x=w["!comments"],_=!1,A="";x&&x.length>0&&(eQ(d,A="xl/comments"+m+"."+f,(y=A,k=t,(".bin"===y.slice(-4)?function(e){var t=ri(),r=[];return ro(t,628),ro(t,630),e.forEach(function(e){e[1].forEach(function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),ro(t,632,rC(e.a.slice(0,54))))})}),ro(t,631),ro(t,633),e.forEach(function(e){e[1].forEach(function(a){var n,s,i,o,c,l;a.iauthor=r.indexOf(a.a),ro(t,635,(n=[{s:rg(e[0]),e:rg(e[0])},a],null==s&&(s=rn(36)),s.write_shift(4,n[1].iauthor),rB(n[0],s),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s)),a.t&&a.t.length>0&&ro(t,637,(o=!1,null==i&&(o=!0,i=rn(23+4*a.t.length)),i.write_shift(1,1),rC(a.t,i),i.write_shift(4,1),c={ich:0,ifnt:0},(l=i)||(l=rn(4)),l.write_shift(2,c.ich||0),l.write_shift(2,c.ifnt||0),o?i.slice(0,i.l):i)),ro(t,636),delete a.iauthor})}),ro(t,634),ro(t,629),t.end()}:n7)(x,k))),u.comments.push(A),r6(b,-1,"../comments"+m+"."+f,r2.CMNT),_=!0),w["!legacy"]&&_&&eQ(d,"xl/drawings/vmlDrawing"+m+".vml",n6(m,w["!comments"])),delete w["!comments"],delete w["!legacy"]}b["!id"].rId1&&eQ(d,r4(p),r5(b))}return null!=t.Strings&&t.Strings.length>0&&(eQ(d,p="xl/sharedStrings."+f,(r=t.Strings,a=p,n=t,(".bin"===a.slice(-4)?function(e){var t,r=ri();ro(r,159,(t||(t=rn(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t));for(var a=0;a<e.length;++a)ro(r,19,nb(e[a]));return ro(r,160),r.end()}:nv)(r,n))),u.strs.push(p),r6(t.wbrels,-1,"sharedStrings."+f,r2.SST)),eQ(d,p="xl/workbook."+f,(s=p,i=t,(".bin"===s.slice(-4)?function(e,t){var r=ri();ro(r,131),ro(r,128,function(e,t){t||(t=rn(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return rC("SheetJS",t),rC(c.version,t),rC(c.version,t),rC("7262",t),t.length>t.l?t.slice(0,t.l):t}()),ro(r,153,(n=e.Workbook&&e.Workbook.WBProps||null,s||(s=rn(72)),i=0,n&&n.filterPrivacy&&(i|=8),s.write_shift(4,i),s.write_shift(4,0),rC(n&&n.CodeName||"ThisWorkbook",s),s.slice(0,s.l))),function(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,a,n=t.Workbook.Sheets,s=0,i=-1,o=-1;s<n.length;++s)n[s]&&(n[s].Hidden||-1!=i)?1==n[s].Hidden&&-1==o&&(o=s):i=s;o>i||(ro(e,135),ro(e,158,(r=i,a||(a=rn(29)),a.write_shift(-4,0),a.write_shift(-4,460),a.write_shift(4,28800),a.write_shift(4,17600),a.write_shift(4,500),a.write_shift(4,r),a.write_shift(4,r),a.write_shift(1,120),a.length>a.l?a.slice(0,a.l):a)),ro(e,136))}}(r,e,t);ro(r,143);for(var a=0;a!=e.SheetNames.length;++a){var n,s,i,o={Hidden:e.Workbook&&e.Workbook.Sheets&&e.Workbook.Sheets[a]&&e.Workbook.Sheets[a].Hidden||0,iTabID:a+1,strRelID:"rId"+(a+1),name:e.SheetNames[a]},l=void 0;ro(r,156,(l||(l=rn(127)),l.write_shift(4,o.Hidden),l.write_shift(4,o.iTabID),rP(o.strRelID,l),rC(o.name.slice(0,31),l),l.length>l.l?l.slice(0,l.l):l))}return ro(r,144),ro(r,132),r.end()}:ii)(e,i))),u.workbooks.push(p),r6(t.rels,1,p,r2.WB),eQ(d,p="xl/theme/theme1.xml",n4(e.Themes,t)),u.themes.push(p),r6(t.wbrels,-1,"theme/theme1.xml",r2.THEME),eQ(d,p="xl/styles."+f,(o=p,l=t,(".bin"===o.slice(-4)?function(e,t){var r,a,n,s,i,o,c,l=ri();return ro(l,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var a=e[0];a<=e[1];++a)null!=t[a]&&++r}),0!=r&&(ro(e,615,r_(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var a=r[0];a<=r[1];++a)null!=t[a]&&ro(e,44,function(e,t,r){r||(r=rn(6+4*t.length)),r.write_shift(2,e),rC(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),a}(a,t[a]))}),ro(e,616))}}(l,e.SSF),function(e){var t,r,a,n,s,i;ro(e,611,r_(1)),ro(e,43,(t={sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"},r||(r=rn(153)),r.write_shift(2,20*t.sz),(a=r)||(a=rn(2)),n=2*!!t.italic|8*!!t.strike|16*!!t.outline|32*!!t.shadow|64*!!t.condense|128*!!t.extend,a.write_shift(1,n),a.write_shift(1,0),r.write_shift(2,t.bold?700:400),s=0,"superscript"==t.vertAlign?s=1:"subscript"==t.vertAlign&&(s=2),r.write_shift(2,s),r.write_shift(1,t.underline||0),r.write_shift(1,t.family||0),r.write_shift(1,t.charset||0),r.write_shift(1,0),rV(t.color,r),i=0,"major"==t.scheme&&(i=1),"minor"==t.scheme&&(i=2),r.write_shift(1,i),rC(t.name,r),r.length>r.l?r.slice(0,r.l):r)),ro(e,612)}(l,e),ro(l,603,r_(2)),ro(l,45,nj({patternType:"none"})),ro(l,45,nj({patternType:"gray125"})),ro(l,604),ro(l,613,r_(1)),ro(l,46,(r||(r=rn(51)),r.write_shift(1,0),nX(null,r),nX(null,r),nX(null,r),nX(null,r),nX(null,r),r.length>r.l?r.slice(0,r.l):r)),ro(l,614),ro(l,626,r_(1)),ro(l,47,n$({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),ro(l,627),ro(l,617,r_((a=t.cellXfs).length)),a.forEach(function(e){ro(l,47,n$(e,0))}),ro(l,618),ro(l,619,r_(1)),ro(l,48,(n={xfId:0,builtinId:0,name:"Normal"},s||(s=rn(52)),s.write_shift(4,n.xfId),s.write_shift(2,1),s.write_shift(1,+n.builtinId),s.write_shift(1,0),rP(n.name||"",s),s.length>s.l?s.slice(0,s.l):s)),ro(l,620),ro(l,505,r_(0)),ro(l,506),ro(l,508,(i="TableStyleMedium9",o="PivotStyleMedium4",(c=rn(2052)).write_shift(4,0),rP(i,c),rP(o,c),c.length>c.l?c.slice(0,c.l):c)),ro(l,509),ro(l,279),l.end()}:nz)(e,l))),u.styles.push(p),r6(t.wbrels,-1,"styles."+f,r2.STY),e.vbaraw&&h&&(eQ(d,p="xl/vbaProject.bin",e.vbaraw),u.vba.push(p),r6(t.wbrels,-1,"vbaProject.bin",r2.VBA)),eQ(d,p="xl/metadata."+f,(".bin"===p.slice(-4)?function(){var e,t,r,a,n,s=ri();return ro(s,332),ro(s,334,r_(1)),ro(s,335,((t=rn(12+2*(e={name:"XLDAPR",version:12e4,flags:0xd06ac0b0}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),rC(e.name,t),t.slice(0,t.l))),ro(s,336),ro(s,339,((a=rn(8+2*(r="XLDAPR").length)).write_shift(4,1),rC(r,a),a.slice(0,a.l))),ro(s,52),ro(s,35,r_(514)),ro(s,4096,r_(0)),ro(s,4097,aO(1)),ro(s,36),ro(s,53),ro(s,340),ro(s,337,((n=rn(8)).write_shift(4,1),n.write_shift(4,1),n)),ro(s,51,function(e){var t=rn(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),ro(s,338),ro(s,333),s.end()}:n3)()),u.metadata.push(p),r6(t.wbrels,-1,"metadata."+f,r2.XLMETA),eQ(d,"[Content_Types].xml",r0(u,t)),eQ(d,"_rels/.rels",r5(t.rels)),eQ(d,"xl/_rels/workbook."+f+".rels",r5(t.wbrels)),delete t.revssf,delete t.ssf,d}(t,n):function(e,t){n5=1024,e&&!e.SSF&&(e.SSF=eW(z)),e&&e.SSF&&(ew(),eb(e.SSF),t.revssf=eC(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,sP?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,a=n9.indexOf(t.bookType)>-1,n=r1();i2(t=t||{});var s=e1(),i="",o=0;if(t.cellXfs=[],sB(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eQ(s,i="docProps/core.xml",aa(e.Props,t)),n.coreprops.push(i),r6(t.rels,2,i,r2.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,eQ(s,i,ao(e.Props,t)),n.extprops.push(i),r6(t.rels,3,i,r2.EXT_PROPS),e.Custprops!==e.Props&&ex(e.Custprops||{}).length>0&&(eQ(s,i="docProps/custom.xml",al(e.Custprops,t)),n.custprops.push(i),r6(t.rels,4,i,r2.CUST_PROPS));var f=["SheetJ5"];for(o=1,t.tcid=0;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];if((u||{})["!type"],eQ(s,i="xl/worksheets/sheet"+o+".xml",s2(o-1,t,e,h)),n.sheets.push(i),r6(t.wbrels,-1,"worksheets/sheet"+o+".xml",r2.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach(function(e){e[1].forEach(function(e){!0==e.T&&(g=!0)})}),g&&(eQ(s,m="xl/threadedComments/threadedComment"+o+".xml",function(e,t,r){var a=[e4,tA("ThreadedComments",null,{xmlns:tI.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(e){var n="";(e[1]||[]).forEach(function(s,i){if(!s.T)return void delete s.ID;s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==i?n=o.id:o.parentId=n,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(tA("threadedComment",tx("text",s.t||""),o))})}),a.push("</ThreadedComments>"),a.join("")}(d,f,t)),n.threadedcomments.push(m),r6(h,-1,"../threadedComments/threadedComment"+o+".xml",r2.TCMNT)),eQ(s,m="xl/comments"+o+".xml",n7(d,t)),n.comments.push(m),r6(h,-1,"../comments"+o+".xml",r2.CMNT),p=!0}u["!legacy"]&&p&&eQ(s,"xl/drawings/vmlDrawing"+o+".vml",n6(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&eQ(s,r4(i),r5(h))}return null!=t.Strings&&t.Strings.length>0&&(eQ(s,i="xl/sharedStrings.xml",nv(t.Strings,t)),n.strs.push(i),r6(t.wbrels,-1,"sharedStrings.xml",r2.SST)),eQ(s,i="xl/workbook.xml",ii(e,t)),n.workbooks.push(i),r6(t.rels,1,i,r2.WB),eQ(s,i="xl/theme/theme1.xml",n4(e.Themes,t)),n.themes.push(i),r6(t.wbrels,-1,"theme/theme1.xml",r2.THEME),eQ(s,i="xl/styles.xml",nz(e,t)),n.styles.push(i),r6(t.wbrels,-1,"styles.xml",r2.STY),e.vbaraw&&a&&(eQ(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),r6(t.wbrels,-1,"vbaProject.bin",r2.VBA)),eQ(s,i="xl/metadata.xml",n3()),n.metadata.push(i),r6(t.wbrels,-1,"metadata.xml",r2.XLMETA),f.length>1&&(eQ(s,i="xl/persons/person.xml",(r=[e4,tA("personList",null,{xmlns:tI.TCMNT,"xmlns:x":tN[0]}).replace(/[\/]>/,">")],f.forEach(function(e,t){r.push(tA("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))}),r.push("</personList>"),r.join(""))),n.people.push(i),r6(t.wbrels,-1,"persons/person.xml",r2.PEOPLE)),eQ(s,"[Content_Types].xml",r0(n,t)),eQ(s,"_rels/.rels",r5(t.rels)),eQ(s,"xl/_rels/workbook.xml.rels",r5(t.wbrels)),delete t.revssf,delete t.ssf,s}(t,n),n);default:throw Error("Unrecognized bookType |"+s.bookType+"|")}},Wp:()=>ol});var n,s,i,o,c={};c.version="0.18.5";var l=1200,f=1252,h=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],u={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},d=function(e){-1!=h.indexOf(e)&&(f=u[0]=e)},p=function(e){l=e,d(e)};function m(){p(1200),d(1252)}function g(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function v(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var b=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);if(255==t&&254==r){for(var a=e.slice(2),n=[],s=0;s<a.length>>1;++s)n[s]=String.fromCharCode(a.charCodeAt(2*s)+(a.charCodeAt(2*s+1)<<8));return n.join("")}return 254==t&&255==r?v(e.slice(2)):65279==t?e.slice(1):e},w=function(e){return String.fromCharCode(e)},T=function(e){return String.fromCharCode(e)},E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function S(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)s=(r=e.charCodeAt(l++))>>2,i=(3&r)<<4|(a=e.charCodeAt(l++))>>4,o=(15&a)<<2|(n=e.charCodeAt(l++))>>6,c=63&n,isNaN(a)?o=c=64:isNaN(n)&&(c=64),t+=E.charAt(s)+E.charAt(i)+E.charAt(o)+E.charAt(c);return t}function y(e){var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)t+=String.fromCharCode((s=E.indexOf(e.charAt(l++)))<<2|(i=E.indexOf(e.charAt(l++)))>>4),a=(15&i)<<4|(o=E.indexOf(e.charAt(l++)))>>2,64!==o&&(t+=String.fromCharCode(a)),n=(3&o)<<6|(c=E.indexOf(e.charAt(l++))),64!==c&&(t+=String.fromCharCode(n));return t}var k="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,x=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function _(e){return k?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}function A(e){return k?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}var C=function(e){return k?x(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function O(e){if("undefined"==typeof ArrayBuffer)return C(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=255&e.charCodeAt(a);return t}function R(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function I(e){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(e instanceof ArrayBuffer)return I(new Uint8Array(e));for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var N=k?function(e){return Buffer.concat(e.map(function(e){return Buffer.isBuffer(e)?e:x(e)}))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else if("string"==typeof e[t])throw"wtf";else a.set(new Uint8Array(e[t]),r);return a}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))},D=/\u0000/g,F=/[\u0001-\u0006]/g;function P(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function L(e,t){var r=""+e;return r.length>=t?r:eH("0",t-r.length)+r}function M(e,t){var r=""+e;return r.length>=t?r:eH(" ",t-r.length)+r}function U(e,t){var r=""+e;return r.length>=t?r:r+eH(" ",t-r.length)}function B(e,t){var r,a;return e>0x100000000||e<-0x100000000?(r=""+Math.round(e)).length>=t?r:eH("0",t-r.length)+r:(a=""+Math.round(e)).length>=t?a:eH("0",t-a.length)+a}function W(e,t){return t=t||0,e.length>=7+t&&(32|e.charCodeAt(t))==103&&(32|e.charCodeAt(t+1))==101&&(32|e.charCodeAt(t+2))==110&&(32|e.charCodeAt(t+3))==101&&(32|e.charCodeAt(t+4))==114&&(32|e.charCodeAt(t+5))==97&&(32|e.charCodeAt(t+6))==108}var H=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],V=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],z={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},G={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},j={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function X(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,c=1,l=0,f=0,h=Math.floor(n);l<t&&(o=(h=Math.floor(n))*i+s,f=h*l+c,!(n-h<5e-8));)n=1/(n-h),s=i,i=o,c=l,l=f;if(f>t&&(l>t?(f=c,o=s):(f=l,o=i)),!r)return[0,a*o,f];var u=Math.floor(a*o/f);return[u,a*o-u*f,f]}function Y(e,t,r){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(o.u)&&(o.u=0),t&&t.date1904&&(a+=1462),o.u>.9999&&(o.u=0,86400==++n&&(o.T=n=0,++a,++o.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var c,l,f,h=new Date(1900,0,1);h.setDate(h.getDate()+a-1),i=[h.getFullYear(),h.getMonth()+1,h.getDate()],s=h.getDay(),a<60&&(s=(s+6)%7),r&&(c=h,l=i,l[0]-=581,f=c.getDay(),c<60&&(f=(f+6)%7),s=f)}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,o.M=(n=Math.floor(n/60))%60,o.H=n=Math.floor(n/60),o.q=s,o}var K=new Date(1899,11,31,0,0,0),J=K.getTime(),q=new Date(1900,2,1,0,0,0);function Z(e,t){var r=e.getTime();return t?r-=1262304e5:e>=q&&(r+=864e5),(r-(J+(e.getTimezoneOffset()-K.getTimezoneOffset())*6e4))/864e5}function Q(e){return -1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function ee(e){var t,r,a,n,s,i=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return i>=-4&&i<=-1?s=e.toPrecision(10+i):9>=Math.abs(i)?(t=e<0?12:11,s=(r=Q(e.toFixed(12))).length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)):s=10===i?e.toFixed(10).substr(0,12):(a=Q(e.toFixed(11))).length>(e<0?12:11)||"0"===a||"-0"===a?e.toPrecision(6):a,Q(-1==(n=s.toUpperCase()).indexOf("E")?n:n.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function et(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):ee(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return eg(14,Z(e,t&&t.date1904),t)}throw Error("unsupported value in General format: "+e)}function er(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var ea=/%/g,en=/# (\?+)( ?)\/( ?)(\d+)/,es=/^#*0*\.([0#]+)/,ei=/\).*[0#]/,eo=/\(###\) ###\\?-####/;function ec(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function el(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function ef(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function eh(e,t,r){return(0|r)===r?function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(ei)){var n,s=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",s,a):"("+e("n",s,-a)+")"}if(44===r.charCodeAt(r.length-1)){for(var i=r,o=i.length-1;44===i.charCodeAt(o-1);)--o;return eh(t,i.substr(0,o),a/Math.pow(10,3*(i.length-o)))}if(-1!==r.indexOf("%"))return l=(c=r).replace(ea,""),f=c.length-l.length,eh(t,l,a*Math.pow(10,2*f))+eH("%",f);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),!(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).match(/[Ee]/)){var o=Math.floor(Math.log(r)*Math.LOG10E);-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(o-a.length+i):a+="E+"+(o-i),a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var c,l,f,h,u,d,p,m=Math.abs(a),g=a<0?"-":"";if(r.match(/^00+$/))return g+L(m,r.length);if(r.match(/^[#?]+$/))return h=""+a,0===a&&(h=""),h.length>r.length?h:ec(r.substr(0,r.length-h.length))+h;if(u=r.match(en))return g+(0===m?"":""+m)+eH(" ",(n=u)[1].length+2+n[4].length);if(r.match(/^#+0+$/))return g+L(m,r.length-r.indexOf("0"));if(u=r.match(es))return h=(h=(""+a).replace(/^([^\.]+)$/,"$1."+ec(u[1])).replace(/\.$/,"."+ec(u[1]))).replace(/\.(\d*)$/,function(e,t){return"."+t+eH("0",ec(u[1]).length-t.length)}),-1!==r.indexOf("0.")?h:h.replace(/^0\./,".");if(u=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return g+(""+m).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,u[1].length?"0.":".");if(u=r.match(/^#{1,3},##0(\.?)$/))return g+er(""+m);if(u=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):er(""+a)+"."+eH("0",u[1].length);if(u=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(u=r.match(/^([0#]+)(\\?-([0#]+))+$/))return h=P(e(t,r.replace(/[\\-]/g,""),a)),d=0,P(P(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return d<h.length?h.charAt(d++):"0"===e?"0":""}));if(r.match(eo))return"("+(h=e(t,"##########",a)).substr(0,3)+") "+h.substr(3,3)+"-"+h.substr(6);var v="";if(u=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return p=X(m,Math.pow(10,d=Math.min(u[4].length,7))-1,!1),h=""+g," "==(v=eh("n",u[1],p[1])).charAt(v.length-1)&&(v=v.substr(0,v.length-1)+"0"),h+=v+u[2]+"/"+u[3],(v=U(p[2],d)).length<u[4].length&&(v=ec(u[4].substr(u[4].length-v.length))+v),h+=v;if(u=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return g+((p=X(m,Math.pow(10,d=Math.min(Math.max(u[1].length,u[4].length),7))-1,!0))[0]||(p[1]?"":"0"))+" "+(p[1]?M(p[1],d)+u[2]+"/"+u[3]+U(p[2],d):eH(" ",2*d+1+u[2].length+u[3].length));if(u=r.match(/^[#0?]+$/))return(h=""+a,r.length<=h.length)?h:ec(r.substr(0,r.length-h.length))+h;if(u=r.match(/^([#0]+)\.([#0]+)$/)){d=(h=""+a.toFixed(Math.min(u[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var b=r.indexOf(".")-d,w=r.length-h.length-b;return ec(r.substr(0,b)+h+r.substr(r.length-w))}if(u=r.match(/^00,000\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):er(""+a).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?L(0,3-e.length):"")+e})+"."+L(0,u[1].length);switch(r){case"###,###":case"##,###":case"#,###":var T=er(""+m);return"0"!==T?g+T:"";default:if(r.match(/\.[0#?]*$/))return e(t,r.slice(0,r.lastIndexOf(".")),a)+ec(r.slice(r.lastIndexOf(".")))}throw Error("unsupported format |"+r+"|")}(e,t,r):function e(t,r,a){if(40===t.charCodeAt(0)&&!r.match(ei)){var n,s,i,o,c,l,f=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",f,a):"("+e("n",f,-a)+")"}if(44===r.charCodeAt(r.length-1)){for(var h=r,u=h.length-1;44===h.charCodeAt(u-1);)--u;return eh(t,h.substr(0,u),a/Math.pow(10,3*(h.length-u)))}if(-1!==r.indexOf("%"))return p=(d=r).replace(ea,""),m=d.length-p.length,eh(t,p,a*Math.pow(10,2*m))+eH("%",m);if(-1!==r.indexOf("E"))return function e(t,r){var a,n=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),-1===(a=(r/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).indexOf("e")){var o=Math.floor(Math.log(r)*Math.LOG10E);for(-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(o-a.length+i):a+="E+"+(o-i);"0."===a.substr(0,2);)a=(a=a.charAt(0)+a.substr(2,s)+"."+a.substr(2+s)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,a){return t+r+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=r.toExponential(n);return t.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),t.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(r,a);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),a);var d,p,m,g,v,b,w,T=Math.abs(a),E=a<0?"-":"";if(r.match(/^00+$/))return E+B(T,r.length);if(r.match(/^[#?]+$/))return"0"===(g=B(a,0))&&(g=""),g.length>r.length?g:ec(r.substr(0,r.length-g.length))+g;if(v=r.match(en))return o=Math.floor((i=Math.round(T*(s=parseInt((n=v)[4],10))))/s),c=i-o*s,E+(0===o?"":""+o)+" "+(0===c?eH(" ",n[1].length+1+n[4].length):M(c,n[1].length)+n[2]+"/"+n[3]+L(s,n[4].length));if(r.match(/^#+0+$/))return E+B(T,r.length-r.indexOf("0"));if(v=r.match(es))return g=el(a,v[1].length).replace(/^([^\.]+)$/,"$1."+ec(v[1])).replace(/\.$/,"."+ec(v[1])).replace(/\.(\d*)$/,function(e,t){return"."+t+eH("0",ec(v[1]).length-t.length)}),-1!==r.indexOf("0.")?g:g.replace(/^0\./,".");if(v=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return E+el(T,v[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,v[1].length?"0.":".");if(v=r.match(/^#{1,3},##0(\.?)$/))return E+er(B(T,0));if(v=r.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(t,r,-a):er(""+(Math.floor(a)+ +((l=v[1].length)<(""+Math.round((a-Math.floor(a))*Math.pow(10,l))).length)))+"."+L(ef(a,v[1].length),v[1].length);if(v=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),a);if(v=r.match(/^([0#]+)(\\?-([0#]+))+$/))return g=P(e(t,r.replace(/[\\-]/g,""),a)),b=0,P(P(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return b<g.length?g.charAt(b++):"0"===e?"0":""}));if(r.match(eo))return"("+(g=e(t,"##########",a)).substr(0,3)+") "+g.substr(3,3)+"-"+g.substr(6);var S="";if(v=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return w=X(T,Math.pow(10,b=Math.min(v[4].length,7))-1,!1),g=""+E," "==(S=eh("n",v[1],w[1])).charAt(S.length-1)&&(S=S.substr(0,S.length-1)+"0"),g+=S+v[2]+"/"+v[3],(S=U(w[2],b)).length<v[4].length&&(S=ec(v[4].substr(v[4].length-S.length))+S),g+=S;if(v=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return E+((w=X(T,Math.pow(10,b=Math.min(Math.max(v[1].length,v[4].length),7))-1,!0))[0]||(w[1]?"":"0"))+" "+(w[1]?M(w[1],b)+v[2]+"/"+v[3]+U(w[2],b):eH(" ",2*b+1+v[2].length+v[3].length));if(v=r.match(/^[#0?]+$/))return(g=B(a,0),r.length<=g.length)?g:ec(r.substr(0,r.length-g.length))+g;if(v=r.match(/^([#0?]+)\.([#0]+)$/)){b=(g=""+a.toFixed(Math.min(v[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var y=r.indexOf(".")-b,k=r.length-g.length-y;return ec(r.substr(0,y)+g+r.substr(r.length-k))}if(v=r.match(/^00,000\.([#0]*0)$/))return b=ef(a,v[1].length),a<0?"-"+e(t,r,-a):er(a<0x7fffffff&&a>-0x80000000?""+(a>=0?0|a:a-1|0):""+Math.floor(a)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?L(0,3-e.length):"")+e})+"."+L(b,v[1].length);switch(r){case"###,##0.00":return e(t,"#,##0.00",a);case"###,###":case"##,###":case"#,###":var x=er(B(T,0));return"0"!==x?E+x:"";case"###,###.00":return e(t,"###,##0.00",a).replace(/^0\./,".");case"#,###.00":return e(t,"#,##0.00",a).replace(/^0\./,".")}throw Error("unsupported format |"+r+"|")}(e,t,r)}var eu=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ed(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":W(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase()||"AM/PM"===e.substr(t,5).toUpperCase()||"上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(eu))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(" "==e.charAt(t)||"*"==e.charAt(t))&&++t;break;case"(":case")":case" ":default:++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);}return!1}var ep=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function em(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function eg(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:z)[e])&&(a=r.table&&r.table[G[e]]||z[G[e]]),null==a&&(a=j[e]||"General")}if(W(a,0))return et(t,r);t instanceof Date&&(t=Z(t,r.date1904));var n=function(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(ep),o=r[1].match(ep);return em(t,i)?[a,r[0]]:em(t,o)?[a,r[1]]:[a,r[null!=i&&null!=o?2:1]]}return[a,s]}(a,t);if(W(n[1]))return et(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,o=[],c="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!W(e,l))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},l+=7;break;case'"':for(c="";34!==(i=e.charCodeAt(++l))&&l<e.length;)c+=String.fromCharCode(i);o[o.length]={t:"t",v:c},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++l;break;case"_":o[o.length]={t:"t",v:" "},l+=2;break;case"@":o[o.length]={t:"T",v:t},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=Y(t,r,"2"===e.charAt(l+1))))return"";o[o.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||null==n&&null==(n=Y(t,r)))return"";for(c=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)c+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:c},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=Y(t,r)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":for(c=f;"]"!==e.charAt(l++)&&l<e.length;)c+=e.charAt(l);if("]"!==c.slice(-1))throw'unterminated "[" block: |'+c+"|";if(c.match(eu)){if(null==n&&null==(n=Y(t,r)))return"";o[o.length]={t:"Z",v:c.toLowerCase()},h=c.charAt(1)}else c.indexOf("$")>-1&&(c=(c.match(/\$([^-\[\]]*)/)||[])[1]||"$",ed(e)||(o[o.length]={t:"t",v:c}));break;case".":if(null!=n){for(c=f;++l<e.length&&"0"===(f=e.charAt(l));)c+=f;o[o.length]={t:"s",v:c};break}case"0":case"#":for(c=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)c+=f;o[o.length]={t:"n",v:c};break;case"?":for(c=f;e.charAt(++l)===f;)c+=f;o[o.length]={t:f,v:c},h=f;break;case"*":++l,(" "==e.charAt(l)||"*"==e.charAt(l))&&++l;break;case"(":case")":o[o.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(c=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)c+=e.charAt(l);o[o.length]={t:"D",v:c};break;case" ":o[o.length]={t:f,v:f},++l;break;case"$":o[o.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++l}var g,v=0,b=0;for(l=o.length-1,h="t";l>=0;--l)switch(o[l].t){case"h":case"H":o[l].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[l].v.match(/\.0+$/))&&(b=Math.max(b,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[l].t;break;case"m":"s"===h&&(o[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[l].v.match(/[Hh]/)&&(v=1),v<2&&o[l].v.match(/[Mm]/)&&(v=2),v<3&&o[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var w,T="";for(l=0;l<o.length;++l)switch(o[l].t){case"t":case"T":case" ":case"D":break;case"X":o[l].v="",o[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[l].v=function(e,t,r,a){var n,s="",i=0,o=0,c=r.y,l=0;switch(e){case 98:c=r.y+543;case 121:switch(t.length){case 1:case 2:n=c%100,l=2;break;default:n=c%1e4,l=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,l=t.length;break;case 3:return V[r.m-1][1];case 5:return V[r.m-1][0];default:return V[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,l=t.length;break;case 3:return H[r.q][0];default:return H[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;if(0===r.u&&("s"==t||"ss"==t))return L(r.S,t.length);if((i=Math.round((o=a>=2?3===a?1e3:100:1===a?10:1)*(r.S+r.u)))>=60*o&&(i=0),"s"===t)return 0===i?"0":""+i/o;if(s=L(i,2+a),"ss"===t)return s.substr(0,2);return"."+s.substr(2,t.length-1);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=(24*r.D+r.H)*60+r.M;break;case"[s]":case"[ss]":n=((24*r.D+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=3===t.length?1:2;break;case 101:n=c,l=1}return l>0?L(n,l):""}(o[l].t.charCodeAt(0),o[l].v,n,b),o[l].t="t";break;case"n":case"?":for(w=l+1;null!=o[w]&&("?"===(f=o[w].t)||"D"===f||(" "===f||"t"===f)&&null!=o[w+1]&&("?"===o[w+1].t||"t"===o[w+1].t&&"/"===o[w+1].v)||"("===o[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[w].v||" "===o[w].v&&null!=o[w+1]&&"?"==o[w+1].t));)o[l].v+=o[w].v,o[w]={v:"",t:";"},++w;T+=o[l].v,l=w-1;break;case"G":o[l].t="t",o[l].v=et(t,r)}var E,S,y="";if(T.length>0){40==T.charCodeAt(0)?(E=t<0&&45===T.charCodeAt(0)?-t:t,S=eh("n",T,E)):(S=eh("n",T,E=t<0&&a>1?-t:t),E<0&&o[0]&&"t"==o[0].t&&(S=S.substr(1),o[0].v="-"+o[0].v)),w=S.length-1;var k=o.length;for(l=0;l<o.length;++l)if(null!=o[l]&&"t"!=o[l].t&&o[l].v.indexOf(".")>-1){k=l;break}var x=o.length;if(k===o.length&&-1===S.indexOf("E")){for(l=o.length-1;l>=0;--l)null!=o[l]&&-1!=="n?".indexOf(o[l].t)&&(w>=o[l].v.length-1?(w-=o[l].v.length,o[l].v=S.substr(w+1,o[l].v.length)):w<0?o[l].v="":(o[l].v=S.substr(0,w+1),w=-1),o[l].t="t",x=l);w>=0&&x<o.length&&(o[x].v=S.substr(0,w+1)+o[x].v)}else if(k!==o.length&&-1===S.indexOf("E")){for(w=S.indexOf(".")-1,l=k;l>=0;--l)if(null!=o[l]&&-1!=="n?".indexOf(o[l].t)){for(s=o[l].v.indexOf(".")>-1&&l===k?o[l].v.indexOf(".")-1:o[l].v.length-1,y=o[l].v.substr(s+1);s>=0;--s)w>=0&&("0"===o[l].v.charAt(s)||"#"===o[l].v.charAt(s))&&(y=S.charAt(w--)+y);o[l].v=y,o[l].t="t",x=l}for(w>=0&&x<o.length&&(o[x].v=S.substr(0,w+1)+o[x].v),w=S.indexOf(".")+1,l=k;l<o.length;++l)if(null!=o[l]&&(-1!=="n?(".indexOf(o[l].t)||l===k)){for(s=o[l].v.indexOf(".")>-1&&l===k?o[l].v.indexOf(".")+1:0,y=o[l].v.substr(0,s);s<o[l].v.length;++s)w<S.length&&(y+=S.charAt(w++));o[l].v=y,o[l].t="t",x=l}}}for(l=0;l<o.length;++l)null!=o[l]&&"n?".indexOf(o[l].t)>-1&&(E=a>1&&t<0&&l>0&&"-"===o[l-1].v?-t:t,o[l].v=eh(o[l].t,o[l].v,E),o[l].t="t");var _="";for(l=0;l!==o.length;++l)null!=o[l]&&(_+=o[l].v);return _}(n[1],t,r,n[0])}function ev(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r){if(void 0==z[r]){t<0&&(t=r);continue}if(z[r]==e){t=r;break}}t<0&&(t=391)}return z[t]=e,t}function eb(e){for(var t=0;392!=t;++t)void 0!==e[t]&&ev(e[t],t)}function ew(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',z=e}var eT={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},eE=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,eS=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}(),r=function(e){var t=0,r=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],o=r[4],c=r[5],l=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[(a^e.charCodeAt(n++))&255];return~a},e.buf=function(e,r){for(var b=-1^r,w=e.length-15,T=0;T<w;)b=v[e[T++]^255&b]^g[e[T++]^b>>8&255]^m[e[T++]^b>>16&255]^p[e[T++]^b>>>24]^d[e[T++]]^u[e[T++]]^h[e[T++]]^f[e[T++]]^l[e[T++]]^c[e[T++]]^o[e[T++]]^i[e[T++]]^s[e[T++]]^n[e[T++]]^a[e[T++]]^t[e[T++]];for(w+=15;T<w;)b=b>>>8^t[(b^e[T++])&255];return~b},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,o=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[(a^i)&255]:i<2048?a=(a=a>>>8^t[(a^(192|i>>6&31))&255])>>>8^t[(a^(128|63&i))&255]:i>=55296&&i<57344?(i=(1023&i)+64,o=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[(a^(240|i>>8&7))&255])>>>8^t[(a^(128|i>>2&63))&255])>>>8^t[(a^(128|o>>6&15|(3&i)<<4))&255])>>>8^t[(a^(128|63&o))&255]):a=(a=(a=a>>>8^t[(a^(224|i>>12&15))&255])>>>8^t[(a^(128|i>>6&63))&255])>>>8^t[(a^(128|63&i))&255];return~a},e}(),ey=function(){var e,t,r={};function a(e){if("/"==e.charAt(e.length-1))return -1===e.slice(0,-1).indexOf("/")?e:a(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(0,t+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(t+1)}function s(e){rr(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};21589===a&&(1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime))),e.l=s,t[a]=i}return t}function i(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return eo(e,t);if((32|e[0])==109&&(32|e[1])==105)return function(e,t){if("mime-version:"!=b(e.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var r=t&&t.root||"",a=(k&&Buffer.isBuffer(e)?e.toString("binary"):b(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if((s=a[n],/^Content-Location:/i.test(s))&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw Error("MAD cannot find boundary");var o="--"+(i[1]||""),c={FileIndex:[],FullPaths:[]};l(c);var f,h=0;for(n=0;n<a.length;++n){var u=a[n];(u===o||u===o+"--")&&(h++&&function(e,t,r){for(var a,n="",s="",i="",o=0;o<10;++o){var c=t[o];if(!c||c.match(/^\s*$/))break;var l=c.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++o,s.toLowerCase()){case"base64":a=C(y(t.slice(o).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return C(t.join("\r\n"))}(t.slice(o));break;default:throw Error("Unsupported Content-Transfer-Encoding "+s)}var f=el(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}(c,a.slice(f,n),r),f=n)}return c}(e,t);if(e.length<512)throw Error("CFB file size "+e.length+" < 512");var r=3,a=512,n=0,s=0,i=0,o=0,f=0,h=[],m=e.slice(0,512);rr(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(p,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=g[0]){case 3:a=512;break;case 4:a=4096;break;case 0:if(0==g[1])return eo(e,t);default:throw Error("Major Version: Expected 3 or 4 saw "+r)}512!==a&&rr(m=e.slice(0,a),28);var v=e.slice(0,a),w=m,T=r,E=9;switch(w.l+=2,E=w.read_shift(2)){case 9:if(3!=T)throw Error("Sector Shift: Expected 9 saw "+E);break;case 12:if(4!=T)throw Error("Sector Shift: Expected 12 saw "+E);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+E)}w.chk("0600","Mini Sector Shift: "),w.chk("000000000000","Reserved: ");var S=m.read_shift(4,"i");if(3===r&&0!==S)throw Error("# Directory Sectors: Expected 0 saw "+S);m.l+=4,i=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),o=m.read_shift(4,"i"),n=m.read_shift(4,"i"),f=m.read_shift(4,"i"),s=m.read_shift(4,"i");for(var x=-1,_=0;_<109&&!((x=m.read_shift(4,"i"))<0);++_)h[_]=x;var A=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,a);!function e(t,r,a,n,s){var i=d;if(t===d){if(0!==r)throw Error("DIFAT chain shorter than expected")}else if(-1!==t){var o=a[t],c=(n>>>2)-1;if(!o)return;for(var l=0;l<c&&(i=t5(o,4*l))!==d;++l)s.push(i);e(t5(o,n-4),r-1,a,n,s)}}(f,s,A,a,h);var O=function(e,t,r,a){var n=e.length,s=[],i=[],o=[],c=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(o=[],(u=f+t)>=n&&(u-=n),!i[u]){c=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,c.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m]||p[h=t5(e[m],d)])break}s[u]={nodes:o,data:tP([c])}}return s}(A,i,h,a);O[i].name="!Directory",n>0&&o!==d&&(O[o].name="!MiniFAT"),O[h[0]].name="!FAT",O.fat_addrs=h,O.ssz=a;var R=[],I=[],D=[];(function(e,t,r,a,n,s,i,o){for(var l,f=0,h=2*!!a.length,p=t[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);rr(v,64),g=v.read_shift(2),l=tM(v,0,g-h),a.push(l);var b={name:l,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.ct=c(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.mt=c(v,v.l-8)),b.start=v.read_shift(4,"i"),b.size=v.read_shift(4,"i"),b.size<0&&b.start<0&&(b.size=b.type=0,b.start=d,b.name=""),5===b.type?(f=b.start,n>0&&f!==d&&(t[f].name="!StreamData")):b.size>=4096?(b.storage="fat",void 0===t[b.start]&&(t[b.start]=function(e,t,r,a,n){var s=[],i=[];n||(n=[]);var o=a-1,c=0,l=0;for(c=t;c>=0;){n[c]=!0,s[s.length]=c,i.push(e[c]);var f=r[Math.floor(4*c/a)];if(a<4+(l=4*c&o))throw Error("FAT boundary crossed: "+c+" 4 "+a);if(!e[f])break;c=t5(e[f],l)}return{nodes:s,data:tP([i])}}(r,b.start,t.fat_addrs,t.ssz)),t[b.start].name=b.name,b.content=t[b.start].data.slice(0,b.size)):(b.storage="minifat",b.size<0?b.size=0:f!==d&&b.start!==d&&t[f]&&(b.content=function(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*u,i*u+u)),n-=u,i=t5(r,4*i);return 0===s.length?rn(0):N(s).slice(0,e.size)}(b,t[f].data,(t[o]||{}).data))),b.content&&rr(b.content,0),s[l]=b,i.push(b)}})(i,O,A,R,n,{},I,o),function(e,t,r){for(var a=0,n=0,s=0,i=0,o=0,c=r.length,l=[],f=[];a<c;++a)l[a]=f[a]=a,t[a]=r[a];for(;o<f.length;++o)n=e[a=f[o]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<o&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<o&&f.push(s));for(a=1;a<c;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<c;++a)if(0!==e[a].type){if((o=a)!=l[o])do o=l[o],t[a]=t[o]+"/"+t[a];while(0!==o&&-1!==l[o]&&o!=l[o]);l[a]=-1}for(t[0]+="/",a=1;a<c;++a)2!==e[a].type&&(t[a]+="/")}(I,D,R),R.shift();var F={FileIndex:I,FullPaths:D};return t&&t.raw&&(F.raw={header:v,sectors:A}),F}function c(e,t){return new Date((t3(e,t+4)/1e7*0x100000000+t3(e,t)/1e7-0x2b6109100)*1e3)}function l(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(!ey.find(e,"/"+t)){var r=rn(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),f(e)}}(e)}function f(e,t){l(e);for(var r=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var o=e.FileIndex[i];switch(o.type){case 0:s?r=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(r=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(r=!0);break;default:r=!0}}if(r||t){var c=new Date(1987,1,19),f=0,h=Object.create?Object.create(null):{},u=[];for(i=0;i<e.FullPaths.length;++i)h[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&u.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<u.length;++i){var d=a(u[i][0]);(s=h[d])||(u.push([d,{name:n(d).replace("/",""),type:1,clsid:g,ct:c,mt:c,content:null}]),h[d]=!0)}for(u.sort(function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])}),e.FullPaths=[],e.FileIndex=[],i=0;i<u.length;++i)e.FullPaths[i]=u[i][0],e.FileIndex[i]=u[i][1];for(i=0;i<u.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||g,0===i)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(f=i+1;f<u.length&&a(e.FullPaths[f])!=m;++f);for(p.C=f>=u.length?-1:f,f=i+1;f<u.length&&a(e.FullPaths[f])!=a(m);++f);p.R=f>=u.length?-1:f,p.type=1}else a(e.FullPaths[i+1]||"")==a(m)&&(p.R=i+1),p.type=2}}}function h(e,r){var a=r||{};if("mad"==a.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],c=1;c<e.FullPaths.length;++c)if(i=e.FullPaths[c].slice(s.length),(o=e.FileIndex[c]).size&&o.content&&"\x01Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});for(var l=o.content,f=k&&Buffer.isBuffer(l)?l.toString("binary"):b(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+function(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&ec[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&ec[a[1]]?ec[a[1]]:"application/octet-stream"}(o,i)),n.push(""),n.push(m?function(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)});"\n"==(t=t.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var r=[],a=t.split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0==s.length){r.push("");continue}for(var i=0;i<s.length;){var o=76,c=s.slice(i,i+o);"="==c.charAt(o-1)?o--:"="==c.charAt(o-2)?o-=2:"="==c.charAt(o-3)&&(o-=3),c=s.slice(i,i+o),(i+=o)<s.length&&(c+="="),r.push(c)}}return r.join("\r\n")}(f):function(e){for(var t=S(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,a);if(f(e),"zip"===a.fileType)return function(e,r){var a=[],n=[],s=rn(1),i=8*!!(r||{}).compression,o=0,c=0,l=0,f=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(o=1;o<e.FullPaths.length;++o)if(u=e.FullPaths[o].slice(h.length),(d=e.FileIndex[o]).size&&d.content&&"\x01Sh33tJ5"!=u){var g,v=l,b=rn(u.length);for(c=0;c<u.length;++c)b.write_shift(1,127&u.charCodeAt(c));b=b.slice(0,b.l),p[f]=eS.buf(d.content,0);var w=d.content;8==i&&(g=w,w=t?t.deflateRawSync(g):Q(g)),(s=rn(30)).write_shift(4,0x4034b50),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),d.mt?function(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}(s,d.mt):s.write_shift(4,0),s.write_shift(-4,(0,p[f])),s.write_shift(4,(0,w.length)),s.write_shift(4,(0,d.content.length)),s.write_shift(2,b.length),s.write_shift(2,0),l+=s.length,a.push(s),l+=b.length,a.push(b),l+=w.length,a.push(w),(s=rn(46)).write_shift(4,0x2014b50),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(-4,p[f]),s.write_shift(4,w.length),s.write_shift(4,d.content.length),s.write_shift(2,b.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,v),m+=s.l,n.push(s),m+=b.length,n.push(b),++f}return(s=rn(22)).write_shift(4,0x6054b50),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,f),s.write_shift(2,f),s.write_shift(4,m),s.write_shift(4,l),s.write_shift(2,0),N([N(a),N(n),s])}(e,a);var n=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,o=t+7>>3,c=t+127>>7,l=o+r+i+c,f=l+127>>7,h=f<=109?0:Math.ceil((f-109)/127);l+f+h+127>>7>f;)h=++f<=109?0:Math.ceil((f-109)/127);var u=[1,h,f,c,i,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),s=rn(n[7]<<9),i=0,o=0;for(i=0;i<8;++i)s.write_shift(1,m[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,n[2]),s.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:d),s.write_shift(4,n[3]),s.write_shift(-4,n[1]?n[0]-1:d),s.write_shift(4,n[1]),i=0;i<109;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);if(n[1])for(o=0;o<n[1];++o){for(;i<236+127*o;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);s.write_shift(-4,o===n[1]-1?d:o+1)}var c=function(e){for(o+=e;i<o-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,d))};for(o=(i=0)+n[1];i<o;++i)s.write_shift(-4,v.DIFSECT);for(o+=n[2];i<o;++i)s.write_shift(-4,v.FATSECT);c(n[3]),c(n[4]);for(var l=0,h=0,u=e.FileIndex[0];l<e.FileIndex.length;++l)(u=e.FileIndex[l]).content&&((h=u.content.length)<4096||(u.start=o,c(h+511>>9)));for(c(n[6]+7>>3);511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(l=0,o=i=0;l<e.FileIndex.length;++l)(u=e.FileIndex[l]).content&&(h=u.content.length)&&!(h>=4096)&&(u.start=o,c(h+63>>6));for(;511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(i=0;i<n[4]<<2;++i){var p=e.FullPaths[i];if(!p||0===p.length){for(l=0;l<17;++l)s.write_shift(4,0);for(l=0;l<3;++l)s.write_shift(4,-1);for(l=0;l<12;++l)s.write_shift(4,0);continue}u=e.FileIndex[i],0===i&&(u.start=u.size?u.start-1:d);var g=0===i&&a.root||u.name;if(h=2*(g.length+1),s.write_shift(64,g,"utf16le"),s.write_shift(2,h),s.write_shift(1,u.type),s.write_shift(1,u.color),s.write_shift(-4,u.L),s.write_shift(-4,u.R),s.write_shift(-4,u.C),u.clsid)s.write_shift(16,u.clsid,"hex");else for(l=0;l<4;++l)s.write_shift(4,0);s.write_shift(4,u.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,u.start),s.write_shift(4,u.size),s.write_shift(4,0)}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>=4096)if(s.l=u.start+1<<9,k&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+511&-512;else{for(l=0;l<u.size;++l)s.write_shift(1,u.content[l]);for(;511&l;++l)s.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>0&&u.size<4096)if(k&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+63&-64;else{for(l=0;l<u.size;++l)s.write_shift(1,u.content[l]);for(;63&l;++l)s.write_shift(1,0)}if(k)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}r.version="1.2.1";var u=64,d=-2,p="d0cf11e0a1b11ae1",m=[208,207,17,224,161,177,26,225],g="00000000000000000000000000000000",v={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:p,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:g,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function b(e){for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}for(var w=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],T=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],E=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],O="undefined"!=typeof Uint8Array,R=O?new Uint8Array(256):[],I=0;I<256;++I)R[I]=function(e){var t=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(t>>16|t>>8|t)&255}(I);function P(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function L(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function M(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function U(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a||(i|=e[n+1]<<8-a,r<16-a||(i|=e[n+2]<<16-a,r<24-a))?i&s:(i|=e[n+3]<<24-a)&s}function B(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function W(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function H(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function V(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(k){var s=A(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(O){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function z(e){for(var t=Array(e),r=0;r<e;++r)t[r]=0;return t}function G(e,t,r){var a=1,n=0,s=0,i=0,o=0,c=e.length,l=O?new Uint16Array(32):z(32);for(s=0;s<32;++s)l[s]=0;for(s=c;s<r;++s)e[s]=0;c=e.length;var f=O?new Uint16Array(c):z(c);for(s=0;s<c;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(s=1,l[0]=0;s<=a;++s)l[s+16]=o=o+l[s-1]<<1;for(s=0;s<c;++s)0!=(o=e[s])&&(f[s]=l[o+16]++);var h=0;for(s=0;s<c;++s)if(0!=(h=e[s]))for(o=function(e,t){var r=R[255&e];return t<=8?r>>>8-t:(r=r<<8|R[e>>8&255],t<=16)?r>>>16-t:(r=r<<8|R[e>>16&255])>>>24-t}(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[o|i<<h]=15&h|s<<4;return a}var j=O?new Uint16Array(512):z(512),X=O?new Uint16Array(32):z(32);if(!O){for(var Y=0;Y<512;++Y)j[Y]=0;for(Y=0;Y<32;++Y)X[Y]=0}for(var K=[],J=0;J<32;J++)K.push(5);G(K,X,32);var q=[];for(J=0;J<=143;J++)q.push(8);for(;J<=255;J++)q.push(9);for(;J<=279;J++)q.push(7);for(;J<=287;J++)q.push(8);G(q,j,288);var Z=function(){for(var e=O?new Uint8Array(32768):[],t=0,r=0;t<E.length-1;++t)for(;r<E[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=O?new Uint8Array(259):[];for(t=0,r=0;t<T.length-1;++t)for(;r<T[t+1];++r)a[r]=t;return function(t,r){if(t.length<8){for(var n=0;n<t.length;){var s=Math.min(65535,t.length-n),i=n+s==t.length;for(r.write_shift(1,+i),r.write_shift(2,s),r.write_shift(2,65535&~s);s-- >0;)r[r.l++]=t[n++]}return r.l}return function(t,r){for(var n=0,s=0,i=O?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(n=B(r,n,+(s+o==t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];n=8*r.l;continue}n=B(r,n,+(s+o==t.length)+2);for(var c=0;o-- >0;){var l,f,h=t[s],u=-1,d=0;if((u=i[c=(c<<5^h)&32767])&&((u|=-32768&s)>s&&(u-=32768),u<s))for(;t[u+d]==t[s+d]&&d<250;)++d;if(d>2){(h=a[d])<=22?n=W(r,n,R[h+1]>>1)-1:(W(r,n,3),W(r,n+=5,R[h-23]>>5),n+=3);var p=h<8?0:h-4>>2;p>0&&(H(r,n,d-T[h]),n+=p),n=W(r,n,R[h=e[s-u]]>>3)-3;var m=h<4?0:h-2>>1;m>0&&(H(r,n,s-u-E[h]),n+=m);for(var g=0;g<d;++g)i[c]=32767&s,c=(c<<5^t[s])&32767,++s;o-=d-1}else h<=143?h+=48:(f=(1&(f=1))<<(7&(l=n)),r[l>>>3]|=f,n=l+1),n=W(r,n,R[h]),i[c]=32767&s,++s}n=W(r,n,0)-1}return r.l=(n+7)/8|0,r.l}(t,r)}}();function Q(e){var t=rn(50+Math.floor(1.1*e.length)),r=Z(e,t);return t.slice(0,r)}var ee=O?new Uint16Array(32768):z(32768),et=O?new Uint16Array(32768):z(32768),er=O?new Uint16Array(128):z(128),ea=1,en=1;function es(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[_(t),2];for(var r=0,a=0,n=A(t||262144),s=0,i=n.length>>>0,o=0,c=0;(1&a)==0;){if(a=P(e,r),r+=3,a>>>1==0){7&r&&(r+=8-(7&r));var l=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,l>0)for(!t&&i<s+l&&(i=(n=V(n,s+l)).length);l-- >0;)n[s++]=e[r>>>3],r+=8;continue}for(a>>1==1?(o=9,c=5):(r=function(e,t){var r,a,n,s=L(e,t)+257,i=L(e,t+=5)+1;t+=5;var o=(a=7&(r=t),((e[n=r>>>3]|(a<=4?0:e[n+1]<<8))>>>a&15)+4);t+=4;for(var c=0,l=O?new Uint8Array(19):z(19),f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=O?new Uint8Array(8):z(8),d=O?new Uint8Array(8):z(8),p=l.length,m=0;m<o;++m)l[w[m]]=c=P(e,t),h<c&&(h=c),u[c]++,t+=3;var g=0;for(m=1,u[0]=0;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=l[m])&&(f[m]=d[g]++);var v=0;for(m=0;m<p;++m)if(0!=(v=l[m])){g=R[f[m]]>>8-v;for(var b=(1<<7-v)-1;b>=0;--b)er[g|b<<v]=7&v|m<<3}var T=[];for(h=1;T.length<s+i;)switch(g=er[M(e,t)],t+=7&g,g>>>=3){case 16:for(c=3+function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}(e,t),t+=2,g=T[T.length-1];c-- >0;)T.push(g);break;case 17:for(c=3+P(e,t),t+=3;c-- >0;)T.push(0);break;case 18:for(c=11+M(e,t),t+=7;c-- >0;)T.push(0);break;default:T.push(g),h<g&&(h=g)}var E=T.slice(0,s),S=T.slice(s);for(m=s;m<286;++m)E[m]=0;for(m=i;m<30;++m)S[m]=0;return ea=G(E,ee,286),en=G(S,et,30),t}(e,r),o=ea,c=en);;){!t&&i<s+32767&&(i=(n=V(n,s+32767)).length);var f=U(e,r,o),h=a>>>1==1?j[f]:ee[f];if(r+=15&h,((h>>>=4)>>>8&255)==0)n[s++]=h;else if(256==h)break;else{var u=(h-=257)<8?0:h-4>>2;u>5&&(u=0);var d=s+T[h];u>0&&(d+=U(e,r,u),r+=u),f=U(e,r,c),r+=15&(h=a>>>1==1?X[f]:et[f]);var p=(h>>>=4)<4?0:h-2>>1,m=E[h];for(p>0&&(m+=U(e,r,p),r+=p),!t&&i<d&&(i=(n=V(n,d+100)).length);s<d;)n[s]=n[s-m],++s}}}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function ei(e,t){if(e)"undefined"!=typeof console&&console.error(t);else throw Error(t)}function eo(e,r){rr(e,0);var a={FileIndex:[],FullPaths:[]};l(a,{root:r.root});for(var n=e.length-4;(80!=e[n]||75!=e[n+1]||5!=e[n+2]||6!=e[n+3])&&n>=0;)--n;e.l=n+4,e.l+=4;var i=e.read_shift(2);e.l+=6;var o=e.read_shift(4);for(n=0,e.l=o;n<i;++n){e.l+=20;var c=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d=e.read_shift(2);e.l+=8;var p=e.read_shift(4),m=s(e.slice(e.l+h,e.l+h+u));e.l+=h+u+d;var g=e.l;e.l=p+4,function(e,r,a,n,i){e.l+=2;var o,c,l,f,h,u,d,p=e.read_shift(2),m=e.read_shift(2),g=(o=65535&e.read_shift(2),c=65535&e.read_shift(2),l=new Date,f=31&c,h=15&(c>>>=5),c>>>=4,l.setMilliseconds(0),l.setFullYear(c+1980),l.setMonth(h-1),l.setDate(f),u=31&o,d=63&(o>>>=5),o>>>=6,l.setHours(o),l.setMinutes(d),l.setSeconds(u<<1),l);if(8257&p)throw Error("Unsupported ZIP encryption");for(var v=e.read_shift(4),b=e.read_shift(4),w=e.read_shift(4),T=e.read_shift(2),E=e.read_shift(2),S="",y=0;y<T;++y)S+=String.fromCharCode(e[e.l++]);if(E){var k=s(e.slice(e.l,e.l+E));(k[21589]||{}).mt&&(g=k[21589].mt),((i||{})[21589]||{}).mt&&(g=i[21589].mt)}e.l+=E;var x=e.slice(e.l,e.l+b);switch(m){case 8:x=function(e,r){if(!t)return es(e,r);var a=new t.InflateRaw,n=a._processChunk(e.slice(e.l),a._finishFlushFlag);return e.l+=a.bytesRead,n}(e,w);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+m)}var _=!1;8&p&&(0x8074b50==e.read_shift(4)&&(e.read_shift(4),_=!0),b=e.read_shift(4),w=e.read_shift(4)),b!=r&&ei(_,"Bad compressed size: "+r+" != "+b),w!=a&&ei(_,"Bad uncompressed size: "+a+" != "+w),el(n,S,x,{unsafe:!0,mt:g})}(e,c,f,a,m),e.l=g}return a}var ec={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function el(e,t,r,a){var s=a&&a.unsafe;s||l(e);var i=!s&&ey.find(e,t);if(!i){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),i={name:n(t),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||ey.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,a&&(a.CLSID&&(i.clsid=a.CLSID),a.mt&&(i.mt=a.mt),a.ct&&(i.ct=a.ct)),i}return r.find=function(e,t){var r=e.FullPaths.map(function(e){return e.toUpperCase()}),a=r.map(function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]}),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(F);for(s=s.replace(D,""),o&&(s=s.replace(F,"!")),i=0;i<r.length;++i)if((o?r[i].replace(F,"!"):r[i]).replace(D,"")==s||(o?a[i].replace(F,"!"):a[i]).replace(D,"")==s)return e.FileIndex[i];return null},r.read=function(t,r){var a=r&&r.type;switch(!a&&k&&Buffer.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return i(),o(e.readFileSync(t),r);case"base64":return o(C(y(t)),r);case"binary":return o(C(t),r)}return o(t,r)},r.parse=o,r.write=function(t,r){var a=h(t,r);switch(r&&r.type||"buffer"){case"file":i(),e.writeFileSync(r.filename,a);break;case"binary":return"string"==typeof a?a:b(a);case"base64":return S("string"==typeof a?a:b(a));case"buffer":if(k)return Buffer.isBuffer(a)?a:x(a);case"array":return"string"==typeof a?C(a):a}return a},r.writeFile=function(t,r,a){i();var n=h(t,a);e.writeFileSync(r,n)},r.utils={cfb_new:function(e){var t={};return l(t,e),t},cfb_add:el,cfb_del:function(e,t){l(e);var r=ey.find(e,t);if(r){for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0}return!1},cfb_mov:function(e,t,r){l(e);var a=ey.find(e,t);if(a){for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==a)return e.FileIndex[s].name=n(r),e.FullPaths[s]=r,!0}return!1},cfb_gc:function(e){f(e,!0)},ReadShift:t6,CheckField:rt,prep_blob:rr,bconcat:N,use_zlib:function(e){try{var r=new e.InflateRaw;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),r.bytesRead)t=e;else throw Error("zlib does not expose bytesRead")}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:Q,_inflateRaw:es,consts:v},r}();function ek(e,t,r){if(void 0!==a&&a.writeFileSync)return r?a.writeFileSync(e,t,r):a.writeFileSync(e,t);if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=O(t);break;default:throw Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?tv(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!=typeof Blob){var s=new Blob([function(e){if("string"==typeof e)return O(e);if(Array.isArray(e)){if("undefined"==typeof Uint8Array)throw Error("Unsupported");return new Uint8Array(e)}return e}(n)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(s,e);if("undefined"!=typeof saveAs)return saveAs(s,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(s);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var o=document.createElement("a");if(null!=o.download)return o.download=e,o.href=i,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var c=File(e);return c.open("w"),c.encoding="binary",Array.isArray(t)&&(t=R(t)),c.write(t),c.close(),t}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("cannot save file "+e)}function ex(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function e_(e,t){for(var r=[],a=ex(e),n=0;n!==a.length;++n)null==r[e[a[n]][t]]&&(r[e[a[n]][t]]=a[n]);return r}function eA(e){for(var t=[],r=ex(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function eC(e){for(var t=[],r=ex(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}var eO=new Date(1899,11,30,0,0,0);function eR(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(eO.getTime()+(e.getTimezoneOffset()-eO.getTimezoneOffset())*6e4))/864e5}var eI=new Date,eN=eO.getTime()+(eI.getTimezoneOffset()-eO.getTimezoneOffset())*6e4,eD=eI.getTimezoneOffset();function eF(e){var t=new Date;return t.setTime(24*e*36e5+eN),t.getTimezoneOffset()!==eD&&t.setTime(t.getTime()+(t.getTimezoneOffset()-eD)*6e4),t}var eP=new Date("2017-02-19T19:06:09.000Z"),eL=isNaN(eP.getFullYear())?new Date("2/19/17"):eP,eM=2017==eL.getFullYear();function eU(e,t){var r=new Date(e);if(eM)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==eL.getFullYear()&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function eB(e,t){if(k&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return tv(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return tv(v(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return tv(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return tv(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return r[e]||e})}catch(e){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function eW(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=eW(e[r]));return t}function eH(e,t){for(var r="";r.length<t;)r+=e;return r}function eV(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return isNaN(t=Number(a))&&isNaN(t=Number(a=a.replace(/[(](.*)[)]/,function(e,t){return r=-r,t})))?t:t/r}var ez=["january","february","march","april","may","june","july","august","september","october","november","december"];function eG(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==ez.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&101!=a?t:e.match(/[^-0-9:,\/\\]/)?r:t}var ej=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(t,r,a){if(e||"string"==typeof r)return t.split(r);for(var n=t.split(r),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function e$(e){return e?e.content&&e.type?eB(e.content,!0):e.data?b(e.data):e.asNodeBuffer&&k?b(e.asNodeBuffer().toString("binary")):e.asBinary?b(e.asBinary()):e._data&&e._data.getContent?b(eB(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function eX(e){if(!e)return null;if(e.data)return g(e.data);if(e.asNodeBuffer&&k)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?g(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function eY(e,t){for(var r=e.FullPaths||ex(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function eK(e,t){var r=eY(e,t);if(null==r)throw Error("Cannot find file "+t+" in zip");return r}function eJ(e,t,r){if(!r){var a;return(a=eK(e,t))&&".bin"===a.name.slice(-4)?eX(a):e$(a)}if(!t)return null;try{return eJ(e,t)}catch(e){return null}}function eq(e,t,r){if(!r)return e$(eK(e,t));if(!t)return null;try{return eq(e,t)}catch(e){return null}}function eZ(e){for(var t=e.FullPaths||ex(e.files),r=[],a=0;a<t.length;++a)"/"!=t[a].slice(-1)&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function eQ(e,t,r){if(e.FullPaths){if("string"==typeof r){var a;return a=k?x(r):function(e){for(var t=[],r=0,a=e.length+250,n=_(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=(1023&i)+64;var o=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|o>>6&15|(3&i)<<4,n[r++]=128|63&o}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=_(65535),a=65530)}return t.push(n.slice(0,r)),N(t)}(r),ey.utils.cfb_add(e,t,a)}ey.utils.cfb_add(e,t,r)}else e.file(t,r)}function e1(){return ey.utils.cfb_new()}function e0(e,t){switch(t.type){case"base64":return ey.read(e,{type:"base64"});case"binary":return ey.read(e,{type:"binary"});case"buffer":case"array":return ey.read(e,{type:"buffer"})}throw Error("Unrecognized type "+t.type)}function e2(e,t){if("/"==e.charAt(0))return e.slice(1);var r=t.split("/");"/"!=t.slice(-1)&&r.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?r.pop():"."!==n&&r.push(n)}return r.join("/")}var e4='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',e3=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,e5=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,e6=e4.match(e5)?e5:/<[^>]*>/g,e8=/<\w*:/,e7=/<(\/?)\w+:/;function e9(e,t,r){for(var a={},n=0,s=0;n!==e.length&&32!==(s=e.charCodeAt(n))&&10!==s&&13!==s;++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(e3),o=0,c="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(s=0,h=i[l];s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(o=0,u=+(34==(n=h.charCodeAt(s+1))||39==n),c=h.slice(s+1+u,h.length-u);o!=f.length&&58!==f.charCodeAt(o);++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=c,r||(a[f.toLowerCase()]=c);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(a[d]&&"ext"==f.slice(o-3,o))continue;a[d]=c,r||(a[d.toLowerCase()]=c)}}return a}function te(e){return e.replace(e7,"<$1")}var tt={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},tr=eA(tt),ta=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,function(e,t){return tt[e]||String.fromCharCode(parseInt(t,e.indexOf("x")>-1?16:10))||e}).replace(t,function(e,t){return String.fromCharCode(parseInt(t,16))});var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}}(),tn=/[&<>'"]/g,ts=/[\u0000-\u0008\u000b-\u001f]/g;function ti(e){return(e+"").replace(tn,function(e){return tr[e]}).replace(ts,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function to(e){return ti(e).replace(/ /g,"_x0020_")}var tc=/[\u0000-\u001f]/g;function tl(e){return(e+"").replace(tn,function(e){return tr[e]}).replace(/\n/g,"<br/>").replace(tc,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}var tf=function(){var e=/&#(\d+);/g;function t(e,t){return String.fromCharCode(parseInt(t,10))}return function(r){return r.replace(e,t)}}();function th(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function tu(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0;r<e.length;){if((a=e.charCodeAt(r++))<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){t+=String.fromCharCode((31&a)<<6|63&n);continue}if(s=e.charCodeAt(r++),a<240){t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s);continue}t+=String.fromCharCode(55296+((o=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&e.charCodeAt(r++))-65536)>>>10&1023)),t+=String.fromCharCode(56320+(1023&o))}return t}function td(e){var t,r,a,n=_(2*e.length),s=1,i=0,o=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=(31&a)*64+(63&e.charCodeAt(r+1)),s=2):a<240?(t=(15&a)*4096+(63&e.charCodeAt(r+1))*64+(63&e.charCodeAt(r+2)),s=3):(s=4,o=55296+((t=(7&a)*262144+(63&e.charCodeAt(r+1))*4096+(63&e.charCodeAt(r+2))*64+(63&e.charCodeAt(r+3))-65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(n[i++]=255&o,n[i++]=o>>>8,o=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function tp(e){return x(e,"binary").toString("utf8")}var tm="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",tg=k&&(tp(tm)==tu(tm)&&tp||td(tm)==tu(tm)&&td)||tu,tv=k?function(e){return x(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,t.push(String.fromCharCode(240+((n=e.charCodeAt(r++)-56320+(a<<10))>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},tb=function(){var e={};return function(t,r){var a=t+"|"+(r||"");return e[a]?e[a]:e[a]=RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",r||"")}}(),tw=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[RegExp("&"+e[0]+";","ig"),e[1]]});return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),tT=function(){var e={};return function(t){return void 0!==e[t]?e[t]:e[t]=RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}(),tE=/<\/?(?:vt:)?variant>/g,tS=/<(?:vt:)([^>]*)>([\s\S]*)</;function ty(e,t){var r=e9(e),a=e.match(tT(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(e){var t=e.replace(tE,"").match(tS);t&&n.push({v:tg(t[2]),t:t[1]})}),n}var tk=/(^\s|\s$|\n)/;function tx(e,t){return"<"+e+(t.match(tk)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function t_(e){return ex(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function tA(e,t,r){return"<"+e+(null!=r?t_(r):"")+(null!=t?(t.match(tk)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function tC(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}function tO(e){if(k&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return tg(R(I(e)));throw Error("Bad input format: expected Buffer or string")}var tR=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,tI={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},tN=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],tD={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},tF=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},tP=k?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(e){return Buffer.isBuffer(e)?e:x(e)})):tF(e)}:tF,tL=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(t2(e,n)));return a.join("").replace(D,"")},tM=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(D,""):tL(e,t,r)}:tL,tU=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},tB=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):tU(e,t,r)}:tU,tW=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(t0(e,n)));return a.join("")},tH=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):tW(e,t,r)}:tW,tV=function(e,t){var r=t3(e,t);return r>0?tH(e,t+4,t+4+r-1):""},tz=tV,tG=function(e,t){var r=t3(e,t);return r>0?tH(e,t+4,t+4+r-1):""},tj=tG,t$=function(e,t){var r=2*t3(e,t);return r>0?tH(e,t+4,t+4+r-1):""},tX=t$,tY=function(e,t){var r=t3(e,t);return r>0?tM(e,t+4,t+4+r):""},tK=tY,tJ=function(e,t){var r=t3(e,t);return r>0?tH(e,t+4,t+4+r):""},tq=tJ,tZ=function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?1/0*r:NaN:(0==a?a=-1022:(a-=1023,n+=0x10000000000000),r*Math.pow(2,a-52)*n)},tQ=tZ,t1=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};k&&(tz=function(e,t){if(!Buffer.isBuffer(e))return tV(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tj=function(e,t){if(!Buffer.isBuffer(e))return tG(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tX=function(e,t){if(!Buffer.isBuffer(e))return t$(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},tK=function(e,t){if(!Buffer.isBuffer(e))return tY(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},tq=function(e,t){if(!Buffer.isBuffer(e))return tJ(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},tQ=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):tZ(e,t)},t1=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==n&&(tM=function(e,t,r){return n.utils.decode(1200,e.slice(t,r)).replace(D,"")},tH=function(e,t,r){return n.utils.decode(65001,e.slice(t,r))},tz=function(e,t){var r=t3(e,t);return r>0?n.utils.decode(f,e.slice(t+4,t+4+r-1)):""},tj=function(e,t){var r=t3(e,t);return r>0?n.utils.decode(l,e.slice(t+4,t+4+r-1)):""},tX=function(e,t){var r=2*t3(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},tK=function(e,t){var r=t3(e,t);return r>0?n.utils.decode(1200,e.slice(t+4,t+4+r)):""},tq=function(e,t){var r=t3(e,t);return r>0?n.utils.decode(65001,e.slice(t+4,t+4+r)):""});var t0=function(e,t){return e[t]},t2=function(e,t){return 256*e[t+1]+e[t]},t4=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-((65535-r+1)*1)},t3=function(e,t){return 0x1000000*e[t+3]+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},t5=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]};function t6(e,t){var r,a,s,i,o,c,f="",h=[];switch(t){case"dbcs":if(c=this.l,k&&Buffer.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)f+=String.fromCharCode(t2(this,c)),c+=2;e*=2;break;case"utf8":f=tH(this,this.l,this.l+e);break;case"utf16le":e*=2,f=tM(this,this.l,this.l+e);break;case"wstr":if(void 0===n)return t6.call(this,e,"dbcs");f=n.utils.decode(l,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=tz(this,this.l),e=4+t3(this,this.l);break;case"lpstr-cp":f=tj(this,this.l),e=4+t3(this,this.l);break;case"lpwstr":f=tX(this,this.l),e=4+2*t3(this,this.l);break;case"lpp4":e=4+t3(this,this.l),f=tK(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+t3(this,this.l),f=tq(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(s=t0(this,this.l+e++));)h.push(w(s));f=h.join("");break;case"_wstr":for(e=0,f="";0!==(s=t2(this,this.l+e));)h.push(w(s)),e+=2;e+=2,f=h.join("");break;case"dbcs-cont":for(o=0,f="",c=this.l;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=t0(this,c),this.l=c+1,i=t6.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),h.join("")+i;h.push(w(t2(this,c))),c+=2}f=h.join(""),e*=2;break;case"cpstr":if(void 0!==n){f=n.utils.decode(l,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(o=0,f="",c=this.l;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=t0(this,c),this.l=c+1,i=t6.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),h.join("")+i;h.push(w(t0(this,c))),c+=1}f=h.join("");break;default:switch(e){case 1:return r=t0(this,this.l),this.l++,r;case 2:return r=("i"===t?t4:t2)(this,this.l),this.l+=2,r;case 4:case -4:if("i"===t||(128&this[this.l+3])==0)return r=(e>0?t5:function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]})(this,this.l),this.l+=4,r;return a=t3(this,this.l),this.l+=4,a;case 8:case -8:if("f"===t)return a=8==e?tQ(this,this.l):tQ([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:f=tB(this,this.l,e)}}return this.l+=e,f}var t8=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},t7=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},t9=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function re(e,t,r){var a=0,s=0;if("dbcs"===r){for(s=0;s!=t.length;++s)t9(this,t.charCodeAt(s),this.l+2*s);a=2*t.length}else if("sbcs"===r){if(void 0!==n&&874==f)for(s=0;s!=t.length;++s){var i=n.utils.encode(f,t.charAt(s));this[this.l+s]=i[0]}else for(s=0,t=t.replace(/[^\x00-\x7F]/g,"_");s!=t.length;++s)this[this.l+s]=255&t.charCodeAt(s);a=t.length}else if("hex"===r){for(;s<e;++s)this[this.l++]=parseInt(t.slice(2*s,2*s+2),16)||0;return this}else if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(s=0;s<Math.min(t.length,e);++s){var c=t.charCodeAt(s);this[this.l++]=255&c,this[this.l++]=c>>8}for(;this.l<o;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=255&t;break;case 2:a=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:a=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:a=4,t8(this,t,this.l);break;case 8:if(a=8,"f"===r){!function(e,t,r){var a=(t<0||1/t==-1/0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<0x10000000000000)?n=-1022:(s-=0x10000000000000,n+=1023)):(n=2047,s=26985*!!isNaN(t));for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case -4:a=4,t7(this,t,this.l)}return this.l+=a,this}function rt(e,t){var r=tB(this,this.l,e.length>>1);if(r!==e)throw Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function rr(e,t){e.l=t,e.read_shift=t6,e.chk=rt,e.write_shift=re}function ra(e,t){e.l+=t}function rn(e){var t=_(e);return rr(t,0),t}function rs(e,t,r){if(e){rr(e,e.l||0);for(var a,n,s,i=e.length,o=0,c=0;e.l<i;){128&(o=e.read_shift(1))&&(o=(127&o)+((127&e.read_shift(1))<<7));var l=iT[o]||iT[65535];for(n=1,s=127&(a=e.read_shift(1));n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;c=e.l+s;var f=l.f&&l.f(e,s,r);if(e.l=c,t(f,l,o))return}}}function ri(){var e=[],t=k?256:2048,r=function(e){var t=rn(e);return rr(t,0),t},a=r(t),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),N(e)},_bufs:e}}function ro(e,t,r,a){var n,s=+t;if(!isNaN(s)){a||(a=iT[s].p||(r||[]).length||0),n=1+ +(s>=128)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,(127&s)+128),i.write_shift(1,s>>7));for(var o=0;4!=o;++o)if(a>=128)i.write_shift(1,(127&a)+128),a>>=7;else{i.write_shift(1,a);break}a>0&&t1(r)&&e.push(r)}}function rc(e,t,r){var a=eW(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function rl(e,t,r){var a=eW(e);return a.s=rc(a.s,t.s,r),a.e=rc(a.e,t.s,r),a}function rf(e,t){if(e.cRel&&e.c<0)for(e=eW(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=eW(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=rv(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=r.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),r}function rh(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?rf(e.s,t.biff)+":"+rf(e.e,t.biff):(e.s.rRel?"":"$")+rd(e.s.r)+":"+(e.e.rRel?"":"$")+rd(e.e.r):(e.s.cRel?"":"$")+rm(e.s.c)+":"+(e.e.cRel?"":"$")+rm(e.e.c)}function ru(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function rd(e){return""+(e+1)}function rp(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function rm(e){if(e<0)throw Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function rg(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function rv(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function rb(e){var t=e.indexOf(":");return -1==t?{s:rg(e),e:rg(e)}:{s:rg(e.slice(0,t)),e:rg(e.slice(t+1))}}function rw(e,t){return void 0===t||"number"==typeof t?rw(e.s,e.e):("string"!=typeof e&&(e=rv(e)),"string"!=typeof t&&(t=rv(t)),e==t?e:e+":"+t)}function rT(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)r=10*r+n;return t.e.r=--r,t}function rE(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=eg(e.z,r?eR(t):t)}catch(e){}try{return e.w=eg((e.XF||{}).numFmtId||14*!!r,r?eR(t):t)}catch(e){return""+t}}function rS(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t)?rJ[e.v]||e.v:void 0==t?rE(e,e.v):rE(e,t)}function ry(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function rk(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,o=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var c="string"==typeof a.origin?rg(a.origin):a.origin;i=c.r,o=c.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=rT(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=i+h,m=o+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||z[14],a.cellDates?(d.t="d",d.w=eg(d.z,eR(d.v))):(d.t="n",d.v=eR(d.v),d.w=eg(d.z,d.v))):d.t="s";else d=t[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=rv({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return l.s.c<1e7&&(s["!ref"]=rw(l)),s}function rx(e,t){return rk(null,e,t)}function r_(e,t){return t||(t=rn(4)),t.write_shift(4,e),t}function rA(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function rC(e,t){var r=!1;return null==t&&(r=!0,t=rn(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rO(e,t){var r=e.l,a=e.read_shift(1),n=rA(e),s=[],i={t:n,h:n};if((1&a)!=0){for(var o=e.read_shift(4),c=0;c!=o;++c)s.push({ich:e.read_shift(2),ifnt:e.read_shift(2)});i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}function rR(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function rI(e,t){return null==t&&(t=rn(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rN(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function rD(e,t){return null==t&&(t=rn(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rF(e){var t=e.read_shift(4);return 0===t||0xffffffff===t?"":e.read_shift(t,"dbcs")}function rP(e,t){var r=!1;return null==t&&(r=!0,t=rn(127)),t.write_shift(4,e.length>0?e.length:0xffffffff),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rL(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?tQ([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):t5(t,0)>>2;return r?n/100:n}function rM(e,t){null==t&&(t=rn(4));var r=0,a=0,n=100*e;if(e==(0|e)&&e>=-0x20000000&&e<0x20000000?a=1:n==(0|n)&&n>=-0x20000000&&n<0x20000000&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw Error("unsupported RkNumber "+e)}function rU(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var rB=function(e,t){return t||(t=rn(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function rW(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function rH(e,t){return(t||rn(8)).write_shift(8,e,"f")}function rV(e,t){if(t||(t=rn(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function rz(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[e.read_shift(4)]||""}if(r>400)throw Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var rG=[80,81],rj={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},r$={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},rX={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},rY=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],rK=eW([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]})),rJ={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rq={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},rZ={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},rQ={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function r1(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function r0(e,t){var r,a=function(e){for(var t=[],r=ex(e),a=0;a!==r.length;++a)null==t[e[r[a]]]&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}(rZ),n=[];n[n.length]=e4,n[n.length]=tA("Types",null,{xmlns:tI.CT,"xmlns:xsd":tI.xsd,"xmlns:xsi":tI.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return tA("Default",null,{Extension:e[0],ContentType:e[1]})}));var s=function(a){e[a]&&e[a].length>0&&(r=e[a][0],n[n.length]=tA("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:rQ[a][t.bookType]||rQ[a].xlsx}))},i=function(r){(e[r]||[]).forEach(function(e){n[n.length]=tA("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:rQ[r][t.bookType]||rQ[r].xlsx})})},o=function(t){(e[t]||[]).forEach(function(e){n[n.length]=tA("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:a[t][0]})})};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var r2={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function r4(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function r3(e,t){var r={"!id":{}};if(!e)return r;"/"!==t.charAt(0)&&(t="/"+t);var a={};return(e.match(e6)||[]).forEach(function(e){var n=e9(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode),r["External"===n.TargetMode?n.Target:e2(n.Target,t)]=s,a[n.Id]=s}}),r["!id"]=a,r}function r5(e){var t=[e4,tA("Relationships",null,{xmlns:tI.RELS})];return ex(e["!id"]).forEach(function(r){t[t.length]=tA("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function r6(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,s?n.TargetMode=s:[r2.HLINK,r2.XPATH,r2.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}function r8(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function r7(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+c.version+"</meta:generator></office:meta></office:document-meta>"}var r9=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],ae=function(){for(var e=Array(r9.length),t=0;t<r9.length;++t){var r=r9[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function at(e){var t={};e=tg(e);for(var r=0;r<r9.length;++r){var a=r9[r],n=e.match(ae[r]);null!=n&&n.length>0&&(t[a[1]]=ta(n[1])),"date"===a[2]&&t[a[1]]&&(t[a[1]]=eU(t[a[1]]))}return t}function ar(e,t,r,a,n){null==n[e]&&null!=t&&""!==t&&(n[e]=t,t=ti(t),a[a.length]=r?tA(e,t,r):tx(e,t))}function aa(e,t){var r=t||{},a=[e4,tA("cp:coreProperties",null,{"xmlns:cp":tI.CORE_PROPS,"xmlns:dc":tI.dc,"xmlns:dcterms":tI.dcterms,"xmlns:dcmitype":tI.dcmitype,"xmlns:xsi":tI.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(null!=e.CreatedDate&&ar("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:tC(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&ar("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:tC(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=r9.length;++s){var i=r9[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&ar(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var an=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],as=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ai(e,t,r,a){var n=[];if("string"==typeof e)n=ty(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map(function(e){return{v:e}}));var i="string"==typeof t?ty(t,a).map(function(e){return e.v}):t,o=0,c=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(c=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsbl\xe4tter":case"\xc7alışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de c\xe1lculo":case"Planilhas":case"Regneark":case"Hojas de c\xe1lculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=i.slice(o,o+c);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne omr\xe5der":r.NamedRanges=c,r.DefinedNames=i.slice(o,o+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=i.slice(o,o+c)}o+=c}}function ao(e){var t=[];return e||(e={}),e.Application="SheetJS",t[t.length]=e4,t[t.length]=tA("Properties",null,{xmlns:tI.EXT_PROPS,"xmlns:vt":tI.vt}),an.forEach(function(r){var a;if(void 0!==e[r[1]]){switch(r[2]){case"string":a=ti(String(e[r[1]]));break;case"bool":a=e[r[1]]?"true":"false"}void 0!==a&&(t[t.length]=tA(r[0],a))}}),t[t.length]=tA("HeadingPairs",tA("vt:vector",tA("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+tA("vt:variant",tA("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=tA("TitlesOfParts",tA("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+ti(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var ac=/<[^>]+>[^<]*/g;function al(e){var t=[e4,tA("Properties",null,{xmlns:tI.CUST_PROPS,"xmlns:vt":tI.vt})];if(!e)return t.join("");var r=1;return ex(e).forEach(function(a){++r,t[t.length]=tA("property",function(e,t){switch(typeof e){case"string":var r=tA("vt:lpwstr",ti(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return tA((0|e)==e?"vt:i4":"vt:r8",ti(String(e)));case"boolean":return tA("vt:bool",e?"true":"false")}if(e instanceof Date)return tA("vt:filetime",tC(e));throw Error("Unable to serialize "+e)}(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:ti(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var af={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function ah(e){var t=e.read_shift(4);return new Date((e.read_shift(4)/1e7*0x100000000+t/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function au(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function ad(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function ap(e,t,r){return 31===t?ad(e):au(e,t,r)}function am(e,t,r){return ap(e,t,4*(!1!==r))}function ag(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(D,"").replace(F,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function av(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function ab(e,t,r){var a,n,s=e.read_shift(2),i=r||{};if(e.l+=2,12!==t&&s!==t&&-1===rG.indexOf(t)&&((65534&t)!=4126||(65534&s)!=4126))throw Error("Expected type "+t+" saw "+s);switch(12===t?s:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return au(e,s,4).replace(D,"");case 31:return ad(e);case 64:return ah(e);case 65:return av(e);case 71:return(a={}).Size=e.read_shift(4),e.l+=a.Size+3-(a.Size-1)%4,a;case 80:return am(e,s,!i.raw).replace(D,"");case 81:return(function(e,t){if(!t)throw Error("VtUnalignedString must have positive length");return ap(e,t,0)})(e,s).replace(D,"");case 4108:for(var o=e.read_shift(4),c=[],l=0;l<o/2;++l)c.push(function(e){var t=e.l,r=ab(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,ab(e,3)]}(e));return c;case 4126:case 4127:return 4127==s?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(D,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(D,"");return r}(e);default:throw Error("TypedPropertyValue unrecognized type "+t+" "+s)}}function aw(e,t){var r,a,n,s,i,o=rn(4),c=rn(4);switch(o.write_shift(4,80==e?31:e),e){case 3:c.write_shift(-4,t);break;case 5:(c=rn(8)).write_shift(8,t,"f");break;case 11:c.write_shift(4,+!!t);break;case 64:a=(r=("string"==typeof t?new Date(Date.parse(t)):t).getTime()/1e3+0x2b6109100)%0x100000000,n=(r-a)/0x100000000*1e7,(s=(a*=1e7)/0x100000000|0)>0&&(a%=0x100000000,n+=s),(i=rn(8)).write_shift(4,a),i.write_shift(4,n),c=i;break;case 31:case 80:for((c=rn(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),c.write_shift(0,t,"dbcs");c.l!=c.length;)c.write_shift(1,0);break;default:throw Error("TypedPropertyValue unrecognized type "+e+" "+t)}return N([o,c])}function aT(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,o=0,c=-1,l={};for(i=0;i!=n;++i){var f=e.read_shift(4),h=e.read_shift(4);s[i]=[f,h+r]}s.sort(function(e,t){return e[1]-t[1]});var u={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var m=t[s[i][0]];if(u[m.n]=ab(e,m.t,{raw:!0}),"version"===m.p&&(u[m.n]=String(u[m.n]>>16)+"."+("0000"+String(65535&u[m.n])).slice(-4)),"CodePage"==m.n)switch(u[m.n]){case 0:u[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:p(o=u[m.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+u[m.n])}}else if(1===s[i][0]){if(p(o=u.CodePage=ab(e,2)),-1!==c){var g=e.l;e.l=s[c][1],l=ag(e,o),e.l=g}}else if(0===s[i][0]){if(0===o){c=i,e.l=s[i+1][1];continue}l=ag(e,o)}else{var v,b=l[s[i][0]];switch(e[e.l]){case 65:e.l+=4,v=av(e);break;case 30:case 31:e.l+=4,v=am(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=a_(e,4);break;case 64:e.l+=4,v=eU(ah(e));break;default:throw Error("unparsed value: "+e[e.l])}u[b]=v}}return e.l=r+a,u}var aE=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function aS(e,t,r){var a=rn(8),n=[],s=[],i=8,o=0,c=rn(8),l=rn(8);if(c.write_shift(4,2),c.write_shift(4,1200),l.write_shift(4,1),s.push(c),n.push(l),i+=8+c.length,!t){(l=rn(8)).write_shift(4,0),n.unshift(l);var f=[rn(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((c=rn(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),c.write_shift(4,h.length+1),c.write_shift(0,h,"dbcs");c.l!=c.length;)c.write_shift(1,0);f.push(c)}c=N(f),s.unshift(c),i+=8+c.length}for(o=0;o<e.length;++o)if(!(t&&!t[e[o][0]]||aE.indexOf(e[o][0])>-1||as.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(m[0]<<16)+(+m[1]||0)}c=aw(p.t,u)}else{var g=function(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return -1}(u);-1==g&&(g=31,u=String(u)),c=aw(g,u)}s.push(c),(l=rn(8)).write_shift(4,t?d:2+o),n.push(l),i+=8+c.length}var v=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,v),v+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),N([a].concat(n).concat(s))}function ay(e,t,r){var a,n=e.content;if(!n)return{};rr(n,0);var s,i,o,c,l=0;n.chk("feff","Byte Order: "),n.read_shift(2);var f=n.read_shift(4),h=n.read_shift(16);if(h!==ey.utils.consts.HEADER_CLSID&&h!==r)throw Error("Bad PropertySet CLSID "+h);if(1!==(s=n.read_shift(4))&&2!==s)throw Error("Unrecognized #Sets: "+s);if(i=n.read_shift(16),c=n.read_shift(4),1===s&&c!==n.l)throw Error("Length mismatch: "+c+" !== "+n.l);2===s&&(o=n.read_shift(16),l=n.read_shift(4));var u=aT(n,t),d={SystemIdentifier:f};for(var p in u)d[p]=u[p];if(d.FMTID=i,1===s)return d;if(l-n.l==2&&(n.l+=2),n.l!==l)throw Error("Length mismatch 2: "+n.l+" !== "+l);try{a=aT(n,null)}catch(e){}for(p in a)d[p]=a[p];return d.FMTID=[i,o],d}function ak(e,t,r,a,n,s){var i=rn(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,0x32363237),i.write_shift(16,ey.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var c=aS(e,r,a);if(o.push(c),n){var l=aS(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+c.length),o.push(l)}return N(o)}function ax(e,t){return e.read_shift(t),null}function a_(e,t){return 1===e.read_shift(t)}function aA(e,t){return t||(t=rn(2)),t.write_shift(2,+!!e),t}function aC(e){return e.read_shift(2,"u")}function aO(e,t){return t||(t=rn(2)),t.write_shift(2,e),t}function aR(e,t){for(var r=[],a=e.l+t;e.l<a;)r.push(aC(e,a-e.l));if(a!==e.l)throw Error("Slurp error");return r}function aI(e,t,r){return r||(r=rn(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,+("e"==t)),r}function aN(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont",s=l;r&&r.biff>=8&&(l=1200),r&&8!=r.biff?12==r.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont"),r.biff>=2&&r.biff<=5&&(n="cpstr");var i=a?e.read_shift(a,n):"";return l=s,i}function aD(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function aF(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):aD(e,a,r)}function aP(e,t,r){if(r.biff>5)return aF(e,t,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function aL(e,t,r){return r||(r=rn(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function aM(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(D,""):""}function aU(e,t){t||(t=rn(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function aB(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function aW(e,t){var r=aB(e,t);return r[3]=0,r}function aH(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function aV(e,t,r,a){return a||(a=rn(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function az(e){return[e.read_shift(2),rL(e)]}function aG(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function aj(e,t){return t||(t=rn(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function a$(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}function aX(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function aY(e){e.l+=2,e.l+=e.read_shift(2)}var aK={0:aY,4:aY,5:aY,6:aY,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:aY,9:aY,10:aY,11:aY,12:aY,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:aY,15:aY,16:aY,17:aY,18:aY,19:aY,20:aY,21:aX};function aJ(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function aq(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;default:throw Error("unsupported BIFF version")}var s=rn(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function aZ(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function aQ(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c:c}}}function a1(e,t,r,a){var n=r&&5==r.biff;a||(a=rn(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function a0(e,t,r){var a,n=aH(e,6);(2==r.biff||9==t)&&++e.l;var s=(a=e.read_shift(1),1===e.read_shift(1)?a:1===a);return n.val=s,n.t=!0===s||!1===s?"b":"e",n}var a2=function(e,t,r){return 0===t?"":aP(e,t,r)};function a4(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=aN(e,t,r),s=e.read_shift(2);if(s!==(a-=e.l))throw Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"==typeof a&&(s.Name=a),s}var a3=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function a5(e,t,r){var a,n,s,i,o,c,l,f=e.l+t,h=e.read_shift(2),u=e.read_shift(1),d=e.read_shift(1),p=e.read_shift(r&&2==r.biff?1:2),m=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),m=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var g=aD(e,d,r);32&h&&(g=a3[g.charCodeAt(0)]);var v=f-e.l;return r&&2==r.biff&&--v,{chKey:u,Name:g,itab:m,rgce:f!=e.l&&0!==p&&v>0?(a=e,n=v,s=r,i=p,c=a.l+n,l=sE(a,i,s),c!==a.l&&(o=sT(a,c-a.l,l,s)),[l,o]):[]}}function a6(e,t,r){if(r.biff<8){var a,n,s,i;return a=e,n=t,s=r,3==a[a.l+1]&&a[a.l]++,3==(i=aN(a,n,s)).charCodeAt(0)?i.slice(1):i}for(var o=[],c=e.l+t,l=e.read_shift(r.biff>8?4:2);0!=l--;)o.push(function(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}(e,r.biff,r));if(e.l!=c)throw Error("Bad ExternSheet: "+e.l+" != "+c);return o}function a8(e,t,r){var a=a$(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(a=t-2,void(e.l+=a))];var c=sE(e,o,r);return t!==o+i&&(n=sT(e,t-o-i,c,r)),e.l=s,[c,n]}(e,t,r,a)]}var a7={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function a9(e,t,r){if(!r.cellStyles)return void(e.l+=t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),c=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:o,flags:c};return(r.biff>=5||!r.biff)&&(l.level=c>>8&7),l}var ne=[2,3,48,49,131,139,140,245],nt=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=eA({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var s=rx(function(t,r){var a=[],s=_(1);switch(r.type){case"base64":s=C(y(t));break;case"binary":s=C(t);break;case"buffer":case"array":s=t}rr(s,0);var i=s.read_shift(1),o=!!(136&i),c=!1,l=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:c=!0,o=!0;break;case 140:l=!0;break;default:throw Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=r.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),l&&(s.l+=36);for(var p=[],m={},g=Math.min(s.length,2==i?521:h-10-264*!!c),v=l?32:11;s.l<g&&13!=s[s.l];)switch((m={}).name=n.utils.decode(d,s.slice(s.l,s.l+v)).replace(/[\u0000\r\n].*$/g,""),s.l+=v,m.type=String.fromCharCode(s.read_shift(1)),2!=i&&!l&&(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=l?13:14),m.type){case"B":(!c||8!=m.len)&&r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var b=0,w=0;for(w=0,a[0]=[];w!=p.length;++w)a[0][w]=p[w].name;for(;f-- >0;){if(42===s[s.l]){s.l+=u;continue}for(++s.l,a[++b]=[],w=0,w=0;w!=p.length;++w){var T=s.slice(s.l,s.l+p[w].len);s.l+=p[w].len,rr(T,0);var E=n.utils.decode(d,T);switch(p[w].type){case"C":E.trim().length&&(a[b][w]=E.replace(/\s+$/,""));break;case"D":8===E.length?a[b][w]=new Date(+E.slice(0,4),E.slice(4,6)-1,+E.slice(6,8)):a[b][w]=E;break;case"F":a[b][w]=parseFloat(E.trim());break;case"+":case"I":a[b][w]=l?0x80000000^T.read_shift(-4,"i"):T.read_shift(4,"i");break;case"L":switch(E.trim().toUpperCase()){case"Y":case"T":a[b][w]=!0;break;case"N":case"F":a[b][w]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+E+"|")}break;case"M":if(!o)throw Error("DBF Unexpected MEMO for type "+i.toString(16));a[b][w]="##MEMO##"+(l?parseInt(E.trim(),10):T.read_shift(4));break;case"N":(E=E.replace(/\u0000/g,"").trim())&&"."!=E&&(a[b][w]=+E||0);break;case"@":a[b][w]=new Date(T.read_shift(-8,"f")-621356832e5);break;case"T":a[b][w]=new Date((T.read_shift(4)-2440588)*864e5+T.read_shift(4));break;case"Y":a[b][w]=T.read_shift(4,"i")/1e4+T.read_shift(4,"i")/1e4*0x100000000;break;case"O":a[b][w]=-T.read_shift(-8,"f");break;case"B":if(c&&8==p[w].len){a[b][w]=T.read_shift(8,"f");break}case"G":case"P":T.l+=p[w].len;break;case"0":if("_NullFlags"===p[w].name)break;default:throw Error("DBF Unsupported data type "+p[w].type)}}}if(2!=i&&s.l<s.length&&26!=s[s.l++])throw Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return r&&r.sheetRows&&(a=a.slice(0,r.sheetRows)),r.DBF=p,a}(t,a),a);return s["!cols"]=a.DBF.map(function(e){return{wch:e.len,DBF:e}}),delete a.DBF,s}var a={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return ry(r(e,t),t)}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var n=r||{};if(+n.codepage>=0&&p(+n.codepage),"string"==n.type)throw Error("Cannot write DBF to JS string");var s=ri(),i=oe(e,{header:1,raw:!0,cellDates:!0}),o=i[0],c=i.slice(1),l=e["!cols"]||[],h=0,u=0,d=0,m=1;for(h=0;h<o.length;++h){if(((l[h]||{}).DBF||{}).name){o[h]=l[h].DBF.name,++d;continue}if(null!=o[h]){if(++d,"number"==typeof o[h]&&(o[h]=o[h].toString(10)),"string"!=typeof o[h])throw Error("DBF Invalid column name "+o[h]+" |"+typeof o[h]+"|");if(o.indexOf(o[h])!==h){for(u=0;u<1024;++u)if(-1==o.indexOf(o[h]+"_"+u)){o[h]+="_"+u;break}}}}var g=rT(e["!ref"]),v=[],b=[],w=[];for(h=0;h<=g.e.c-g.s.c;++h){var T="",E="",S=0,y=[];for(u=0;u<c.length;++u)null!=c[u][h]&&y.push(c[u][h]);if(0==y.length||null==o[h]){v[h]="?";continue}for(u=0;u<y.length;++u){switch(typeof y[u]){case"number":E="B";break;case"string":default:E="C";break;case"boolean":E="L";break;case"object":E=y[u]instanceof Date?"D":"C"}S=Math.max(S,String(y[u]).length),T=T&&T!=E?"C":E}S>250&&(S=250),"C"==(E=((l[h]||{}).DBF||{}).type)&&l[h].DBF.len>S&&(S=l[h].DBF.len),"B"==T&&"N"==E&&(T="N",w[h]=l[h].DBF.dec,S=l[h].DBF.len),b[h]="C"==T||"N"==E?S:a[T]||0,m+=b[h],v[h]=T}var k=s.next(32);for(k.write_shift(4,0x13021130),k.write_shift(4,c.length),k.write_shift(2,296+32*d),k.write_shift(2,m),h=0;h<4;++h)k.write_shift(4,0);for(k.write_shift(4,(+t[f]||3)<<8),h=0,u=0;h<o.length;++h)if(null!=o[h]){var x=s.next(32),_=(o[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);x.write_shift(1,_,"sbcs"),x.write_shift(1,"?"==v[h]?"C":v[h],"sbcs"),x.write_shift(4,u),x.write_shift(1,b[h]||a[v[h]]||0),x.write_shift(1,w[h]||0),x.write_shift(1,2),x.write_shift(4,0),x.write_shift(1,0),x.write_shift(4,0),x.write_shift(4,0),u+=b[h]||a[v[h]]||0}var A=s.next(264);for(A.write_shift(4,13),h=0;h<65;++h)A.write_shift(4,0);for(h=0;h<c.length;++h){var C=s.next(m);for(C.write_shift(1,0),u=0;u<o.length;++u)if(null!=o[u])switch(v[u]){case"L":C.write_shift(1,null==c[h][u]?63:c[h][u]?84:70);break;case"B":C.write_shift(8,c[h][u]||0,"f");break;case"N":var O="0";for("number"==typeof c[h][u]&&(O=c[h][u].toFixed(w[u]||0)),d=0;d<b[u]-O.length;++d)C.write_shift(1,32);C.write_shift(1,O,"sbcs");break;case"D":c[h][u]?(C.write_shift(4,("0000"+c[h][u].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(c[h][u].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+c[h][u].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=c[h][u]?c[h][u]:"").slice(0,b[u]);for(C.write_shift(1,R,"sbcs"),d=0;d<b[u]-R.length;++d)C.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),nr=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=RegExp("\x1bN("+ex(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var a=e[r];return"number"==typeof a?T(a):a},a=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:T(a)};function s(e,s){var i,o=e.split(/[\n\r]+/),c=-1,l=-1,f=0,h=0,u=[],d=[],m=null,g={},v=[],b=[],w=[],T=0;for(+s.codepage>=0&&p(+s.codepage);f!==o.length;++f){T=0;var E,S=o[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),y=S.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")}),k=y[0];if(S.length>0)switch(k){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==y[1].charAt(0)&&d.push(S.slice(3).replace(/;;/g,";"));break;case"C":var x=!1,_=!1,A=!1,C=!1,O=-1,R=-1;for(h=1;h<y.length;++h)switch(y[h].charAt(0)){case"A":case"G":break;case"X":l=parseInt(y[h].slice(1))-1,_=!0;break;case"Y":for(c=parseInt(y[h].slice(1))-1,_||(l=0),i=u.length;i<=c;++i)u[i]=[];break;case"K":'"'===(E=y[h].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(eV(E))?isNaN(eG(E).getDate())||(E=eU(E)):(E=eV(E),null!==m&&ed(m)&&(E=eF(E))),void 0!==n&&"string"==typeof E&&"string"!=(s||{}).type&&(s||{}).codepage&&(E=n.utils.decode(s.codepage,E)),x=!0;break;case"E":C=!0;var I=se(y[h].slice(1),{r:c,c:l});u[c][l]=[u[c][l],I];break;case"S":A=!0,u[c][l]=[u[c][l],"S5S"];break;case"R":O=parseInt(y[h].slice(1))-1;break;case"C":R=parseInt(y[h].slice(1))-1;break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}if(x&&(u[c][l]&&2==u[c][l].length?u[c][l][0]=E:u[c][l]=E,m=null),A){if(C)throw Error("SYLK shared formula cannot have own formula");var N=O>-1&&u[O][R];if(!N||!N[1])throw Error("SYLK shared formula cannot find base");u[c][l][1]=sa(N[1],{r:c-O,c:l-R})}break;case"F":var D=0;for(h=1;h<y.length;++h)switch(y[h].charAt(0)){case"X":l=parseInt(y[h].slice(1))-1,++D;break;case"Y":for(c=parseInt(y[h].slice(1))-1,i=u.length;i<=c;++i)u[i]=[];break;case"M":T=parseInt(y[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":m=d[parseInt(y[h].slice(1))];break;case"W":for(i=parseInt((w=y[h].slice(1).split(" "))[0],10);i<=parseInt(w[1],10);++i)T=parseInt(w[2],10),b[i-1]=0===T?{hidden:!0}:{wch:T},nL(b[i-1]);break;case"C":b[l=parseInt(y[h].slice(1))-1]||(b[l]={});break;case"R":v[c=parseInt(y[h].slice(1))-1]||(v[c]={}),T>0?(v[c].hpt=T,v[c].hpx=nU(T)):0===T&&(v[c].hidden=!0);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}D<1&&(m=null);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+S)}}return v.length>0&&(g["!rows"]=v),b.length>0&&(g["!cols"]=b),s&&s.sheetRows&&(u=u.slice(0,s.sheetRows)),[u,g]}function i(e,t){var r=function(e,t){switch(t.type){case"base64":return s(y(e),t);case"binary":return s(e,t);case"buffer":return s(k&&Buffer.isBuffer(e)?e.toString("binary"):R(e),t);case"array":return s(eB(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),a=r[0],n=r[1],i=rx(a,t);return ex(n).forEach(function(e){i[e]=n[e]}),i}return e["|"]=254,{to_workbook:function(e,t){return ry(i(e,t),t)},to_sheet:i,from_sheet:function(e,t){var r,a=["ID;PWXL;N;E"],n=[],s=rT(e["!ref"]),i=Array.isArray(e);a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&e["!cols"].forEach(function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=nI(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=nN(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&a.push(r)}),e["!rows"]&&e["!rows"].forEach(function(e,t){var r="F;";e.hidden?r+="M0;":e.hpt?r+="M"+20*e.hpt+";":e.hpx&&(r+="M"+20*nM(e.hpx)+";"),r.length>2&&a.push(r+"R"+(t+1))}),a.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var o=s.s.r;o<=s.e.r;++o)for(var c=s.s.c;c<=s.e.c;++c){var l=rv({r:o,c:c});(r=i?(e[o]||[])[c]:e[l])&&(null!=r.v||r.f&&!r.F)&&n.push(function(e,t,r,a){var n="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+sr(e.f,{r:r,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}(r,0,o,c,t))}return a.join("\r\n")+"\r\n"+n.join("\r\n")+"\r\nE\r\n"}}}(),na=function(){var e,t;function r(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){if("BOT"===r[s].trim()){i[++a]=[],n=0;continue}if(!(a<0)){for(var o=r[s].trim().split(","),c=o[0],l=o[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+c){case -1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(eV(l))?isNaN(eG(l).getDate())?i[a][n]=l:i[a][n]=eU(l):i[a][n]=eV(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function a(e,t){return rx(function(e,t){switch(t.type){case"base64":return r(y(e),t);case"binary":return r(e,t);case"buffer":return r(k&&Buffer.isBuffer(e)?e.toString("binary"):R(e),t);case"array":return r(eB(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),t)}return{to_workbook:function(e,t){return ry(a(e,t),t)},to_sheet:a,from_sheet:(e=function(e,t,r,a,n){e.push(t),e.push(r+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},t=function(e,t,r,a){e.push(t+","+r),e.push(1==t?'"'+a.replace(/"/g,'""')+'"':a)},function(r){var a,n=[],s=rT(r["!ref"]),i=Array.isArray(r);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){t(n,-1,0,"BOT");for(var c=s.s.c;c<=s.e.c;++c){var l=rv({r:o,c:c});if(!(a=i?(r[o]||[])[c]:r[l])){t(n,1,0,"");continue}switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?!a.f||a.F?t(n,1,0,""):t(n,1,0,"="+a.f):t(n,0,f,"V");break;case"b":t(n,0,+!!a.v,a.v?"TRUE":"FALSE");break;case"s":t(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=eg(a.z||z[14],eR(eU(a.v)))),t(n,0,a.w,"V");break;default:t(n,1,0,"")}}}return t(n,-1,0,"EOD"),n.join("\r\n")})}}(),nn=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return rx(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var c=rg(o[1]);if(i.length<=c.r)for(a=i.length;a<=c.r;++a)i[a]||(i[a]=[]);switch(a=c.r,n=c.c,o[2]){case"t":i[a][n]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+o[3];break;case"vtf":var l=o[o.length-1];case"vtc":"nl"===o[3]?i[a][n]=!!+o[4]:i[a][n]=+o[4],"vtf"==o[2]&&(i[a][n]=[i[a][n],l])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(e,r){return ry(t(e,r),r)},to_sheet:t,from_sheet:function(t){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",r,"# SocialCalc Spreadsheet Control Save\npart:sheet",r,function(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=rb(t["!ref"]),o=Array.isArray(t),c=i.s.r;c<=i.e.r;++c)for(var l=i.s.c;l<=i.e.c;++l)if(s=rv({r:c,c:l}),(r=o?(t[c]||[])[l]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=eR(eU(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||eg(r.z||z[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}(t),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),ns=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(eV(e))?isNaN(eG(e).getDate())?t[r][a]=e:t[r][a]=eU(e):t[r][a]=eV(e))}var t={44:",",9:"	",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort(function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]}),t[i.pop()[1]]||44}function s(t,r){var s,i="",o="string"==r.type?[0,0,0,0]:i3(t,r);switch(r.type){case"base64":i=y(t);break;case"binary":case"string":i=t;break;case"buffer":i=65001==r.codepage?t.toString("utf8"):r.codepage&&void 0!==n?n.utils.decode(r.codepage,t):k&&Buffer.isBuffer(t)?t.toString("binary"):R(t);break;case"array":i=eB(t);break;default:throw Error("Unrecognized type "+r.type)}return(239==o[0]&&187==o[1]&&191==o[2]?i=tg(i.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?i=tg(i):"binary"==r.type&&void 0!==n&&r.codepage&&(i=n.utils.decode(r.codepage,n.utils.encode(28591,i))),"socialcalc:version:"==i.slice(0,19))?nn.to_sheet("string"==r.type?i:tg(i),r):(s=i,!(r&&r.PRN)||r.FS||"sep="==s.slice(0,4)||s.indexOf("	")>=0||s.indexOf(",")>=0||s.indexOf(";")>=0?function(e,t){var r,n,s=t||{},i="",o=s.dense?[]:{},c={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(i=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(i=e.charAt(4),e=e.slice(6)):i=a(e.slice(0,1024)):i=s&&s.FS?s.FS:a(e.slice(0,1024));var l=0,f=0,h=0,u=0,d=0,p=i.charCodeAt(0),m=!1,g=0,v=e.charCodeAt(0);e=e.replace(/\r\n/mg,"\n");var b=null!=s.dateNF?RegExp("^"+("number"==typeof(r=s.dateNF)?z[r]:r).replace(eE,"(\\d+)")+"$"):null;function w(){var t=e.slice(u,d),r={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)r.t="z";else if(s.raw)r.t="s",r.v=t;else if(0===t.trim().length)r.t="s",r.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(r.t="s",r.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(r.t="n",r.f=t.slice(1)):(r.t="s",r.v=t);else if("TRUE"==t)r.t="b",r.v=!0;else if("FALSE"==t)r.t="b",r.v=!1;else if(isNaN(h=eV(t)))if(!isNaN(eG(t).getDate())||b&&t.match(b)){r.z=s.dateNF||z[14];var a,n,i,m,w,T,E,S,y,k,x=0;b&&t.match(b)&&(a=s.dateNF,n=t.match(b)||[],i=-1,m=-1,w=-1,T=-1,E=-1,S=-1,(a.match(eE)||[]).forEach(function(e,t){var r=parseInt(n[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":i=r;break;case"d":w=r;break;case"h":T=r;break;case"s":S=r;break;case"m":T>=0?E=r:m=r}}),S>=0&&-1==E&&m>=0&&(E=m,m=-1),7==(y=(""+(i>=0?i:new Date().getFullYear())).slice(-4)+"-"+("00"+(m>=1?m:1)).slice(-2)+"-"+("00"+(w>=1?w:1)).slice(-2)).length&&(y="0"+y),8==y.length&&(y="20"+y),k=("00"+(T>=0?T:0)).slice(-2)+":"+("00"+(E>=0?E:0)).slice(-2)+":"+("00"+(S>=0?S:0)).slice(-2),t=-1==T&&-1==E&&-1==S?y:-1==i&&-1==m&&-1==w?k:y+"T"+k,x=1),s.cellDates?(r.t="d",r.v=eU(t,x)):(r.t="n",r.v=eR(eU(t,x))),!1!==s.cellText&&(r.w=eg(r.z,r.v instanceof Date?eR(r.v):r.v)),s.cellNF||delete r.z}else r.t="s",r.v=t;else r.t="n",!1!==s.cellText&&(r.w=t),r.v=h;if("z"==r.t||(s.dense?(o[l]||(o[l]=[]),o[l][f]=r):o[rv({c:f,r:l})]=r),u=d+1,v=e.charCodeAt(u),c.e.c<f&&(c.e.c=f),c.e.r<l&&(c.e.r=l),g==p)++f;else if(f=0,++l,s.sheetRows&&s.sheetRows<=l)return!0}e:for(;d<e.length;++d)switch(g=e.charCodeAt(d)){case 34:34===v&&(m=!m);break;case p:case 10:case 13:if(!m&&w())break e}return d-u>0&&w(),o["!ref"]=rw(c),o}(s,r):rx(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var o=10,c=0,l=0;l<=i;++l)-1==(c=s[l].indexOf(" "))?c=s[l].length:c++,o=Math.max(o,c);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,o).trim(),n,l,f,a),f=1;f<=(s[l].length-o)/10+1;++f)e(s[l].slice(o+(f-1)*10,o+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(s,r),r))}return{to_workbook:function(e,t){return ry(s(e,t),t)},to_sheet:s,from_sheet:function(e){for(var t,r=[],a=rT(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],o=a.s.c;o<=a.e.c;++o){var c=rv({r:s,c:o});if(!(t=n?(e[s]||[])[o]:e[c])||null==t.v){i.push("          ");continue}for(var l=(t.w||(rS(t),t.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===o?" ":""))}r.push(i.join(""))}return r.join("\n")}}}(),ni=function(){function e(e,t,r){if(e){rr(e,e.l||0);for(var a=r.Enum||h;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),o=e.l+i,c=s.f&&s.f(e,i,r);if(e.l=o,t(c,s,n))return}}}function t(t,r){if(!t)return t;var a=r||{},n=a.dense?[]:{},s="Sheet1",i="",o=0,c={},l=[],f=[],d={s:{r:0,c:0},e:{r:0,c:0}},p=a.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw Error("Unsupported Works 3 for Mac file");if(2==t[2])a.Enum=h,e(t,function(e,t,r){switch(r){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:d=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&(112&e[2])==112&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||z[14],a.cellDates&&(e[1].t="d",e[1].v=eF(e[1].v))),a.qpro&&e[3]>o&&(n["!ref"]=rw(d),c[s]=n,l.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=i||"Sheet"+(o+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[rv(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rv(e[0])]=e[1]}},a);else if(26==t[2]||14==t[2])a.Enum=u,14==t[2]&&(a.qpro=!0,t.l=0),e(t,function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(n["!ref"]=rw(d),c[s]=n,l.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},s="Sheet"+((o=e[3])+1)),p>0&&e[0].r>=p)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[rv(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==o&&(s=e[1])}},a);else throw Error("Unrecognized LOTUS BOF "+t[2]);if(n["!ref"]=rw(d),c[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:c};for(var m={},g=[],v=0;v<f.length;++v)c[l[v]]?(g.push(f[v]||l[v]),m[f[v]]=c[f[v]]||c[l[v]]):(g.push(f[v]),m[f[v]]={"!ref":"A1"});return{SheetNames:g,Sheets:m}}function r(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,t,a){var n=e.l+t,s=r(e,t,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function n(e,t,r){var a=32768&t;return t&=-32769,t=(a?e:0)+(t>=8192?t-16384:t),(a?"":"$")+(r?rm(t):rd(t))}var s={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},i=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function o(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function c(e,t){var r=o(e,t),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&0xc0000000===n?(r[1].t="e",r[1].v=15):0===a&&0xd0000000===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function l(e,t){var r=o(e,t),a=e.read_shift(8,"f");return r[1].v=a,r}function f(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}var h={0:{n:"BOF",f:aC},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2)):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0)),a}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,a){var n=r(e,t,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,t,a){var o=e.l+t,c=r(e,t,a);if(c[1].v=e.read_shift(8,"f"),a.qpro)e.l=o;else{var l=e.read_shift(2);(function(e,t){rr(e,0);for(var r=[],a=0,o="",c="",l="",f="";e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:c=n(t[0].c,e.read_shift(2),!0),o=n(t[0].r,e.read_shift(2),!1),r.push(c+o);break;case 2:var u=n(t[0].c,e.read_shift(2),!0),d=n(t[0].r,e.read_shift(2),!1);c=n(t[0].c,e.read_shift(2),!0),o=n(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+c+o);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:f=r.pop(),l=r.pop(),r.push(["AND","OR"][h-20]+"("+l+","+f+")");break;default:if(h<32&&i[h])f=r.pop(),l=r.pop(),r.push(l+i[h]+f);else if(s[h]){if(69==(a=s[h][1])&&(a=e[e.l++]),a>r.length)return void console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-a);r.length-=a,r.push(s[h][0]+"("+m.join(",")+")")}else if(h<=7)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=24)return console.error("WK1 unsupported op "+h.toString(16));else if(h<=30)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=115)return console.error("WK1 unsupported function opcode "+h.toString(16));else return console.error("WK1 unrecognized opcode "+h.toString(16))}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")})(e.slice(e.l,e.l+l),c),e.l+=l}return c}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:f},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},u={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=o(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:c},24:{n:"NUMBER18",f:function(e,t){var r=o(e,t);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=(a>>3)*5e3;break;case 1:a=(a>>3)*500;break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=c(e,14);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=o(e,t),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:l},40:{n:"FORMULA28",f:function(e,t){var r=l(e,14);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:f},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r,a,n,s,i=t||{};if(+i.codepage>=0&&p(+i.codepage),"string"==i.type)throw Error("Cannot write WK1 to JS string");var o=ri(),c=rT(e["!ref"]),l=Array.isArray(e),f=[];iS(o,0,(r=1030,(a=rn(2)).write_shift(2,1030),a)),iS(o,6,(n=c,(s=rn(8)).write_shift(2,n.s.c),s.write_shift(2,n.s.r),s.write_shift(2,n.e.c),s.write_shift(2,n.e.r),s));for(var h=Math.min(c.e.r,8191),u=c.s.r;u<=h;++u)for(var d=rd(u),m=c.s.c;m<=c.e.c;++m){u===c.s.r&&(f[m]=rm(m));var g=f[m]+d,v=l?(e[u]||[])[m]:e[g];v&&"z"!=v.t&&("n"==v.t?(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?iS(o,13,function(e,t,r){var a=rn(7);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r,"i"),a}(u,m,v.v)):iS(o,14,function(e,t,r){var a=rn(13);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r,"f"),a}(u,m,v.v)):iS(o,15,function(e,t,r){var a=rn(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}(u,m,rS(v).slice(0,239))))}return iS(o,1),o.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&p(+r.codepage),"string"==r.type)throw Error("Cannot write WK3 to JS string");var a=ri();iS(a,0,function(e){var t=rn(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++n;var c=rb(o["!ref"]);r<c.e.r&&(r=c.e.r),a<c.e.c&&(a=c.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&iS(a,27,function(e,t){var r=rn(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var o=e.Sheets[e.SheetNames[n]];if(o&&o["!ref"]){for(var c=rT(o["!ref"]),l=Array.isArray(o),f=[],h=Math.min(c.e.r,8191),u=c.s.r;u<=h;++u)for(var d=rd(u),m=c.s.c;m<=c.e.c;++m){u===c.s.r&&(f[m]=rm(m));var g=f[m]+d,v=l?(o[u]||[])[m]:o[g];v&&"z"!=v.t&&("n"==v.t?iS(a,23,function(e,t,r,a){var n=rn(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s=0,i=0,o=0,c=0;return a<0&&(s=1,a=-a),i=0|Math.log2(a),a/=Math.pow(2,i-31),(0x80000000&(c=a>>>0))==0&&(a/=2,++i,c=a>>>0),a-=c,c|=0x80000000,c>>>=0,a*=0x100000000,o=a>>>0,n.write_shift(4,o),n.write_shift(4,c),i+=16383+32768*!!s,n.write_shift(2,i),n}(u,m,i,v.v)):iS(a,22,function(e,t,r,a){var n=rn(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}(u,m,i,rS(v).slice(0,239))))}++i}}return iS(a,1),a.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(C(y(e)),r);case"binary":return t(C(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),no=function(){var e=tb("t"),t=tb("rPr");function r(r){var a=r.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:ta(a[1])},s=r.match(t);return s&&(n.s=function(e){var t={},r=e.match(e6),a=0,n=!1;if(r)for(;a!=r.length;++a){var s=e9(r[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;t.cp=u[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting"}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":t.i=1;break;case"</i>":case"<color>":case"<color/>":case"</color>":case"<family>":case"<family/>":case"</family>":case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<family":t.family=s.val;break;case"<vertAlign":t.valign=s.val;break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw Error("Unrecognized rich format "+s[0])}}return t}(s[1])),n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(e){return e.replace(a,"").split(n).map(r).filter(function(e){return e.v})}}(),nc=function(){var e=/(\r\n|\n)/g;function t(t){var r,a,n,s,i,o=[[],t.v,[]];return t.v?(t.s&&(r=t.s,a=o[0],n=o[2],s=[],r.u&&s.push("text-decoration: underline;"),r.uval&&s.push("text-underline-style:"+r.uval+";"),r.sz&&s.push("font-size:"+r.sz+"pt;"),r.outline&&s.push("text-effect: outline;"),r.shadow&&s.push("text-shadow: auto;"),a.push('<span style="'+s.join("")+'">'),r.b&&(a.push("<b>"),n.push("</b>")),r.i&&(a.push("<i>"),n.push("</i>")),r.strike&&(a.push("<s>"),n.push("</s>")),"superscript"==(i=r.valign||"")||"super"==i?i="sup":"subscript"==i&&(i="sub"),""!=i&&(a.push("<"+i+">"),n.push("</"+i+">")),n.push("</span>")),o[0].join("")+o[1].replace(e,"<br/>")+o[2].join("")):""}return function(e){return e.map(t).join("")}}(),nl=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,nf=/<(?:\w+:)?r>/,nh=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function nu(e,t){var r=!t||t.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=ta(tg(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=tg(e),r&&(a.h=tl(a.t))):e.match(nf)&&(a.r=tg(e),a.t=ta(tg((e.replace(nh,"").match(nl)||[]).join("").replace(e6,""))),r&&(a.h=nc(no(a.r)))),a):{t:""}}var nd=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,np=/<(?:\w+:)?(?:si|sstItem)>/g,nm=/<\/(?:\w+:)?(?:si|sstItem)>/,ng=/^\s|\s$|[\t\n\r]/;function nv(e,t){if(!t.bookSST)return"";var r=[e4];r[r.length]=tA("sst",null,{xmlns:tN[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(ng)&&(s+=' xml:space="preserve"'),s+=">"+ti(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var nb=function(e,t){var r=!1;return null==t&&(r=!0,t=rn(15+4*e.t.length)),t.write_shift(1,0),rC(e.t,t),r?t.slice(0,t.l):t};function nw(e){if(void 0!==n)return n.utils.encode(f,e);for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function nT(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function nE(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function nS(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function ny(e){var t,r,a=0,n=nw(e),s=n.length+1;for(r=1,(t=_(s))[0]=n.length;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=((16384&a)!=0|a<<1&32767)^t[r];return 52811^a}var nk=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){var r;return((r=e^t)/2|128*r)&255},n=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(a^=r[n]),i*=2,--n;return a};return function(t){for(var r,s,i,o=nw(t),c=n(o),l=o.length,f=_(16),h=0;16!=h;++h)f[h]=0;for((1&l)==1&&(r=c>>8,f[l]=a(e[0],r),--l,r=255&c,s=o[o.length-1],f[l]=a(s,r));l>0;)--l,r=c>>8,f[l]=a(o[l],r),--l,r=255&c,f[l]=a(o[l],r);for(l=15,i=15-o.length;i>0;)r=c>>8,f[l]=a(e[i],r),--l,--i,r=255&c,f[l]=a(o[l],r),--l,--i;return f}}(),nx=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=nk(e)),s=0;s!=t.length;++s)i=((i=t[s]^a[r])>>5|i<<3)&255,n[s]=i,++r;return[n,r,a]},n_=function(e){var t=0,r=nk(e);return function(e){var a=nx("",e,t,r);return t=a[1],a[0]}},nA=function(){function e(e,r){switch(r.type){case"base64":return t(y(e),r);case"binary":return t(e,r);case"buffer":return t(k&&Buffer.isBuffer(e)?e.toString("binary"):R(e),r);case"array":return t(eB(e),r)}throw Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach(function(e,t){Array.isArray(r)&&(r[t]=[]);for(var a,s=/\\\w+\b/g,i=0,o=-1;a=s.exec(e);){if("\\cell"===a[0]){var c=e.slice(i,s.lastIndex-a[0].length);if(" "==c[0]&&(c=c.slice(1)),++o,c.length){var l={v:c,t:"s"};Array.isArray(r)?r[t][o]=l:r[rv({r:t,c:o})]=l}}i=s.lastIndex}o>n.e.c&&(n.e.c=o)}),r["!ref"]=rw(n),r}return{to_workbook:function(t,r){return ry(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],a=rT(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var o=rv({r:s,c:i});(t=n?(e[s]||[])[i]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(rS(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function nC(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function nO(e,t){if(0===t)return e;var r,a=function(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(0===i)return[0,0,t];var o=0,c=0,l=n+s;switch(c=i/(l>1?2-l:l),n){case t:o=((r-a)/i+6)%6;break;case r:o=(a-t)/i+2;break;case a:o=(t-r)/i+4}return[o/6,c,l/2]}([parseInt((r=e.slice(+("#"===e[0])).slice(0,6)).slice(0,2),16),parseInt(r.slice(2,4),16),parseInt(r.slice(4,6),16)]);return t<0?a[2]=a[2]*(1+t):a[2]=1-(1-a[2])*(1-t),nC(function(e){var t,r=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,o=[i,i,i],c=6*r;if(0!==a)switch(0|c){case 0:case 6:t=s*c,o[0]+=s,o[1]+=t;break;case 1:t=s*(2-c),o[0]+=t,o[1]+=s;break;case 2:t=s*(c-2),o[1]+=s,o[2]+=t;break;case 3:t=s*(4-c),o[1]+=t,o[2]+=s;break;case 4:t=s*(c-4),o[2]+=s,o[0]+=t;break;case 5:t=s*(6-c),o[2]+=t,o[0]+=s}for(var l=0;3!=l;++l)o[l]=Math.round(255*o[l]);return o}(a))}var nR=6;function nI(e){return Math.floor((e+Math.round(128/nR)/256)*nR)}function nN(e){return Math.floor((e-5)/nR*100+.5)/100}function nD(e){return Math.round((e*nR+5)/nR*256)/256}function nF(e){return nD(nN(nI(e)))}function nP(e){var t=Math.abs(e-nF(e)),r=nR;if(t>.005)for(nR=1;nR<15;++nR)Math.abs(e-nF(e))<=t&&(t=Math.abs(e-nF(e)),r=nR);nR=r}function nL(e){e.width?(e.wpx=nI(e.width),e.wch=nN(e.wpx),e.MDW=nR):e.wpx?(e.wch=nN(e.wpx),e.width=nD(e.wch),e.MDW=nR):"number"==typeof e.wch&&(e.width=nD(e.wch),e.wpx=nI(e.width),e.MDW=nR),e.customWidth&&delete e.customWidth}function nM(e){return 96*e/96}function nU(e){return 96*e/96}var nB={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"},nW=["numFmtId","fillId","fontId","borderId","xfId"],nH=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"],nV=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,t=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,r=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,o){var c,l,f,h,d,p,m,g,v,b,w,T,E,S={};return s?((E=(s=s.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(e))&&function(e,t,r){t.NumberFmt=[];for(var a=ex(z),n=0;n<a.length;++n)t.NumberFmt[a[n]]=z[a[n]];var s=e[0].match(e6);if(s)for(n=0;n<s.length;++n){var i=e9(s[n]);switch(te(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var o=ta(tg(i.formatCode)),c=parseInt(i.numFmtId,10);if(t.NumberFmt[c]=o,c>0){if(c>392){for(c=392;c>60&&null!=t.NumberFmt[c];--c);t.NumberFmt[c]=o}ev(o,c)}break;default:if(r.WTF)throw Error("unrecognized "+i[0]+" in numFmts")}}}(E,S,o),(E=s.match(a))&&(c=E,S.Fonts=[],l={},f=!1,(c[0].match(e6)||[]).forEach(function(e){var t=e9(e);switch(te(t[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":S.Fonts.push(l),l={};break;case"<name":t.val&&(l.name=tg(t.val));break;case"<b":l.bold=t.val?th(t.val):1;break;case"<b/>":l.bold=1;break;case"<i":l.italic=t.val?th(t.val):1;break;case"<i/>":l.italic=1;break;case"<u":switch(t.val){case"none":l.underline=0;break;case"single":l.underline=1;break;case"double":l.underline=2;break;case"singleAccounting":l.underline=33;break;case"doubleAccounting":l.underline=34}break;case"<u/>":l.underline=1;break;case"<strike":l.strike=t.val?th(t.val):1;break;case"<strike/>":l.strike=1;break;case"<outline":l.outline=t.val?th(t.val):1;break;case"<outline/>":l.outline=1;break;case"<shadow":l.shadow=t.val?th(t.val):1;break;case"<shadow/>":l.shadow=1;break;case"<condense":l.condense=t.val?th(t.val):1;break;case"<condense/>":l.condense=1;break;case"<extend":l.extend=t.val?th(t.val):1;break;case"<extend/>":l.extend=1;break;case"<sz":t.val&&(l.sz=+t.val);break;case"<vertAlign":t.val&&(l.vertAlign=t.val);break;case"<family":t.val&&(l.family=parseInt(t.val,10));break;case"<scheme":t.val&&(l.scheme=t.val);break;case"<charset":if("1"==t.val)break;t.codepage=u[parseInt(t.val,10)];break;case"<color":if(l.color||(l.color={}),t.auto&&(l.color.auto=th(t.auto)),t.rgb)l.color.rgb=t.rgb.slice(-6);else if(t.indexed){l.color.index=parseInt(t.indexed,10);var r=rK[l.color.index];81==l.color.index&&(r=rK[1]),r||(r=rK[1]),l.color.rgb=r[0].toString(16)+r[1].toString(16)+r[2].toString(16)}else t.theme&&(l.color.theme=parseInt(t.theme,10),t.tint&&(l.color.tint=parseFloat(t.tint)),t.theme&&i.themeElements&&i.themeElements.clrScheme&&(l.color.rgb=nO(i.themeElements.clrScheme[l.color.theme].rgb,l.color.tint||0)));break;case"<AlternateContent":case"<ext":f=!0;break;case"</AlternateContent>":case"</ext>":f=!1;break;default:if(o&&o.WTF&&!f)throw Error("unrecognized "+t[0]+" in fonts")}})),(E=s.match(r))&&(h=E,S.Fills=[],d={},p=!1,(h[0].match(e6)||[]).forEach(function(e){var t=e9(e);switch(te(t[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":d={},S.Fills.push(d);break;case"<gradientFill":case"</gradientFill>":S.Fills.push(d),d={};break;case"<patternFill":case"<patternFill>":t.patternType&&(d.patternType=t.patternType);break;case"<bgColor":d.bgColor||(d.bgColor={}),t.indexed&&(d.bgColor.indexed=parseInt(t.indexed,10)),t.theme&&(d.bgColor.theme=parseInt(t.theme,10)),t.tint&&(d.bgColor.tint=parseFloat(t.tint)),t.rgb&&(d.bgColor.rgb=t.rgb.slice(-6));break;case"<fgColor":d.fgColor||(d.fgColor={}),t.theme&&(d.fgColor.theme=parseInt(t.theme,10)),t.tint&&(d.fgColor.tint=parseFloat(t.tint)),null!=t.rgb&&(d.fgColor.rgb=t.rgb.slice(-6));break;case"<ext":p=!0;break;case"</ext>":p=!1;break;default:if(o&&o.WTF&&!p)throw Error("unrecognized "+t[0]+" in fills")}})),(E=s.match(n))&&(m=E,S.Borders=[],g={},v=!1,(m[0].match(e6)||[]).forEach(function(e){var t=e9(e);switch(te(t[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":g={},t.diagonalUp&&(g.diagonalUp=th(t.diagonalUp)),t.diagonalDown&&(g.diagonalDown=th(t.diagonalDown)),S.Borders.push(g);break;case"<ext":v=!0;break;case"</ext>":v=!1;break;default:if(o&&o.WTF&&!v)throw Error("unrecognized "+t[0]+" in borders")}})),(E=s.match(t))&&(b=E,S.CellXf=[],T=!1,(b[0].match(e6)||[]).forEach(function(e){var t=e9(e),r=0;switch(te(t[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(w=t,delete w[0],r=0;r<nW.length;++r)w[nW[r]]&&(w[nW[r]]=parseInt(w[nW[r]],10));for(r=0;r<nH.length;++r)w[nH[r]]&&(w[nH[r]]=th(w[nH[r]]));if(S.NumberFmt&&w.numFmtId>392){for(r=392;r>60;--r)if(S.NumberFmt[w.numFmtId]==S.NumberFmt[r]){w.numFmtId=r;break}}S.CellXf.push(w);break;case"<alignment":case"<alignment/>":var a={};t.vertical&&(a.vertical=t.vertical),t.horizontal&&(a.horizontal=t.horizontal),null!=t.textRotation&&(a.textRotation=t.textRotation),t.indent&&(a.indent=t.indent),t.wrapText&&(a.wrapText=th(t.wrapText)),w.alignment=a;break;case"<AlternateContent":case"<ext":T=!0;break;case"</AlternateContent>":case"</ext>":T=!1;break;default:if(o&&o.WTF&&!T)throw Error("unrecognized "+t[0]+" in cellXfs")}})),S):S}}();function nz(e,t){var r,a,n,s,i,o=[e4,tA("styleSheet",null,{xmlns:tN[0],"xmlns:vt":tI.vt})];return e.SSF&&null!=(r=e.SSF,a=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=r[t]&&(a[a.length]=tA("numFmt",null,{numFmtId:t,formatCode:ti(r[t])}))}),i=1===a.length?"":(a[a.length]="</numFmts>",a[0]=tA("numFmts",null,{count:a.length-2}).replace("/>",">"),a.join("")))&&(o[o.length]=i),o[o.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',o[o.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',o[o.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',o[o.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',n=t.cellXfs,(s=[])[s.length]=tA("cellXfs",null),n.forEach(function(e){s[s.length]=tA("xf",null,e)}),s[s.length]="</cellXfs>",(i=2===s.length?"":(s[0]=tA("cellXfs",null,{count:s.length-2}).replace("/>",">"),s.join("")))&&(o[o.length]=i),o[o.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',o[o.length]='<dxfs count="0"/>',o[o.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',o.length>2&&(o[o.length]="</styleSheet>",o[1]=o[1].replace("/>",">")),o.join("")}var nG=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function nj(e,t){t||(t=rn(84)),i||(i=eA(nG));var r=i[e.patternType];null==r&&(r=40),t.write_shift(4,r);var a=0;if(40!=r)for(rV({auto:1},t),rV({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function n$(e,t,r){return r||(r=rn(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function nX(e,t){return t||(t=rn(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var nY=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function nK(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(e6)||[]).forEach(function(e){var n=e9(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[nY.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw Error("Unrecognized "+n[0]+" in clrScheme")}})}function nJ(){}function nq(){}var nZ=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,nQ=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,n1=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,n0=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function n2(e,t){e&&0!==e.length||(e=n4());var r,a,n,s={};if(!(n=e.match(n0)))throw Error("themeElements not found in theme");return r=n[0],s.themeElements={},[["clrScheme",nZ,nK],["fontScheme",nQ,nJ],["fmtScheme",n1,nq]].forEach(function(e){if(!(a=r.match(e[1])))throw Error(e[0]+" not found in themeElements");e[2](a,s,t)}),s.raw=e,s}function n4(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[e4];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function n3(){var e=[e4];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var n5=1024;function n6(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[tA("xml",null,{"xmlns:v":tD.v,"xmlns:o":tD.o,"xmlns:x":tD.x,"xmlns:mv":tD.mv}).replace(/\/>/,">"),tA("o:shapelayout",tA("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),tA("v:shapetype",[tA("v:stroke",null,{joinstyle:"miter"}),tA("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];n5<1e3*e;)n5+=1e3;return t.forEach(function(e){var t=rg(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var a="gradient"==r.type?tA("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=tA("v:fill",a,r);++n5,n=n.concat(["<v:shape"+t_({id:"_x0000_s"+n5,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,tA("v:shadow",null,{on:"t",obscured:"t"}),tA("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",tx("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),tx("x:AutoFill","False"),tx("x:Row",String(t.r)),tx("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function n8(e,t,r,a){var n,s=Array.isArray(e);t.forEach(function(t){var i=rg(t.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[t.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[t.ref]=n;var o=rT(e["!ref"]||"BDWGO1000001:A1");o.s.r>i.r&&(o.s.r=i.r),o.e.r<i.r&&(o.e.r=i.r),o.s.c>i.c&&(o.s.c=i.c),o.e.c<i.c&&(o.e.c=i.c);var c=rw(o);c!==e["!ref"]&&(e["!ref"]=c)}n.c||(n.c=[]);var l={a:t.author,t:t.t,r:t.r,T:r};t.h&&(l.h=t.h);for(var f=n.c.length-1;f>=0;--f){if(!r&&n.c[f].T)return;r&&!n.c[f].T&&n.c.splice(f,1)}if(r&&a){for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}}n.c.push(l)})}function n7(e){var t=[e4,tA("comments",null,{xmlns:tN[0]})],r=[];return t.push("<authors>"),e.forEach(function(e){e[1].forEach(function(e){var a=ti(e.a);-1==r.indexOf(a)&&(r.push(a),t.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))})}),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?a=r.indexOf("tc="+e[1][0].ID):e[1].forEach(function(e){e.a&&(a=r.indexOf(ti(e.a))),n.push(e.t||"")}),t.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>'),n.length<=1)t.push(tx("t",ti(n[0]||"")));else{for(var s="Comment:\n    "+n[0]+"\n",i=1;i<n.length;++i)s+="Reply:\n    "+n[i]+"\n";t.push(tx("t",ti(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}var n9=["xlsb","xlsm","xlam","biff8","xla"],se=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var o=a.length>0?0|parseInt(a,10):0,c=n.length>0?0|parseInt(n,10):0;return s?c+=t.c:--c,i?o+=t.r:--o,r+(s?"":"$")+rm(c)+(i?"":"$")+rd(o)}return function(a,n){return t=n,a.replace(e,r)}}(),st=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,sr=function(e,t){return e.replace(st,function(e,r,a,n,s,i){var o=rp(n)-(a?0:t.c),c=ru(i)-(s?0:t.r);return r+"R"+(0==c?"":s?c+1:"["+c+"]")+"C"+(0==o?"":a?o+1:"["+o+"]")})};function sa(e,t){return e.replace(st,function(e,r,a,n,s,i){return r+("$"==a?a+n:rm(rp(n)+t.c))+("$"==s?s+i:rd(ru(i)+t.r))})}function sn(e){return e.replace(/_xlfn\./g,"")}function ss(e){e.l+=1}function si(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function so(e,t,r){var a=2;if(r)if(r.biff>=2&&r.biff<=5)return sc(e,t,r);else 12==r.biff&&(a=4);var n=e.read_shift(a),s=e.read_shift(a),i=si(e,2),o=si(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function sc(e){var t=si(e,2),r=si(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function sl(e,t,r){if(r&&r.biff>=2&&r.biff<=5){var a,n,s;return n=si(a=e,2),s=a.read_shift(1),{r:n[0],c:s,cRel:n[1],rRel:n[2]}}var i=e.read_shift(r&&12==r.biff?4:2),o=si(e,2);return{r:i,c:o[0],cRel:o[1],rRel:o[2]}}function sf(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function sh(e){return[e.read_shift(1),e.read_shift(1)]}function su(e,t,r){var a;return e.l+=2,[{r:e.read_shift(2),c:255&(a=e.read_shift(2)),fQuoted:!!(16384&a),cRel:a>>15,rRel:a>>15}]}function sd(e){return e.l+=6,[]}function sp(e){return e.l+=2,[aC(e),1&e.read_shift(2)]}var sm=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],sg={1:{n:"PtgExp",f:function(e,t,r){return(e.l++,r&&12==r.biff)?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:ra},3:{n:"PtgAdd",f:ss},4:{n:"PtgSub",f:ss},5:{n:"PtgMul",f:ss},6:{n:"PtgDiv",f:ss},7:{n:"PtgPower",f:ss},8:{n:"PtgConcat",f:ss},9:{n:"PtgLt",f:ss},10:{n:"PtgLe",f:ss},11:{n:"PtgEq",f:ss},12:{n:"PtgGe",f:ss},13:{n:"PtgGt",f:ss},14:{n:"PtgNe",f:ss},15:{n:"PtgIsect",f:ss},16:{n:"PtgUnion",f:ss},17:{n:"PtgRange",f:ss},18:{n:"PtgUplus",f:ss},19:{n:"PtgUminus",f:ss},20:{n:"PtgPercent",f:ss},21:{n:"PtgParen",f:ss},22:{n:"PtgMissArg",f:ss},23:{n:"PtgStr",f:function(e,t,r){return e.l++,aN(e,t-1,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,rJ[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,rW(e,8)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[sR[n],sO[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a,n=e[e.l++],s=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:[(a=e)[a.l+1]>>7,32767&a.read_shift(2)];return[s,(0===i[0]?sO:sC)[i[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,sl(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,so(e,r.biff>=2&&r.biff<=5?6:8,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:ra},40:{n:"PtgMemNoMem",f:ra},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,function(e,t,r){var a,n,s,i,o,c=r&&r.biff?r.biff:8;if(c>=2&&c<=5){return n=(a=e).read_shift(2),s=a.read_shift(1),i=(32768&n)>>15,o=(16384&n)>>14,n&=16383,1==i&&n>=8192&&(n-=16384),1==o&&s>=128&&(s-=256),{r:n,c:s,cRel:o,rRel:i}}var l=e.read_shift(c>=12?4:2),f=e.read_shift(2),h=(16384&f)>>14,u=(32768&f)>>15;if(f&=16383,1==u)for(;l>524287;)l-=1048576;if(1==h)for(;f>8191;)f-=16384;return{r:l,c:f,cRel:h,rRel:u}}(e,0,r)]}},45:{n:"PtgAreaN",f:function(e,t,r){return[(96&e[e.l++])>>5,function(e,t,r){if(r.biff<8)return sc(e,t,r);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=si(e,2),i=si(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,t-1,r)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){var a,n,s,i;return 5==r.biff?(n=(a=e).read_shift(1)>>>5&3,s=a.read_shift(2,"i"),a.l+=8,i=a.read_shift(2),a.l+=12,[n,s,i]):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,sl(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return[a,n,so(e,s,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},sv={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},sb={1:{n:"PtgElfLel",f:sp},2:{n:"PtgElfRw",f:su},3:{n:"PtgElfCol",f:su},6:{n:"PtgElfRwV",f:su},7:{n:"PtgElfColV",f:su},10:{n:"PtgElfRadical",f:su},11:{n:"PtgElfRadicalS",f:sd},13:{n:"PtgElfColS",f:sd},15:{n:"PtgElfColSV",f:sd},16:{n:"PtgElfRadicalLel",f:sp},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=sm[r>>2&31];return{ixti:t,coltype:3&r,rt:i,idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},sw={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:sf},33:{n:"PtgAttrBaxcel",f:sf},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),sh(e,2)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),sh(e,2)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function sT(e,t,r,a){if(a.biff<8)return n=t,void(e.l+=n);for(var n,s,i=e.l+t,o=[],c=0;c!==r.length;++c)switch(r[c][0]){case"PtgArray":r[c][1]=function(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=function(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=a_(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=rJ[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=rW(e,8);break;case 2:r[1]=aP(e,0,{biff:t>0&&t<8?2:t});break;default:throw Error("Bad SerAr: "+r[0])}return r}(e,r.biff);return i}(e,0,a),o.push(r[c][1]);break;case"PtgMemArea":r[c][2]=function(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?rU:aG)(e,8));return n}(e,r[c][1],a),o.push(r[c][2]);break;case"PtgExp":a&&12==a.biff&&(r[c][1][1]=e.read_shift(4),o.push(r[c][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[c][0]}return 0!=(t=i-e.l)&&o.push((s=t,void(e.l+=s))),o}function sE(e,t,r){for(var a,n,s,i=e.l+t,o=[];i!=e.l;)(t=i-e.l,n=sg[s=e[e.l]]||sg[sv[s]],(24===s||25===s)&&(n=(24===s?sb:sw)[e[e.l+1]]),n&&n.f)?o.push([n.n,n.f(e,t,r)]):(a=t,e.l+=a);return o}var sS={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function sy(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:if(null!=r.SID)return e.SheetNames[r.SID];return"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[a[0]][0][3])return"SH33TJSERR2";return n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]}}function sk(e,t,r){var a=sy(e,t,r);return"#REF"==a?a:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,r)}function sx(e,t,r,a,n){var s,i,o,c,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,b=e[0].length;v<b;++v){var w=e[0][v];switch(w[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=eH(" ",e[0][m][1][1]);break;case 1:g=eH("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+sS[w[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=rc(w[1][1],f,n),h.push(rf(o,l));break;case"PtgRefN":o=r?rc(w[1][1],r,n):w[1][1],h.push(rf(o,l));break;case"PtgRef3d":u=w[1][1],o=rc(w[1][2],f,n),p=sk(a,u,n),h.push(p+"!"+rf(o,l));break;case"PtgFunc":case"PtgFuncVar":var T=w[1][0],E=w[1][1];T||(T=0);var S=0==(T&=127)?[]:h.slice(-T);h.length-=T,"User"===E&&(E=S.shift()),h.push(E+"("+S.join(",")+")");break;case"PtgBool":h.push(w[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(w[1]);break;case"PtgNum":h.push(String(w[1]));break;case"PtgStr":h.push('"'+w[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":c=rl(w[1][1],r?{s:r}:f,n),h.push(rh(c,n));break;case"PtgArea":c=rl(w[1][1],f,n),h.push(rh(c,n));break;case"PtgArea3d":u=w[1][1],c=w[1][2],p=sk(a,u,n),h.push(p+"!"+rh(c,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=w[1][2];var y=(a.names||[])[d-1]||(a[0]||[])[d],k=y?y.Name:"SH33TJSNAME"+String(d);k&&"_xlfn."==k.slice(0,6)&&!n.xlfn&&(k=k.slice(6)),h.push(k);break;case"PtgNameX":var x,_=w[1][1];if(d=w[1][2],n.biff<=5)_<0&&(_=-_),a[_]&&(x=a[_][d]);else{var A="";if(14849==((a[_]||[])[0]||[])[0]||(1025==((a[_]||[])[0]||[])[0]?a[_][d]&&a[_][d].itab>0&&(A=a.SheetNames[a[_][d].itab-1]+"!"):A=a.SheetNames[d-1]+"!"),a[_]&&a[_][d])A+=a[_][d].Name;else if(a[0]&&a[0][d])A+=a[0][d].Name;else{var C=(sy(a,_,n)||"").split(";;");C[d-1]?A=C[d-1]:A+="SH33TJSERRX"}h.push(A);break}x||(x={Name:"SH33TJSERRY"}),h.push(x.Name);break;case"PtgParen":var O="(",R=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:O=eH(" ",e[0][m][1][1])+O;break;case 3:O=eH("\r",e[0][m][1][1])+O;break;case 4:R=eH(" ",e[0][m][1][1])+R;break;case 5:R=eH("\r",e[0][m][1][1])+R;break;default:if(n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(O+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:w[1][1],r:w[1][0]};var I={c:r.c,r:r.r};if(a.sharedf[rv(o)]){var N=a.sharedf[rv(o)];h.push(sx(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if((i=a.arrayf[s],!(o.c<i[0].s.c)&&!(o.c>i[0].e.c))&&!(o.r<i[0].s.r)&&!(o.r>i[0].e.r)){h.push(sx(i[1],f,I,a,n)),D=!0;break}D||h.push(w[1])}break;case"PtgArray":h.push("{"+function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];i?2===i[0]?n.push('"'+i[1].replace(/"/g,'""')+'"'):n.push(i[1]):n.push("")}t.push(n.join(","))}return t.join(";")}(w[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+w[1].idx+"[#"+w[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(w))}var F=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=n.biff&&m>=0&&-1==F.indexOf(e[0][v][0])){w=e[0][m];var P=!0;switch(w[1][0]){case 4:P=!1;case 0:g=eH(" ",w[1][1]);break;case 5:P=!1;case 1:g=eH("\r",w[1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+w[1][0])}h.push((P?g:"")+h.pop()+(P?"":g)),m=-1}}if(h.length>1&&n.WTF)throw Error("bad formula stack");return h[0]}function s_(e,t,r){var a=e.l+t,n=aH(e,6);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==t2(e,e.l+6))return[rW(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e,8),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var a,n,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(a=t-2,void(e.l+=a))];var c=sE(e,o,r);return t!==o+i&&(n=sT(e,t-o-i,c,r)),e.l=s,[c,n]}(e,a-e.l,r);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function sA(e,t,r){var a=e.read_shift(4),n=sE(e,a,r),s=e.read_shift(4),i=s>0?sT(e,s,n,r):null;return[n,i]}var sC={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},sO={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},sR={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function sI(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,t){return t.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function sN(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var sD={},sF={},sP="undefined"!=typeof Map;function sL(e,t,r){var a=0,n=e.length;if(r){if(sP?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var s=sP?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t:t},e.Count++,e.Unique++,r&&(sP?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function sM(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(nR=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?a=nN(t.wpx):null!=t.wch&&(a=t.wch),a>-1?(r.width=nD(a),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function sU(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function sB(e,t,r){var a=r.revssf[null!=t.z?t.z:"General"],n=60,s=e.length;if(null==a&&r.ssf){for(;n<392;++n)if(null==r.ssf[n]){ev(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function sW(e,t,r,a,n,s){try{a.cellNF&&(e.z=z[t])}catch(e){if(a.WTF)throw e}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"==typeof e.v&&(e.v=eU(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==z[t]&&ev(eT[t]||"General",t),"e"===e.t)e.w=e.w||rJ[e.v];else if(0===t)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=ee(e.v);else if("d"===e.t){var i=eR(e.v);(0|i)===i?e.w=i.toString(10):e.w=ee(i)}else{if(void 0===e.v)return"";e.w=et(e.v,sF)}else"d"===e.t?e.w=eg(t,eR(e.v),sF):e.w=eg(t,e.v,sF)}catch(e){if(a.WTF)throw e}if(a.cellStyles&&null!=r)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=nO(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=nO(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(e){if(a.WTF&&s.Fills)throw e}}}var sH=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,sV=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,sz=/<(?:\w:)?hyperlink [^>]*>/mg,sG=/"(\w*:\w*)"/,sj=/<(?:\w:)?col\b[^>]*[\/]?>/g,s$=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,sX=/<(?:\w:)?pageMargins[^>]*\/>/g,sY=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,sK=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,sJ=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function sq(e,t,r,a){var n=e9(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=ta(tg(n.codeName)))}var sZ=["objects","scenarios","selectLockedCells","selectUnlockedCells"],sQ=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"],s1=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/,s0=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=tb("v"),i=tb("f");return function(o,c,l,f,h,u){for(var d,p,m,g,v,b=0,w="",T=[],E=[],S=0,y=0,k=0,x="",_=0,A=0,C=0,O=0,R=Array.isArray(u.CellXf),I=[],N=[],D=Array.isArray(c),F=[],P={},L=!1,M=!!l.sheetStubs,U=o.split(t),B=0,W=U.length;B!=W;++B){var H=(w=U[B].trim()).length;if(0!==H){var V=0;t:for(b=0;b<H;++b)switch(w[b]){case">":if("/"!=w[b-1]){++b;break t}if(l&&l.cellStyles){if(_=null!=(p=e9(w.slice(V,b),!0)).r?parseInt(p.r,10):_+1,A=-1,l.sheetRows&&l.sheetRows<_)continue;P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=nU(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[_-1]=P)}break;case"<":V=b}if(V>=b)break;if(_=null!=(p=e9(w.slice(V,b),!0)).r?parseInt(p.r,10):_+1,A=-1,!l.sheetRows||!(l.sheetRows<_)){f.s.r>_-1&&(f.s.r=_-1),f.e.r<_-1&&(f.e.r=_-1),l&&l.cellStyles&&(P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=nU(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[_-1]=P)),T=w.slice(b).split(e);for(var G=0;G!=T.length&&"<"==T[G].trim().charAt(0);++G);for(b=0,T=T.slice(G);b!=T.length;++b)if(0!==(w=T[b].trim()).length){if(E=w.match(r),S=b,y=0,k=0,w="<c "+("<"==w.slice(0,1)?">":"")+w,null!=E&&2===E.length){for(y=0,S=0,x=E[1];y!=x.length&&!((k=x.charCodeAt(y)-64)<1)&&!(k>26);++y)S=26*S+k;A=--S}else++A;for(y=0;y!=w.length&&62!==w.charCodeAt(y);++y);if(++y,(p=e9(w.slice(0,y),!0)).r||(p.r=rv({r:_-1,c:A})),x=w.slice(y),d={t:""},null!=(E=x.match(s))&&""!==E[1]&&(d.v=ta(E[1])),l.cellFormula){if(null!=(E=x.match(i))&&""!==E[1]){if(d.f=ta(tg(E[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=sn(d.f)),E[0].indexOf('t="array"')>-1)d.F=(x.match(n)||[])[1],d.F.indexOf(":")>-1&&I.push([rT(d.F),d.F]);else if(E[0].indexOf('t="shared"')>-1){g=e9(E[0]);var j=ta(tg(E[1]));l.xlfn||(j=sn(j)),N[parseInt(g.si,10)]=[g,j,p.r]}}else(E=x.match(/<f[^>]*\/>/))&&N[(g=e9(E[0])).si]&&(d.f=function(e,t,r){var a=rb(t).s,n=rg(r);return sa(e,{r:n.r-a.r,c:n.c-a.c})}(N[g.si][1],N[g.si][2],p.r));var X=rg(p.r);for(y=0;y<I.length;++y)X.r>=I[y][0].s.r&&X.r<=I[y][0].e.r&&X.c>=I[y][0].s.c&&X.c<=I[y][0].e.c&&(d.F=I[y][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!M)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>A&&(f.s.c=A),f.e.c<A&&(f.e.c=A),d.t){case"n":if(""==d.v||null==d.v){if(!M)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if(void 0===d.v){if(!M)continue;d.t="z"}else m=sD[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?tg(d.v):"",l.cellHTML&&(d.h=tl(d.v));break;case"inlineStr":E=x.match(a),d.t="s",null!=E&&(m=nu(E[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=th(d.v);break;case"d":l.cellDates?d.v=eU(d.v,1):(d.v=eR(eU(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=rq[d.v]}if(C=O=0,v=null,R&&void 0!==p.s&&null!=(v=u.CellXf[p.s])&&(null!=v.numFmtId&&(C=v.numFmtId),l.cellStyles&&null!=v.fillId&&(O=v.fillId)),sW(d,C,O,l,h,u),l.cellDates&&R&&"n"==d.t&&ed(z[C])&&(d.t="d",d.v=eF(d.v)),p.cm&&l.xlmeta){var Y=(l.xlmeta.Cell||[])[p.cm-1];Y&&"XLDAPR"==Y.type&&(d.D=!0)}if(D){var K=rg(p.r);c[K.r]||(c[K.r]=[]),c[K.r][K.c]=d}else c[p.r]=d}}}}F.length>0&&(c["!rows"]=F)}}();function s2(e,t,r,a){var n,s=[e4,tA("worksheet",null,{xmlns:tN[0],"xmlns:r":tI.r})],i=r.SheetNames[e],o=0,c="",l=r.Sheets[i];null==l&&(l={});var f=l["!ref"]||"A1",h=rT(f);if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw Error("Range "+f+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575),f=rw(h)}a||(a={}),l["!comments"]=[];var u=[];!function(e,t,r,a,n){var s=!1,i={},o=null;if("xlsx"!==a.bookType&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch(e){}s=!0,i.codeName=tv(ti(c))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+tA("outlinePr",null,l)}(s||o)&&(n[n.length]=tA("sheetPr",o,i))}(l,r,e,t,s),s[s.length]=tA("dimension",null,{ref:f}),s[s.length]=(d={workbookViewId:"0"},(((r||{}).Workbook||{}).Views||[])[0]&&(d.rightToLeft=r.Workbook.Views[0].RTL?"1":"0"),tA("sheetViews",tA("sheetView",null,d),{})),t.sheetFormat&&(s[s.length]=tA("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=l["!cols"]&&l["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,a=["<cols>"],n=0;n!=t.length;++n)(r=t[n])&&(a[a.length]=tA("col",null,sM(n,r)));return a[a.length]="</cols>",a.join("")}(0,l["!cols"])),s[o=s.length]="<sheetData/>",l["!links"]=[],null!=l["!ref"]&&(c=function(e,t,r,a){var n,s,i=[],o=[],c=rT(e["!ref"]),l="",f="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:f},v=-1;for(d=c.s.c;d<=c.e.c;++d)h[d]=rm(d);for(u=c.s.r;u<=c.e.r;++u){for(o=[],f=rd(u),d=c.s.c;d<=c.e.c;++d){n=h[d]+f;var b=m?(e[u]||[])[d]:e[n];void 0!==b&&null!=(l=function(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var n="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=rJ[e.v];break;case"d":a&&a.cellDates?n=eU(e.v,-1).toISOString():((e=eW(e)).t="n",n=""+(e.v=eR(eU(e.v)))),void 0===e.z&&(e.z=z[14]);break;default:n=e.v}var o=tx("v",ti(n)),c={r:t},l=sB(a.cellXfs,e,a);switch(0!==l&&(c.s=l),e.t){case"n":case"z":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){o=tx("v",""+sL(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str"}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=tA("f",ti(e.f),f)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(c.cm=1),tA("c",o,c)}(b,n,e,t,r,a))&&o.push(l)}(o.length>0||p&&p[u])&&(g={r:f},p&&p[u]&&((s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=nM(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level)),i[i.length]=tA("row",o.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},(s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=nM(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level),i[i.length]=tA("row","",g));return i.join("")}(l,t,e,r,a)).length>0&&(s[s.length]=c),s.length>o+1&&(s[s.length]="</sheetData>",s[o]=s[o].replace("/>",">")),l["!protect"]&&(s[s.length]=(p=l["!protect"],m={sheet:1},sZ.forEach(function(e){null!=p[e]&&p[e]&&(m[e]="1")}),sQ.forEach(function(e){null==p[e]||p[e]||(m[e]="0")}),p.password&&(m.password=ny(p.password).toString(16).toUpperCase()),tA("sheetProtection",null,m))),null!=l["!autofilter"]&&(s[s.length]=function(e,t,r,a){var n="string"==typeof e.ref?e.ref:rw(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=rb(n);i.s.r==i.e.r&&(i.e.r=rb(t["!ref"]).e.r,n=rw(i));for(var o=0;o<s.length;++o){var c=s[o];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),tA("autoFilter",null,{ref:n})}(l["!autofilter"],l,r,e)),null!=l["!merges"]&&l["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+rw(e[r])+'"/>';return t+"</mergeCells>"}(l["!merges"]));var d,p,m,g,v=-1,b=-1;return l["!links"].length>0&&(s[s.length]="<hyperlinks>",l["!links"].forEach(function(e){e[1].Target&&(g={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(b=r6(a,-1,ti(e[1].Target).replace(/#.*$/,""),r2.HLINK),g["r:id"]="rId"+b),(v=e[1].Target.indexOf("#"))>-1&&(g.location=ti(e[1].Target.slice(v+1))),e[1].Tooltip&&(g.tooltip=ti(e[1].Tooltip)),s[s.length]=tA("hyperlink",null,g))}),s[s.length]="</hyperlinks>"),delete l["!links"],null!=l["!margins"]&&(s[s.length]=(sU(n=l["!margins"]),tA("pageMargins",null,n))),(!t||t.ignoreEC||void 0==t.ignoreEC)&&(s[s.length]=tx("ignoredErrors",tA("ignoredError",null,{numberStoredAsText:1,sqref:f}))),u.length>0&&(b=r6(a,-1,"../drawings/drawing"+(e+1)+".xml",r2.DRAW),s[s.length]=tA("drawing",null,{"r:id":"rId"+b}),l["!drawing"]=u),l["!comments"].length>0&&(b=r6(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",r2.VML),s[s.length]=tA("legacyDrawing",null,{"r:id":"rId"+b}),l["!legacy"]=b),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}function s4(e){return[rN(e),rW(e),"n"]}var s3=["left","right","top","bottom","header","footer"],s5=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],s6=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],s8=[],s7=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function s9(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=th(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function ie(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=th(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function it(e){ie(e.WBProps,s5),ie(e.CalcPr,s7),s9(e.WBView,s6),s9(e.Sheets,s8),sF.date1904=th(e.WBProps.date1904)}var ir="][*?/\\".split("");function ia(e,t){if(e.length>31){if(t)return!1;throw Error("Sheet names cannot exceed 31 chars")}var r=!0;return ir.forEach(function(a){if(-1!=e.indexOf(a)){if(!t)throw Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}var is=/<\w+:workbook/;function ii(e){var t=[e4];t[t.length]=tA("workbook",null,{xmlns:tN[0],"xmlns:r":tI.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(s5.forEach(function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(a[t[0]]=e.Workbook.WBProps[t[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=tA("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(s=0,t[t.length]="<bookViews>";s!=e.SheetNames.length&&n[s]&&n[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(s=0,t[t.length]="<sheets>";s!=e.SheetNames.length;++s){var i={name:ti(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=tA("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=tA("definedName",ti(e.Ref),r))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function io(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}var ic=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,il=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function ih(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,o,c=e.match(ic);if(c)for(o=0;o!=c.length;++o)-1===(s=(n=c[o].match(il))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function iu(e,t){var r,a,i,c=t||{};ew();var l=b(tO(e));("binary"==c.type||"array"==c.type||"base64"==c.type)&&(l=void 0!==n?n.utils.decode(65001,g(l)):tg(l));var f=l.slice(0,1024).toLowerCase(),h=!1;if((1023&(f=f.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&f.indexOf(","),1023&f.indexOf(";"))){var u=eW(c);return u.type="string",ns.to_workbook(l,u)}if(-1==f.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(e){f.indexOf("<"+e)>=0&&(h=!0)}),h){var d=l,p=c,m=d.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!m||0==m.length)throw Error("Invalid HTML: could not find <table>");if(1==m.length)return ry(ix(m[0],p),p);var v=oi();return m.forEach(function(e,t){oo(v,ix(e,p),"Sheet"+(t+1))}),v}o={"General Number":"General","General Date":z[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":z[15],"Short Date":z[14],"Long Time":z[19],"Medium Time":z[18],"Short Time":z[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:z[2],Standard:z[4],Percent:z[10],Scientific:z[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var w,T,E,S=[],y={},k=[],x=c.dense?[]:{},_="",A={},C={},O=ih('<Data ss:Type="String">'),R=0,I=0,N=0,D={s:{r:2e6,c:2e6},e:{r:0,c:0}},F={},P={},L="",M=0,U=[],B={},W={},H=0,V=[],G=[],j={},X=[],K=!1,J=[],q=[],Z={},Q=0,er=0,ea={Sheets:[],WBProps:{date1904:!1}},en={};tR.lastIndex=0,l=l.replace(/<!--([\s\S]*?)-->/mg,"");for(var es="";w=tR.exec(l);)switch(w[3]=(es=w[3]).toLowerCase()){case"data":if("data"==es){if("/"===w[1]){if((T=S.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else"/"!==w[0].charAt(w[0].length-2)&&S.push([w[3],!0]);break}if(S[S.length-1][1])break;"/"===w[1]?function(e,t,r,a,n,s,i,c,l,f){var h="General",u=a.StyleID,d={};f=f||{};var p=[],m=0;for(void 0===u&&c&&(u=c.StyleID),void 0===u&&i&&(u=i.StyleID);void 0!==s[u]&&(s[u].nf&&(h=s[u].nf),s[u].Interior&&p.push(s[u].Interior),s[u].Parent);)u=s[u].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=th(e);break;case"String":a.t="s",a.r=tf(ta(e)),a.v=e.indexOf("<")>-1?ta(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(eU(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=ta(e):a.v<60&&(a.v=a.v-1),h&&"General"!=h||(h="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=rq[e],!1!==f.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=tf(t||e))}if(!function(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{if("e"===e.t)e.w=e.w||rJ[e.v];else if("General"===t)"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=ee(e.v):e.w=et(e.v);else{var a,n,s;a=t||"General",n=e.v,s=o[a]||ta(a),e.w="General"===s?et(n):eg(s,n)}}catch(e){if(r.WTF)throw e}try{var i=o[t]||t||"General";if(r.cellNF&&(e.z=i),r.cellDates&&"n"==e.t&&ed(i)){var c=Y(e.v);c&&(e.t="d",e.v=new Date(c.y,c.m-1,c.d,c.H,c.M,c.S,c.u))}}catch(e){if(r.WTF)throw e}}}(a,h,f),!1!==f.cellFormula)if(a.Formula){var g=ta(a.Formula);61==g.charCodeAt(0)&&(g=g.slice(1)),a.f=se(g,n),delete a.Formula,"RC"==a.ArrayRange?a.F=se("RC:RC",n):a.ArrayRange&&(a.F=se(a.ArrayRange,n),l.push([rT(a.F),a.F]))}else for(m=0;m<l.length;++m)n.r>=l[m][0].s.r&&n.r<=l[m][0].e.r&&n.c>=l[m][0].s.c&&n.c<=l[m][0].e.c&&(a.F=l[m][1]);f.cellStyles&&(p.forEach(function(e){!d.patternType&&e.patternType&&(d.patternType=e.patternType)}),a.s=d),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}(l.slice(R,w.index),L,O,"comment"==S[S.length-1][0]?j:A,{c:I,r:N},F,X[I],C,J,c):(L="",O=ih(w[0]),R=w.index+w[0].length);break;case"cell":if("/"===w[1])if(G.length>0&&(A.c=G),(!c.sheetRows||c.sheetRows>N)&&void 0!==A.v&&(c.dense?(x[N]||(x[N]=[]),x[N][I]=A):x[rm(I)+rd(N)]=A),A.HRef&&(A.l={Target:ta(A.HRef)},A.HRefScreenTip&&(A.l.Tooltip=A.HRefScreenTip),delete A.HRef,delete A.HRefScreenTip),(A.MergeAcross||A.MergeDown)&&(Q=I+(0|parseInt(A.MergeAcross,10)),er=N+(0|parseInt(A.MergeDown,10)),U.push({s:{c:I,r:N},e:{c:Q,r:er}})),c.sheetStubs)if(A.MergeAcross||A.MergeDown){for(var ei=I;ei<=Q;++ei)for(var eo=N;eo<=er;++eo)(ei>I||eo>N)&&(c.dense?(x[eo]||(x[eo]=[]),x[eo][ei]={t:"z"}):x[rm(ei)+rd(eo)]={t:"z"});I=Q+1}else++I;else A.MergeAcross?I=Q+1:++I;else(A=function(e){var t=e.split(/\s+/),r={};if(1===t.length)return r;var a,n,s,i,o=e.match(ic);if(o)for(i=0;i!=o.length;++i)-1===(n=(a=o[i].match(il))[1].indexOf(":"))?r[a[1]]=a[2].slice(1,a[2].length-1):r["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(n+1)]=a[2].slice(1,a[2].length-1);return r}(w[0])).Index&&(I=A.Index-1),I<D.s.c&&(D.s.c=I),I>D.e.c&&(D.e.c=I),"/>"===w[0].slice(-2)&&++I,G=[];break;case"row":"/"===w[1]||"/>"===w[0].slice(-2)?(N<D.s.r&&(D.s.r=N),N>D.e.r&&(D.e.r=N),"/>"===w[0].slice(-2)&&(C=ih(w[0])).Index&&(N=C.Index-1),I=0,++N):((C=ih(w[0])).Index&&(N=C.Index-1),Z={},("0"==C.AutoFitHeight||C.Height)&&(Z.hpx=parseInt(C.Height,10),Z.hpt=nM(Z.hpx),q[N]=Z),"1"==C.Hidden&&(Z.hidden=!0,q[N]=Z));break;case"worksheet":if("/"===w[1]){if((T=S.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"));k.push(_),D.s.r<=D.e.r&&D.s.c<=D.e.c&&(x["!ref"]=rw(D),c.sheetRows&&c.sheetRows<=D.e.r&&(x["!fullref"]=x["!ref"],D.e.r=c.sheetRows-1,x["!ref"]=rw(D))),U.length&&(x["!merges"]=U),X.length>0&&(x["!cols"]=X),q.length>0&&(x["!rows"]=q),y[_]=x}else D={s:{r:2e6,c:2e6},e:{r:0,c:0}},N=I=0,S.push([w[3],!1]),_=ta((T=ih(w[0])).Name),x=c.dense?[]:{},U=[],J=[],q=[],en={name:_,Hidden:0},ea.Sheets.push(en);break;case"table":if("/"===w[1]){if((T=S.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else"/>"==w[0].slice(-2)||(S.push([w[3],!1]),X=[],K=!1);break;case"style":if("/"===w[1]){var ec=P;if(c.cellStyles&&ec.Interior){var el=ec.Interior;el.Pattern&&(el.patternType=nB[el.Pattern]||el.Pattern)}F[ec.ID]=ec}else P=ih(w[0]);break;case"numberformat":P.nf=ta(ih(w[0]).Format||"General"),o[P.nf]&&(P.nf=o[P.nf]);for(var ef=0;392!=ef&&z[ef]!=P.nf;++ef);if(392==ef){for(ef=57;392!=ef;++ef)if(null==z[ef]){ev(P.nf,ef);break}}break;case"column":if("table"!==S[S.length-1][0])break;if((E=ih(w[0])).Hidden&&(E.hidden=!0,delete E.Hidden),E.Width&&(E.wpx=parseInt(E.Width,10)),!K&&E.wpx>10){K=!0,nR=6;for(var eh=0;eh<X.length;++eh)X[eh]&&nL(X[eh])}K&&nL(E),X[E.Index-1||X.length]=E;for(var eu=0;eu<+E.Span;++eu)X[X.length]=eW(E);break;case"namedrange":if("/"===w[1])break;ea.Names||(ea.Names=[]);var ep=e9(w[0]),em={Name:ep.Name,Ref:se(ep.RefersTo.slice(1),{r:0,c:0})};ea.Sheets.length>0&&(em.Sheet=ea.Sheets.length-1),ea.Names.push(em);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===w[0].slice(-2)||("/"===w[1]?L+=l.slice(M,w.index):M=w.index+w[0].length);break;case"interior":if(!c.cellStyles)break;P.Interior=ih(w[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===w[0].slice(-2)||("/"===w[1]?(r=es,a=l.slice(H,w.index),s||(s=eA(af)),B[r=s[r]||r]=a):H=w.index+w[0].length);break;case"styles":case"workbook":if("/"===w[1]){if((T=S.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else S.push([w[3],!1]);break;case"comment":if("/"===w[1]){if((T=S.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"));(i=j).t=i.v||"",i.t=i.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),i.v=i.w=i.ixfe=void 0,G.push(j)}else S.push([w[3],!1]),j={a:(T=ih(w[0])).Author};break;case"autofilter":if("/"===w[1]){if((T=S.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else if("/"!==w[0].charAt(w[0].length-2)){var eb=ih(w[0]);x["!autofilter"]={ref:se(eb.Range).replace(/\$/g,"")},S.push([w[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===w[1]){if((T=S.pop())[0]!==w[3])throw Error("Bad state: "+T.join("|"))}else"/"!==w[0].charAt(w[0].length-2)&&S.push([w[3],!0]);break;default:if(0==S.length&&"document"==w[3]||0==S.length&&"uof"==w[3])return iI(l,c);var eT=!0;switch(S[S.length-1][0]){case"officedocumentsettings":switch(w[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:eT=!1}break;case"componentoptions":switch(w[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:eT=!1}break;case"excelworkbook":switch(w[3]){case"date1904":ea.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:eT=!1}break;case"workbookoptions":switch(w[3]){case"owcversion":case"height":case"width":break;default:eT=!1}break;case"worksheetoptions":switch(w[3]){case"visible":if("/>"===w[0].slice(-2));else if("/"===w[1])switch(l.slice(H,w.index)){case"SheetHidden":en.Hidden=1;break;case"SheetVeryHidden":en.Hidden=2}else H=w.index+w[0].length;break;case"header":x["!margins"]||sU(x["!margins"]={},"xlml"),isNaN(+e9(w[0]).Margin)||(x["!margins"].header=+e9(w[0]).Margin);break;case"footer":x["!margins"]||sU(x["!margins"]={},"xlml"),isNaN(+e9(w[0]).Margin)||(x["!margins"].footer=+e9(w[0]).Margin);break;case"pagemargins":var eE=e9(w[0]);x["!margins"]||sU(x["!margins"]={},"xlml"),isNaN(+eE.Top)||(x["!margins"].top=+eE.Top),isNaN(+eE.Left)||(x["!margins"].left=+eE.Left),isNaN(+eE.Right)||(x["!margins"].right=+eE.Right),isNaN(+eE.Bottom)||(x["!margins"].bottom=+eE.Bottom);break;case"displayrighttoleft":ea.Views||(ea.Views=[]),ea.Views[0]||(ea.Views[0]={}),ea.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":x["!outline"]||(x["!outline"]={}),x["!outline"].left=!0;break;default:eT=!1}break;case"pivottable":case"pivotcache":switch(w[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:eT=!1}break;case"pagebreaks":switch(w[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:eT=!1}break;case"autofilter":switch(w[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:eT=!1}break;case"querytable":switch(w[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:eT=!1}break;case"datavalidation":switch(w[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:eT=!1}break;case"sorting":case"conditionalformatting":switch(w[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:eT=!1}break;case"mapinfo":case"schema":case"data":switch(w[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:eT=!1}break;case"smarttags":break;default:eT=!1}if(eT||w[3].match(/!\[CDATA/))break;if(!S[S.length-1][1])throw"Unrecognized tag: "+w[3]+"|"+S.join("|");if("customdocumentproperties"===S[S.length-1][0]){"/>"===w[0].slice(-2)||("/"===w[1]?function(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=th(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=eU(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+r[0])}e[ta(t)]=n}(W,es,V,l.slice(H,w.index)):(V=w,H=w.index+w[0].length));break}if(c.WTF)throw"Unrecognized tag: "+w[3]+"|"+S.join("|")}var eS={};return c.bookSheets||c.bookProps||(eS.Sheets=y),eS.SheetNames=k,eS.Workbook=ea,eS.SSF=eW(z),eS.Props=B,eS.Custprops=W,eS}function id(e,t){switch(i0(t=t||{}),t.type||"base64"){case"base64":return iu(y(e),t);case"binary":case"buffer":case"file":return iu(e,t);case"array":return iu(R(e),t)}}function ip(e){return tA("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+sr(e.Ref,{r:0,c:0})})}var im=[60,1084,2066,2165,2175];function ig(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=z[a])}catch(e){if(t.WTF)throw e}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||rJ[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=ee(e.v):e.w=et(e.v):e.w=eg(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(e){if(t.WTF)throw e}if(t.cellDates&&a&&"n"==e.t&&ed(z[a]||String(a))){var n=Y(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function iv(e,t,r){return{v:e,ixfe:t,t:r}}var ib={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function iw(e,t){if(t||(t={}),i0(t),m(),t.codepage&&d(t.codepage),e.FullPaths){if(ey.find(e,"/encryption"))throw Error("File is password-protected");n=ey.find(e,"!CompObj"),s=ey.find(e,"/Workbook")||ey.find(e,"/Book")}else{switch(t.type){case"base64":e=C(y(e));break;case"binary":e=C(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}rr(e,0),s={content:e}}if(n&&function(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=rz(r,1),r.length-r.l<=4)return;var a=r.read_shift(4);if(0!=a&&!(a>40)&&(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),!(r.length-r.l<=4)&&0x71b239f4===(a=r.read_shift(4)))&&(t.UnicodeClipboardFormat=rz(r,2),0!=(a=r.read_shift(4))&&!(a>40)))r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}(n),t.bookProps&&!t.bookSheets)i={};else{var r,a,n,s,i,o,c=k?"buffer":"array";if(s&&s.content)i=function(e,t){var r,a,n,s,i={opts:{}},o={},c,l,f,h,u,d=t.dense?[]:{},m={},g={},v=null,b=[],w="",T={},E="",S={},y=[],k=[],x=[],_={Sheets:[],WBProps:{date1904:!1},Views:[{}]},A={},C=function(e){return e<8?rK[e]:e<64&&x[e-8]||rK[e]},O=function(e,t,r){var a,n=t.XF.data;n&&n.patternType&&r&&r.cellStyles&&(t.s={},t.s.patternType=n.patternType,(a=nC(C(n.icvFore)))&&(t.s.fgColor={rgb:a}),(a=nC(C(n.icvBack)))&&(t.s.bgColor={rgb:a}))},R=function(e,t,r){if(!(W>1)&&(!r.sheetRows||!(e.r>=r.sheetRows))){if(r.cellStyles&&t.XF&&t.XF.data&&O(e,t,r),delete t.ixfe,delete t.XF,c=e,E=rv(e),g&&g.s&&g.e||(g={s:{r:0,c:0},e:{r:0,c:0}}),e.r<g.s.r&&(g.s.r=e.r),e.c<g.s.c&&(g.s.c=e.c),e.r+1>g.e.r&&(g.e.r=e.r+1),e.c+1>g.e.c&&(g.e.c=e.c+1),r.cellFormula&&t.f){for(var a=0;a<y.length;++a)if(!(y[a][0].s.c>e.c)&&!(y[a][0].s.r>e.r)&&!(y[a][0].e.c<e.c)&&!(y[a][0].e.r<e.r)){t.F=rw(y[a][0]),(y[a][0].s.c!=e.c||y[a][0].s.r!=e.r)&&delete t.f,t.f&&(t.f=""+sx(y[a][1],g,e,U,I));break}}r.dense?(d[e.r]||(d[e.r]=[]),d[e.r][e.c]=t):d[E]=t}},I={enc:!1,sbcch:0,snames:[],sharedf:S,arrayf:y,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(I.password=t.password);var D=[],F=[],P=[],L=[],M=!1,U=[];U.SheetNames=I.snames,U.sharedf=I.sharedf,U.arrayf=I.arrayf,U.names=[],U.XTI=[];var B=0,W=0,H=0,V=[],G=[];I.codepage=1200,p(1200);for(var j=!1;e.l<e.length-1;){var X=e.l,Y=e.read_shift(2);if(0===Y&&10===B)break;var K=e.l===e.length?0:e.read_shift(2),J=iE[Y];if(J&&J.f){if(t.bookSheets&&133===B&&133!==Y)break;if(B=Y,2===J.r||12==J.r){var q,Z=e.read_shift(2);if(K-=2,!I.enc&&Z!==Y&&((255&Z)<<8|Z>>8)!==Y)throw Error("rt mismatch: "+Z+"!="+Y);12==J.r&&(e.l+=10,K-=10)}var Q={};if(Q=10===Y?J.f(e,K,I):function(e,t,r,a,n){var s=a,i=[],o=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&o.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(o)}i.push(o),r.l+=s;for(var c=t2(r,r.l),l=iE[c],f=0;null!=l&&im.indexOf(c)>-1;)s=t2(r,r.l+2),f=r.l+4,2066==c?f+=4:(2165==c||2175==c)&&(f+=12),o=r.slice(f,r.l+4+s),i.push(o),r.l+=4+s,l=iE[c=t2(r,r.l)];var h=N(i);rr(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}(Y,J,e,K,I),0==W&&-1===[9,521,1033,2057].indexOf(B))continue;switch(Y){case 34:i.opts.Date1904=_.WBProps.date1904=Q;break;case 134:i.opts.WriteProtect=!0;break;case 47:if(I.enc||(e.l=0),I.enc=Q,!t.password)throw Error("File is password-protected");if(null==Q.valid)throw Error("Encryption scheme unsupported");if(!Q.valid)throw Error("Password is incorrect");break;case 92:I.lastuser=Q;break;case 66:var ee=Number(Q);switch(ee){case 21010:ee=1200;break;case 32768:ee=1e4;break;case 32769:ee=1252}p(I.codepage=ee),j=!0;break;case 317:I.rrtabid=Q;break;case 25:I.winlocked=Q;break;case 439:i.opts.RefreshAll=Q;break;case 12:i.opts.CalcCount=Q;break;case 16:i.opts.CalcDelta=Q;break;case 17:i.opts.CalcIter=Q;break;case 13:i.opts.CalcMode=Q;break;case 14:i.opts.CalcPrecision=Q;break;case 95:i.opts.CalcSaveRecalc=Q;break;case 15:I.CalcRefMode=Q;break;case 2211:i.opts.FullCalc=Q;break;case 129:Q.fDialog&&(d["!type"]="dialog"),Q.fBelow||((d["!outline"]||(d["!outline"]={})).above=!0),Q.fRight||((d["!outline"]||(d["!outline"]={})).left=!0);break;case 224:k.push(Q);break;case 430:U.push([Q]),U[U.length-1].XTI=[];break;case 35:case 547:U[U.length-1].push(Q);break;case 24:case 536:s={Name:Q.Name,Ref:sx(Q.rgce,g,null,U,I)},Q.itab>0&&(s.Sheet=Q.itab-1),U.names.push(s),U[0]||(U[0]=[],U[0].XTI=[]),U[U.length-1].push(Q),"_xlnm._FilterDatabase"==Q.Name&&Q.itab>0&&Q.rgce&&Q.rgce[0]&&Q.rgce[0][0]&&"PtgArea3d"==Q.rgce[0][0][0]&&(G[Q.itab-1]={ref:rw(Q.rgce[0][0][1][2])});break;case 22:I.ExternCount=Q;break;case 23:0==U.length&&(U[0]=[],U[0].XTI=[]),U[U.length-1].XTI=U[U.length-1].XTI.concat(Q),U.XTI=U.XTI.concat(Q);break;case 2196:if(I.biff<8)break;null!=s&&(s.Comment=Q[1]);break;case 18:d["!protect"]=Q;break;case 19:0!==Q&&I.WTF&&console.error("Password verifier: "+Q);break;case 133:m[Q.pos]=Q,I.snames.push(Q.name);break;case 10:if(--W)break;if(g.e){if(g.e.r>0&&g.e.c>0){if(g.e.r--,g.e.c--,d["!ref"]=rw(g),t.sheetRows&&t.sheetRows<=g.e.r){var et=g.e.r;g.e.r=t.sheetRows-1,d["!fullref"]=d["!ref"],d["!ref"]=rw(g),g.e.r=et}g.e.r++,g.e.c++}D.length>0&&(d["!merges"]=D),F.length>0&&(d["!objects"]=F),P.length>0&&(d["!cols"]=P),L.length>0&&(d["!rows"]=L),_.Sheets.push(A)}""===w?T=d:o[w]=d,d=t.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===I.biff&&(I.biff=({9:2,521:3,1033:4})[Y]||({512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2})[Q.BIFFVer]||8),I.biffguess=0==Q.BIFFVer,0==Q.BIFFVer&&4096==Q.dt&&(I.biff=5,j=!0,p(I.codepage=28591)),8==I.biff&&0==Q.BIFFVer&&16==Q.dt&&(I.biff=2),W++)break;if(d=t.dense?[]:{},I.biff<8&&!j&&(j=!0,p(I.codepage=t.codepage||1252)),I.biff<5||0==Q.BIFFVer&&4096==Q.dt){""===w&&(w="Sheet1"),g={s:{r:0,c:0},e:{r:0,c:0}};var er={pos:e.l-K,name:w};m[er.pos]=er,I.snames.push(w)}else w=(m[X]||{name:""}).name;32==Q.dt&&(d["!type"]="chart"),64==Q.dt&&(d["!type"]="macro"),D=[],F=[],I.arrayf=y=[],P=[],L=[],M=!1,A={Hidden:(m[X]||{hs:0}).hs,name:w};break;case 515:case 3:case 2:"chart"==d["!type"]&&(t.dense?(d[Q.r]||[])[Q.c]:d[rv({c:Q.c,r:Q.r})])&&++Q.c,r={ixfe:Q.ixfe,XF:k[Q.ixfe]||{},v:Q.val,t:"n"},H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 5:case 517:r={ixfe:Q.ixfe,XF:k[Q.ixfe],v:Q.val,t:Q.t},H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 638:r={ixfe:Q.ixfe,XF:k[Q.ixfe],v:Q.rknum,t:"n"},H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 189:for(var ea=Q.c;ea<=Q.C;++ea){var en=Q.rkrec[ea-Q.c][0];r={ixfe:en,XF:k[en],v:Q.rkrec[ea-Q.c][1],t:"n"},H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:ea,r:Q.r},r,t)}break;case 6:case 518:case 1030:if("String"==Q.val){v=Q;break}if((r=iv(Q.val,Q.cell.ixfe,Q.tt)).XF=k[r.ixfe],t.cellFormula){var es=Q.formula;if(es&&es[0]&&es[0][0]&&"PtgExp"==es[0][0][0]){var ei=es[0][0][1][0],eo=es[0][0][1][1],ec=rv({r:ei,c:eo});S[ec]?r.f=""+sx(Q.formula,g,Q.cell,U,I):r.F=((t.dense?(d[ei]||[])[eo]:d[ec])||{}).F}else r.f=""+sx(Q.formula,g,Q.cell,U,I)}H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R(Q.cell,r,t),v=Q;break;case 7:case 519:if(v)v.val=Q,(r=iv(Q,v.cell.ixfe,"s")).XF=k[r.ixfe],t.cellFormula&&(r.f=""+sx(v.formula,g,v.cell,U,I)),H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R(v.cell,r,t),v=null;else throw Error("String record expects Formula");break;case 33:case 545:y.push(Q);var el=rv(Q[0].s);if(l=t.dense?(d[Q[0].s.r]||[])[Q[0].s.c]:d[el],t.cellFormula&&l){if(!v||!el||!l)break;l.f=""+sx(Q[1],g,Q[0],U,I),l.F=rw(Q[0])}break;case 1212:if(!t.cellFormula)break;if(E){if(!v)break;S[rv(v.cell)]=Q[0],((l=t.dense?(d[v.cell.r]||[])[v.cell.c]:d[rv(v.cell)])||{}).f=""+sx(Q[0],g,c,U,I)}break;case 253:r=iv(b[Q.isst].t,Q.ixfe,"s"),b[Q.isst].h&&(r.h=b[Q.isst].h),r.XF=k[r.ixfe],H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 513:t.sheetStubs&&(r={ixfe:Q.ixfe,XF:k[Q.ixfe],t:"z"},H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t));break;case 190:if(t.sheetStubs)for(var ef=Q.c;ef<=Q.C;++ef){var eh=Q.ixfe[ef-Q.c];r={ixfe:eh,XF:k[eh],t:"z"},H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:ef,r:Q.r},r,t)}break;case 214:case 516:case 4:(r=iv(Q.val,Q.ixfe,"s")).XF=k[r.ixfe],H>0&&(r.z=V[r.ixfe>>8&63]),ig(r,t,i.opts.Date1904),R({c:Q.c,r:Q.r},r,t);break;case 0:case 512:1===W&&(g=Q);break;case 252:b=Q;break;case 1054:if(4==I.biff){V[H++]=Q[1];for(var eu=0;eu<H+163&&z[eu]!=Q[1];++eu);eu>=163&&ev(Q[1],H+163)}else ev(Q[1],Q[0]);break;case 30:V[H++]=Q;for(var ed=0;ed<H+163&&z[ed]!=Q;++ed);ed>=163&&ev(Q,H+163);break;case 229:D=D.concat(Q);break;case 93:F[Q.cmo[0]]=I.lastobj=Q;break;case 438:I.lastobj.TxO=Q;break;case 127:I.lastobj.ImData=Q;break;case 440:for(u=Q[0].s.r;u<=Q[0].e.r;++u)for(h=Q[0].s.c;h<=Q[0].e.c;++h)(l=t.dense?(d[u]||[])[h]:d[rv({c:h,r:u})])&&(l.l=Q[1]);break;case 2048:for(u=Q[0].s.r;u<=Q[0].e.r;++u)for(h=Q[0].s.c;h<=Q[0].e.c;++h)(l=t.dense?(d[u]||[])[h]:d[rv({c:h,r:u})])&&l.l&&(l.l.Tooltip=Q[1]);break;case 28:if(I.biff<=5&&I.biff>=2)break;l=t.dense?(d[Q[0].r]||[])[Q[0].c]:d[rv(Q[0])];var ep=F[Q[2]];l||(t.dense?(d[Q[0].r]||(d[Q[0].r]=[]),l=d[Q[0].r][Q[0].c]={t:"z"}):l=d[rv(Q[0])]={t:"z"},g.e.r=Math.max(g.e.r,Q[0].r),g.s.r=Math.min(g.s.r,Q[0].r),g.e.c=Math.max(g.e.c,Q[0].c),g.s.c=Math.min(g.s.c,Q[0].c)),l.c||(l.c=[]),f={a:Q[1],t:ep.TxO.t},l.c.push(f);break;case 2173:k[Q.ixfe],Q.ext.forEach(function(e){e[0]});break;case 125:if(!I.cellStyles)break;for(;Q.e>=Q.s;)P[Q.e--]={width:Q.w/256,level:Q.level||0,hidden:!!(1&Q.flags)},M||(M=!0,nP(Q.w/256)),nL(P[Q.e+1]);break;case 520:var em={};null!=Q.level&&(L[Q.r]=em,em.level=Q.level),Q.hidden&&(L[Q.r]=em,em.hidden=!0),Q.hpt&&(L[Q.r]=em,em.hpt=Q.hpt,em.hpx=nU(Q.hpt));break;case 38:case 39:case 40:case 41:d["!margins"]||sU(d["!margins"]={}),d["!margins"][({38:"left",39:"right",40:"top",41:"bottom"})[Y]]=Q;break;case 161:d["!margins"]||sU(d["!margins"]={}),d["!margins"].header=Q.header,d["!margins"].footer=Q.footer;break;case 574:Q.RTL&&(_.Views[0].RTL=!0);break;case 146:x=Q;break;case 2198:n=Q;break;case 140:a=Q;break;case 442:w?A.CodeName=Q||A.name:_.WBProps.CodeName=Q||"ThisWorkbook"}}else J||console.error("Missing Info for XLS Record 0x"+Y.toString(16)),e.l+=K}return i.SheetNames=ex(m).sort(function(e,t){return Number(e)-Number(t)}).map(function(e){return m[e].name}),t.bookSheets||(i.Sheets=o),!i.SheetNames.length&&T["!ref"]?(i.SheetNames.push("Sheet1"),i.Sheets&&(i.Sheets.Sheet1=T)):i.Preamble=T,i.Sheets&&G.forEach(function(e,t){i.Sheets[i.SheetNames[t]]["!autofilter"]=e}),i.Strings=b,i.SSF=eW(z),I.enc&&(i.Encryption=I.enc),n&&(i.Themes=n),i.Metadata={},void 0!==a&&(i.Metadata.Country=a),U.names.length>0&&(_.Names=U.names),i.Workbook=_,i}(s.content,t);else if((o=ey.find(e,"PerfectOffice_MAIN"))&&o.content)i=ni.to_workbook(o.content,(t.type=c,t));else if((o=ey.find(e,"NativeContent_MAIN"))&&o.content)i=ni.to_workbook(o.content,(t.type=c,t));else if((o=ey.find(e,"MN0"))&&o.content)throw Error("Unsupported Works 4 for Mac file");else throw Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&ey.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(i.vbaraw=(r=e,a=ey.utils.cfb_new({root:"R"}),r.FullPaths.forEach(function(e,t){if("/"!==e.slice(-1)&&e.match(/_VBA_PROJECT_CUR/)){var n=e.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");ey.utils.cfb_add(a,n,r.FileIndex[t].content)}}),ey.write(a)))}var l={};return e.FullPaths&&function(e,t,r){var a=ey.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=ay(a,rj,ib.DSI);for(var s in n)t[s]=n[s]}catch(e){if(r.WTF)throw e}var i=ey.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var o=ay(i,r$,ib.SI);for(var c in o)null==t[c]&&(t[c]=o[c])}catch(e){if(r.WTF)throw e}t.HeadingPairs&&t.TitlesOfParts&&(ai(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}(e,l,t),i.Props=i.Custprops=l,t.bookFiles&&(i.cfb=e),i}var iT={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[rR(e)]}},2:{f:function(e){return[rR(e),rL(e),"n"]}},3:{f:function(e){return[rR(e),e.read_shift(1),"e"]}},4:{f:function(e){return[rR(e),e.read_shift(1),"b"]}},5:{f:function(e){return[rR(e),rW(e),"n"]}},6:{f:function(e){return[rR(e),rA(e),"str"]}},7:{f:function(e){return[rR(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=rR(e);n.r=r["!row"];var s=[n,rA(e),"str"];if(r.cellFormula){e.l+=2;var i=sA(e,a-e.l,r);s[3]=sx(i,null,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=rR(e);n.r=r["!row"];var s=[n,rW(e),"n"];if(r.cellFormula){e.l+=2;var i=sA(e,a-e.l,r);s[3]=sx(i,null,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=rR(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=sA(e,a-e.l,r);s[3]=sx(i,null,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=rR(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=sA(e,a-e.l,r);s[3]=sx(i,null,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[rN(e)]}},13:{f:function(e){return[rN(e),rL(e),"n"]}},14:{f:function(e){return[rN(e),e.read_shift(1),"e"]}},15:{f:function(e){return[rN(e),e.read_shift(1),"b"]}},16:{f:s4},17:{f:function(e){return[rN(e),rA(e),"str"]}},18:{f:function(e){return[rN(e),e.read_shift(4),"s"]}},19:{f:rO},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=rA(e),i=sA(e,0,r),o=rF(e);e.l=a;var c={Name:s,Ptg:i};return n<0xfffffff&&(c.Sheet=n),o&&(c.Comment=o),c}},40:{},42:{},43:{f:function(e,t,r){var a,n={};n.sz=e.read_shift(2)/20;var s=(a=e.read_shift(1),e.l++,{fBold:1&a,fItalic:2&a,fUnderline:4&a,fStrikeout:8&a,fOutline:16&a,fShadow:32&a,fCondense:64&a,fExtend:128&a});switch(s.fItalic&&(n.italic=1),s.fCondense&&(n.condense=1),s.fExtend&&(n.extend=1),s.fShadow&&(n.shadow=1),s.fOutline&&(n.outline=1),s.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(n.underline=i);var o=e.read_shift(1);o>0&&(n.family=o);var c=e.read_shift(1);switch(c>0&&(n.charset=c),e.l++,n.color=function(e){var t={},r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r>>>1){case 0:t.auto=1;break;case 1:t.index=a;var c=rK[a];c&&(t.rgb=nC(c));break;case 2:t.rgb=nC([s,i,o]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e,8),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=rA(e,t-21),n}},44:{f:function(e,t){return[e.read_shift(2),rA(e,t-2)]}},45:{f:ra},46:{f:ra},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:a9},62:{f:function(e){return[rR(e),rO(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=rv(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:ra,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=rA(e,t-19),r}},148:{f:rU,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?rA(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=rF(e,t-8),r.name=rA(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:rU},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:rU},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:rA(e,t-8)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:rF},357:{},358:{},359:{},360:{T:1},361:{},362:{f:a6},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=rU(e,16),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var o=sA(e,a-e.l,r);i[1]=o}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[rU(e,16)];if(r.cellFormula){var s=sA(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return s3.forEach(function(r){t[r]=rW(e,8)}),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=rU(e,16),n=rF(e),s=rA(e),i=rA(e),o=rA(e);e.l=r;var c={rfx:a,relId:n,loc:s,display:o};return i&&(c.Tooltip=i),c}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:rF},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:rA},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=rU(e,16);return t.rfx=r.s,t.ref=rv(r.s),e.l+=16,t}},636:{T:-1},637:{f:rO},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:rA(e,t-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},iE={6:{f:s_},10:{f:ax},12:{f:aC},13:{f:aC},14:{f:a_},15:{f:a_},16:{f:rW},17:{f:a_},18:{f:a_},19:{f:aC},20:{f:a2},21:{f:a2},23:{f:a6},24:{f:a5},25:{f:a_},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=aP(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},o,i,s]}}(e,0,r)}},29:{},34:{f:a_},35:{f:a4},38:{f:rW},39:{f:rW},40:{f:rW},41:{f:rW},42:{f:a_},43:{f:a_},47:{f:function(e,t,r){var a,n,s,i,o={Type:r.biff>=8?e.read_shift(2):0};return o.Type?(a=t-2,(n=o||{}).Info=e.read_shift(2),e.l-=2,1===n.Info?n.Data=function(e){var t={},r=t.EncryptionVersionInfo=nT(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e,a):n.Data=function(e,t){var r={},a=r.EncryptionVersionInfo=nT(e,4);if(t-=4,2!=a.Minor)throw Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=nE(e,n),r.EncryptionVerifier=nS(e,t-=n),r}(e,a)):(r.biff,i={key:aC(e),verificationBytes:aC(e)},r.password&&(i.verifier=ny(r.password)),o.valid=i.verificationBytes===i.verifier,o.valid&&(o.insitu=n_(r.password))),o}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=aN(e,0,r),a}},51:{f:aC},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:a_},65:{f:function(){}},66:{f:aC},77:{},80:{},81:{},82:{},85:{f:aC},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=aP(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8){var a,n,s,i,o,c,l;return a=e,n=t,s=r,a.l+=4,i=a.read_shift(2),o=a.read_shift(2),c=a.read_shift(2),a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=6,n-=36,(l=[]).push((a7[i]||ra)(a,n,s)),{cmo:[o,i,c],ft:l}}var f=aX(e,22),h=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(aK[n](e,r-e.l))}catch(t){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,f[1]);return{cmo:f,ft:h}}},94:{},95:{f:a_},96:{},97:{},99:{f:a_},125:{f:a9},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:aC},131:{f:a_},132:{f:a_},133:{f:function(e,t,r){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=aN(e,0,r);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=rX[t]||t,t=e.read_shift(2),r[1]=rX[t]||t,r}},141:{f:aC},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aW(e,8));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:aC},157:{},158:{},160:{f:aR},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=rW(e,8),r.footer=rW(e,8),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(az(e));if(e.l!==r)throw Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:ax},197:{},198:{},199:{},200:{},201:{},202:{f:a_},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:aC},220:{},221:{f:a_},222:{},224:{f:function(e,t,r){var a,n,s,i,o,c,l={};return l.ifnt=e.read_shift(2),l.numFmtId=e.read_shift(2),l.flags=e.read_shift(2),l.fStyle=l.flags>>2&1,t-=6,l.fStyle,n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),c=e.read_shift(2),n.patternType=rY[o>>26],r.cellStyles&&(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&o,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=127&c,n.icvBack=c>>7&127,n.fsxButton=c>>14&1),l.data=n,l}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:ax},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(aG(e,t));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(function(e){var t=l;l=1200;var r,a=e.read_shift(2),n=e.read_shift(1),s=4&n,i=8&n,o=0,c={};i&&(o=e.read_shift(2)),s&&(r=e.read_shift(4));var f=0===a?"":e.read_shift(a,2==1+(1&n)?"dbcs-cont":"sbcs-cont");return i&&(e.l+=4*o),s&&(e.l+=r),c.t=f,i||(c.raw="<t>"+c.t+"</t>",c.r=c.t),l=t,c}(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var t=aH(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:aR},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:a_},353:{f:ax},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw Error("Unexpected SupBook type: "+s);for(var i=aD(e,s),o=[];a>e.l;)o.push(aF(e));return[s,n,i,o]}},431:{f:a_},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s,i,o=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(o)?e.l+=6:(e.read_shift(1),e.l++,e.read_shift(2),e.l+=2);var c=e.read_shift(2);e.read_shift(2),aC(e,2);var l=e.read_shift(2);e.l+=l;for(var f=1;f<e.lens.length-1;++f){if(e.l-a!=e.lens[f])throw Error("TxO: bad continue record");var h=e[e.l],u=aD(e,e.lens[f+1]-e.lens[f]-1);if((n+=u).length>=(h?c:2*c))break}if(n.length!==c&&n.length!==2*c)throw Error("cchText: "+c+" != "+n.length);return e.l=a+t,{t:n}}catch(r){return e.l=a+t,{t:n}}}},439:{f:a_},440:{f:function(e,t){var r=aG(e,8);return e.l+=16,[r,function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,c,l,f,h="";16&n&&(s=aM(e,r-e.l)),128&n&&(i=aM(e,r-e.l)),(257&n)==257&&(o=aM(e,r-e.l)),(257&n)==1&&(c=function(e,t){var r,a,n,s,i=e.read_shift(16);switch(t-=16,i){case"e0c9ea79f9bace118c8200aa004ba90b":return r=e.read_shift(4),a=e.l,n=!1,r>24&&(e.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(n=!0),e.l=a),s=e.read_shift((n?r-24:r)>>1,"utf16le").replace(D,""),n&&(e.l+=24),s;case"0303000000000000c000000000000046":for(var o=e.read_shift(2),c="";o-- >0;)c+="../";var l=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw Error("Bad FileMoniker");if(0===e.read_shift(4))return c+l.replace(/\\/g,"/");var f=e.read_shift(4);if(3!=e.read_shift(2))throw Error("Bad FileMoniker");return c+e.read_shift(f>>1,"utf16le").replace(D,"");default:throw Error("Unsupported Moniker "+i)}}(e,r-e.l)),8&n&&(h=aM(e,r-e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=ah(e)),e.l=r;var u=i||o||c||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24)]}},441:{},442:{f:aF},443:{},444:{f:aC},445:{},446:{},448:{f:ax},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:ax},512:{f:aQ},513:{f:aH},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=aH(e,6);return a.val=rW(e,8),a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=e.l+t,n=aH(e,6);return 2==r.biff&&e.l++,n.val=aF(e,a-e.l,r),n}},517:{f:a0},519:{f:aF},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:a8},549:{f:aZ},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=az(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),aP(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=a$(e,6);e.l++;var n=e.read_shift(1);return[function(e,t,r){var a,n,s=e.l+t,i=e.read_shift(2),o=sE(e,i,r);return 65535==i?[[],(a=t-2,void(e.l+=a))]:(t!==i+2&&(n=sT(e,s-i-2,o,r)),[o,n])}(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=aG(e,8),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(D,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:aJ},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:ax},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),s=[];n-- >0;)s.push(function(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=function(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){e.l+=t}(e,4);break;case 2:t.xclrValue=aB(e,4);break;case 3:t.xclrValue=e.read_shift(4)}return e.l+=8,t}(e,r);break;case 6:a[1]=void(e.l+=r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw Error("Unrecognized ExtProp type: "+t+" "+r)}return a}(e,r-e.l));return{ixfe:a,ext:s}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:a_,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2);return[aD(e,a,r),aD(e,n,r)]},r:12},2197:{},2198:{f:function(e,t,r){var a,n=e.l+t;if(124226!==e.read_shift(4)){if(!r.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;try{a=e0(s,{type:"array"})}catch(e){return}var i=eq(a,"theme/theme/theme1.xml",!0);if(i)return n2(i,r)}},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:ax},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t,r,a=(t=e.read_shift(2),r=e.read_shift(2),e.l+=8,{type:t,flags:r});if(2211!=a.type)throw Error("Invalid Future Record "+a.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:aC},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(aW(e,8));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:aQ},1:{},2:{f:function(e){var t=aH(e,6);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=aH(e,6);++e.l;var r=rW(e,8);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=aH(e,6);++e.l;var n=aP(e,t-7,r);return a.t="str",a.val=n,a}},5:{f:a0},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:aJ},11:{},22:{f:aC},30:{f:aP},31:{},32:{},33:{f:a8},36:{},37:{f:aZ},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:aC},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=aH(e,6),s=e.read_shift(2),i=aD(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:s_},521:{f:aJ},536:{f:a5},547:{f:a4},561:{},579:{},1030:{f:s_},1033:{f:aJ},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function iS(e,t,r,a){if(!isNaN(t)){var n=a||(r||[]).length||0,s=e.next(4);s.write_shift(2,t),s.write_shift(2,n),n>0&&t1(r)&&e.push(r)}}function iy(e,t,r){return e||(e=rn(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function ik(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];a&&a["!ref"]&&rb(a["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var n=t||{};switch(n.biff||2){case 8:case 5:var s=t||{},i=[];e&&!e.SSF&&(e.SSF=eW(z)),e&&e.SSF&&(ew(),eb(e.SSF),s.revssf=eC(e.SSF),s.revssf[e.SSF[65535]]=0,s.ssf=e.SSF),s.Strings=[],s.Strings.Count=0,s.Strings.Unique=0,i2(s),s.cellXfs=[],sB(s.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var o=0;o<e.SheetNames.length;++o)i[i.length]=function(e,t,r){var a,n,s,i,o,c,l,f,h,u=ri(),d=r.SheetNames[e],p=r.Sheets[d]||{},m=(r||{}).Workbook||{},g=(m.Sheets||[])[e]||{},v=Array.isArray(p),b=8==t.biff,w="",T=[],E=rT(p["!ref"]||"A1"),S=b?65536:16384;if(E.e.c>255||E.e.r>=S){if(t.WTF)throw Error("Range "+(p["!ref"]||"A1")+" exceeds format limit A1:IV16384");E.e.c=Math.min(E.e.c,255),E.e.r=Math.min(E.e.c,S-1)}iS(u,2057,aq(r,16,t)),iS(u,13,aO(1)),iS(u,12,aO(100)),iS(u,15,aA(!0)),iS(u,17,aA(!1)),iS(u,16,rH(.001)),iS(u,95,aA(!0)),iS(u,42,aA(!1)),iS(u,43,aA(!1)),iS(u,130,aO(1)),iS(u,128,(a=[0,0],(n=rn(8)).write_shift(4,0),n.write_shift(2,a[0]?a[0]+1:0),n.write_shift(2,a[1]?a[1]+1:0),n)),iS(u,131,aA(!1)),iS(u,132,aA(!1)),b&&function(e,t){if(t){var r=0;t.forEach(function(t,a){var n,s,i;++r<=256&&t&&iS(e,125,(n=sM(a,t),(s=rn(12)).write_shift(2,a),s.write_shift(2,a),s.write_shift(2,256*n.width),s.write_shift(2,0),i=0,n.hidden&&(i|=1),s.write_shift(1,i),i=n.level||0,s.write_shift(1,i),s.write_shift(2,0),s))})}}(u,p["!cols"]),iS(u,512,((i=rn(2*(s=8!=t.biff&&t.biff?2:4)+6)).write_shift(s,E.s.r),i.write_shift(s,E.e.r+1),i.write_shift(2,E.s.c),i.write_shift(2,E.e.c+1),i.write_shift(2,0),i)),b&&(p["!links"]=[]);for(var y=E.s.r;y<=E.e.r;++y){w=rd(y);for(var k=E.s.c;k<=E.e.c;++k){y===E.s.r&&(T[k]=rm(k)),h=T[k]+w;var x=v?(p[y]||[])[k]:p[h];x&&(!function(e,t,r,a,n){var s=16+sB(n.cellXfs,t,n);if(null==t.v&&!t.bf)return iS(e,513,aV(r,a,s));if(t.bf)iS(e,6,function(e,t,r,a,n){var s=aV(t,r,n),i=function(e){if(null==e){var t=rn(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return"number"==typeof e?rH(e):rH(0)}(e.v),o=rn(6);o.write_shift(2,33),o.write_shift(4,0);for(var c=rn(e.bf.length),l=0;l<e.bf.length;++l)c[l]=e.bf[l];return N([s,i,o,c])}(t,r,a,0,s));else switch(t.t){case"d":case"n":var i,o="d"==t.t?eR(eU(t.v)):t.v;iS(e,515,(aV(r,a,s,i=rn(14)),rH(o,i),i));break;case"b":case"e":iS(e,517,(c=t.v,l=t.t,aV(r,a,s,f=rn(8)),aI(c,l,f),f));break;case"s":case"str":if(n.bookSST){var c,l,f,h,u,d,p,m=sL(n.Strings,t.v,n.revStrings);iS(e,253,(aV(r,a,s,p=rn(10)),p.write_shift(4,m),p))}else iS(e,516,(h=(t.v||"").slice(0,255),aV(r,a,s,d=rn(8+ +(u=!n||8==n.biff)+(1+u)*h.length)),d.write_shift(2,h.length),u&&d.write_shift(1,1),d.write_shift((1+u)*h.length,h,u?"utf16le":"sbcs"),d));break;default:iS(e,513,aV(r,a,s))}}(u,x,y,k,t),b&&x.l&&p["!links"].push([h,x.l]))}}var _=g.CodeName||g.name||d;return b&&iS(u,574,(o=(m.Views||[])[0],c=rn(18),l=1718,o&&o.RTL&&(l|=64),c.write_shift(2,l),c.write_shift(4,0),c.write_shift(4,64),c.write_shift(4,0),c.write_shift(4,0),c)),b&&(p["!merges"]||[]).length&&iS(u,229,function(e){var t=rn(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)aj(e[r],t);return t}(p["!merges"])),b&&function(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];iS(e,440,function(e){var t=rn(24),r=rg(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return N([t,function(e){var t=rn(512),r=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)aU(a=a.slice(1),t);else if(2&s){for(r=0,i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&aU(n>-1?a.slice(n+1):"",t)}else{for(r=0,i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var c=0;"../"==a.slice(3*c,3*c+3)||"..\\"==a.slice(3*c,3*c+3);)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,255&a.charCodeAt(r+3*c));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}(e[1])])}(a)),a[1].Tooltip&&iS(e,2048,function(e){var t=e[1].Tooltip,r=rn(10+2*(t.length+1));r.write_shift(2,2048);var a=rg(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}(a))}delete t["!links"]}(u,p),iS(u,442,aL(_,t)),b&&((f=rn(19)).write_shift(4,2151),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,1),f.write_shift(4,0),iS(u,2151,f),(f=rn(39)).write_shift(4,2152),f.write_shift(4,0),f.write_shift(4,0),f.write_shift(2,3),f.write_shift(1,0),f.write_shift(4,0),f.write_shift(2,1),f.write_shift(4,4),f.write_shift(2,0),aj(rT(p["!ref"]||"A1"),f),f.write_shift(4,4),iS(u,2152,f)),iS(u,10),u.end()}(o,s,e);return i.unshift(function(e,t,r){var a,n,s,i,o,c,l,f=ri(),h=(e||{}).Workbook||{},u=h.Sheets||[],d=h.WBProps||{},p=8==r.biff,m=5==r.biff;iS(f,2057,aq(e,5,r)),"xla"==r.bookType&&iS(f,135),iS(f,225,p?aO(1200):null),iS(f,193,function(e,t){t||(t=rn(2));for(var r=0;r<2;++r)t.write_shift(1,0);return t}(2)),m&&iS(f,191),m&&iS(f,192),iS(f,226),iS(f,92,function(e,t){var r=!t||8==t.biff,a=rn(r?112:54);for(a.write_shift(8==t.biff?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,0x33336853),a.write_shift(4,5458548|0x20000000*!r);a.l<a.length;)a.write_shift(1,32*!r);return a}(0,r)),iS(f,66,aO(p?1200:1252)),p&&iS(f,353,aO(0)),p&&iS(f,448),iS(f,317,function(e){for(var t=rn(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),p&&e.vbaraw&&iS(f,211),p&&e.vbaraw&&iS(f,442,aL(d.CodeName||"ThisWorkbook",r)),iS(f,156,aO(17)),iS(f,25,aA(!1)),iS(f,18,aA(!1)),iS(f,19,aO(0)),p&&iS(f,431,aA(!1)),p&&iS(f,444,aO(0)),iS(f,61,((a=rn(18)).write_shift(2,0),a.write_shift(2,0),a.write_shift(2,29280),a.write_shift(2,17600),a.write_shift(2,56),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,1),a.write_shift(2,500),a)),iS(f,64,aA(!1)),iS(f,141,aO(0)),iS(f,34,aA("true"==(e.Workbook&&e.Workbook.WBProps&&th(e.Workbook.WBProps.date1904)?"true":"false"))),iS(f,14,aA(!0)),p&&iS(f,439,aA(!1)),iS(f,218,aO(0)),iS(f,49,(s=(n={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(o=rn((i=r&&5==r.biff)?15+s.length:16+2*s.length)).write_shift(2,20*(n.sz||12)),o.write_shift(4,0),o.write_shift(2,400),o.write_shift(4,0),o.write_shift(2,0),o.write_shift(1,s.length),i||o.write_shift(1,1),o.write_shift((i?1:2)*s.length,s,i?"sbcs":"utf16le"),o)),c=e.SSF,c&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=c[t]&&iS(f,1054,function(e,t,r,a){var n=r&&5==r.biff;a||(a=rn(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}(t,c[t],r))});for(var g=0;g<16;++g)iS(f,224,a1({numFmtId:0,style:!0},0,r));r.cellXfs.forEach(function(e){iS(f,224,a1(e,0,r))}),p&&iS(f,352,aA(!1));var v=f.end(),b=ri();p&&iS(b,140,(l||(l=rn(4)),l.write_shift(2,1),l.write_shift(2,1),l)),p&&r.Strings&&function(e,t,r,a){var n=(r||[]).length||0;if(n<=8224)return iS(e,252,r,n);if(!isNaN(252)){for(var s=r.parts||[],i=0,o=0,c=0;c+(s[i]||8224)<=8224;)c+=s[i]||8224,i++;var l=e.next(4);for(l.write_shift(2,t),l.write_shift(2,c),e.push(r.slice(o,o+c)),o+=c;o<n;){for((l=e.next(4)).write_shift(2,60),c=0;c+(s[i]||8224)<=8224;)c+=s[i]||8224,i++;l.write_shift(2,c),e.push(r.slice(o,o+c)),o+=c}}}(b,252,function(e,t){var r=rn(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=function(e){var t=e.t||"",r=rn(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=rn(2*t.length);return a.write_shift(2*t.length,t,"utf16le"),N([r,a])}(e[n],t);var s=N([r].concat(a));return s.parts=[r.length].concat(a.map(function(e){return e.length})),s}(r.Strings,r)),iS(b,10);var w=b.end(),T=ri(),E=0,S=0;for(S=0;S<e.SheetNames.length;++S)E+=(p?12:11)+(p?2:1)*e.SheetNames[S].length;var y=v.length+E+w.length;for(S=0;S<e.SheetNames.length;++S)iS(T,133,function(e,t){var r=!t||t.biff>=8?2:1,a=rn(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}({pos:y,hs:(u[S]||{}).Hidden||0,dt:0,name:e.SheetNames[S]},r)),y+=t[S].length;var k=T.end();if(E!=k.length)throw Error("BS8 "+E+" != "+k.length);var x=[];return v.length&&x.push(v),k.length&&x.push(k),w.length&&x.push(w),N(x)}(e,i,s)),N(i);case 4:case 3:case 2:for(var c=t||{},l=ri(),f=0,h=0;h<e.SheetNames.length;++h)e.SheetNames[h]==c.sheet&&(f=h);if(0==f&&c.sheet&&e.SheetNames[0]!=c.sheet)throw Error("Sheet not found: "+c.sheet);return iS(l,4==c.biff?1033:3==c.biff?521:9,aq(e,16,c)),!function(e,t,r,a){var n,s=Array.isArray(t),i=rT(t["!ref"]||"A1"),o="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),n=rw(i)}for(var l=i.s.r;l<=i.e.r;++l){o=rd(l);for(var f=i.s.c;f<=i.e.c;++f){l===i.s.r&&(c[f]=rm(f)),n=c[f]+o;var h=s?(t[l]||[])[f]:t[n];h&&function(e,t,r,a){if(null!=t.v)switch(t.t){case"d":case"n":var n,s,i,o,c,l,f,h="d"==t.t?eR(eU(t.v)):t.v;h==(0|h)&&h>=0&&h<65536?iS(e,2,(iy(l=rn(9),r,a),l.write_shift(2,h),l)):iS(e,3,(iy(f=rn(15),r,a),f.write_shift(8,h,"f"),f));return;case"b":case"e":iS(e,5,(n=t.v,s=t.t,iy(i=rn(9),r,a),aI(n,s||"b",i),i));return;case"s":case"str":iS(e,4,(iy(c=rn(8+2*(o=(t.v||"").slice(0,255)).length),r,a),c.write_shift(1,o.length),c.write_shift(o.length,o,"sbcs"),c.l<c.length?c.slice(0,c.l):c));return}iS(e,1,iy(null,r,a))}(e,h,l,f,a)}}}(l,e.Sheets[e.SheetNames[f]],0,c,e),iS(l,10),l.end()}throw Error("invalid type "+n.bookType+" for BIFF")}function ix(e,t){var r=t||{},a=r.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,o=s&&s.index||e.length,c=ej(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<c.length;++i){var m=c[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"==g){if(++l,r.sheetRows&&r.sheetRows<=l){--l;break}f=0;continue}if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(o=0;o<v.length;++o){var b=v[o].trim();if(b.match(/<t[dh]/i)){for(var w=b,T=0;"<"==w.charAt(0)&&(T=w.indexOf(">"))>-1;)w=w.slice(T+1);for(var E=0;E<p.length;++E){var S=p[E];S.s.c==f&&S.s.r<l&&l<=S.e.r&&(f=S.e.c+1,E=-1)}var y=e9(b.slice(0,b.indexOf(">")));u=y.colspan?+y.colspan:1,((h=+y.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var k=y.t||y["data-t"]||"";if(!w.length||(w=tw(w),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),!w.length)){f+=u;continue}var x={t:"s",v:w};r.raw||!w.trim().length||"s"==k||("TRUE"===w?x={t:"b",v:!0}:"FALSE"===w?x={t:"b",v:!1}:isNaN(eV(w))?isNaN(eG(w).getDate())||(x={t:"d",v:eU(w)},r.cellDates||(x={t:"n",v:eR(x.v)}),x.z=r.dateNF||z[14]):x={t:"n",v:eV(w)}),r.dense?(a[l]||(a[l]=[]),a[l][f]=x):a[rv({r:l,c:f})]=x,f+=u}}}}return a["!ref"]=rw(d),p.length&&(a["!merges"]=p),a}function i_(e,t){var r,a,n,s=t||{},i=null!=s.header?s.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',o=null!=s.footer?s.footer:"</body></html>",c=[i],l=rb(e["!ref"]);s.dense=Array.isArray(e),c.push((r=0,a=0,"<table"+((n=s)&&n.id?' id="'+n.id+'"':"")+">"));for(var f=l.s.r;f<=l.e.r;++f)c.push(function(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o=0,c=0,l=0;l<n.length;++l)if(!(n[l].s.r>r)&&!(n[l].s.c>i)&&!(n[l].e.r<r)&&!(n[l].e.c<i)){if(n[l].s.r<r||n[l].s.c<i){o=-1;break}o=n[l].e.r-n[l].s.r+1,c=n[l].e.c-n[l].s.c+1;break}if(!(o<0)){var f=rv({r:r,c:i}),h=a.dense?(e[r]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||tl(h.w||(rS(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),c>1&&(d.colspan=c),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(tA("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}(e,l,f,s));return c.push("</table>"+o),c.join("")}function iA(e,t,r){var a=r||{},n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?rg(a.origin):a.origin;n=i.r,s=i.c}var o=t.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,o.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=rb(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),-1==n&&(l.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,b=0,w=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<c;++p){var T=o[p];if(iO(T)){if(a.display)continue;d[m]={hidden:!0}}var E=T.children;for(g=v=0;g<E.length;++g){var S=E[g];if(!(a.display&&iO(S))){var y=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):tw(S.innerHTML),k=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var x=h[u];x.s.c==v+s&&x.s.r<m+n&&m+n<=x.e.r&&(v=x.e.c+1-s,u=-1)}w=+S.getAttribute("colspan")||1,((b=+S.getAttribute("rowspan")||1)>1||w>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(b||1)-1,c:v+s+(w||1)-1}});var _={t:"s",v:y},A=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=y&&(0==y.length?_.t=A||"z":a.raw||0==y.trim().length||"s"==A||("TRUE"===y?_={t:"b",v:!0}:"FALSE"===y?_={t:"b",v:!1}:isNaN(eV(y))?isNaN(eG(y).getDate())||(_={t:"d",v:eU(y)},a.cellDates||(_={t:"n",v:eR(_.v)}),_.z=a.dateNF||z[14]):_={t:"n",v:eV(y)})),void 0===_.z&&null!=k&&(_.z=k);var C="",O=S.getElementsByTagName("A");if(O&&O.length)for(var R=0;R<O.length&&(!O[R].hasAttribute("href")||"#"==(C=O[R].getAttribute("href")).charAt(0));++R);C&&"#"!=C.charAt(0)&&(_.l={Target:C}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=_):e[rv({c:v+s,r:m+n})]=_,l.e.c<v+s&&(l.e.c=v+s),v+=w}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+n),e["!ref"]=rw(l),m>=c&&(e["!fullref"]=rw((l.e.r=o.length-p+m-1+n,l))),e}function iC(e,t){return iA((t||{}).dense?[]:{},e,t)}function iO(e){var t,r="",a=(t=e).ownerDocument.defaultView&&"function"==typeof t.ownerDocument.defaultView.getComputedStyle?t.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null;return a&&(r=a(e).getPropertyValue("display")),r||(r=e.style&&e.style.display),"none"===r}var iR={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function iI(e,t){var r=t||{},a,n,s,i,o,c,l,f=tO(e),h=[],u={name:""},d="",p=0,m={},g=[],v=r.dense?[]:{},b={value:""},w="",T=0,E=[],S=-1,y=-1,k={s:{r:1e6,c:1e7},e:{r:0,c:0}},x=0,_={},A=[],C={},O=0,R=0,I=[],N=1,D=1,F=[],P={Names:[]},L={},M=["",""],U=[],B={},W="",H=0,V=!1,z=!1,G=0;for(tR.lastIndex=0,f=f.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");o=tR.exec(f);)switch(o[3]=o[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===o[1]?(k.e.c>=k.s.c&&k.e.r>=k.s.r?v["!ref"]=rw(k):v["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=k.e.r&&(v["!fullref"]=v["!ref"],k.e.r=r.sheetRows-1,v["!ref"]=rw(k)),A.length&&(v["!merges"]=A),I.length&&(v["!rows"]=I),s.name=s["名称"]||s.name,"undefined"!=typeof JSON&&JSON.stringify(s),g.push(s.name),m[s.name]=v,z=!1):"/"!==o[0].charAt(o[0].length-2)&&(s=e9(o[0],!1),S=y=-1,k.s.r=k.s.c=1e7,k.e.r=k.e.c=0,v=r.dense?[]:{},A=[],I=[],z=!0);break;case"table-row-group":"/"===o[1]?--x:++x;break;case"table-row":case"行":if("/"===o[1]){S+=N,N=1;break}if((i=e9(o[0],!1))["行号"]?S=i["行号"]-1:-1==S&&(S=0),(N=+i["number-rows-repeated"]||1)<10)for(G=0;G<N;++G)x>0&&(I[S+G]={level:x});y=-1;break;case"covered-table-cell":"/"!==o[1]&&++y,r.sheetStubs&&(r.dense?(v[S]||(v[S]=[]),v[S][y]={t:"z"}):v[rv({r:S,c:y})]={t:"z"}),w="",E=[];break;case"table-cell":case"数据":if("/"===o[0].charAt(o[0].length-2))++y,D=parseInt((b=e9(o[0],!1))["number-columns-repeated"]||"1",10),c={t:"z",v:null},b.formula&&!1!=r.cellFormula&&(c.f=sI(ta(b.formula))),"string"==(b["数据类型"]||b["value-type"])&&(c.t="s",c.v=ta(b["string-value"]||""),r.dense?(v[S]||(v[S]=[]),v[S][y]=c):v[rv({r:S,c:y})]=c),y+=D-1;else if("/"!==o[1]){w="",T=0,E=[],D=1;var j=N?S+N-1:S;if(++y>k.e.c&&(k.e.c=y),y<k.s.c&&(k.s.c=y),S<k.s.r&&(k.s.r=S),j>k.e.r&&(k.e.r=j),b=e9(o[0],!1),U=[],B={},c={t:b["数据类型"]||b["value-type"],v:null},r.cellFormula)if(b.formula&&(b.formula=ta(b.formula)),b["number-matrix-columns-spanned"]&&b["number-matrix-rows-spanned"]&&(C={s:{r:S,c:y},e:{r:S+(O=parseInt(b["number-matrix-rows-spanned"],10)||0)-1,c:y+(parseInt(b["number-matrix-columns-spanned"],10)||0)-1}},c.F=rw(C),F.push([C,c.F])),b.formula)c.f=sI(b.formula);else for(G=0;G<F.length;++G)S>=F[G][0].s.r&&S<=F[G][0].e.r&&y>=F[G][0].s.c&&y<=F[G][0].e.c&&(c.F=F[G][1]);switch((b["number-columns-spanned"]||b["number-rows-spanned"])&&(C={s:{r:S,c:y},e:{r:S+(O=parseInt(b["number-rows-spanned"],10)||0)-1,c:y+(parseInt(b["number-columns-spanned"],10)||0)-1}},A.push(C)),b["number-columns-repeated"]&&(D=parseInt(b["number-columns-repeated"],10)),c.t){case"boolean":c.t="b",c.v=th(b["boolean-value"]);break;case"float":case"percentage":case"currency":c.t="n",c.v=parseFloat(b.value);break;case"date":c.t="d",c.v=eU(b["date-value"]),r.cellDates||(c.t="n",c.v=eR(c.v)),c.z="m/d/yy";break;case"time":c.t="n",c.v=function(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[s],10)}return t}(b["time-value"])/86400,r.cellDates&&(c.t="d",c.v=eF(c.v)),c.z="HH:MM:SS";break;case"number":c.t="n",c.v=parseFloat(b["数据数值"]);break;default:if("string"!==c.t&&"text"!==c.t&&c.t)throw Error("Unsupported value type "+c.t);c.t="s",null!=b["string-value"]&&(w=ta(b["string-value"]),E=[])}}else{if(V=!1,"s"===c.t&&(c.v=w||"",E.length&&(c.R=E),V=0==T),L.Target&&(c.l=L),U.length>0&&(c.c=U,U=[]),w&&!1!==r.cellText&&(c.w=w),V&&(c.t="z",delete c.v),(!V||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=S))for(var X=0;X<N;++X){if(D=parseInt(b["number-columns-repeated"]||"1",10),r.dense)for(v[S+X]||(v[S+X]=[]),v[S+X][y]=0==X?c:eW(c);--D>0;)v[S+X][y+D]=eW(c);else for(v[rv({r:S+X,c:y})]=c;--D>0;)v[rv({r:S+X,c:y+D})]=eW(c);k.e.c<=y&&(k.e.c=y)}y+=(D=parseInt(b["number-columns-repeated"]||"1",10))-1,D=0,c={},w="",E=[]}L={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!0]);break;case"annotation":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a;B.t=w,E.length&&(B.R=E),B.a=W,U.push(B)}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);W="",H=0,w="",T=0,E=[];break;case"creator":"/"===o[1]?W=f.slice(H,o.index):H=o.index+o[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);w="",T=0,E=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===o[1]){if(_[u.name]=d,(a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&(d="",u=e9(o[0],!1),h.push([o[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(h[h.length-1][0]){case"time-style":case"date-style":n=e9(o[0],!1),d+=iR[o[3]][+("long"===n.style)]}break;case"text":if("/>"===o[0].slice(-2));else if("/"===o[1])switch(h[h.length-1][0]){case"number-style":case"date-style":case"time-style":d+=f.slice(p,o.index)}else p=o.index+o[0].length;break;case"named-range":M=sN((n=e9(o[0],!1))["cell-range-address"]);var Y={Name:n.name,Ref:M[0]+"!"+M[1]};z&&(Y.Sheet=g.length),P.Names.push(Y);break;case"p":case"文本串":if(["master-styles"].indexOf(h[h.length-1][0])>-1)break;if("/"!==o[1]||b&&b["string-value"])e9(o[0],!1),T=o.index+o[0].length;else{var K=[ta(f.slice(T,o.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];w=(w.length>0?w+"\n":"")+K[0]}break;case"database-range":if("/"===o[1])break;try{m[(M=sN(e9(o[0])["target-range-address"]))[0]]["!autofilter"]={ref:M[1]}}catch(e){}break;case"a":if("/"!==o[1]){if(!(L=e9(o[0],!1)).href)break;L.Target=ta(L.href),delete L.href,"#"==L.Target.charAt(0)&&L.Target.indexOf(".")>-1?(M=sN(L.Target.slice(1)),L.Target="#"+M[0]+"!"+M[1]):L.Target.match(/^\.\.[\\\/]/)&&(L.Target=L.Target.slice(3))}break;default:switch(o[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw Error(o)}}var J={Sheets:m,SheetNames:g,Workbook:P};return r.bookSheets&&delete J.Sheets,J}var iN=function(){var e="<office:document-styles "+t_({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+'><office:master-styles><style:master-page style:name="mp1" style:page-layout-name="mp1"><style:header/><style:header-left style:display="false"/><style:footer/><style:footer-left style:display="false"/></style:master-page></office:master-styles></office:document-styles>';return function(){return e4+e}}(),iD=function(){var e="          <table:table-cell />\n",t=function(t,r,a){var n=[];n.push('      <table:table table:name="'+ti(r.SheetNames[a])+'" table:style-name="ta1">\n');var s=0,i=0,o=rb(t["!ref"]||"A1"),c=t["!merges"]||[],l=0,f=Array.isArray(t);if(t["!cols"])for(i=0;i<=o.e.c;++i)n.push("        <table:table-column"+(t["!cols"][i]?' table:style-name="co'+t["!cols"][i].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(s=0;s<o.s.r;++s)h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+"></table:table-row>\n");for(;s<=o.e.r;++s){for(h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+">\n"),i=0;i<o.s.c;++i)n.push(e);for(;i<=o.e.c;++i){var d=!1,p={},m="";for(l=0;l!=c.length;++l)if(!(c[l].s.c>i)&&!(c[l].s.r>s)&&!(c[l].e.c<i)&&!(c[l].e.r<s)){(c[l].s.c!=i||c[l].s.r!=s)&&(d=!0),p["table:number-columns-spanned"]=c[l].e.c-c[l].s.c+1,p["table:number-rows-spanned"]=c[l].e.r-c[l].s.r+1;break}if(d){n.push("          <table:covered-table-cell/>\n");continue}var g=rv({r:s,c:i}),v=f?(t[s]||[])[i]:t[g];if(v&&v.f&&(p["table:formula"]=ti(("of:="+v.f.replace(st,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var b=rb(v.F);p["table:number-matrix-columns-spanned"]=b.e.c-b.s.c+1,p["table:number-matrix-rows-spanned"]=b.e.r-b.s.r+1}if(!v){n.push(e);continue}switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||eU(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=eU(v.v).toISOString(),p["table:style-name"]="ce1";break;default:n.push(e);continue}var w=ti(m).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var T=v.l.Target;"#"==(T="#"==T.charAt(0)?"#"+T.slice(1).replace(/\./,"!"):T).charAt(0)||T.match(/^\w+:/)||(T="../"+T),w=tA("text:a",w,{"xlink:href":T.replace(/&/g,"&amp;")})}n.push("          "+tA("table:table-cell",tA("text:p",w,{}),p)+"\n")}n.push("        </table:table-row>\n")}return n.push("      </table:table>\n"),n.join("")},r=function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!cols"]){for(var a=0;a<t["!cols"].length;++a)if(t["!cols"][a]){var n=t["!cols"][a];if(null==n.width&&null==n.wpx&&null==n.wch)continue;nL(n),n.ods=r;var s=t["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}});var a=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!rows"]){for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=a;var n=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n'),e.push("  </style:style>\n"),++a}}}),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,a){var n=[e4],s=t_({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),i=t_({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==a.bookType?(n.push("<office:document"+s+i+">\n"),n.push(r7().replace(/office:document-meta/g,"office:meta"))):n.push("<office:document-content"+s+">\n"),r(n,e),n.push("  <office:body>\n"),n.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)n.push(t(e.Sheets[e.SheetNames[o]],e,o,a));return n.push("    </office:spreadsheet>\n"),n.push("  </office:body>\n"),"fods"==a.bookType?n.push("</office:document>"):n.push("</office:document-content>"),n.join("")}}();function iF(e,t){if("fods"==t.bookType)return iD(e,t);var r=e1(),a="",n=[],s=[];return eQ(r,a="mimetype","application/vnd.oasis.opendocument.spreadsheet"),eQ(r,a="content.xml",iD(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),eQ(r,a="styles.xml",iN(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),eQ(r,a="meta.xml",e4+r7()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),eQ(r,a="manifest.rdf",function(e){var t=[e4];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(r8(e[r][0],e[r][1])),t.push(['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join(""));return t.push(r8("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(s)),n.push([a,"application/rdf+xml"]),eQ(r,a="META-INF/manifest.xml",function(e){var t=[e4];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}function iP(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function iL(e){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(e):tg(R(e))}function iM(e){var t=new Uint8Array(e.reduce(function(e,t){return e+t.length},0)),r=0;return e.forEach(function(e){t.set(e,r),r+=e.length}),t}function iU(e){return e-=e>>1&0x55555555,((e=(0x33333333&e)+(e>>2&0x33333333))+(e>>4)&0xf0f0f0f)*0x1010101>>>24}function iB(e,t){var r=t?t[0]:0,a=127&e[r];r:if(e[r++]>=128&&(a|=(127&e[r])<<7,e[r++]<128||(a|=(127&e[r])<<14,e[r++]<128)||(a|=(127&e[r])<<21,e[r++]<128)||(a+=(127&e[r])*0x10000000,++r,e[r++]<128)||(a+=(127&e[r])*0x800000000,++r,e[r++]<128)||(a+=(127&e[r])*0x40000000000,++r,e[r++]<128)))break r;return t&&(t[0]=r),a}function iW(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;a:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=0xfffffff)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=0x7ffffffff)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=0x3ffffffffff))break a;t[r-1]|=128,t[r]=e/0x1000000>>>21&127,++r}return t.slice(0,r)}function iH(e){var t=0,r=127&e[0];r:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128||(r|=(127&e[t])<<14,e[t++]<128)||(r|=(127&e[t])<<21,e[t++]<128))break r;r|=(127&e[t])<<28}return r}function iV(e){for(var t=[],r=[0];r[0]<e.length;){var a,n=r[0],s=iB(e,r),i=7&s,o=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var c=r[0];e[r[0]++]>=128;);a=e.slice(c,r[0]);break;case 5:o=4,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=iB(e,r),a=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==t[s]?t[s]=[l]:t[s].push(l)}return t}function iz(e){var t=[];return e.forEach(function(e,r){e.forEach(function(e){e.data&&(t.push(iW(8*r+e.type)),2==e.type&&t.push(iW(e.data.length)),t.push(e.data))})}),iM(t)}function iG(e,t){return(null==e?void 0:e.map(function(e){return t(e.data)}))||[]}function ij(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=iB(e,a),s=iV(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:iH(s[1][0].data),messages:[]};s[2].forEach(function(t){var r=iV(t.data),n=iH(r[3][0].data);i.messages.push({meta:r,data:e.slice(a[0],a[0]+n)}),a[0]+=n}),(null==(t=s[3])?void 0:t[0])&&(i.merge=iH(s[3][0].data)>>>0>0),r.push(i)}return r}function i$(e){var t=[];return e.forEach(function(e){var r=[];r[1]=[{data:iW(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:iW(+!!e.merge),type:0}]);var a=[];e.messages.forEach(function(e){a.push(e.data),e.meta[3]=[{type:0,data:iW(e.data.length)}],r[2].push({data:iz(e.meta),type:2})});var n=iz(r);t.push(iW(n.length)),t.push(n),a.forEach(function(e){return t.push(e)})}),iM(t)}function iX(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(function(e,t){if(0!=e)throw Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=iB(t,r),n=[];r[0]<t.length;){var s=3&t[r[0]];if(0==s){var i=t[r[0]++]>>2;if(i<60)++i;else{var o=i-59;i=t[r[0]],o>1&&(i|=t[r[0]+1]<<8),o>2&&(i|=t[r[0]+2]<<16),o>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=o}n.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}var c=0,l=0;if(1==s?(l=(t[r[0]]>>2&7)+4,c=(224&t[r[0]++])<<3|t[r[0]++]):(l=(t[r[0]++]>>2)+1,2==s?(c=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(c=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[iM(n)],0==c)throw Error("Invalid offset 0");if(c>n[0].length)throw Error("Invalid offset beyond length");if(l>=c)for(n.push(n[0].slice(-c)),l-=c;l>=n[n.length-1].length;)n.push(n[n.length-1]),l-=n[n.length-1].length;n.push(n[0].slice(-c,-c+l))}var f=iM(n);if(f.length!=a)throw Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw Error("data is not a valid framed stream!");return iM(t)}function iY(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,0xfffffff),n=new Uint8Array(4);t.push(n);var s=iW(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=0x1000000?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=0x100000000&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return iM(t)}function iK(e,t){var r=new Uint8Array(32),a=iP(r),n=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var a=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=255&n;e[t+15]|=r>=0?0:128}(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,+!!e.v,!0),s|=2,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r.slice(0,n)}function iJ(e,t){var r=new Uint8Array(32),a=iP(r),n=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,+!!e.v,!0),s|=32,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),r.slice(0,n)}function iq(e){return iB(iV(e)[1][0].data)}function iZ(e,t){var r=iV(t.data),a=iH(r[1][0].data),n=r[3],s=[];return(n||[]).forEach(function(t){var r=iV(t.data),n=iH(r[1][0].data)>>>0;switch(a){case 1:s[n]=iL(r[3][0].data);break;case 8:var i=iV(e[iq(r[9][0].data)][0].data),o=e[iq(i[1][0].data)][0],c=iH(o.meta[1][0].data);if(2001!=c)throw Error("2000 unexpected reference to ".concat(c));var l=iV(o.data);s[n]=l[3].map(function(e){return iL(e.data)}).join("")}}),s}function iQ(e){var t,r,a,n,s={},i=[];if(e.FullPaths.forEach(function(e){if(e.match(/\.iwpv2/))throw Error("Unsupported password protection")}),e.FileIndex.forEach(function(e){var t,r;if(e.name.match(/\.iwa$/)){try{t=iX(e.content)}catch(t){return console.log("?? "+e.content.length+" "+(t.message||t))}try{r=ij(t)}catch(e){return console.log("## "+(e.message||e))}r.forEach(function(e){s[e.id]=e.messages,i.push(e.id)})}}),!i.length)throw Error("File has no messages");var o=(null==(n=null==(a=null==(r=null==(t=null==s?void 0:s[1])?void 0:t[0])?void 0:r.meta)?void 0:a[1])?void 0:n[0].data)&&1==iH(s[1][0].meta[1][0].data)&&s[1][0];if(o||i.forEach(function(e){s[e].forEach(function(e){if(1==iH(e.meta[1][0].data)>>>0)if(o)throw Error("Document has multiple roots");else o=e})}),!o)throw Error("Cannot find Document root");var c=o,l=oi();if(iG(iV(c.data)[1],iq).forEach(function(e){s[e].forEach(function(e){if(2==iH(e.meta[1][0].data)){var t,r,a,n=(a={name:(null==(t=(r=iV(e.data))[1])?void 0:t[0])?iL(r[1][0].data):"",sheets:[]},iG(r[2],iq).forEach(function(e){s[e].forEach(function(e){6e3==iH(e.meta[1][0].data)&&a.sheets.push(function(e,t){var r=iV(t.data),a={"!ref":"A1"},n=e[iq(r[2][0].data)],s=iH(n[0].meta[1][0].data);if(6001!=s)throw Error("6000 unexpected reference to ".concat(s));return!function(e,t,r){var a,n=iV(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(iH(n[6][0].data)>>>0)-1,s.e.r<0)throw Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(iH(n[7][0].data)>>>0)-1,s.e.c<0)throw Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=rw(s);var i=iV(n[4][0].data),o=iZ(e,e[iq(i[4][0].data)][0]),c=(null==(a=i[17])?void 0:a[0])?iZ(e,e[iq(i[17][0].data)][0]):[],l=iV(i[3][0].data),f=0;l[1].forEach(function(t){var a,n,s,i,l=e[iq(iV(t.data)[2][0].data)][0],h=iH(l.meta[1][0].data);if(6002!=h)throw Error("6001 unexpected reference to ".concat(h));var u=(s=(null==(a=null==(n=iV(l.data))?void 0:n[7])?void 0:a[0])?+(iH(n[7][0].data)>>>0>0):-1,i=iG(n[5],function(e){return function(e,t){var r,a,n,s,i,o,c,l,f,h,u,d,p,m,g,v,b=iV(e),w=iH(b[1][0].data)>>>0,T=iH(b[2][0].data)>>>0,E=(null==(a=null==(r=b[8])?void 0:r[0])?void 0:a.data)&&iH(b[8][0].data)>0||!1;if((null==(s=null==(n=b[7])?void 0:n[0])?void 0:s.data)&&0!=t)g=null==(o=null==(i=b[7])?void 0:i[0])?void 0:o.data,v=null==(l=null==(c=b[6])?void 0:c[0])?void 0:l.data;else if((null==(h=null==(f=b[4])?void 0:f[0])?void 0:h.data)&&1!=t)g=null==(d=null==(u=b[4])?void 0:u[0])?void 0:d.data,v=null==(m=null==(p=b[3])?void 0:p[0])?void 0:m.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var S=E?4:1,y=iP(g),k=[],x=0;x<g.length/2;++x){var _=y.getUint16(2*x,!0);_<65535&&k.push([x,_])}if(k.length!=T)throw"Expected ".concat(T," cells, found ").concat(k.length);var A=[];for(x=0;x<k.length-1;++x)A[k[x][0]]=v.subarray(k[x][1]*S,k[x+1][1]*S);return k.length>=1&&(A[k[k.length-1][0]]=v.subarray(k[k.length-1][1]*S)),{R:w,cells:A}}(e,s)}),{nrows:iH(n[4][0].data)>>>0,data:i.reduce(function(e,t){return e[t.R]||(e[t.R]=[]),t.cells.forEach(function(r,a){if(e[t.R][a])throw Error("Duplicate cell r=".concat(t.R," c=").concat(a));e[t.R][a]=r}),e},[])});u.data.forEach(function(e,t){e.forEach(function(e,a){var n=rv({r:f+t,c:a}),s=function(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return function(e,t,r,a){var n,s=iP(e),i=s.getUint32(4,!0),o=(a>1?12:8)+4*iU(i&(a>1?3470:398)),c=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(c=s.getUint32(o,!0),o+=4),o+=4*iU(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(o,!0),o+=4),32&i&&(f=s.getFloat64(o,!0),o+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(o,!0)),o+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:t[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(c>-1)n={t:"s",v:r[c]};else if(l>-1)n={t:"s",v:t[l]};else if(isNaN(f))throw Error("Unsupported cell type ".concat(e.slice(0,4)));else n={t:"n",v:f};break;default:throw Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}(e,t,r,e[0]);case 5:return function(e,t,r){var a,n=iP(e),s=n.getUint32(8,!0),i=12,o=-1,c=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=function(e,t){for(var r=(127&e[t+15])<<7|e[t+14]>>1,a=1&e[t+14],n=t+13;n>=t;--n)a=256*a+e[n];return(128&e[t+15]?-a:a)*Math.pow(10,r-6176)}(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(c=n.getUint32(i,!0),i+=4),16&s&&(o=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:case 10:a={t:"n",v:l};break;case 3:a={t:"s",v:t[c]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(o>-1)a={t:"s",v:r[o]};else throw Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));break;default:throw Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}(e,t,r);default:throw Error("Unsupported payload version ".concat(e[0]))}}(e,o,c);s&&(r[n]=s)})}),f+=u.nrows})}(e,n[0],a),a}(s,e))})}),a);n.sheets.forEach(function(e,t){oo(l,e,0==t?n.name:n.name+"_"+t,!0)})}})}),0==l.SheetNames.length)throw Error("Empty NUMBERS file");return l}function i1(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];void 0===t[a[0]]&&(t[a[0]]=a[1]),"n"===a[2]&&(t[a[0]]=Number(t[a[0]]))}}}function i0(e){i1([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function i2(e){i1([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function i4(e){return"/"==e.charAt(0)?e.slice(1):e}function i3(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=y(e.slice(0,12));break;case"binary":r=e;break;default:throw Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function i5(e,t){var r=0;n:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return id(e.slice(r),t);default:break n}return ns.to_workbook(e,t)}function i6(e,t,r,a){return a?(r.type="string",ns.to_workbook(e,r)):ns.to_workbook(t,r)}function i8(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return ek(t.file,ey.write(e,{type:k?"buffer":""}));case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");default:throw Error("Unrecognized type "+t.type)}return ey.write(e,t)}function i7(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return S(tv(a));case"binary":return tv(a);case"string":return e;case"file":return ek(t.file,a,"utf8");case"buffer":if(k)return x(a,"utf8");if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(a);return i7(a,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}function i9(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return"base64"==t.type?S(r):"string"==t.type?tg(r):r;case"file":return ek(t.file,e);case"buffer":return e;default:throw Error("Unrecognized type "+t.type)}}function oe(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,o="",c={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":c=rT(f);break;case"number":(c=rT(e["!ref"])).s.r=f;break;default:c=f}a>0&&(n=0);var h=rd(c.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=c.s.r,b=0,w={};g&&!e[v]&&(e[v]=[]);var T=l.skipHidden&&e["!cols"]||[],E=l.skipHidden&&e["!rows"]||[];for(b=c.s.c;b<=c.e.c;++b)if(!(T[b]||{}).hidden)switch(u[b]=rm(b),r=g?e[v][b]:e[u[b]+h],a){case 1:s[b]=b-c.s.c;break;case 2:s[b]=u[b];break;case 3:s[b]=l.header[b-c.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=i=rS(r,null,l),m=w[i]||0){do o=i+"_"+m++;while(w[o]);w[i]=m,w[o]=1}else w[i]=1;s[b]=o}for(v=c.s.r+n;v<=c.e.r;++v)if(!(E[v]||{}).hidden){var S=function(e,t,r,a,n,s,i,o){var c=rd(r),l=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[a[d]+c];if(void 0===p||void 0===p.t){if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l);continue}var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:rS(p,m,o);null!=m&&(h=!1)}}return{row:u,isempty:h}}(e,c,v,u,a,s,g,l);(!1===S.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=S.row)}return d.length=p,d}var ot=/"/g;function or(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";var n=rT(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",c=o.charCodeAt(0),l=RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=rm(p));for(var m=0,g=n.s.r;g<=n.e.r;++g)!(d[g]||{}).hidden&&null!=(f=function(e,t,r,a,n,s,i,o){for(var c=!0,l=[],f="",h=rd(r),u=t.s.c;u<=t.e.c;++u)if(a[u]){var d=o.dense?(e[r]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){c=!1,f=""+(o.rawNumbers&&"n"==d.t?d.v:rS(d,null,o));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||o.forceQuotes){f='"'+f.replace(ot,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(c=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(ot,'""')+'"'));l.push(f)}return!1===o.blankrows&&c?null:l.join(i)}(e,n,g,h,i,c,s,a))&&(a.strip&&(f=f.replace(l,"")),(f||!1!==a.blankrows)&&r.push((m++?o:"")+f));return delete a.dense,r.join("")}function oa(e,t){t||(t={}),t.FS="	",t.RS="\n";var r=or(e,t);if(void 0===n||"string"==t.type)return r;var a=n.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function on(e,t,r){var a,n=r||{},s=+!n.skipHeader,i=e||{},o=0,c=0;if(i&&null!=n.origin)if("number"==typeof n.origin)o=n.origin;else{var l="string"==typeof n.origin?rg(n.origin):n.origin;o=l.r,c=l.c}var f={s:{c:0,r:0},e:{c:c,r:o+t.length-1+s}};if(i["!ref"]){var h=rT(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+t.length-1+s)}else -1==o&&(o=0,f.e.r=t.length-1+s);var u=n.header||[],d=0;t.forEach(function(e,t){ex(e).forEach(function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var l=e[r],f="z",h="",p=rv({c:c+d,r:o+t+s});a=os(i,p),!l||"object"!=typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",n.cellDates||(f="n",l=eR(l)),h=n.dateNF||z[14]):null===l&&n.nullError&&(f="e",l=0),a?(a.t=f,a.v=l,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:l},h&&(a.z=h)):i[p]=l})}),f.e.c=Math.max(f.e.c,c+u.length-1);var p=rd(o);if(s)for(d=0;d<u.length;++d)i[rm(d+c)+p]={t:"s",v:u[d]};return i["!ref"]=rw(f),i}function os(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var a=rg(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return"number"!=typeof t?os(e,rv(t)):os(e,rv({r:t,c:r||0}))}function oi(){return{SheetNames:[],Sheets:{}}}function oo(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(ia(r),e.SheetNames.indexOf(r)>=0)throw Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function oc(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var ol={encode_col:rm,encode_row:rd,encode_cell:rv,encode_range:rw,decode_col:rp,decode_row:ru,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:rg,decode_range:rb,format_cell:rS,sheet_add_aoa:rk,sheet_add_json:on,sheet_add_dom:iA,aoa_to_sheet:rx,json_to_sheet:function(e,t){return on(null,e,t)},table_to_sheet:iC,table_to_book:function(e,t){return ry(iC(e,t),t)},sheet_to_csv:or,sheet_to_txt:oa,sheet_to_json:oe,sheet_to_html:i_,sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=rT(e["!ref"]),i="",o=[],c=[],l=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)o[n]=rm(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=rd(f),n=s.s.c;n<=s.e.c;++n)if(r=o[n]+i,t=l?(e[f]||[])[n]:e[r],a="",void 0!==t){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else if("z"==t.t)continue;else if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}c[c.length]=r+"="+a}return c},sheet_to_row_object_array:oe,sheet_get_cell:os,book_new:oi,book_append_sheet:oo,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw Error("Cannot find sheet name |"+t+"|")}throw Error("Cannot find sheet |"+t+"|")}(e,t);switch(!e.Workbook.Sheets[a]&&(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:oc,cell_set_internal_link:function(e,t,r){return oc(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:rT(t),s="string"==typeof t?t:rw(t),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var c=os(e,i,o);c.t="n",c.F=s,delete c.v,i==n.s.r&&o==n.s.c&&(c.f=r,a&&(c.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};c.version}};
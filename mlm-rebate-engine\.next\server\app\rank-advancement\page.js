(()=>{var e={};e.id=6242,e.ids=[6242],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11483:(e,s,r)=>{Promise.resolve().then(r.bind(r,54056))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19766:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(65239),n=r(48088),i=r(88170),a=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o={children:["",{children:["rank-advancement",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80606)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rank-advancement\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rank-advancement\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/rank-advancement/page",pathname:"/rank-advancement",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54056:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687),n=r(43210),i=r(82136),a=r(16189),l=r(68367),d=r(23877);let o=({ranks:e=[],currentRankId:s})=>{let[r,i]=(0,n.useState)(s||null),a=e=>{i(r===e?null:e)},l=e=>{switch(e?.toLowerCase()){case"starter":default:return"bg-gray-100 text-gray-800 border-gray-300";case"bronze":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"silver":return"bg-gray-200 text-gray-800 border-gray-400";case"gold":return"bg-yellow-200 text-yellow-800 border-yellow-400";case"platinum":return"bg-blue-100 text-blue-800 border-blue-300";case"diamond":return"bg-purple-100 text-purple-800 border-purple-300"}},o=[{id:1,name:"Starter",level:1,commissionRate:5,overrideBonus:0,leadershipBonus:0,benefits:[{icon:(0,t.jsx)(d.gdQ,{}),title:"5% Commission",description:"Earn 5% commission on personal sales"},{icon:(0,t.jsx)(d.YXz,{}),title:"Binary Structure",description:"Start building your downline with a binary structure"}]},{id:2,name:"Bronze",level:2,commissionRate:8,overrideBonus:1,leadershipBonus:0,benefits:[{icon:(0,t.jsx)(d.gdQ,{}),title:"8% Commission",description:"Earn 8% commission on personal sales"},{icon:(0,t.jsx)(d.MxO,{}),title:"1% Override Bonus",description:"Earn 1% override bonus on your direct downline's sales"},{icon:(0,t.jsx)(d.YXz,{}),title:"Team Building Tools",description:"Access to basic team building tools and resources"}]},{id:3,name:"Silver",level:3,commissionRate:10,overrideBonus:2,leadershipBonus:.5,benefits:[{icon:(0,t.jsx)(d.gdQ,{}),title:"10% Commission",description:"Earn 10% commission on personal sales"},{icon:(0,t.jsx)(d.MxO,{}),title:"2% Override Bonus",description:"Earn 2% override bonus on your direct downline's sales"},{icon:(0,t.jsx)(d.Z0L,{}),title:"0.5% Leadership Bonus",description:"Earn 0.5% leadership bonus on your entire organization's sales"},{icon:(0,t.jsx)(d.Wp,{}),title:"Silver Welcome Kit",description:"Receive a Silver rank welcome kit with exclusive products"}]},{id:4,name:"Gold",level:4,commissionRate:12,overrideBonus:3,leadershipBonus:1,benefits:[{icon:(0,t.jsx)(d.gdQ,{}),title:"12% Commission",description:"Earn 12% commission on personal sales"},{icon:(0,t.jsx)(d.MxO,{}),title:"3% Override Bonus",description:"Earn 3% override bonus on your direct downline's sales"},{icon:(0,t.jsx)(d.Z0L,{}),title:"1% Leadership Bonus",description:"Earn 1% leadership bonus on your entire organization's sales"},{icon:(0,t.jsx)(d.Wp,{}),title:"Gold Welcome Kit",description:"Receive a Gold rank welcome kit with exclusive products"},{icon:(0,t.jsx)(d.SBv,{}),title:"Recognition",description:"Recognition at regional events and online platforms"}]},{id:5,name:"Platinum",level:5,commissionRate:15,overrideBonus:4,leadershipBonus:1.5,benefits:[{icon:(0,t.jsx)(d.gdQ,{}),title:"15% Commission",description:"Earn 15% commission on personal sales"},{icon:(0,t.jsx)(d.MxO,{}),title:"4% Override Bonus",description:"Earn 4% override bonus on your direct downline's sales"},{icon:(0,t.jsx)(d.Z0L,{}),title:"1.5% Leadership Bonus",description:"Earn 1.5% leadership bonus on your entire organization's sales"},{icon:(0,t.jsx)(d.Wp,{}),title:"Platinum Welcome Kit",description:"Receive a Platinum rank welcome kit with exclusive products"},{icon:(0,t.jsx)(d.SBv,{}),title:"VIP Recognition",description:"VIP recognition at national events and online platforms"},{icon:(0,t.jsx)(d.YXz,{}),title:"Leadership Training",description:"Access to exclusive leadership training and development programs"}]},{id:6,name:"Diamond",level:6,commissionRate:20,overrideBonus:5,leadershipBonus:2,benefits:[{icon:(0,t.jsx)(d.gdQ,{}),title:"20% Commission",description:"Earn 20% commission on personal sales"},{icon:(0,t.jsx)(d.MxO,{}),title:"5% Override Bonus",description:"Earn 5% override bonus on your direct downline's sales"},{icon:(0,t.jsx)(d.Z0L,{}),title:"2% Leadership Bonus",description:"Earn 2% leadership bonus on your entire organization's sales"},{icon:(0,t.jsx)(d.Wp,{}),title:"Diamond Welcome Kit",description:"Receive a Diamond rank welcome kit with exclusive products and luxury items"},{icon:(0,t.jsx)(d.SBv,{}),title:"Elite Recognition",description:"Elite recognition at international events and online platforms"},{icon:(0,t.jsx)(d.YXz,{}),title:"Executive Training",description:"Access to exclusive executive training and development programs"},{icon:(0,t.jsx)(d.MxO,{}),title:"Annual Bonus Pool",description:"Participate in the annual Diamond bonus pool"}]}],c=e.length>0?e:o;return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,t.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-b",children:(0,t.jsxs)("h3",{className:"font-medium text-gray-700 flex items-center",children:[(0,t.jsx)(d.SBv,{className:"mr-2 text-yellow-500"})," Rank Benefits"]})}),(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:c.map(e=>(0,t.jsxs)("div",{className:"overflow-hidden",children:[(0,t.jsxs)("button",{onClick:()=>a(e.id),className:`w-full px-4 py-3 flex justify-between items-center text-left transition-colors ${e.id===s?"bg-blue-50":"hover:bg-gray-50"}`,children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${l(e.name)}`,children:e.level}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Level ",e.level]})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[e.id===s&&(0,t.jsx)("span",{className:"mr-3 text-xs bg-green-100 text-green-800 px-2 py-1 rounded",children:"Current"}),r===e.id?(0,t.jsx)(d.Ucs,{className:"text-gray-400"}):(0,t.jsx)(d.Vr3,{className:"text-gray-400"})]})]}),r===e.id&&(0,t.jsxs)("div",{className:"px-4 py-3 bg-gray-50",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"bg-white p-3 rounded border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center text-blue-600 mb-1",children:[(0,t.jsx)(d.gdQ,{className:"mr-1"}),(0,t.jsx)("span",{className:"font-medium",children:"Commission Rate"})]}),(0,t.jsxs)("p",{className:"text-2xl font-bold",children:[e.commissionRate,"%"]})]}),(0,t.jsxs)("div",{className:"bg-white p-3 rounded border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center text-green-600 mb-1",children:[(0,t.jsx)(d.MxO,{className:"mr-1"}),(0,t.jsx)("span",{className:"font-medium",children:"Override Bonus"})]}),(0,t.jsxs)("p",{className:"text-2xl font-bold",children:[e.overrideBonus,"%"]})]}),(0,t.jsxs)("div",{className:"bg-white p-3 rounded border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center text-purple-600 mb-1",children:[(0,t.jsx)(d.Z0L,{className:"mr-1"}),(0,t.jsx)("span",{className:"font-medium",children:"Leadership Bonus"})]}),(0,t.jsxs)("p",{className:"text-2xl font-bold",children:[e.leadershipBonus,"%"]})]})]}),(0,t.jsx)("h5",{className:"font-medium text-gray-700 mb-2",children:"Additional Benefits"}),(0,t.jsx)("ul",{className:"space-y-2",children:e.benefits.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"mt-1 mr-2 text-blue-500",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-gray-800",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},s))})]})]},e.id))})]})};function c(){let{data:e,status:s}=(0,i.useSession)();(0,a.useRouter)();let[r,c]=(0,n.useState)(!0),[m,x]=(0,n.useState)(null),[u,p]=(0,n.useState)([]),[h,g]=(0,n.useState)(!1),[j,v]=(0,n.useState)(null),[b,f]=(0,n.useState)(!1),y=async()=>{c(!0);try{let e=await fetch("/api/users/rank-advancement");if(!e.ok)throw Error("Failed to fetch rank advancement eligibility");let s=await e.json();x(s)}catch(e){console.error("Error fetching rank advancement eligibility:",e)}finally{c(!1)}},N=async()=>{try{let e=await fetch("/api/users/rank-advancement/history");if(!e.ok)throw Error("Failed to fetch rank advancement history");let s=await e.json();p(s.rankAdvancements||[])}catch(e){console.error("Error fetching rank advancement history:",e)}},w=async()=>{if(m?.eligible){g(!0);try{let e=await fetch("/api/users/rank-advancement",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to process rank advancement");let s=await e.json();v(s),s.success&&(await y(),await N())}catch(e){console.error("Error processing rank advancement:",e),v({success:!1,message:"An error occurred while processing your rank advancement."})}finally{g(!1)}}},k=e=>{switch(e?.toLowerCase()){case"starter":default:return"bg-gray-100 text-gray-800";case"bronze":return"bg-yellow-100 text-yellow-800";case"silver":return"bg-gray-200 text-gray-800";case"gold":return"bg-yellow-200 text-yellow-800";case"platinum":return"bg-blue-100 text-blue-800";case"diamond":return"bg-purple-100 text-purple-800"}},q=e=>e>=100?"bg-green-500":e>=75?"bg-blue-500":e>=50?"bg-yellow-500":"bg-red-500";return"loading"===s||r?(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,t.jsx)("div",{className:"text-xl",children:"Loading..."})]})}):(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-semibold mb-6 flex items-center",children:[(0,t.jsx)(d.SBv,{className:"mr-2 text-yellow-500"})," Rank Advancement"]}),m&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,t.jsx)(d.tz0,{className:"mr-2 text-blue-500"})," Your Current Rank"]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`px-4 py-2 rounded-full text-lg font-medium ${k(m.currentRank.name)}`,children:m.currentRank.name}),m.nextRank&&(0,t.jsxs)("div",{className:"flex items-center ml-4",children:[(0,t.jsx)(d.uCC,{className:"text-gray-400 mx-2"}),(0,t.jsxs)("div",{className:"text-gray-500",children:["Next: ",m.nextRank.name]})]})]})]}),m&&m.nextRank&&m.requirements&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,t.jsx)(d.YYR,{className:"mr-2 text-green-500"})," Requirements for ",m.nextRank.name]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.AsH,{className:"text-blue-500 mr-2"}),(0,t.jsx)("h3",{className:"font-medium",children:"Personal Sales"})]}),m.requirements.personalSales.qualified?(0,t.jsx)(d.CMH,{className:"text-green-500"}):(0,t.jsx)(d.QCr,{className:"text-red-500"})]}),(0,t.jsxs)("div",{className:"mb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:["₱",m.requirements.personalSales.actual.toLocaleString()," / ₱",m.requirements.personalSales.required.toLocaleString()]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:`h-2.5 rounded-full ${q(m.requirements.personalSales.actual/m.requirements.personalSales.required*100)}`,style:{width:`${Math.min(m.requirements.personalSales.actual/m.requirements.personalSales.required*100,100)}%`}})})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.AsH,{className:"text-purple-500 mr-2"}),(0,t.jsx)("h3",{className:"font-medium",children:"Group Sales"})]}),m.requirements.groupSales.qualified?(0,t.jsx)(d.CMH,{className:"text-green-500"}):(0,t.jsx)(d.QCr,{className:"text-red-500"})]}),(0,t.jsxs)("div",{className:"mb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:["₱",m.requirements.groupSales.actual.toLocaleString()," / ₱",m.requirements.groupSales.required.toLocaleString()]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:`h-2.5 rounded-full ${q(m.requirements.groupSales.actual/m.requirements.groupSales.required*100)}`,style:{width:`${Math.min(m.requirements.groupSales.actual/m.requirements.groupSales.required*100,100)}%`}})})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.YXz,{className:"text-green-500 mr-2"}),(0,t.jsx)("h3",{className:"font-medium",children:"Direct Downline"})]}),m.requirements.directDownline.qualified?(0,t.jsx)(d.CMH,{className:"text-green-500"}):(0,t.jsx)(d.QCr,{className:"text-red-500"})]}),(0,t.jsxs)("div",{className:"mb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[m.requirements.directDownline.actual," /",m.requirements.directDownline.required]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:`h-2.5 rounded-full ${q(m.requirements.directDownline.actual/m.requirements.directDownline.required*100)}`,style:{width:`${Math.min(m.requirements.directDownline.actual/m.requirements.directDownline.required*100,100)}%`}})})]})]}),m.requirements.qualifiedDownline.required>0&&(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.YXz,{className:"text-yellow-500 mr-2"}),(0,t.jsx)("h3",{className:"font-medium",children:"Qualified Downline"})]}),m.requirements.qualifiedDownline.qualified?(0,t.jsx)(d.CMH,{className:"text-green-500"}):(0,t.jsx)(d.QCr,{className:"text-red-500"})]}),(0,t.jsxs)("div",{className:"mb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[m.requirements.qualifiedDownline.actual," /",m.requirements.qualifiedDownline.required]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:`h-2.5 rounded-full ${q(m.requirements.qualifiedDownline.actual/m.requirements.qualifiedDownline.required*100)}`,style:{width:`${Math.min(m.requirements.qualifiedDownline.actual/m.requirements.qualifiedDownline.required*100,100)}%`}})})]})]})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 rounded-lg border border-gray-200",children:[(0,t.jsx)("div",{className:"flex items-center",children:m.eligible?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"p-2 rounded-full bg-green-100 text-green-500 mr-3",children:(0,t.jsx)(d.CMH,{})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-green-600",children:"You are eligible for advancement!"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["You have met all the requirements to advance to ",m.nextRank.name,"."]})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"p-2 rounded-full bg-yellow-100 text-yellow-500 mr-3",children:(0,t.jsx)(d.uCC,{})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-yellow-600",children:"Keep working towards your next rank!"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Continue building your network and increasing your sales to reach ",m.nextRank.name,"."]})]})]})}),m.eligible&&(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("button",{onClick:w,disabled:h,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.hW,{className:"animate-spin mr-2"})," Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.uCC,{className:"mr-2"})," Advance to ",m.nextRank.name]})})})]}),j&&(0,t.jsx)("div",{className:`mt-4 p-4 rounded-lg ${j.success?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"}`,children:(0,t.jsxs)("div",{className:"flex items-center",children:[j.success?(0,t.jsx)(d.CMH,{className:"text-green-500 mr-2"}):(0,t.jsx)(d.QCr,{className:"text-red-500 mr-2"}),(0,t.jsx)("p",{className:j.success?"text-green-700":"text-red-700",children:j.message})]})})]}),m&&!m.nextRank&&(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 rounded-full bg-purple-100 text-purple-500 mr-4",children:(0,t.jsx)(d.SBv,{className:"h-6 w-6"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"Congratulations!"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["You have reached the highest rank in our program: ",(0,t.jsx)("span",{className:"font-medium",children:m.currentRank.name}),"."]})]})]})}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(d.__w,{className:"mr-2 text-blue-500"})," Rank Benefits"]}),(0,t.jsx)("div",{className:"ml-2 text-sm text-gray-500",children:"Learn about the benefits of each rank"})]}),(0,t.jsx)(o,{ranks:[],currentRankId:m?.currentRank?.id})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold flex items-center",children:[(0,t.jsx)(d.OKX,{className:"mr-2 text-blue-500"})," Rank Advancement History"]}),(0,t.jsx)("button",{onClick:()=>f(!b),className:"text-blue-500 hover:text-blue-700",children:b?"Hide":"Show"})]}),b&&(0,t.jsx)("div",{className:"p-6",children:u.length>0?(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"From"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"To"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Personal Sales"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Group Sales"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Direct Downline"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${k(e.previousRank.name)}`,children:e.previousRank.name})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${k(e.newRank.name)}`,children:e.newRank.name})}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.personalSales.toLocaleString()]}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["₱",e.groupSales.toLocaleString()]}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.directDownlineCount})]},e.id))})]})}):(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No rank advancement history found."})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80606:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\rank-advancement\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\rank-advancement\\page.tsx","default")},97411:(e,s,r)=>{Promise.resolve().then(r.bind(r,80606))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>r(19766));module.exports=t})();
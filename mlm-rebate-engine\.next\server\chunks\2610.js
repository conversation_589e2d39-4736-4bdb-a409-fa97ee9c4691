exports.id=2610,exports.ids=[2610],exports.modules={12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12909:(e,t,r)=>{"use strict";r.d(t,{Er:()=>s,Nh:()=>d,aP:()=>u});var n=r(96330),o=r(13581),a=r(85663),i=r(55511),l=r.n(i);async function s(e){return await a.Ay.hash(e,10)}function u(){let e=l().randomBytes(32).toString("hex"),t=new Date;return t.setHours(t.getHours()+1),{token:e,expiresAt:t}}new n.PrismaClient;let d={debug:!0,logger:{error:(e,t)=>{console.error(`NextAuth error: ${e}`,t)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,t)=>{console.log(`NextAuth debug: ${e}`,t)}},providers:[(0,o.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,t){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let t=new n.PrismaClient,r=await t.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await t.$disconnect(),!r)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",r.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let o=await a.Ay.compare(e.password,r.password);if(console.log("Password valid:",o),!o)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",r.id);let{password:i,...l}=r;return{id:r.id.toString(),email:r.email,name:r.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:t})=>(console.log("Session callback called",{session:e,token:t}),t&&e.user&&(e.user.id=t.sub),e),jwt:async({token:e,user:t})=>(console.log("JWT callback called",{token:e,user:t}),t&&(e.sub=t.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var o=r(12269);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var l=o?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(n,a,l):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(35426));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var n=r(96330);let o=global.prisma||new n.PrismaClient({log:["query"]})},47697:(e,t,r)=>{"use strict";r.d(t,{Jr:()=>u,Qh:()=>f,Qx:()=>m,W$:()=>i,aL:()=>c,o8:()=>o,pv:()=>l});var n=r(31183);async function o(e,t=6){let r=await n.z.user.findUnique({where:{id:e},include:{rank:!0}});if(!r)throw Error(`User with ID ${e} not found`);return await a(r,0,t,null)}async function a(e,t,r,o){if(t>r)return{user:e,level:t,position:o,left:null,right:null};let i={user:e,level:t,position:o,left:null,right:null};if(null!==e.leftLegId){let o=await n.z.user.findUnique({where:{id:e.leftLegId},include:{rank:!0}});o&&(i.left=await a(o,t+1,r,"left"))}if(null!==e.rightLegId){let o=await n.z.user.findUnique({where:{id:e.rightLegId},include:{rank:!0}});o&&(i.right=await a(o,t+1,r,"right"))}return i}async function i(e,t=6){let r=await o(e,t),n=[];return!function e(t){n.push({user:t.user,level:t.level,position:t.position}),t.left&&e(t.left),t.right&&e(t.right)}(r),n}async function l(e,t,r){let o=await n.z.user.findUnique({where:{id:e},select:{leftLegId:!0,rightLegId:!0}});if(!o)throw Error(`User with ID ${e} not found`);let a=0,i=0;return null!==o.leftLegId&&(a=await s(o.leftLegId,t,r)),null!==o.rightLegId&&(i=await s(o.rightLegId,t,r)),{leftLegPV:a,rightLegPV:i,totalPV:a+i}}async function s(e,t,r){let o=await i(e),a=[e,...o.map(e=>e.user.id)];return(await n.z.purchase.findMany({where:{userId:{in:a},createdAt:{gte:t,lte:r},status:"completed"},select:{totalPV:!0}})).reduce((e,t)=>e+t.totalPV,0)}async function u(e,t,r){let o=await n.z.commissionRate.findMany({where:{active:!0}}),a=o.find(e=>"direct_referral"===e.type),s=o.filter(e=>"level_commission"===e.type).reduce((e,t)=>(null!==t.level&&(e[t.level]=t),e),{}),u=o.find(e=>"group_volume"===e.type),d={directReferralBonus:0,levelCommissions:0,groupVolumeBonus:0,totalCommission:0,breakdown:{directReferral:{amount:0,details:[]},levelCommissions:{amount:0,byLevel:{},details:[]},groupVolume:{amount:0,details:[]}}},c=await n.z.user.findMany({where:{uplineId:e,createdAt:{gte:t,lte:r}},select:{id:!0,name:!0,email:!0,createdAt:!0}});if(a){let e="fixed"===a.rewardType?a.fixedAmount:0;d.directReferralBonus=e*c.length,d.breakdown.directReferral={amount:d.directReferralBonus,details:c.map(t=>({userId:t.id,name:t.name,email:t.email,joinDate:t.createdAt,bonus:e}))}}let f=(await i(e,6)).reduce((e,t)=>{let r=t.level;return e[r]||(e[r]=[]),e[r].push(t.user),e},{}),m=0,p={},w=[];for(let e=1;e<=6;e++){let o=f[e]||[],a=s[e];if(a&&o.length>0){let i=o.map(e=>e.id),l=await n.z.purchase.findMany({where:{userId:{in:i},createdAt:{gte:t,lte:r},status:"completed"},include:{user:!0,product:!0}}),s=0;for(let t of l){let r=0;s+=r="percentage"===a.rewardType?t.totalPV*(a.percentage/100):a.fixedAmount,w.push({level:e,purchaseId:t.id,userId:t.userId,userName:t.user.name,productName:t.product.name,pv:t.totalPV,commissionAmount:r})}m+=s,p[e]=s}}if(d.levelCommissions=m,d.breakdown.levelCommissions={amount:m,byLevel:p,details:w},u){let{leftLegPV:n,rightLegPV:o}=await l(e,t,r),a=Math.min(n,o),i=0;d.groupVolumeBonus=i="percentage"===u.rewardType?a*(u.percentage/100):Math.floor(a/1e3)*u.fixedAmount,d.breakdown.groupVolume={amount:i,details:[{leftLegPV:n,rightLegPV:o,weakerLegPV:a,bonusRate:"percentage"===u.rewardType?`${u.percentage}%`:`${u.fixedAmount} per 1000 PV`,bonusAmount:i}]}}return d.totalCommission=d.directReferralBonus+d.levelCommissions+d.groupVolumeBonus,d}async function d(e,t,r,o){return await n.z.monthlyPerformance.upsert({where:{userId_year_month:{userId:e,year:t,month:r}},update:o,create:{userId:e,year:t,month:r,...o}})}async function c(e,t,r){let o={userId:e};return void 0!==t&&(o.year=t),void 0!==r&&(o.month=r),await n.z.monthlyPerformance.findMany({where:o,orderBy:[{year:"desc"},{month:"desc"}]})}async function f(e,t,r=10){return await n.z.monthlyPerformance.findMany({where:{year:e,month:t},orderBy:{totalEarnings:"desc"},take:r,include:{user:{select:{id:!0,name:!0,email:!0,rankId:!0,rank:!0}}}})}async function m(e,t,r){let n=new Date(t,r-1,1),o=new Date(t,r,0,23,59,59,999),a=await u(e,n,o),i=await p(e,n,o),{leftLegPV:s,rightLegPV:c,totalPV:f}=await l(e,n,o);return await d(e,t,r,{personalPV:i,leftLegPV:s,rightLegPV:c,totalGroupPV:f,directReferralBonus:a.directReferralBonus,levelCommissions:a.levelCommissions,groupVolumeBonus:a.groupVolumeBonus,totalEarnings:a.totalCommission}),a}async function p(e,t,r){return(await n.z.purchase.findMany({where:{userId:e,createdAt:{gte:t,lte:r},status:"completed"},select:{totalPV:!0}})).reduce((e,t)=>e+t.totalPV,0)}},78335:()=>{},96487:()=>{}};
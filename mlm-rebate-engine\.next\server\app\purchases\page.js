(()=>{var e={};e.id=7313,e.ids=[7313],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24746:(e,s,t)=>{Promise.resolve().then(t.bind(t,31987))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31987:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\client\\\\Lyn\\\\MLM\\\\mlm-rebate-engine\\\\src\\\\app\\\\purchases\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\purchases\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67704:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>m,routeModule:()=>o,tree:()=>c});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["purchases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,31987)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\purchases\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\purchases\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},o=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/purchases/page",pathname:"/purchases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},85987:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(60687),r=t(43210),i=t(82136),n=t(16189),l=t(68367),d=t(23877),c=t(30474),m=t(85814),x=t.n(m);function o({purchase:e,onClose:s}){let[t,i]=(0,r.useState)("details"),n=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),l=e=>new Date(e).toLocaleString();return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Purchase Details"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(d.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 mb-6",children:[(0,a.jsx)("div",{className:"md:w-1/3",children:(0,a.jsx)("div",{className:"relative w-full h-48 rounded-md overflow-hidden border border-gray-200",children:e.product.image?(0,a.jsx)(c.default,{src:e.product.image,alt:e.product.name,fill:!0,className:"object-contain"}):(0,a.jsx)("div",{className:"w-full h-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)(d.AsH,{className:"text-gray-400 text-4xl"})})})}),(0,a.jsxs)("div",{className:"md:w-2/3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:e.product.name}),(0,a.jsxs)("p",{className:"text-gray-500",children:["Order #",e.id]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.bfZ,{className:"text-gray-400 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:l(e.createdAt)})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Price per unit"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:n(e.product.price)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Quantity"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:e.quantity})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"PV"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:e.totalPV})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"Status"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:e.status})]})]}),(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Subtotal:"}),(0,a.jsx)("span",{className:"font-medium",children:n(e.product.price*e.quantity)})]}),null!==e.shippingFee&&e.shippingFee>0&&(0,a.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Shipping Fee:"}),(0,a.jsx)("span",{className:"font-medium",children:n(e.shippingFee)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-2 text-lg font-bold",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsx)("span",{children:n(e.totalAmount)})]})]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsx)("button",{className:`px-4 py-2 font-medium text-sm ${"details"===t?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>i("details"),children:"Order Details"}),(0,a.jsx)("button",{className:`px-4 py-2 font-medium text-sm ${"shipping"===t?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>i("shipping"),children:"Shipping"}),(0,a.jsx)("button",{className:`px-4 py-2 font-medium text-sm ${"payment"===t?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>i("payment"),children:"Payment"})]}),(0,a.jsxs)("div",{className:"py-4",children:["details"===t&&(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Order Summary"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Product:"}),(0,a.jsx)("span",{className:"font-medium",children:e.product.name})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Price per unit:"}),(0,a.jsx)("span",{className:"font-medium",children:n(e.product.price)})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Quantity:"}),(0,a.jsx)("span",{className:"font-medium",children:e.quantity})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"PV:"}),(0,a.jsx)("span",{className:"font-medium",children:e.totalPV})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Order Date:"}),(0,a.jsx)("span",{className:"font-medium",children:l(e.createdAt)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Order Status:"}),(0,a.jsx)("span",{className:"font-medium",children:e.status})]})]})]})}),"shipping"===t&&(0,a.jsx)("div",{className:"space-y-4",children:e.shippingMethod?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)("div",{className:"mr-3 text-xl",children:(e=>{if(!e)return(0,a.jsx)(d.CE5,{className:"text-gray-500"});switch(e){case"pickup":return(0,a.jsx)(d.Tvt,{className:"text-blue-500"});case"lalamove":return(0,a.jsx)(d.N8c,{className:"text-orange-500"});case"jnt":return(0,a.jsx)(d.dv1,{className:"text-red-500"});default:return(0,a.jsx)(d.CE5,{className:"text-gray-500"})}})(e.shippingMethod.code)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.shippingMethod.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:null!==e.shippingFee&&e.shippingFee>0?`Shipping Fee: ${n(e.shippingFee)}`:"Free Shipping"})]})]})]}),e.shippingStatus&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Status"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(e=>{if(!e)return null;switch(e){case"pending":return(0,a.jsxs)("div",{className:"flex items-center text-yellow-600",children:[(0,a.jsx)(d.w_X,{className:"mr-2"}),(0,a.jsx)("span",{children:"Pending"})]});case"processing":return(0,a.jsxs)("div",{className:"flex items-center text-blue-600",children:[(0,a.jsx)(d.rrY,{className:"mr-2"}),(0,a.jsx)("span",{children:"Processing"})]});case"shipped":return(0,a.jsxs)("div",{className:"flex items-center text-purple-600",children:[(0,a.jsx)(d.CE5,{className:"mr-2"}),(0,a.jsx)("span",{children:"Shipped"})]});case"delivered":return(0,a.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,a.jsx)(d.CMH,{className:"mr-2"}),(0,a.jsx)("span",{children:"Delivered"})]});case"cancelled":return(0,a.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,a.jsx)(d.QCr,{className:"mr-2"}),(0,a.jsx)("span",{children:"Cancelled"})]});default:return(0,a.jsx)("div",{className:"flex items-center text-gray-600",children:(0,a.jsx)("span",{children:e})})}})(e.shippingStatus),e.trackingNumber&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Tracking Number:"}),(0,a.jsx)("span",{className:"font-medium ml-2",children:e.trackingNumber})]})]})]}),e.shippingAddress&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Address"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(d.vq8,{className:"text-red-500 mt-1 mr-2"}),(0,a.jsx)("div",{className:"text-gray-800",children:e.shippingAddress})]})]}),e.shippingDetails&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Shipping Details"}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(()=>{if(!e.shippingDetails)return null;try{let s=JSON.parse(e.shippingDetails);return(0,a.jsx)("div",{className:"mt-4 space-y-2",children:Object.entries(s).map(([e,s])=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[e,":"]}),(0,a.jsx)("span",{className:"font-medium",children:String(s)})]},e))})}catch(e){return console.error("Error parsing shipping details:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing shipping details"})}})()})]}),"pickup"===e.shippingMethod.code&&(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(d.__w,{className:"text-blue-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-blue-700",children:"Store Pickup Information"}),(0,a.jsxs)("p",{className:"text-sm text-blue-600 mt-1",children:["You can pick up your order at our store located at:",(0,a.jsx)("strong",{children:" 123 Main Street, Makati City, Metro Manila"})]}),(0,a.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Store Hours: Monday to Saturday, 9:00 AM to 6:00 PM"})]})]})]}):(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(d.BS8,{className:"text-yellow-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700",children:"No Shipping Information"}),(0,a.jsx)("p",{className:"text-sm text-yellow-600 mt-1",children:"This order does not have shipping information."})]})]})}),"payment"===t&&(0,a.jsxs)("div",{className:"space-y-4",children:[e.paymentMethod?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(d.MxO,{className:"text-green-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.paymentMethod.name}),e.referenceNumber&&(0,a.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:["Reference Number: ",e.referenceNumber]})]})]})]}),e.paymentDetails&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Details"}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-md",children:(()=>{if(!e.paymentDetails)return null;try{let s=JSON.parse(e.paymentDetails);return(0,a.jsx)("div",{className:"mt-4 space-y-2",children:Object.entries(s).map(([e,s])=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[e,":"]}),(0,a.jsx)("span",{className:"font-medium",children:String(s)})]},e))})}catch(e){return console.error("Error parsing payment details:",e),(0,a.jsx)("div",{className:"mt-4 text-red-500",children:"Error parsing payment details"})}})()})]})]}):(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-md flex items-start",children:[(0,a.jsx)(d.BS8,{className:"text-yellow-500 mt-1 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-700",children:"No Payment Information"}),(0,a.jsx)("p",{className:"text-sm text-yellow-600 mt-1",children:"This order does not have payment information."})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Payment Summary"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Subtotal:"}),(0,a.jsx)("span",{className:"font-medium",children:n(e.product.price*e.quantity)})]}),null!==e.shippingFee&&e.shippingFee>0&&(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Shipping Fee:"}),(0,a.jsx)("span",{className:"font-medium",children:n(e.shippingFee)})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-2 border-t border-gray-200 font-bold",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsx)("span",{children:n(e.totalAmount)})]})]})]})]})]})]})]})]})})}function p(){let{data:e,status:s}=(0,i.useSession)();(0,n.useRouter)();let[t,m]=(0,r.useState)(!0),[p,h]=(0,r.useState)([]),[u,j]=(0,r.useState)({total:0,limit:10,offset:0,hasMore:!1}),[g,f]=(0,r.useState)(null),[N,y]=(0,r.useState)(null),[b,v]=(0,r.useState)(!1),w=e=>{j({...u,offset:e})},P=e=>{y(e),v(!0)},S=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),M=e=>new Date(e).toLocaleDateString(),k=e=>{if(!e)return(0,a.jsx)(d.CE5,{className:"text-gray-500"});switch(e){case"pickup":return(0,a.jsx)(d.Tvt,{className:"text-blue-500"});case"lalamove":return(0,a.jsx)(d.N8c,{className:"text-orange-500"});case"jnt":return(0,a.jsx)(d.dv1,{className:"text-red-500"});default:return(0,a.jsx)(d.CE5,{className:"text-gray-500"})}},C=e=>{if(!e)return null;switch(e){case"pending":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800",children:[(0,a.jsx)(d.w_X,{className:"mr-1"})," Pending"]});case"processing":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800",children:[(0,a.jsx)(d.rrY,{className:"mr-1"})," Processing"]});case"shipped":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800",children:[(0,a.jsx)(d.CE5,{className:"mr-1"})," Shipped"]});case"delivered":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:[(0,a.jsx)(d.CMH,{className:"mr-1"})," Delivered"]});case"cancelled":return(0,a.jsxs)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:[(0,a.jsx)(d.QCr,{className:"mr-1"})," Cancelled"]});default:return(0,a.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800",children:e})}};return"loading"===s||t?(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen",children:[(0,a.jsx)(d.hW,{className:"animate-spin text-blue-500 mr-2"}),(0,a.jsx)("span",{children:"Loading purchases..."})]})}):(0,a.jsxs)(l.A,{children:[(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"My Purchases"}),g&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:g}),0===p.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)(d.AsH,{className:"mx-auto h-12 w-12 text-gray-300 mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"No purchases yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"You haven't made any purchases yet. Start shopping to see your purchase history."}),(0,a.jsx)(x(),{href:"/shop",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Browse Products"})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order Details"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Shipping"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10 relative",children:e.product.image?(0,a.jsx)(c.default,{src:e.product.image,alt:e.product.name,fill:!0,className:"rounded-md object-cover"}):(0,a.jsx)("div",{className:"h-10 w-10 bg-gray-200 rounded-md flex items-center justify-center",children:(0,a.jsx)(d.AsH,{className:"text-gray-500"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.product.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Quantity: ",e.quantity]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:S(e.totalAmount)}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.totalPV," PV"]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.shippingMethod?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center text-sm font-medium text-gray-900",children:[k(e.shippingMethod.code),(0,a.jsx)("span",{className:"ml-1",children:e.shippingMethod.name})]}),(0,a.jsx)("div",{className:"mt-1",children:C(e.shippingStatus)}),e.trackingNumber&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Tracking: ",e.trackingNumber]})]}):(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Not specified"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.paymentMethod?(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.paymentMethod.name}):(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Not specified"}),e.referenceNumber&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Ref: ",e.referenceNumber]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.bfZ,{className:"text-gray-400 mr-1"}),M(e.createdAt)]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsx)("button",{onClick:()=>P(e),className:"text-blue-600 hover:text-blue-900",children:"View Details"})})]},e.id))})]})}),u.total>u.limit&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",u.offset+1," to"," ",Math.min(u.offset+u.limit,u.total)," of"," ",u.total," purchases"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>w(Math.max(0,u.offset-u.limit)),disabled:0===u.offset,className:"px-3 py-1 border rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>w(u.offset+u.limit),disabled:!u.hasMore,className:"px-3 py-1 border rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})]}),b&&N&&(0,a.jsx)(o,{purchase:N,onClose:()=>v(!1)})]})}},87794:(e,s,t)=>{Promise.resolve().then(t.bind(t,85987))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,8414,9567,3877,474,4859,3024],()=>t(67704));module.exports=a})();
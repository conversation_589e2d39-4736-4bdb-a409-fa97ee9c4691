"use strict";(()=>{var e={};e.id=9139,e.ids=[9139],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4229:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>j,serverHooks:()=>v,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>R});var s={};t.r(s),t.d(s,{GET:()=>g,POST:()=>h});var o=t(96559),n=t(48088),a=t(37719),u=t(31183),i=t(32190),c=t(19854),f=t(12909),p=t(27942),l=t(77352),m=t(70762);let d=m.z.object({year:m.z.number().int().min(2e3).max(2100),month:m.z.number().int().min(1).max(12),cutoffDay:m.z.number().int().min(1).max(31),notes:m.z.string().optional()}),x=m.z.object({cutoffDay:m.z.number().int().min(1).max(31).optional(),notes:m.z.string().optional()});async function g(e){try{let r,t,s=await (0,c.getServerSession)(f.Nh);if(!s||!s.user)return i.NextResponse.json({error:"You must be logged in to view cutoff data"},{status:401});let o=s.user.email;if(!o)return i.NextResponse.json({error:"User email not found in session"},{status:400});let n=await u.z.user.findUnique({where:{email:o},select:{id:!0,rankId:!0}});if(!n)return i.NextResponse.json({error:"User not found"},{status:404});if(!(n.rankId>=6))return i.NextResponse.json({error:"You do not have permission to view cutoff data"},{status:403});let a=new URL(e.url),l=a.searchParams.get("action")||"list",m=a.searchParams.get("year"),d=a.searchParams.get("month");switch(m&&(r=parseInt(m)),d&&(t=parseInt(d)),l){case"list":let x=await (0,p.A4)(r,t);return i.NextResponse.json({cutoffs:x});case"current":let g=await (0,p.cv)();return i.NextResponse.json({cutoff:g});default:return i.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("Error fetching cutoff data:",e),i.NextResponse.json({error:"Failed to fetch cutoff data"},{status:500})}}async function h(e){try{let r=await (0,c.getServerSession)(f.Nh);if(!r||!r.user)return i.NextResponse.json({error:"You must be logged in to manage cutoffs"},{status:401});let t=r.user.email;if(!t)return i.NextResponse.json({error:"User email not found in session"},{status:400});let s=await u.z.user.findUnique({where:{email:t},select:{id:!0,rankId:!0}});if(!s)return i.NextResponse.json({error:"User not found"},{status:404});if(!(s.rankId>=6))return i.NextResponse.json({error:"You do not have permission to manage cutoffs"},{status:403});let o=await e.json();switch(o.action||"create"){case"create":let n=d.safeParse(o);if(!n.success)return i.NextResponse.json({error:n.error.errors},{status:400});let a=n.data,m=await (0,p.Xu)({year:a.year,month:a.month,cutoffDay:a.cutoffDay,notes:a.notes});return i.NextResponse.json({cutoff:m,message:"Monthly cutoff created successfully"});case"update":let g=o.cutoffId;if(!g||"number"!=typeof g)return i.NextResponse.json({error:"Invalid cutoff ID"},{status:400});let h=x.safeParse(o);if(!h.success)return i.NextResponse.json({error:h.error.errors},{status:400});let j=h.data,y=await (0,p._8)(g,j);return i.NextResponse.json({cutoff:y,message:"Monthly cutoff updated successfully"});case"process":if(!o.year||!o.month)return i.NextResponse.json({error:"Year and month are required"},{status:400});let R=parseInt(o.year),v=parseInt(o.month),w=await (0,l.vm)(R,v);return i.NextResponse.json({result:w,message:"Monthly commissions processed successfully"});default:return i.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("Error managing cutoffs:",e),i.NextResponse.json({error:e instanceof Error?e.message:"Failed to manage cutoffs"},{status:500})}}let j=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/mlm-config/cutoff/route",pathname:"/api/mlm-config/cutoff",filename:"route",bundlePath:"app/api/mlm-config/cutoff/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\mlm-config\\cutoff\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:R,serverHooks:v}=j;function w(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:R})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,8044,3112,8381,2610,7352],()=>t(4229));module.exports=s})();
(()=>{var e={};e.id=9523,e.ids=[9523],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>d});var s=r(96559),n=r(48088),i=r(37719),o=r(32190),c=r(31183),p=r(9252);async function d(e){try{let e=await c.z.product.count({where:{featured:!0}});if(0===e)return o.NextResponse.json(p.featuredProducts);let t=(await c.z.product.findMany({where:{featured:!0},include:{category:!0,productImages:{where:{isDefault:!0},take:1}},orderBy:{createdAt:"desc"}})).map(e=>({id:e.id,name:e.name,description:e.shortDescription||e.description.substring(0,100)+"...",price:e.price,salePrice:e.salePrice,image:e.productImages[0]?.url||"/images/placeholder.png",category:e.category?.name||"Uncategorized",pointValue:e.pointValue,rating:5}));return o.NextResponse.json(t)}catch(e){return console.error("Error fetching featured products:",e),o.NextResponse.json(p.featuredProducts)}}let u=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/products/featured/route",pathname:"/api/products/featured",filename:"route",bundlePath:"app/api/products/featured/route"},resolvedPagePath:"C:\\client\\Lyn\\MLM\\mlm-rebate-engine\\src\\app\\api\\products\\featured\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=u;function h(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},9252:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GET:()=>p,POST:()=>c,featuredProducts:()=>o});var a=r(31183),s=r(32190),n=r(69812),i=r(89605);let o=[{id:1,name:"Biogen Extreme Concentrate",description:"Concentrated organic enzyme formula that helps maintain pH balance and oxygenate cells.",price:1100,srp:1250,salePrice:1100,image:"/images/products/biogen-extreme/biogen-extreme-main.jpg",category:"Health Supplements",pointValue:50,pv:50,stock:100,featured:!0},{id:2,name:"Veggie Coffee 124 in 1",description:"A caffeine-free coffee alternative with 124 natural ingredients that support detoxification, health maintenance, and weight management.",price:850,srp:980,salePrice:850,image:"/images/products/veggie-coffee/veggie-coffee-main.jpg",category:"Health Supplements",pointValue:40,pv:40,stock:150,featured:!0},{id:3,name:"Biogen Shield Herbal Care Soap",description:"A premium herbal soap that whitens, renews, and nourishes skin while providing anti-bacterial protection and deodorizing benefits.",price:99,srp:120,salePrice:99,image:"/images/products/shield-soap/shield-soap-main.jpg",category:"Personal Care",pointValue:10,pv:10,stock:200,featured:!0},{id:4,name:"Immune Support Complex",description:"Powerful blend of vitamins, minerals, and herbs to support immune system function.",price:850,srp:980,salePrice:850,image:"/images/products/immune-support.jpg",category:"Health Supplements",pointValue:40,pv:40,stock:75,featured:!0}];async function c(e){try{let t=await e.json(),r=(0,n.tf)(n.HU,{...t,price:parseFloat(t.price),srp:parseFloat(t.srp||t.price),pv:parseFloat(t.pv||"0"),rebateConfigs:t.rebateConfigs?.map(e=>({level:parseInt(e.level),percentage:parseFloat(e.percentage)}))});if(!r.success)return s.NextResponse.json({errors:r.errors},{status:400});let{name:o,description:c,price:p,srp:d,pv:u,image:l,rebateConfigs:m}=r.data,g=await a.z.$transaction(async e=>{let t=await e.product.create({data:{name:o,description:c,price:p,srp:d,pv:u,image:l}});if(m&&m.length>0)for(let r of m)await e.rebateConfig.create({data:{productId:t.id,level:r.level,percentage:r.percentage}});return t});return i._L.clearNamespace(),s.NextResponse.json(g,{status:201})}catch(e){return console.error("Error creating product:",e),s.NextResponse.json({error:"Failed to create product"},{status:500})}}async function p(e){try{let t=new URL(e.url),r=parseInt(t.searchParams.get("page")||"1"),n=parseInt(t.searchParams.get("pageSize")||"10"),o=t.searchParams.get("search")||"",c=t.searchParams.get("isActive"),p=t.searchParams.get("sortBy")||"createdAt",d=t.searchParams.get("sortOrder")||"desc",u=(r-1)*n,l={};o&&(l.OR=[{name:{contains:o,mode:"insensitive"}},{description:{contains:o,mode:"insensitive"}}]),null!==c&&(l.isActive="true"===c);let m={};"name"===p?m.name=d:"price"===p?m.price=d:m.createdAt=d;let g=`products:${r}:${n}:${o}:${c}:${p}:${d}`,h=await i._L.getOrSet(g,async()=>{let e=await a.z.product.findMany({where:l,include:{rebateConfigs:{orderBy:{level:"asc"}}},skip:u,take:n,orderBy:m}),t=await a.z.product.count({where:l}),s=Math.ceil(t/n);return{products:e,pagination:{page:r,pageSize:n,totalItems:t,totalPages:s}}},3e5);return s.NextResponse.json(h)}catch(e){return console.error("Error fetching products:",e),s.NextResponse.json({error:"Failed to fetch products"},{status:500})}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=global.prisma||new a.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69812:(e,t,r)=>{"use strict";r.d(t,{HU:()=>i,Ld:()=>o,q8:()=>n,tf:()=>c,zK:()=>s});var a=r(70762);a.z.object({email:a.z.string().email("Invalid email address"),password:a.z.string().min(8,"Password must be at least 8 characters"),csrfToken:a.z.string().optional()});let s=a.z.object({name:a.z.string().min(2,"Name must be at least 2 characters"),email:a.z.string().email("Invalid email address"),password:a.z.string().min(8,"Password must be at least 8 characters"),confirmPassword:a.z.string().min(8,"Confirm password must be at least 8 characters"),phone:a.z.string().optional(),birthdate:a.z.string().optional(),address:a.z.string().optional(),city:a.z.string().optional(),region:a.z.string().optional(),postalCode:a.z.string().optional(),uplineId:a.z.string().optional(),profileImage:a.z.string().optional(),preferredPaymentMethod:a.z.string().optional(),bankName:a.z.string().optional(),bankAccountNumber:a.z.string().optional(),bankAccountName:a.z.string().optional(),gcashNumber:a.z.string().optional(),payMayaNumber:a.z.string().optional(),receiveUpdates:a.z.boolean().optional().default(!1),agreeToTerms:a.z.boolean()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>!0===e.agreeToTerms,{message:"You must agree to the terms and conditions",path:["agreeToTerms"]}).refine(e=>"bank"===e.preferredPaymentMethod?!!e.bankName&&!!e.bankAccountNumber&&!!e.bankAccountName:"gcash"===e.preferredPaymentMethod?!!e.gcashNumber:"paymaya"!==e.preferredPaymentMethod||!!e.payMayaNumber,{message:"Payment details are required for the selected payment method",path:["preferredPaymentMethod"]}),n=a.z.object({name:a.z.string().min(2,"Name must be at least 2 characters").optional(),phone:a.z.string().optional(),currentPassword:a.z.string().optional(),newPassword:a.z.string().min(8,"New password must be at least 8 characters").optional(),confirmNewPassword:a.z.string().optional(),profileImage:a.z.string().optional()}).refine(e=>!e.newPassword||e.newPassword===e.confirmNewPassword,{message:"New passwords don't match",path:["confirmNewPassword"]}).refine(e=>!e.newPassword||!!e.currentPassword,{message:"Current password is required to set a new password",path:["currentPassword"]}),i=a.z.object({name:a.z.string().min(2,"Product name must be at least 2 characters"),description:a.z.string().optional(),price:a.z.number().positive("Price must be positive"),image:a.z.string().optional(),rebateConfigs:a.z.array(a.z.object({level:a.z.number().int().positive("Level must be a positive integer"),percentage:a.z.number().positive("Percentage must be positive").max(100,"Percentage cannot exceed 100%")})).min(1,"At least one rebate configuration is required")}),o=a.z.object({productId:a.z.number().int().positive("Product ID must be a positive integer"),quantity:a.z.number().int().positive("Quantity must be a positive integer"),paymentMethodId:a.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:a.z.record(a.z.any()).optional(),referenceNumber:a.z.string().optional(),shippingMethodId:a.z.number().int().positive("Shipping method ID must be a positive integer").optional(),shippingDetails:a.z.record(a.z.any()).optional(),shippingAddress:a.z.string().optional(),shippingFee:a.z.number().nonnegative("Shipping fee must be a non-negative number").optional(),referralCode:a.z.string().optional()});function c(e,t){try{let r=e.parse(t);return{success:!0,data:r}}catch(e){if(e instanceof a.z.ZodError){let t={};return e.errors.forEach(e=>{t[e.path.join(".")]=e.message}),{success:!1,errors:t}}return{success:!1,errors:{_error:"An unexpected error occurred during validation"}}}}a.z.object({amount:a.z.number().positive("Amount must be positive"),type:a.z.enum(["withdrawal","deposit"],{errorMap:()=>({message:"Invalid transaction type"})}),description:a.z.string().optional(),paymentMethodId:a.z.number().int().positive("Payment method ID must be a positive integer").optional(),paymentDetails:a.z.record(a.z.any()).optional(),referenceNumber:a.z.string().optional()}),a.z.object({checkAll:a.z.boolean().optional()})},78335:()=>{},89605:(e,t,r)=>{"use strict";r.d(t,{_L:()=>o,g3:()=>i,ic:()=>c});class a{constructor(e=3e5){this.cache=new Map,this.defaultTTL=e}set(e,t,r){let a=Date.now()+(r||this.defaultTTL);this.cache.set(e,{value:t,expiry:a})}get(e){let t=this.cache.get(e);if(t)return Date.now()>t.expiry?void this.cache.delete(e):t.value}has(e){let t=this.cache.get(e);return!!t&&(!(Date.now()>t.expiry)||(this.cache.delete(e),!1))}delete(e){this.cache.delete(e)}clear(){this.cache.clear()}async getOrSet(e,t,r){let a=this.get(e);if(void 0!==a)return a;let s=await t();return this.set(e,s,r),s}cleanup(){let e=Date.now();for(let[t,r]of this.cache.entries())e>r.expiry&&this.cache.delete(t)}}let s=new a,n=e=>({set:(t,r,a)=>s.set(`${e}:${t}`,r,a),get:t=>s.get(`${e}:${t}`),has:t=>s.has(`${e}:${t}`),delete:t=>s.delete(`${e}:${t}`),getOrSet:(t,r,a)=>s.getOrSet(`${e}:${t}`,r,a),clearNamespace:()=>{for(let[t]of s.cache.entries())t.startsWith(`${e}:`)&&s.delete(t)}}),i=n("user"),o=n("product"),c=n("genealogy");n("rebate"),setInterval(()=>{s.cleanup()},36e5)},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,8381],()=>r(7249));module.exports=a})();
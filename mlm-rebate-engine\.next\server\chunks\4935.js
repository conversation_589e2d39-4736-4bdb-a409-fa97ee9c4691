exports.id=4935,exports.ids=[4935],exports.modules={12909:(e,r,a)=>{"use strict";a.d(r,{Er:()=>l,Nh:()=>d,aP:()=>c});var o=a(96330),s=a(13581),n=a(85663),t=a(55511),i=a.n(t);async function l(e){return await n.Ay.hash(e,10)}function c(){let e=i().randomBytes(32).toString("hex"),r=new Date;return r.setHours(r.getHours()+1),{token:e,expiresAt:r}}new o.PrismaClient;let d={debug:!0,logger:{error:(e,r)=>{console.error(`NextAuth error: ${e}`,r)},warn:e=>{console.warn(`NextAuth warning: ${e}`)},debug:(e,r)=>{console.log(`NextAuth debug: ${e}`,r)}},providers:[(0,s.A)({id:"credentials",name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("Authorize function called with credentials:",e?.email),!e?.email||!e?.password)throw console.log("Missing email or password"),Error("Missing email or password");try{console.log("Looking up user:",e.email);let r=new o.PrismaClient,a=await r.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,password:!0}});if(await r.$disconnect(),!a)throw console.log("User not found"),Error("Invalid email or password");console.log("User found, checking password"),console.log("Stored password hash:",a.password),console.log("Provided password (first few chars):",e.password.substring(0,3)+"...");let s=await n.Ay.compare(e.password,a.password);if(console.log("Password valid:",s),!s)throw console.log("Invalid password"),Error("Invalid email or password");console.log("Authentication successful for user:",a.id);let{password:t,...i}=a;return{id:a.id.toString(),email:a.email,name:a.name}}catch(e){throw console.error("Error in authorize function:",e),Error(e instanceof Error?e.message:"Authentication failed")}}})],session:{strategy:"jwt",maxAge:86400},pages:{signIn:"/login",error:"/login?error=true"},callbacks:{session:async({session:e,token:r})=>(console.log("Session callback called",{session:e,token:r}),r&&e.user&&(e.user.id=r.sub),e),jwt:async({token:e,user:r})=>(console.log("JWT callback called",{token:e,user:r}),r&&(e.sub=r.id),e)},secret:process.env.NEXTAUTH_SECRET||"fallback-secret-do-not-use-in-production",useSecureCookies:!0,cookies:{sessionToken:{name:"__Secure-next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}}}}},31183:(e,r,a)=>{"use strict";a.d(r,{z:()=>s});var o=a(96330);let s=global.prisma||new o.PrismaClient({log:["query"]})},61904:(e,r,a)=>{"use strict";a.d(r,{Z:()=>n});let o=a(49526).createTransport({host:process.env.EMAIL_HOST||"smtp.example.com",port:parseInt(process.env.EMAIL_PORT||"587"),secure:"true"===process.env.EMAIL_SECURE,auth:{user:process.env.EMAIL_USER||"<EMAIL>",pass:process.env.EMAIL_PASSWORD||"password"}}),s={rebateReceived:e=>({subject:`You've Received a Rebate of $${e.amount.toFixed(2)}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">New Rebate Received!</h2>
          <p>Hello ${e.userName},</p>
          <p>Great news! You've received a rebate of <strong style="color: #48bb78;">$${e.amount.toFixed(2)}</strong>.</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Rebate Details:</h3>
            <ul style="padding-left: 20px;">
              <li>Amount: <strong>$${e.amount.toFixed(2)}</strong></li>
              <li>From: <strong>${e.generatorName}</strong></li>
              <li>Level: <strong>${e.level}</strong></li>
              <li>Product: <strong>${e.productName}</strong></li>
            </ul>
          </div>
          <p>This rebate has been added to your wallet balance. You can view your rebate details and wallet balance by logging into your account.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/wallet" style="background-color: #4299e1; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Wallet</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for being part of our MLM network!</p>
        </div>
      `}),rankAdvancement:e=>({subject:`Congratulations on Your Rank Advancement to ${e.newRank}!`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a5568;">Rank Advancement Achievement!</h2>
          <p>Hello ${e.userName},</p>
          <p>Congratulations! You've advanced from <strong>${e.oldRank}</strong> to <strong style="color: #805ad5;">${e.newRank}</strong>!</p>
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 16px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #4a5568;">Your New Benefits:</h3>
            <ul style="padding-left: 20px;">
              ${e.benefits.map(e=>`<li>${e}</li>`).join("")}
            </ul>
          </div>
          <p>Keep up the great work! As you continue to grow your network and increase your sales, you'll unlock even more benefits and higher rebate percentages.</p>
          <div style="margin-top: 24px;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" style="background-color: #805ad5; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Your Dashboard</a>
          </div>
          <p style="margin-top: 24px; color: #718096; font-size: 14px;">Thank you for your dedication and commitment to our MLM network!</p>
        </div>
      `})};async function n(e,r,a){try{let{subject:n,html:t}=s[r](a),i={from:process.env.EMAIL_FROM||"MLM Rebate Engine <<EMAIL>>",to:e,subject:n,html:t},l=await o.sendMail(i);return console.log("Email sent:",l.messageId),{success:!0,messageId:l.messageId}}catch(e){return console.error("Error sending email:",e),{success:!1,error:e}}}},73967:(e,r,a)=>{"use strict";a.d(r,{G:()=>n,e:()=>t});var o=a(31183),s=a(61904);async function n(e,r){try{let a=await o.z.user.findUnique({where:{id:e},select:{id:!0,name:!0,email:!0,rankId:!0,rank:{select:{name:!0,level:!0}}}});if(!a)return{success:!1,message:`Referrer with ID ${e} not found`};let n=await o.z.user.findUnique({where:{id:r},select:{id:!0,name:!0,email:!0}});if(!n)return{success:!1,message:`New user with ID ${r} not found`};let t=await o.z.referralReward.findMany({where:{active:!0},orderBy:{amount:"desc"}});if(0===t.length)return{success:!1,message:"No active referral rewards found"};let i=t[0],l=0;l="fixed"===i.rewardType?i.amount:100*(i.percentage/100);let c=await o.z.walletTransaction.create({data:{userId:e,amount:l,type:"referral",description:`Referral reward for inviting ${n.name}`,status:"completed"}});if(await o.z.user.update({where:{id:e},data:{walletBalance:{increment:l}}}),a.email)try{await (0,s.Z)(a.email,"referralReward",{userName:a.name,amount:l,referredName:n.name})}catch(r){console.error(`Failed to send referral reward email to user ${e}:`,r)}return{success:!0,message:`Referral reward of ${l} processed successfully`,rewardAmount:l,transactionId:c.id}}catch(e){return console.error("Error processing referral reward:",e),{success:!1,message:`Error: ${e instanceof Error?e.message:"Unknown error"}`}}}async function t(e,r,a){try{let n=await o.z.user.findUnique({where:{id:e},select:{id:!0,name:!0,email:!0,rankId:!0,rank:{select:{name:!0,level:!0}}}});if(!n)return{success:!1,message:`User with ID ${e} not found`};let t=await o.z.bonusReward.findMany({where:{active:!0,triggerType:r}});if(0===t.length)return{success:!1,message:`No active bonus rewards found for trigger type ${r}`};let i=t.filter(e=>{if(!e.triggerValue)return!0;try{let o=JSON.parse(e.triggerValue);switch(r){case"rank_advancement":return(!o.rankId||o.rankId===a.newRankId)&&(!o.minLevel||n.rank.level>=o.minLevel);case"sales_milestone":return(!o.minAmount||a.amount>=o.minAmount)&&(!o.minRankLevel||n.rank.level>=o.minRankLevel);default:return!1}}catch(r){return console.error(`Error parsing trigger value for bonus reward ${e.id}:`,r),!1}});if(0===i.length)return{success:!1,message:"No applicable bonus rewards found for the given trigger data"};let l=i.reduce((e,r)=>{let a="fixed"===e.rewardType?e.amount:e.percentage;return("fixed"===r.rewardType?r.amount:r.percentage)>a?r:e},i[0]),c=0;c="fixed"===l.rewardType?l.amount:(a.amount||100)*(l.percentage/100);let d=await o.z.walletTransaction.create({data:{userId:e,amount:c,type:"bonus",description:`Bonus reward: ${l.name}`,status:"completed"}});if(await o.z.user.update({where:{id:e},data:{walletBalance:{increment:c}}}),n.email)try{await (0,s.Z)(n.email,"bonusReward",{userName:n.name,amount:c,bonusName:l.name,bonusDescription:l.description||""})}catch(r){console.error(`Failed to send bonus reward email to user ${e}:`,r)}return{success:!0,message:`Bonus reward of ${c} processed successfully`,rewardAmount:c,rewardName:l.name,transactionId:d.id}}catch(e){return console.error("Error processing bonus reward:",e),{success:!1,message:`Error: ${e instanceof Error?e.message:"Unknown error"}`}}}},78335:()=>{},96487:()=>{}};